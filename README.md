# Welcome to the G3 Project

<!-- TOC -->

- [Welcome to the G3 Project](#welcome-to-the-g3-project)
  - [Linux Project Onboarding](#linux-project-onboarding)
    - [Current Limitations](#current-limitations)
    - [1. Preparations](#1-preparations)
    - [2. Install and Prepare WSL](#2-install-and-prepare-wsl)
      - [Enable Hyper-V Features](#enable-hyper-v-features)
      - [Install instructions - No prior WSL installation](#install-instructions---no-prior-wsl-installation)
      - [Install instructions - Existing WSL installation](#install-instructions---existing-wsl-installation)
      - [Validate WSL Version](#validate-wsl-version)
      - [G3 Dev Tools Installation](#g3-dev-tools-installation)
      - [Clone the G3 Repo](#clone-the-g3-repo)
    - [3. Get familiar with WSL](#3-get-familiar-with-wsl)
      - [Install Terminals - Optional](#install-terminals---optional)
      - [WSL Filesystem access](#wsl-filesystem-access)
      - [WSL GUI based apps](#wsl-gui-based-apps)
    - [4. Get familiar with Docker](#4-get-familiar-with-docker)
    - [5. IntelliJ Setup](#5-intellij-setup)
      - [Import your License](#import-your-license)
      - [Import the Project](#import-the-project)
      - [Set the docker compose version](#set-the-docker-compose-version-)
      - [Configure for Docker](#configure-for-docker)
      - [Configure for SonarCloud](#configure-for-sonarcloud)
      - [Configure for GitHub](#configure-for-github)
      - [Configure for CoPilot](#configure-for-copilot)
      - [Configure for JRebel](#configure-for-jrebel)
    - [6. Docker Build and Run G3](#6-docker-build-and-run-g3)
      - [G3 Build](#g3-build)
      - [G3 DB Migration](#g3-db-migration)
      - [Dockerhub login](#dockerhub-login)
      - [G3 Online App](#g3-online-app)
    - [7. Docker Run prebuilt images](#7-docker-run-prebuilt-images)
      - [Remote Debugging in Intellij](#remote-debugging-in-intellij)
      - [Sandbox properties](#sandbox-properties)
        - [Deploy SandBox Property](#deploy-sandbox-property)
        - [Upload a new sandbox property to S3](#upload-a-new-sandbox-property-to-s3)
      - [G3 Shutdown](#g3-shutdown)
      - [G3 Compose Commands](#g3-compose-commands)
        - [Build](#build)
        - [Build with Tests](#build-with-tests)
        - [DB Upgrade](#db-upgrade)
        - [DB Clean](#db-clean)
        - [Online Application Services](#online-application-services)
        - [Down Application Services](#down-application-services)
        - [Restart Application Services](#restart-application-services)
        - [List Services](#list-services)
        - [List Running Container Processes](#list-running-container-processes)
        - [Cmdline Help](#cmdline-help)
      - [Docker Commands](#docker-commands)
        - [Restart](#restart)
        - [System Prune](#system-prune)
        - [Cmdline Help](#cmdline-help-1)
  - [Setup Local NGI Connectivity via WSL](#setup-local-ngi-connectivity-via-wsl)
  - [Connecting Windows Services to Docker](#connecting-windows-services-to-docker-)
  - [Contributing](#contributing)
    - [Start a new feature branch](#start-a-new-feature-branch)
    - [Sync with Main](#sync-with-main)
      - [Merge](#merge)
      - [Rebase](#rebase)
    - [Push your Feature Branch](#push-your-feature-branch)
    - [Create a PR](#create-a-pr)
    - [Guidelines](#guidelines)
    - [Changes from Gerrit](#changes-from-gerrit)
    - [PR Build results](#pr-build-results)
  - [Helper Commands](#helper-commands)
    - [WSL](#wsl)
      - [Restart](#restart-1)
      - [List Distributions](#list-distributions)
      - [Set Default Distribution](#set-default-distribution)
      - [Check Status](#check-status)
      - [Run Distribution](#run-distribution)
      - [Cmdline Help](#cmdline-help-2)
  - [Repo Paths](#repo-paths)
    - [Repo Root](#repo-root)
    - [Docker Paths](#docker-paths)
    - [Persistence DB and SAS](#persistence-db-and-sas)
  - [Restore a database on WSL local container instance](#restore-a-database-on-wsl-local-container-instance)
  - [Known Issues and solutions](#known-issues-and-solutions)
  - [Developer Notes](#developer-notes)
  _ [Running Unit Tests](#running-unit-tests)
  _ [Running Integration Tests](#running-integration-tests)
  _ [GitHub Container Registry Access](#github-container-registry-access)
  _ [Build only modules instead of entire G3](#build-only-modules-instead-of-entire-g3-)
  _ [LocalStack Integration (Mocking AWS)](docker/aws/README.md)
  _ [Working on release cut/patch version](#working-on-release-cutpatch-version) \* [Readme Confluence link](#readme-confluence-link)
  <!-- TOC -->

## Linux Project Onboarding

The onboarding of this project is in progress of moving towards a pre-packaged, pre-configured,
and ready to go out of the box experience by leveraging the Windows Subsystem for Linux, aka WSL.
The Windows Subsystem for Linux lets developers run a GNU/Linux environment -- including most
command-line tools, utilities, and applications -- directly on Windows, unmodified, without the
overhead of a traditional virtual machine or dual boot setup. WSL also now brings a more recent
addition named Windows Subsystem for Linux GUI (WSLg). WSLg is built with the purpose of enabling
support for running Linux GUI applications on Windows in a fully integrated desktop experience.
Together these two technologies have enabled us to provide a custom-built WSL distribution that
can be downloaded and quickly imported to bring a fully featured and pre-configured Linux based
development environment to your Windows workstation.

For further information about WSL you can reference the docs at https://docs.microsoft.com/en-us/windows/wsl/about

### Current Limitations

---

Before continuing on it is important to understand this is a work in progress and in it current
state has some limitations to be aware of...

- SAS Builds are not supported. SAS builds with Linux support have been moved to a separate repo and
  a SAS container is now produced. SAS development should not be taking place in this repo but in the
  dedicated SAS repo. That stated, it can be stood up and run for testing and QA purposes by simply
  running docker-compose sas service.

Outside of these limitations everything should be fully functional. This includes but is not limited to...

- A linux based Intellij IDE pre-configured to support build, debug, and run unit tests.
- All required programs, maven, java, docker, compose and chrome pre-installed using the required versions.
- Full docker support to build, run, and down all required application services.

In most cases the above should suffice for day to day development and is the recommended approach
going forward. If issues are encountered we ask you work to resolve such issues and push the required
changes.

### 1. Preparations

---

- This setup requires Windows 11 Build 22H2 to proceed. If not already Windows 11 22H2 please work with IT to get upgraded. You can confirm your current Windows version by running the command `winver`. It should report the version as version 22H2.
- Ensure Windows 11 installation is fully up-to-date by visiting Windows Update and installing all
  available updates.
- Make sure to update your graphics driver to the latest driver available from your GPU manufacturer's website to benefit from GPU acceleration in your WSL environment.
- If you have Symantec then disable the firewall under network section.
- Please make sure that your laptop/desktop doesn't have...
  - Windows Sandbox Installed
  - Docker Desktop Installed
- Please make sure that if your machine was set up to run G3 on Windows using the existing Powershell automation that you stop all existing G3 services and set the startup to manual (Apache, Wildfly, SQLServer, OpenAM, and OpenDS). If at any point you need to revert back to the Windows setup this can easily be achieved by simply shutting down wsl `wsl --shutdown` and going back to your old setup.

### 2. Install and Prepare WSL

---

#### Enable Hyper-V Features

It is stated Hyper-V feature should not be required, but we have had mixed install results without this feature enabled and require that this feature be enabled.

From a command prompt with administrator privileges, run the command below and reboot if prompted...

```
DISM /Online /Enable-Feature /All /FeatureName:Microsoft-Hyper-V
```

#### Install instructions - No prior WSL installation

- From a command prompt with administrator privileges, run the command below and reboot if prompted...

  ```
  wsl --install --web-download
  ```

- After reboot the installation will continue. You'll be asked to enter a username and password. These will be your Linux credentials, they can be anything you want and don't have to match your Windows credentials <b>but should be something that you will not forget</b>.

Voilà! WSL and WSLg are installed and ready to be used!

#### Install instructions - Existing WSL installation

If you have an existing WSL installation without WSLg you need to update to the latest version of WSL which includes WSLg. To get the latest and greatest...

- From a command prompt with administrator privileges, run the command <br>`wsl --update --web-download`
- Reboot if prompted.

#### Validate WSL Version

After completion, you can confirm the version by running the below, the versions should line up or be greater than...

```
wsl --version
```

![](md/images/wsl-version.jpg)

- If for any reason your version is still lagging behind and was a fresh install, run the below command to be sure you are running the latest version of WSL.

```
wsl --update
```

#### G3 Dev Tools Installation

All the steps to install the softwares required for running the G3 application in Windows Subsystem for Linux (WSL) are details in the link below.
Please follow the Prerequisites and TL;DR sections in the document.
https://github.com/ideasorg/g3-dev-tools/blob/main/README.md

#### Clone the G3 Repo

Prior to launching Intellij we want to first get this repo cloned into our development environment so we of our project ready to be imported on the first launch of Intellij.

1. Clone the repo into the users repos directory via..

```bash
mkdir repos && cd ~/repos && <NAME_EMAIL>:ideasorg/g3.git
```

2. Creates required directories, settings file etc.

```bash
bash ~/repos/g3/apply_pre_requisites
```

Note : Above script works on relative paths of g3 repo, so keep g3 in ~/repos folder.

3. Set your git email, replace <> with your ideas email address.

```
git config --global user.email <<EMAIL>>
e.g. git config --global user.email <EMAIL>
```

4. Set your git username, replace <> with your username.

```
git config --global user.name <"first lastname">
e.g. git config --global user.name "Utkarsh Archit"
```

### 3. Get familiar with WSL

---

With WSL now installed and the distribution attached, a couple of key productivity items to be aware of...

#### Install Terminals - Optional

When working with WSL distributions it is recommended to install the Terminals app. Windows Terminal is a modern host application for the command-line shells you already love, like Command Prompt, PowerShell, and bash (via Windows Subsystem for Linux (WSL)). Its main features include multiple tabs, panes, Unicode and UTF-8 character support, a GPU accelerated text rendering engine, and the ability to create your own themes and customize text, colors, backgrounds, and shortcuts. For more information on Windows Terminal see https://learn.microsoft.com/en-us/windows/terminal/

1. On your Windows computer, click Start and launch the Microsoft store.
2. In the Microsoft Store, click the Search option and type Windows Terminal.
3. Select Windows Terminal app and click Get.
4. Once installed it is recommended you pin this app to your taskbar via search -> terminal -> right click on the app -> select pin to taskbar.
5. Upon launching this app you will have quick access to the different shell environments that are registered by selecting the open new tab option from the title bar.

#### WSL Filesystem access

When a distribution is attached it should automount and be available via Windows
Explorer listed under Linux as shown in the below screenshot. This enables quick access to copy files directly
from windows to your target WSL distribution and vice versa.

![](md/images/wsl-explorer.jpg)

#### WSL GUI based apps

WSL brings what feels like a native experience when it comes to launching
and working with Linux based GUI applications installed into any WSL distribution. The applications automatically  
register in your windows start menu tagged with the distribution name and the infamous penguin located on the icon
to clearly indicate it is a Linux based application. You can see an example of this as
below. It is recommended to pin the Intellij application to your windows taskbar to enable quicker launch of you IDE.

![](md/images/wsl-gui-apps.jpg)

### 4. Get familiar with Docker

---

To build a basic understanding of Docker, Dockerfile, and Docker-Compose, please go through below course from Oreilly.

[Docker, Dockerfile, and Docker-Compose (2020 Ready!)](https://learning.oreilly.com/course/docker-dockerfile-and/9781800206847/)

### 5. IntelliJ Setup

---

Now that we are a bit familiar with how to work with our WSL environment we can step into the IDE setup.
We have already installed all the necessary apps to begin development. Docker,
Java, Maven, Compose, IntelliJ, Chrome, etc.

#### Import your License

To import your Intellij license will require that you download and import it, using login approach appears more trouble than it is worth. To Import your license follow the below steps...

1. Find "IntelliJ IDEA" from the start menu in Windows with the [Tux](<https://en.wikipedia.org/wiki/Tux_(mascot)>) icon. You can also run it from the terminal by typing intellij (You will need to reload your .bashrc after the installation by running source ~/.bashrc in a terminal).
2. After a few acknowledgements of terms you will be presented with the license screen.
3. In the license screen select the option to use an activation code.
4. Login to https://account.jetbrains.com/licenses using your windows browser.
5. Click the download link to download a zipped archive of your license.
6. Extract the zip and open up the file labeled with `- for 2018.2 or later`.
7. Copy the contents of the license and paste it into the activation code window and hit except.

#### Import the Project

With the license now imported you will be brought to the main Intelli project screen.
Note: $(USER) is your **User ID**
e.g. Here in this case it is utarch
![](md/images/user-id.jpg)

1. Select Open from the main screen. Import the G3 project we cloned in our earlier <b>Clone the G3 Repo</b> steps, it should already be located at `/home/<USER>/repos/g3`
2. When prompted for the path navigate to the G3 repo located at `/home/<USER>/repos/g3`
3. Hit ok to start the project import.

The project should import successfully, and you should now be positioned to start normal development from inside the IDE.

#### Set the docker compose version

In intelliJ, make sure to use docker compose v2: Settings -> Build, Execution, Deployment -> Docker -> Tools -> check Use Compose v2

#### Configure for Docker

The Docker Plugin is already pre-installed and pre-configured. To access you should simply need to..

- Launch the service pane in IntelliJ (Alt + 8)
- Double-Click the Docker Icon to establish the connection.

![](md/images/services.jpg)

#### Configure for SonarCloud

This project relies on SonarCloud to better improve our code quality, security, and test coverage.
Inmost cases builds will be gated and prevent code violations from merging. For this reason it
is required to configure Intellij for sonarcloud to surface this important information in the earliest
stage of development. To configure sonarcloud...

1. Generate a Sonar Token

- Login to SonarCloud using your GitHub login via the below url and selecting the GitHub option.

```
https://sonarcloud.io/login
```

- Once logged in navigate to the far right top user icon and click -> My Account -> Security
- In Generate Tokens enter the Name Development and click Generate.
- Copy the token value to be pasted when completing the next step.

2. Configure the IntelliJ SonarCloud Connection
   - From with-in IntelliJ, navigate to File -> Settings -> Tools -> SonarLint
   - Click the + option under SonarQube / SonarCloud connections.
   - In the new connection field enter Development -> Do not hit the link connect to the online service -> Next
   - For connection type be sure SonarCloud option is selected and click -> Next
   - When prompted for the token, paste the SonarCloud token created in the previous step -> Next
   - In the organization screen ideasorg-github should already be pre-selected -> Next
   - In the notification screen notifications should be enabled -> Next
   - In the configuration completed screen click -> Create
   - After create you should arrive back at the sonar config screen.

![](md/images/sonar-connection-completed.jpg)

3. Configure the project for SonarCloud

- Navigate in settings to Tools -> SonarLint -> Project Settings
- Select the option to bind SonarQube / SonarCloud
- For project key select G3 from the options presented when clicking -> Search in list
- When populated hit the ok, this will trigger the import and update and updates to local storage, this will take a few minutes to complete...

![](md/images/sonar-project.jpg)

#### Configure for GitHub

The GitHub plugin brings the ability to create and review PR's without leaving the IDE. This is a nice
productively enhancement. To configure the GitHub plugins...

1. Generate a personal access token to log in to Github
   - Login to GitHub UI
   - Navigate to User far right -> Settings -> Developer Settings -> Personal access tokens -> Tokens (classic)
   - Generate a new (classic) token with the following scopes
     - repo/workflow/read:org/gist
       ![](md/images/github-personal-access-token-new.png)
   - Copy the access token (you will only see this once and will have to make another if you lose this)
     ![](md/images/github-personal-access-token-completed.png)
   - Authorize it for SSO
2. Set up Github connection in IntelliJ
   - Navigate in settings to Version Control -> Github
   - Select the add icon to select Log In with Token...
   - Paste the generated token that you had generated in the earlier step to complete the linking process

#### Configure for CoPilot

This plugin is in Beta and comes with an additional cost. It brings AI based code generation to the IDE.
With this still in testing phase it will be limited to a selected few, if interested in helping to beta test
this plugin please feel free to reach out to the operations team and let someone know.

#### Configure for JRebel

1. Install JRebel + XRebel plugin from File -> Settings -> Plugins
2. Register your license to activate the plugin
3. Configure jrebel remote server
   - Navigate in File -> Settings -> JRebel & XRebel.
   - Select JRebel Remote Servers
   - Add new remote server with details mentions below
     ![](md/images/jrebel-remote-server-setup.png)
4. Select modules from JRebel panel which you want to reload on the fly
   ![](md/images/jrebel-panel-setup.png)
5. After these settings, you will need to run the build and run app with the new container for the first time use.
6. For this you can follow steps from 5. Docker Build and Run G3.
7. After app is running. Make some changes in the configured module. Ideally changes should get reloaded automatically but if they are not then recompile java file by navigating to Build -> Recompile (ctrl+shift+f9) and click on JRebel : synchronize with remote server button on top right corner of toolbar.

### 6. Docker Build and Run G3

---

As we have taken steps to move G3 to Linux we have also taken the steps to support for containerization. The provided
WSL distribution comes with docker and docker compose installed and running out of the box. The steps below will guide you
through building and running the G3 project via Docker Compose. This is far from a full Docker tutorial, simply the commands
used to get up and running. It is advised to take the time to further read up and to get familiar with both Docker and Docker Compose.

From inside the IntelliJ IDE open a bash shell and run the indicated docker-compose commands . . .

#### G3 Build

```bash
docker compose run --rm build
```

Use the command above to build the G3 project. Subsequent steps require that a full build has been completed
as they require and assume the artifacts are produced and available for packaging. To support the build process
a SQL Server container instance will automatically be started on port 1433 prior to running the build. If
encountering issues in the build related to db, or simply looking to validate the db data, you can use the
below connection details.  
host: 127.0.0.1 username: sa, password: IDeaS123

##### Maven Caching Performance

Maven caching significantly improves build performance after the initial build. **Note: Maven caching must be explicitly enabled** to achieve these performance gains.

- **Initial build**: ~7 minutes (clean build with no cached dependencies)
- **Rebuild with no changes**: ~16 seconds (leverages Maven cache effectively)
- **Partial rebuild** (e.g., change in war-api): ~2.5 minutes (only rebuilds affected modules and dependencies)

The dramatic performance improvement on subsequent builds is due to Maven's incremental compilation and dependency caching. This makes iterative development much more efficient, especially when working on specific modules that don't affect the entire codebase.

##### Partial builds

Depending on your use case, you may or may not want to do a full build of G3.
For example, suppose you are working on `war-api` which has nothing to do with
the Vaadin/UI layer. If you do not have JRebel, then rebuilding the Vaadin/UI
layer is unecessary because there's no direct dependency between the two modules.
Assuming that you've already built the Vaadin/UI layer at least once, you can
rebuild the parts that you need and still bring up G3, thereby reducing the time
it takes to "build" G3.

For the sake of discussion, assume that you have built all G3 modules at least
once for a given pull. Suppose we have a DAG where an edge `A -> B` means that
module A depends on module B:

```
                                __ war-api
                              /
platform-ear -> ejb-pacman <--
                              \ __ job-web
```

###### Only specific modules

If you make changes in `war-api`, then it's unnecessary to build `ejb-pacman` and
`job-web`. You can effectively only build `war-api` with the following command

```bash
docker compose run --rm build only :war-api
```

The command takes in a CSV list of modules, so for example, if you also need to
build `job-web`, then the command would be

```bash
docker compose run --rm build only :war-api,:job-web
```

The ordering of the modules does not matter. For QOL, `:ear` is automatically
included so that you don't have to subsequently provide or build `:ear`

###### Only specific modules and modules that they depend on

If you make changes in `war-api` and you did a pull such that it brings
changes into what it depends on, e.g., `ejb-pacman`, then it may be
beneficial to build it and its dependencies to have the latest changes.
You can do so with the following command

```bash
docker compose run --rm build only_am :war-api
```

The command takes in a CSV list of modules similar to the `only` build; ordering
does not matter

###### Only specific modules and modules that depend on them

If you make changes in `ejb-pacman,` then it may be beneficial to also build
any modules that depend on it, e.g., `platform-ear, war-api,` and `job-web`,
to avoid runtime incompatability. You can do so with the following command

```bash
docker compose run --rm build only_amd :ejb-pacman
```

The command takes in a CSV list of modules similar to the `only` build; ordering
does not matter.

#### G3 DB Migration

```bash
docker compose run --rm db_migrate
```

With the project now built this command will stand up a sqlserver container instance to create and
upgrade all the required G3 databases for the application run time using your local source/db scripts.
This process only needs to be run once initially, and thereafter only when new database changes have
been pulled and are required. It will take some time to run and when completed you should be able to
access the SQL instance to verify the databases now exist by connecting you local sql manager using
the below connection details...  
host: 127.0.0.1 username: sa, password: IDeaS123

#### Dockerhub login

Some docker compose services will need to access for the images from docker

```bash
docker login
```

```text
Username - ideasreadonly
Password - 2fVu*xU5cQ9vx_p~)h@maQt*%#(>u@v4AweX,6$_m!)R}&X,_?N#m>JT%bgr;YC4
```

#### G3 Online App

```bash
docker compose up --build --force-recreate app
```

The above command will first build the app container instance using your freshly locally produced artifacts,
it will then start the db container if not already started and online the G3 application. In development mode
the container directly mounts the locally built G3 classes directly into the container, this enables the container
to pick up any locally modified classes compiled with IDE in realtime, streamlining development and reducing
re-deployments required to incorporate your changes. You can better understand these mappings by looking at the
docker-compose file at the root of this project and the associated mounts specified in the mount section of the
app service definition.
Once startup has completed the G3 application is available for login...  
http://localhost/solutions, username: <EMAIL>, password: password

### 7. Docker Run prebuilt images

---

In many scenarios, particularly for QA purposes, there's often no need to build a codebase from scratch but rather
to run an existing artifact or version. If you wish to quickly deploy a G3 app using a specific existing image version

**Prerequisites**:
To start the G3 app container, the following environment variables need to be available:

- `S3_ACCESS_KEY`
- `S3_SECRET_KEY`
- `OAUTH2_CLIENT_SECRET`

To include the Datadog container, you will need:

- `DD_HOSTNAME`
- `DD_API_KEY`
- `DD_TAGS`

_These variables can be set at startup when using a Devcontainer in VSCode or by executing the bash script `.devcontainer/scripts/bootstrap.sh`. The bash script will pull any local development secrets from the AWS Secret Manager._

**Follow these steps:**

1. Find the image version that you want to deploy from GitHub actions you can find it in the Build G3 Print Parameters.
   ![](md/images/image-version.png)
2. Navigate to the terminal of intellij or WSL within the home directory of your G3 repository.
3. Execute the following commands:

```bash
IMAGE_VERSION=<version> docker compose -f docker-compose.qa.yml run db_migrate
```

This command will bring up the G3 db and using the specified IMAGE_VERSION of db_migrate apply the required sql upgrades.
**Note**:
If you want to deploy an older version than the last deployed version on a local, it might happen that your current db script versions are ahead of the older version that you want to deploy.
In that you may need to manually clean up the flyway version tables to bring it to the required state.

```bash
IMAGE_VERSION=<version> docker compose -f docker-compose.qa.yml up app
```

This command will bring up the G3 app using the specified image version defined by IMAGE_VERSION 4. Run with Datadog: If you want to include the Datadog agent, use the following command:

```bash
IMAGE_VERSION=<version> INCLUDE_DATADOG=true docker compose -f docker-compose.qa.yml -f docker-compose.datadog.yml up -d --build app dd-agent
```

Absolutely! Here's a more concise version of the VSCode task instructions:

### VSCode Task Configuration

To streamline the process of running the prebuilt image with or without Datadog, follow these steps:

1. **Run the Task**:

   - Open the Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P` on macOS).
   - Type and select `Tasks: Run Task`.

2. **Select the Task**:

   - Choose `Docker Compose Up - Local QA - G3 app`.

3. **Provide Parameters**:
   - **Include Datadog Agent**: Select `yes` or `no` when prompted.
   - **Image Version**: Enter the desired `IMAGE_VERSION` (default: `9.7.768d6449f13`).

This will execute the appropriate Docker Compose command based on your selections.

#### Remote Debugging in Intellij

Once the g3 app is up and running, you can attach debug points to the code by simply...

1. Navigate on main menu Run -> Attach to Process (CTrl+ALT+5)
2. Select the java process running with parameter -bmanagment=0.0.0.0 (8787)
3. Once successfully attached, you would get a message in console as "Connected to the target VM, address: pid <>"
4. After attaching above process to debug, we can debug java code as usual way by adding breakpoints.

#### Sandbox properties

##### Deploy SandBox Property

If you need to deploy the sandbox property, you need **_developer_** permissions for _**ideas-avm-g3-dev**_ account.

Once you have required access, you can deploy it using following commands:

1. Authenticate to AWS to fetch the database and sas archives. See [Confluence](https://ideasinc.atlassian.net/wiki/spaces/DevOps/pages/**********/AWS+Single+Sign+On)
   for more details about `sso login`.If you haven't configured AWS_SSO login yet,follow the instructions https://github.com/ideasorg/g3-dev-tools/blob/main/README.md for configuring AWS_SSO login.
   `bash
  aws sso login --sso-session <"session_name">
e.g. aws sso login --sso-session ideas
  `  
    Note:- Default sso-session name is ideas

2. Download the data and populate the database

   ```bash
   docker compose run db_migrate sandbox download <property_code>
   ```

   Note:- If you are using prebuilt images for deployment please use below command to deploy a sandbox property

   ```bash
    IMAGE_VERSION=<version> docker compose -f docker-compose.qa.yml run db_migrate sandbox download <property_code>
   ```

   By default, the AWS_PROFILE is set in the Dockerfile to ************-developer. If you want to use a different profile, override it with the -e option in the docker-compose run command.

   ```bash
    docker compose run -e AWS_PROFILE=<your_custom_profile> db_migrate sandbox download <property_code>
   ```

   Replace <your_custom_profile> with the desired AWS profile name and <property_code> with the property code you want to download.

3. Restart SAS service after above command completed successfully.

Note:

1. Deploying sandbox property will deploy both database and sas datasets.
2. After working on particular sandbox property, to retain original data (rollback), you can so it by following command <br> `docker compose run --rm db_migrate sandbox clean <property_code>`

##### Upload a new sandbox property to S3

If you want to add a new property to sandbox, you need to:

- upload the data in `g3-sandbox-data` s3 bucket of `************` account, into `sas` and `db` folders
- You can achieve the same outcome using the tool provided below:
  ```
  $ docker compose run db_migrate sandbox upload <file_path_in_container> {sas|db} <propertyCode>
  ```
- If you want to use custom aws profile use this command:
  ```
  docker compose run -e AWS_PROFILE=<your_custom_profile> db_migrate sandbox upload <file_path_in_container> {sas|db} <property_code>
  ```
- Please note the following points in order to use the above tool:
  - To upload a file, place it in the `.data/sandbox` folder within the repository since it is mounted as /data to
    the `db_migrate` service. Hence, e.g. a file added as .data/sandbox/<propertyID.zip> should be referred as
    /data/sandbox/<propertyID.zip>
  - The file being uploaded must be a valid zip file.
  - Add property metadata i.e property_id, dbversion etc in the dbmigrate/resources/g3-sandbox-properties.json
  - This tool is also useful for updating property data that has already been uploaded.
- add the properties to the different scripts to populate `global` database:
  - in `docker/dbmigrate/scripts/sql/global/pre-update.sql`, add
    tuple `(<Property_ID>, <Property_Code>, <Property_Name>)` at line 25.
  - add a file named `<property_id>.sql` to folder `docker/dbmigrate/scripts/sql/global` with data that will be added
    to `global` db. See other files as examples.
  - _if needed_, add tuple `(<Property_ID>, <Property_Code>, <Property_Name>)` to the property list in:
    - `docker/dbmigrate/scripts/sql/ratchet/ratchet.sql` (line 7)

#### G3 Shutdown

```bash
docker compose down
```

#### G3 Compose Commands

For most interactions with containers we should be looking to compose, below outlines the most command commands.
IntelliJ has built in services and the docker service is already pre-configured, this is the preferred way to interact with your running containers as it provides
quick access to view what is running, access logs, enter the container, etc. It can be quickly access from inside IntelliJ using the key shortcut (Alt + 8)

##### Build

```bash
docker compose run --rm build
```

##### Build with Tests

```bash
docker compose run --rm build clean install
```

##### DB Upgrade

all databases

```bash
docker compose run --rm db_migrate
```

specific db type

Global

```bash
docker compose run --rm db_migrate global
```

All tenant

```bash
docker compose run --rm db_migrate tenant
```

Specific tenant

```bash
docker compose run --rm db_migrate tenant <propertyId>
```

##### DB Clean

all databases

```bash
docker compose run --rm db_migrate clean
```

specific db type

```bash
docker compose run --rm db_migrate global clean
```

##### Online Application Services

```bash
docker compose up app
```

##### Down Application Services

```bash
docker compose down app
```

##### Restart Application Services

```bash
docker compose restart app
```

##### List Services

```bash
docker compose ps
```

##### List Running Container Processes

```bash
docker compose top
```

##### Cmdline Help

```bash
docker compose --help
```

#### Docker Commands

##### Restart

```bash
sudo service docker restart
```

##### System Prune

```bash
sudo docker system prune -y
```

##### Cmdline Help

```bash
docker --help
```

## Setup Local NGI Connectivity via WSL

If one needs to connect to local NGI from WSL G3 app (REST Connectivity) we need to open a local firewall port using a powershell commandlet
After setting up the WSL on local machine/laptop and prerequisite is NGI setup already done/present

Open powershell prompt with administrative previleges and run command as below

```
New-NetFirewallRule -DisplayName "LocalSpringboot-9090" -Description "NGI Springboot inboubd rule to accept connections" -Name "NGI-9090" -Enabled "True" -Action "Allow" -Direction "Inbound" -Protocol "TCP" -LocalPort 9090
```

## Connecting Windows Services to Docker

If you encounter connectivity issues between a Windows-based service and a Linux-based service running in a Docker container, you can use the netsh command to set up port forwarding. Here's how you can modify the example command:

```
netsh interface portproxy add v4tov4 listenport=<SASListenPort> listenaddress=0.0.0.0 connectport=<DBConnectPort> connectaddress=<DockerIPAddress>
```

For example when SAS is on Windows and DB is on Linux

```
netsh interface portproxy add v4tov4 listenport=1433 listenaddress=0.0.0.0 connectport=1433 connectaddress=*************
```

## Contributing

Below outlines the assumed development workflow for making changes to this project...

### Start a new feature branch

```
git checkout main && git pull && git checkout -b STORYNUMBER
```

You should create a new branch for each story you work on. When starting a new
branch, it's recommended that you branch off of the latest pull of main.

### Sync with Main

Occasionally, you may need to bring in changes from main into your branch.
There are two strategies to sync your branch with main. In both cases, if you
have uncommitted changes, either commit or stash them before performing the
sync

#### Merge

```
git pull origin main
```

To run from your feature branch, this will pull that latest changes from the
remote GitHub main branch. The merge approach involves creating a merge commit
on your branch.

#### Rebase

```
git pull --rebase origin main
```

To run from your feature branch, this will pull that latest changes from the
remote GitHub main branch. The rebase approach will take your commits and rebase
them on top of main.
**This approach involves rewriting history and requires a force push**

### Push your Feature Branch

If have not yet pushed up your branch to GitHub, checkout your branch and run

```
git push -u origin HEAD
```

If your branch is already on GitHub, then you can push your changes by running

```
git push
```

### Create a PR

Once your branch is ready for review and merge, visit GitHub
https://github.com/ideasorg/ideas-g3/pulls & add reviewer to your check-in.
Then create a PR.

### Guidelines

1. Keep your branches up-to-date with main, do not let branches go too far out of sync with main,
   otherwise, you aren't building on-top of the latest changes
2. Be mindful of stale branches/PRs. Close out any stale PRs and/or branches so that developers can
   can quickly look through open PRs
3. Write [good commit messages](https://cbea.ms/git-commit/) and PR descriptions - GitHub will
   generally use your first commit message to auto generate the PR description 1. Include your story number in at least one commit so that it can be linked back to a Jira Story
4. Keep your PRs in a draft state if it's not ready for review so that reviewers are not reviewing
   code that is not yet in a reviewable state
5. When merging your PR, clean up the commit message as needed. If your branch has multiple commits,
   GitHub will, by default, concatenate the commit messages into one which can lead to unreadable
   commit messages

### Changes from Gerrit

In Gerrit, if you had to introduce new changes to your changeset, you had to amend your commit with
the changes. **This is no longer the case with GitHub.** If you need to introduce a new change, you
should add them as a **new commit to the same branch.** If you attempt to amend your commit, you
will have to force push to the branch, which can be dangerous if multiple devs are working off of
the same branch.

Suppose we have the following

```
A -> B -> C (main)
            \
             C' (my-branch)
```

Where `C'` is the first commit on your branch. If you need to make changes to your branch, you
should add a new commit, going through the normal Git flow of
`git add ... -> git commit ... -> git push`, such that we end up with the following

```
A -> B -> C (main)
            \
             C' -> D' (my-branch)
```

### PR Build results

PR builds will run in on prem Jenkins.
If the Jenkins PR build does not pass & reviewer does not approve, the PR is not merge-able

## Helper Commands

---

#### WSL

Below is a list of common good to know WSL commands. WSL commands have to be run from either your Windows command or Powershell.

##### Restart

```
wsl --shutdown
```

##### List Distributions

```
wsl --list -v
```

##### Set Default Distribution

```
wsl --set-default Development
```

##### Check Status

```
wsl --status
```

##### Run Distribution

```
wsl -d Development
```

##### Cmdline Help

```
wsl --help
```

## Repo Paths

---

#### Repo Root

Below outlines the repo's root level directories.

- app-test - this module is used for regression testing.
- docker - the docker container definitions.
- ear - the module to package the final ear for deployment.
- md - for storing markdown files and images.
- module - windows powershell automation directory.
- source - all g3 java backend and uo source code.

#### Docker Paths

Below outlines important files and directories related to docker and docker-compose.

- docker/pom.xml - a maven project to pull g3 artifacts into the docker context for builds.
- docker/build - docker maven build container context.
- docker/build/settings.xml - when running a docker build this settings file is what is used by default.
- docker/dbmigrate - docker db migration container context.
- docker/db - docker db mssql container context.
- docker/app - docker g3 wildfly app container context.
- docker-compose.yaml - the docker compose file that defines and manages all the above defined containers.

#### Persistence DB and SAS

All docker state (db, sas, logs, etc) are stored in the .data directory in the root of this repo, if you
delete the .data directory you will recover fine but all pre-existing state will be lost and your data reset.

- .data - all state for mssql and sas lives here, if deleted you loose all state.
- .data/mssql - all mssql instance data lives here.
- .data/mssql/sandbox - this is where required sandbox mfd and ldf files should be dropped.
- .data/SAS - all sas instance data lives here.
- .data/SAS/Log - all sas log output.
- .data/SAS/Temp - all sas temp data.
- .data/SAS/Properties - this is where required sandbox datasets should be dropped.
- .data/mssql/sandbox - the local copy of downloaded sandbox data.

## Restore a database on WSL local container instance

1. Put the .bak database back-up file at your local i.e. windows file system.

   Like C:\Users\<USER>\<dbname>.bak

   Expample: C:\Users\<USER>\993673_bkp-20231204.bak

2. Open terminal at WSL and copy DB back-up file at your g3 repo location at /root/<Repo_path>/.data/mssql

   ```
   cp /mnt/c/Users/<USER>/<dbname>.bak /root/<g3_repo_path>/.data/mssql
   ```

   Example:

   cp /mnt/c/Users/<USER>/993673_bkp-20231204.bak /root/Repos/g3cp/g3/.data/mssql

   ![](md/images/CopyDBFile.png)

3. Open SSMS on local and connect to local loop-back address 127.0.0.1
   ![](md/images/ConnectWSLDBViaSSMS.png)

4. Right click on databases and select “Restore Databases”
   ![](md/images/SSMSRestoreOption.png)

5. Select radio button “Device”
   ![](md/images/SSMSRestoreProcess1.png)

6. Click on “Add” and select location “/var/opt/mssql” and select database backup file
   ![](md/images/SSMSRestoreProcess2.png)

7. Click OK and start restore
8. Try executing a sample query “select \* from property;” to check connectivity.
   ![](md/images/SSMSAfterRetore.png)
   ![](md/images/TestQueryAfterRestore.png)

## Known Issues and solutions

- To find out the existing issues and their solution in wsl follow this [confluence page](https://ideasinc.atlassian.net/wiki/x/KwFFpw).

## Developer Notes

---

#### Running Unit Tests

- To run all unit test cases as part of the build process, remove -DskipTests option from build command in docker-compose.yml file
- Alternatively to run all unit test cases following command can be used

```
docker compose run build clean install -DdbClean=true -Duser.timezone=America/Chicago
```

- We can execute specific tests using intellij idea ide itself.
- Unit test cases which requires database connections and seed data from test databases i.e. [000005], [000006] are hosted on **db** database service from docker-compose.yml. In order to run those tests, have db service running.

#### Running Integration Tests

- To run integration tests follow this [confluence page](https://ideasinc.atlassian.net/l/cp/jN1MNcuu).

#### GitHub Container Registry Access

- Since we are moving container registry from docker hub to github container registry, access to ghcr is required.
- Create personal access token from github-profile -> settings -> developer's setting -> personal access token
- Provide `write:packages` permission to the token. Copy generated token and authorize the token with SSO
- Run following commands in wsl `export CR_PAT=YOUR_TOKEN` and `echo $CR_PAT | docker login ghcr.io -u USERNAME --password-stdin`
- You should see `Login Succeeded` message
- To know more about working with ghcr, follow this [page](https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-container-registry).

#### Build only modules instead of entire G3

- Developers can build single/multiple modules instead of the complete G3 code base. e.g.
- ```
  docker compose run --rm build clean install -f ./source/services/pom.xml -DskipTests
  ```

### Working on release cut/patch version

When deploying the application using Docker Compose for a patch or release-cut version, follow the steps outlined below:

1. Open the `docker-compose.yml` file in your project.

2. In the volumes configuration for the app service within the `docker-compose.yml` file, locate the following section:

   ```yaml
   volumes:
     - ./ear/platform-ear/target/platform-ear-4.1.0-SNAPSHOT/lib:/opt/wildfly/standalone/deployments/platform-ear.ear/lib
     - ./ear/platform-ear/target/platform-ear-4.1.0-SNAPSHOT/META-INF:/opt/wildfly/standalone/deployments/platform-ear.ear/META-INF
   ```

   Replace the hardcoded version `4.1.0-SNAPSHOT` with the actual release cut version. For example:

   ```yaml
   volumes:
     - ./ear/platform-ear/target/platform-ear-9.2.3332.9804ef2/lib:/opt/wildfly/standalone/deployments/platform-ear.ear/lib
     - ./ear/platform-ear/target/platform-ear-9.2.3332.9804ef2/META-INF:/opt/wildfly/standalone/deployments/platform-ear.ear/META-INF
   ```

   **Note:** The required version can be found in the checkout repository's POM file (e.g., `repos/g3/pom.xml` file. Search for `<version>`).

### Readme Confluence link

The complete G3 documentation has been moved to Confluence page for better organization and accessibility.
**[Visit the G3 Confluence Page](https://ideasinc.atlassian.net/wiki/spaces/ON/pages/3620110518/Welcome+to+the+G3+Project)**
For the latest updates, guides, and details, please refer to the Confluence page.
