package com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity;

import com.ideas.tetris.platform.common.entity.IdAwareEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.joda.time.LocalDate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Optional;

@SuppressWarnings("serial")
@Entity
@NamedQueries({
        @NamedQuery(name = RatchetSrpAttributes.BY_RATCHET_PROPERTY_AND_SRP_ID_AND_ATTRIBUTES, query = "from RatchetSrpAttributes where ratchetProperty=:ratchetProperty and srpId=:srpId and attributes=:attributes"),
        @NamedQuery(name = RatchetSrpAttributes.BY_RATCHET_PROPERTY_AND_SRP_ID, query = "from RatchetSrpAttributes where ratchetProperty=:ratchetProperty and srpId=:srpId"),
        @NamedQuery(name = RatchetSrpAttributes.DELETE_BY_RATCHET_PROPERTY, query = "delete from RatchetSrpAttributes rsa where rsa.ratchetProperty=:ratchetProperty")
})
@Table(name = "Ratchet_Srp_Attributes")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RatchetSrpAttributes extends IdAwareEntity<Integer> {
    public static final String BY_RATCHET_PROPERTY_AND_SRP_ID = "com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetSrpAttributes.BY_RATCHET_PROPERTY_AND_SRP_ID";
    public static final String BY_RATCHET_PROPERTY_AND_SRP_ID_AND_ATTRIBUTES = "com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetSrpAttributes.BY_RATCHET_PROPERTY_AND_SRP_ID_AND_ATTRIBUTES";
    public static final String DELETE_BY_RATCHET_PROPERTY = "com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetSrpAttributes.DELETE_BY_RATCHET_PROPERTY";

    public static final String PARAM_PROPERTY = "ratchetProperty";

    private Integer id;
    private RatchetProperty ratchetProperty;
    private String srpId;
    private String marketCategory;
    private String subMarketCategory;
    private String qualified;
    private String block;
    private String linked;
    private String packageCode;
    private String fenced;
    private String yieldable;
    private String controlType;
    private LocalDate attributedStartDate;
    private LocalDate attributedEndDate;
    private String attributes;
    private Integer status;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "Ratchet_Srp_Attributes_ID")
    public Integer getId() {
        return id;
    }

    @Override
    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne
    @JoinColumn(name = "Ratchet_Property_ID")
    public RatchetProperty getRatchetProperty() {
        return ratchetProperty;
    }

    public void setRatchetProperty(RatchetProperty ratchetProperty) {
        this.ratchetProperty = ratchetProperty;
    }

    @Column(name = "srpid")
    public String getSrpId() {
        return Optional.ofNullable(srpId).map(String::trim).orElse(null);
    }

    public void setSrpId(String srpId) {
        this.srpId = srpId;
    }

    @Column(name = "mktcode")
    public String getMarketCategory() {
        return Optional.ofNullable(marketCategory).map(String::trim).orElse(null);
    }

    public void setMarketCategory(String marketCategory) {
        this.marketCategory = marketCategory;
    }

    @Column(name = "sub_mcat")
    public String getSubMarketCategory() {
        return Optional.ofNullable(subMarketCategory).map(String::trim).orElse(null);
    }

    public void setSubMarketCategory(String subMarketCategory) {
        this.subMarketCategory = subMarketCategory;
    }

    @Column(name = "qualified")
    public String getQualified() {
        return qualified;
    }

    public void setQualified(String qualified) {
        this.qualified = qualified;
    }

    @Column(name = "block")
    public String getBlock() {
        return block;
    }

    public void setBlock(String block) {
        this.block = block;
    }

    @Column(name = "linked")
    public String getLinked() {
        return linked;
    }

    public void setLinked(String linked) {
        this.linked = linked;
    }

    @Column(name = "package")
    public String getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(String packageCode) {
        this.packageCode = packageCode;
    }

    @Column(name = "fenced")
    public String getFenced() {
        return fenced;
    }

    public void setFenced(String fenced) {
        this.fenced = fenced;
    }

    @Column(name = "yieldable")
    public String getYieldable() {
        return yieldable;
    }

    public void setYieldable(String yieldable) {
        this.yieldable = yieldable;
    }

    @Column(name = "control_type")
    public String getControlType() {
        return controlType;
    }

    public void setControlType(String controlType) {
        this.controlType = controlType;
    }

    @Column(name = "attributed_start_dt")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentLocalDate")
    public LocalDate getAttributedStartDate() {
        return attributedStartDate;
    }

    public void setAttributedStartDate(LocalDate attributedStartDate) {
        this.attributedStartDate = attributedStartDate;
    }

    @Column(name = "attributed_end_dt")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentLocalDate")
    public LocalDate getAttributedEndDate() {
        return attributedEndDate;
    }

    public void setAttributedEndDate(LocalDate attributedEndDate) {
        this.attributedEndDate = attributedEndDate;
    }

    @Column(name = "attributes_combined")
    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    @Transient
    public boolean isPersisted() {
        return id != null && id.intValue() > 0;
    }
}
