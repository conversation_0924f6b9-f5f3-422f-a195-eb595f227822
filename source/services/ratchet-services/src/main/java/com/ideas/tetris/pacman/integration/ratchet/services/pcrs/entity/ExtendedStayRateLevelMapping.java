package com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity;

import com.ideas.tetris.pacman.services.dateservice.dto.DateRange;
import com.ideas.tetris.platform.common.entity.IdAwareEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Entity
@NamedQueries({
        @NamedQuery(name = ExtendedStayRateLevelMapping.BY_RATCHET_PROPERTY_CODE, query = "select esrlm From ExtendedStayRateLevelMapping as esrlm where esrlm.ratchetProperty.code=:propertyCode"),
        @NamedQuery(name = ExtendedStayRateLevelMapping.BY_RATCHET_PROPERTY_CODES, query = "select esrlm From ExtendedStayRateLevelMapping as esrlm where esrlm.ratchetProperty.code in (:propertyCodes)"),
        @NamedQuery(name = ExtendedStayRateLevelMapping.EXTENDED_STAY_TRUE_BY_PROP_CODES, query = "select esrlm From ExtendedStayRateLevelMapping as esrlm where esrlm.isExtendedStay='True' and esrlm.ratchetProperty.code in (:propertyCodes)"),
        @NamedQuery(name = ExtendedStayRateLevelMapping.DELETE_BY_RATCHET_PROPERTY, query = "delete from ExtendedStayRateLevelMapping esrlm where esrlm.ratchetProperty=:ratchetProperty"),
        @NamedQuery(name = ExtendedStayRateLevelMapping.GET_ALL_ACTIVE_EXTENDED_STAY_RATE_CODES, query = "select distinct esrlm.rateLevel from ExtendedStayRateLevelMapping esrlm where esrlm.ratchetProperty.id=:propertyId and esrlm.isExtendedStay='True' and esrlm.endDate >= :date")
})
@Table(name = "Extended_Stay_Rate_level_Mapping")
public class ExtendedStayRateLevelMapping extends IdAwareEntity<Integer> {
    public static final String BY_RATCHET_PROPERTY_CODE = "com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.ExtendedStayRateLevelMapping.BY_RATCHET_PROPERTY_CODE";
    public static final String BY_RATCHET_PROPERTY_CODES = "com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.ExtendedStayRateLevelMapping.BY_RATCHET_PROPERTY_IDS";
    public static final String EXTENDED_STAY_TRUE_BY_PROP_CODES = "com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.ExtendedStayRateLevelMapping.EXTENDED_STAY_TRUE_BY_PROP_CODES";
    public static final String DELETE_BY_RATCHET_PROPERTY = "com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.ExtendedStayRateLevelMapping.DELETE_BY_RATCHET_PROPERTY";
    public static final String GET_ALL_ACTIVE_EXTENDED_STAY_RATE_CODES = "com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.ExtendedStayRateLevelMapping.GET_ALL_EXTENDED_STAY_RATE_CODES";

    private Integer id;
    private RatchetProperty ratchetProperty;
    private String rateLevel;
    private String esMarketSegment;
    private String isExtendedStay;
    private Date createDate;
    private Date startDate;
    private Date endDate;

    @Override
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "Extended_Stay_Rate_Level_ID")
    public Integer getId() {
        return id;
    }

    @Override
    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "startDate")
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "endDate")
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @ManyToOne
    @JoinColumn(name = "Ratchet_Property_ID")
    public RatchetProperty getRatchetProperty() {
        return ratchetProperty;
    }

    public void setRatchetProperty(RatchetProperty ratchetPropertyId) {
        this.ratchetProperty = ratchetPropertyId;
    }

    @Column(name = "Rate_Level")
    public String getRateLevel() {
        return rateLevel;
    }

    public void setRateLevel(String rateLevel) {
        this.rateLevel = rateLevel;
    }

    @Column(name = "ES_Market_Segment")
    public String getEsMarketSegment() {
        return esMarketSegment;
    }

    public void setEsMarketSegment(String esMarketSegment) {
        this.esMarketSegment = esMarketSegment;
    }

    @Column(name = "IS_Extended_Stay")
    public String getIsExtendedStay() {
        return isExtendedStay;
    }

    public void setIsExtendedStay(String isExtendedStay) {
        this.isExtendedStay = isExtendedStay;
    }

    @Column(name = "CreateDate")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Transient
    public boolean inRange(Date bookingDate) {
        return new DateRange(startDate, endDate).contains(bookingDate);
    }
}
