<project>
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ideas.g3</groupId>
        <artifactId>services</artifactId>
        <version>4.1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>com.ideas.g3.services</groupId>
    <artifactId>g3-spring-config</artifactId>
    <name>g3::services::g3-spring-config</name>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <forceCreation>true</forceCreation>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>