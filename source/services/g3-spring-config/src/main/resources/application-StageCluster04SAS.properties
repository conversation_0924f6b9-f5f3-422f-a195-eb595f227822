CommitForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupCommitRequest.map
CreateForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupRequest.map
SasMapFileLocation=${base.sas.drive.path}/SAS/Deploy/Maps
archiveFolder=${base.g3.drive.path}/G3/Data
calibrationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/calibRequest.map
catchupRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/catchup
central.rms.data.sync.enabled=true
centralrms.common.alerts.base-uri=https://centralrms.stage.ideasrms.com/api/centralrms-common/G3/alerts/
centralrms.common.base-uri=https://centralrms.stage.ideasrms.com/api/centralrms-common/G3
centralrms.common.host-url=https://portfolionavigator.stage.ideasrms.com
centralrms.enabled=true
centralrms.oauth2.client-id=7ne6thmsh9hpbq7hdldi5p06bg
centralrms.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
centralrms.oauth2.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
centralrms.oauth2.resourceserver.client-id=2btihn8336ffckjd9ag72nb9md
centralrms.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
centralrms.oauth2.token-uri=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
centralrms.pricing.base-uri=https://pricing-config.stage.ideasrms.com/api/central-rms/G3/pricing-config/
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.client-id=3e20uiflvdb1ietdfls97870pc
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
centralrms.security.oauth2.resourceserver.jwt.PRICING.client-id=2btihn8336ffckjd9ag72nb9md
centralrms.security.oauth2.resourceserver.jwt.PRICING.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
centralrms.fds.base-uri=https://fds.stage.ideasrms.com/api
well.known.uri=https://fds.stage.ideasrms.com/api/uis/login/.well-known/jwks.json
channel.srp.population.enabled=true
chaos.monkey.functionality.enabled=true
cmt.datasource.dir=${base.g3.drive.path}/G3/CMA
compress_uncompress_path=C:\Programs\Compress-Uncompress\compress.exe
considerBDEDecisionsInProgressAsComplete=true
crsArchiveBucket=g3-stage-crs-data-archive-cluster-01
crsArchiveFolder=${base.g3.drive.path}/G3/Data/CRSData/archive
crsDataFolder=${base.g3.drive.path}/G3/Data/CRSData
crsExtractsFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/extracts
crsIncomingFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming
crsRatesFolder=${base.g3.drive.path}/G3/Data/Rates
dataFolder=${base.g3.drive.path}/G3/Data
dbHost=mn4sg3xdbsw501.ideasstg.int
default-jndi-URL=jnp://${env.APP_HOST}:1099
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;\span>&lt;\a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.server.host=aspmailhost.ideasstg.int
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=g3_stage_stageperftest
extractArchiveFolder=${base.g3.drive.path}/G3/Data/Extracts/archive
extractErrorFolder=${base.g3.drive.path}/G3/Data/Extracts/error
extractFtpFolder=${base.g3.drive.path}/G3/Data/Temp/ftp
feature.sso.useDynamicCookieDomain=true
fetch.salesforce.email.address.disabled=false
forecastingRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstRequest.map
ftp.folder.requests=Test_Stage
g3.internal.loadbalancer.url=http://${env.APP_HOST}:81
g3.link.client=http://${env.APP_HOST}/solutions/
gftServiceXmlIncomingExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlIncomingRemoteExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlMonitoringUrl=http://localhost:31110/v5iservices-3/servlet/SimpleMonitor
gftServiceXmlOutboundDecisionFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlPath=${base.g3.drive.path}/G3/Data/Config/gftservice.xml
gftServiceXmlUploadBufferFolder=${base.g3.drive.path}/G3/Temp/
hazelcast.cloud.cluster.addresses=
hazelcast.cloud.group.name=G3-Stage-PerfTest
htng.async.callback.service.password=IDeaS@123
htng.async.callback.service.username=<EMAIL>
isMultinodeApp=true
jems.retry.initialInterval=10000
jems.retry.maxInterval=300000
jems.retry.multiplier=5
jndi-URL=jnp://${env.APP_HOST}:1099
jobinstance.log.dir=${base.g3.drive.path}/G3/Logs/Jobs
lms.hours.until.stale=8
lms.refresh.property.enabled=false
lms.single.signon.enabled=false
lms.walkme.url=https://cdn.walkme.com/users/75d90fc4cc7d433c8adc89ecd103b30e/test/walkme_75d90fc4cc7d433c8adc89ecd103b30e_https.js
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:\Program Files\Microsoft SQL Server\MSSQL10_50.G3SQL01\MSSQL\DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
load.extract.cache.on.startup=true
localFTPFolder=${base.g3.drive.path}/G3/FTP
log4j2.formatMsgNoLookups=true
module.dynamicPropertyGroup.group-pricing-evaluation=true
ngi.htng.cloud.migration.parallel.job.limit=2
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.stage.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.stage.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.stage.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.stage.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.stage.ideasrms.com
ngi.rest.url=http://ngistagecluster01.ideasstg.int:9090
ngi.set.cloud.migration.stable.job.enabled=true
oauth.client.id=7ne6thmsh9hpbq7hdldi5p06bg
oauth.client.secret=2ds5or7av70mi08jboi1ba2agqjq0ohgog47mmkfus2539vh4ag
oauth.client.url=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
opera.client.log.dir=${base.g3.drive.path}/G3/Logs/Opera
operapopulationDailyRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operadailypopulation.map
operapopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operapopulation.map
optimizationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/optRequest.map
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
outOfOrderPopulationMap=${base.sas.drive.path}/SAS/Deploy/Maps/type2ooo.map
outOfOrderPopulationPath=${base.sas.drive.path}/SAS/Deploy/DDL/Analytics/ddl_scripts
paceBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacebuildrequest.map
paceHistoryBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacehistoryrequest.map
pacman-jnp=jnp://${env.APP_HOST}:1099
pacman.base.db.scripts.jar=${base.g3.drive.path}/G3/Deploy/Lib/tenant.jar
pacman.base.db.server.url=*************************************************
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.demo.property.daily.load.mappings=StressBox.S60750=StressBox.S60751,StressBox.S60750=StressBox.S60752
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://${env.APP_HOST}/pacman-platformsecurity/rest
pacman.encryption.key=C7DS13P7Fx61uR9n1kpdDE23BeCt7WPK50yrl9Qa3hjflol78Z7UmTs80gbJN69wV4Lp8AC5v32KO7EChN7qLYKNb2mVnN0T
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.faqModule.enabled=false
pacman.fds.admin.url=https://admin.stage.ideasrms.com
pacman.fds.base.url=https://fds.stage.ideasrms.com/api
pacman.fds.client.id=7ne6thmsh9hpbq7hdldi5p06bg
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
fds.base.url=https://fds.stage.ideasrms.com/api
fds.oauth2.token-uri=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
pacman.feature.openam.disabled=true
pacman.g3.api.rest.url=https://${env.APP_HOST}/api
pacman.g3.aws.base.url=https://g3-api.stage.ideasrms.com
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.integration.opera.agentWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/opera-agent.war
pacman.integration.opera.stopOnValidationError=true
pacman.parameterByModule.enabled=false
pacman.portal.google.analytics.account=***********-1
pacman.portal.google.analytics.enabled=true
pacman.portal.google.analytics.siteSpeedSampleRate=30
pacman.portal.showPageLoadTime=false
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.secure.help.base.url=https://help-api.stage.ideasrms.com/
pacman.server.name=${env.APP_HOST}
pendo.prefix=G3Stage3
platform-jnp=jnp://${env.APP_HOST}:1099
platform.jobweb.url=http://${env.APP_HOST}:8080/job-web
platform.security.showFullExceptionStackTrace=false
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.server.name=${env.APP_HOST}
populationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/popRequest.map
propertyConfigurationsFolder=${base.g3.drive.path}/G3/Data/PropertyConfigurations
purgeRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/purgeRequest.map
ratchetValidationBaseLogPath=${base.sas.drive.path}/SAS/Logs
rateShopperCatchupFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
ratesValidationDataMap=${base.sas.drive.path}/SAS/Deploy/Maps/ratedata.map
ratesValidationMap=${base.sas.drive.path}/SAS/Deploy/Maps/raterequest.map
redis.cache.isCluster=false
redis.cache.isEnabled=true
redis.cache.max.heap.setting=maxmemory 2G
redis.cache.node.addresses=
redis.local.instance.enabled=true
report.scheduler.mail.sender.host=aspmailhost.ideasstg.int
reportGenRequestBaseOutputPath=${base.sas.drive.path}/SAS/Data/Reports
reportGenRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rptGenRequest.map
requestMap=C:\Program Files\SASHome\sas_xsl_map\request.map
revisionRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/revRequest.map
roomClassSyncRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rcSynchRequest.map
rssArchiveFolder=${base.g3.drive.path}/G3/Data/RateShopper/archive
rssDataFolder=${base.g3.drive.path}/G3/Data/RateShopper
rssDropFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
rssIncomingFolder=${base.g3.drive.path}/G3/Data/RateShopper/incoming
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
salesforce.organization.id=00DQ000000B57If
salesforce.rest.client.id=3MVG9Oe7T3Ol0ea4KLKLCC4McoPLHnyzYMbZICYQw6yg_vwxfq8kEuEJ5wRhdr80VDvrbVMiwitJCzBb_YvdX
salesforce.rest.client.secret=2547210597410575274
salesforce.rest.password=IDeaS234cbe5AxiNwG8Pcm2nyTXhJvch
salesforce.rest.username=<EMAIL>
salesforce.soap.username=<EMAIL>.g3integ2
sas.jdbc.user=<EMAIL>
sasDomainNodes=mn4sg3xsasl001.ideasstg.int
sihot.extractHealth.baseUrl=https://data-admin.sis.stage.ideasrms.com
ssoLoginRedirect=http://${env.APP_HOST}/solutions/c/portal/login
ssoLogoutRedirect=http://${env.APP_HOST}/solutions/web/guest/home
tetris.gftservicexml.extract.remote.dir=${base.g3.drive.path}/G3/Temp/
typeThreePopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3poprequest.map
typeThreeValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3valrequest.map
typeTwoValidationLogBasePath=${base.sas.drive.path}/SAS/Logs
typeTwoValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/valrequest.map
updateDDLSeedMap=${base.sas.drive.path}/SAS/Deploy/Maps/updateDdlSeedRequest.map
updateDDLSeedPath=${base.sas.drive.path}/SAS/Deploy/DDL/Analytics/ddl_scripts
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.productionMode.enabled=true
validationRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/validation
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
whatIfResponseFileProp5=${base.g3.drive.path}/G3/Temp/prop5WhatIf.xml
whatIfResponseFileProp6=${base.g3.drive.path}/G3/Temp/prop6WhatIf.xml
xapiPassword=e5605bde10f30aea0fa6a18dca00a272f17b8198db09d2b2
xapiUsername=G3Production
pacman.fplos.serviceUrl=https://g3-recommendation-internal.stage.ideasrms.com/api/v1/broker
pacman.fds.universal.redirect.uri=http://${env.APP_HOST}/solutions/universallogin/callback
pacman.fds.g3.environment.ids=50c4fbf5-2230-4493-ae33-9a7569b62dd9
pacman.fds.g3.environment.id=50c4fbf5-2230-4493-ae33-9a7569b62dd9
pacman.fds.uad.environment.id=44ac98f6-1a66-41a8-b349-d689b36e0a49
pacman.fds.navigator.environment.id=ea0abe47-4316-4f4f-8c98-b000987fb805
pacman.fds.optix.environment.id=3abb21c8-3619-45ab-acae-d05a23681ae3
pacman.fds.specialevents.environment.id=c0cdd3ca-6138-4c84-bf9a-13db8cb9afea
awssqs.enabled=${env.SQS_ENABLED:false}
dbInstance=
pacman.base.db.instance=
sqs.name=${env.SQS_NAME:sqs-sns-event-bridge-1}
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.stage.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.stage.ideasrms.com
projectedBookingPace.s3BucketName=projected-booking-pace-us-east-2-stage
emailTemplates.s3BucketName=g3-sfdc-email-templates-stage
pacman.feature.global.universal.login.help=true
report.delivery.enabled.on.nonprod=true