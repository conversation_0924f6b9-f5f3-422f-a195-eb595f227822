base.sas.drive.path=/opt
base.g3.drive.path=/opt
CommitForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupCommitRequest.map
CreateForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupRequest.map
SasAnalyticsDataSetPath=${base.sas.drive.path}/SAS/Data/Properties/
SasCatalogsBasePath=${base.sas.drive.path}/SAS/Deploy/Catalogs/
SasMapFileLocation=${base.sas.drive.path}/SAS/Deploy/Maps
SasRatchetDataSetPath=${base.sas.drive.path}/SAS/Data/Ratchet/Properties/
archiveFolder=${base.g3.drive.path}/G3/Data
central.rms.data.sync.enabled=true
centralrms.common.alerts.base-uri=https://centralrms.dev.ideasrms.com/api/centralrms-common/G3/alerts/
centralrms.common.base-uri=https://centralrms.dev.ideasrms.com/api/centralrms-common/G3
centralrms.enabled=true
centralrms.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
centralrms.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
centralrms.oauth2.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.resourceserver.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
centralrms.pricing.base-uri=https://pricing-config.dev.ideasrms.com/api/central-rms/G3/pricing-config/
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.client-id=iokjj0kfso4e2mj5nfjj4plj1
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.security.oauth2.resourceserver.jwt.PRICING.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.security.oauth2.resourceserver.jwt.PRICING.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.fds.base-uri=https://fds.dev.ideasrms.com/api
well.known.uri=https://fds.dev.ideasrms.com/api/uis/login/.well-known/jwks.json
feature.monitoringdashboard.datadogLinkEnabled=true
feature.monitoringdashboard.datadogLogBaseURL=https://ideas.datadoghq.com/logs
channel.srp.population.enabled=true
chaos.monkey.functionality.enabled=true
considerBDEDecisionsInProgressAsComplete=true
crsArchiveBucket=g3-dev-crs-data-archive-cluster-01
crsArchiveFolder=${base.g3.drive.path}/G3/Data/CRSData/archive
crsDataFolder=${base.g3.drive.path}/G3/Data/CRSData
crsExtractsFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/extracts
crsIncomingFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming
crsRatesFolder=${base.g3.drive.path}/G3/Data/Rates
dataFolder=${base.g3.drive.path}/G3/Data
dbHost=mn4dbldcisl002.ideasdev.int
dbInstance=
default-jndi-URL=jnp://${env.APP_HOST}:1099
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.server.host=aspmailhost.ideasdev.int
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=qa_standalone_Hybrid02
feature.roiSimulator.externalUsersEnabled=false
feature.walkme.menu.enabled=true
fetch.salesforce.email.address.disabled=false
g3.internal.loadbalancer.url=http://${env.APP_HOST}:8080
g3.link.client=http://${env.APP_HOST}/solutions/
gftServiceXmlMonitoringUrl=http://${env.APP_HOST}:31110/v5iservices-3/servlet/SimpleMonitor
ideas.one.embedded.url=https://one.embedded.dev.ideasrms.com/LATEST
jems.retry.autoRetryEnabled=false
jndi-URL=jnp://${env.APP_HOST}:1099
lms.hours.until.stale=8
lms.refresh.property.enabled=true
lms.single.signon.enabled=true
lms.snapshot.enabled=true
lms.snapshot.location=${base.g3.drive.path}/G3/Deploy/LMS/Snapshots
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:\Program Files\Microsoft SQL Server\MSSQL10_50.G3SQL01\MSSQL\DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
lmsSupplementalScriptLocation=${base.g3.drive.path}/G3/Deploy/LMS/Scripts
log4j2.formatMsgNoLookups=true
module.beta.booking-pace-report=true
module.beta.booking-situation-report=true
module.beta.comparative-booking-pace-report=true
module.beta.data-extraction-report=true
module.beta.forecast-validation-report=true
module.beta.input-override-report=true
module.beta.inventory-history-report=true
module.beta.mcat-mapping-report=true
module.beta.output-override-report=true
module.beta.performance-comparison-report=true
module.beta.pick-up-change-and-differential-control-report=true
module.beta.pricing-override-history-report=true
module.beta.pricing-pace-report=true
module.beta.pricing-report=true
module.beta.rate-plan-production-report=true
module.beta.schedule-reports=true
module.dynamicPropertyGroup.group-pricing-evaluation=true
ngi.htng.cloud.migration.parallel.job.limit=2
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.dev.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.dev.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.dev.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.dev.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.dev.ideasrms.com
ngi.rest.url=http://mn4qg3xenvw007.ideasdev.int:9090
oauth.client.id=1m6gqjqnj6e2urbiot4dn0qvp7
oauth.client.secret=${env.FDS_CLIENT_SECRET}
oauth.client.url=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
pacman-jnp=jnp://${env.APP_HOST}:1099
pacman.base.db.instance=
pacman.base.db.port=
pacman.base.db.server.url=jdbc:sqlserver://${env.DB_HOST}:1433
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://${env.APP_HOST}/pacman-platformsecurity/rest
pacman.encryption.key=GaQn}?wF8,@#mZ8!^HnHj!:a5hea%Fa`$sdfd1232sdfsdfsd%^dfdfdfd1238c3dg`$sdfdfd88f8df8d8fdfjdjfdjf
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.fds.admin.url=https://admin.dev.ideasrms.com
pacman.fds.base.url=https://fds.dev.ideasrms.com/api
pacman.fds.client.id=1m6gqjqnj6e2urbiot4dn0qvp7
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
fds.application.name=G3-KITTStandalone
fds.base.url=https://fds.dev.ideasrms.com/api
fds.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
aws.cognito.scim.api.token.url=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
aws.cognito.scim.api.clientId.stage=1m6gqjqnj6e2urbiot4dn0qvp7
aws.cognito.scim.api.client.secret.stage=${env.FDS_CLIENT_SECRET}
aws.cognito.scim.api.url=https://fds.dev.ideasrms.com/api/uis/scim/v2/Users/
aws.cognito.userpool.domain.bstn=fds-dev-g3test-auth
aws.cognito.poolId.external.users.prod.bstn=us-east-2_Qh3nfBuFe
aws.cognito.clientId.external.users.prod.bstn=4frqtc53jltjkju7hjhlfdh0pk
aws.cognito.clientSecret.external.users.prod.bstn=${env.BSTN_CLIENT_SECRET}
pacman.feature.openam.disabled=true
pacman.g3.api.rest.url=https://${env.APP_HOST}/api
pacman.g3.aws.base.url=https://g3-api.dev.ideasrms.com
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.secure.help.base.url=https://help-api.dev.ideasrms.com/
pacman.server.name=${env.APP_HOST}
pendo.prefix=G3QA
platform-jnp=jnp://${env.APP_HOST}:1099
platform.jboss.as7.management.host=${env.APP_HOST}
platform.jboss.as7.management.port=9999
platform.jboss.as7.management.profile=services-profile
platform.jobweb.url=http://${env.APP_HOST}/job-web
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.server.name=${env.APP_HOST}
purge.purgeOperaHistoryJob=true
purge.purgeOperaHistoryJobDaysToDelete=7
ratchetValidationBaseLogPath=${base.sas.drive.path}/SAS/Logs
redis.cache.isEnabled=true
redis.local.instance.enabled=true
report.scheduler.mail.sender.host=aspmailhost.ideasdev.int
runTaskOutputPath=${base.sas.drive.path}/SAS/
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
saleDemoUsersLocation=${base.g3.drive.path}/G3/Deploy/LMS/Sales
salesforce.rest.username=<EMAIL>
sas.db.password=${env.DB_PASSWORD}
sas.db.userid=sa
sas.jdbc.password=${env.DB_PASSWORD}
sasDomainNodes=${env.APP_HOST}
sasPassword=password
sendWebRatesToNGI=true
sihot.extractHealth.baseUrl=https://data-admin.sis.stage.ideasrms.com
ssoLoginRedirect=http://${env.APP_HOST}/solutions/c/portal/login
ssoLogoutRedirect=http://${env.APP_HOST}/solutions/web/guest/home
typeTwoValidationLogBasePath=${base.sas.drive.path}/SAS/Logs
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.shell=true
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
g3.springdoc.enabled=true
ngi.set.cloud.migration.stable.job.enabled=true
pacman.fplos.serviceUrl=https://g3-recommendation-internal.dev.ideasrms.com/api/v1/broker
universal.login.url=https://id.dev.ideasrms.com/login
universal.login.account.settings.url=https://id.dev.ideasrms.com/userprofile
universal.login.logout.url=https://fds.dev.ideasrms.com/api/uis/signout
fds.oauth2.authorize.url=https://id.dev.ideasrms.com/api/uis/login/oauth2/authorize
pacman.fds.universal.client.id=94fda2b6-e67f-40b2-8178-33f09c6e3e63
pacman.fds.universal.client.secret=${env.UNIVERSAL_LOGIN_CLIENT_SECRET}
pacman.fds.universal.redirect.uri=http://${env.APP_HOST}/solutions/universallogin/callback
awssqs.enabled=${env.SQS_ENABLED:true}
sqs.name=${env.SQS_NAME:sqs-sns-event-bridge-kitt-standalone}
htng.param.migration.backward.compatibility.enabled=
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.dev.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.dev.ideasrms.com
projectedBookingPace.s3BucketName=projected-booking-pace-us-east-2-dev
emailTemplates.s3BucketName=g3-sfdc-email-templates-dev
gftServiceXmlIncomingExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlIncomingRemoteExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlOutboundDecisionFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlPath=${base.g3.drive.path}/G3/Data/Config/gftservice.xml
gftServiceXmlUploadBufferFolder=${base.g3.drive.path}/G3/Temp/
pacman.feature.global.universal.login.help=true