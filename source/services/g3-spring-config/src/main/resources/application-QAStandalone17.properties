CommitForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupCommitRequest.map
CreateForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupRequest.map
SasAnalyticsDataSetPath=${base.sas.drive.path}/SAS/Data/Properties/
SasCatalogsBasePath=${base.sas.drive.path}/SAS/Deploy/Catalogs/
SasMapFileLocation=${base.sas.drive.path}/SAS/Deploy/Maps
SasRatchetDataSetPath=${base.sas.drive.path}/SAS/Data/Ratchet/Properties/
archiveFolder=${base.g3.drive.path}/G3/Data
bean.loader.time.out=360000
calibrationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/calibRequest.map
canary.server.url=http://${env.APP_HOST}:7788
ngi.htng.cloud.migration.parallel.job.limit=2
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.dev.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.dev.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.dev.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.dev.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.dev.ideasrms.com
catchupRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/catchup
centralrms.common.alerts.base-uri=https://centralrms.dev.ideasrms.com/api/centralrms-common/G3/alerts/
centralrms.common.base-uri=https://centralrms.dev.ideasrms.com/api/centralrms-common/G3
centralrms.enabled=true
centralrms.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
centralrms.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
centralrms.oauth2.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.resourceserver.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
centralrms.pricing.base-uri=https://pricing-config.dev.ideasrms.com/api/central-rms/G3/pricing-config/
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.client-id=iokjj0kfso4e2mj5nfjj4plj1
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.security.oauth2.resourceserver.jwt.PRICING.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.security.oauth2.resourceserver.jwt.PRICING.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.fds.base-uri=https://fds.dev.ideasrms.com/api
well.known.uri=https://fds.dev.ideasrms.com/api/uis/login/.well-known/jwks.json
channel.srp.population.enabled=true
config.param.migrated.externalSystem=bookassist,smarthotel,protelioair,synxis,rvng,siteminder,synxis10,cubilis,staah,suite8,hermeshotels,synxis6,newbookpms,hotelnetsolution,leanpms,traveltripper,synxis3,synxis9,synxis11,protel,infor,rezlynx,availpro,anyhtng,groupmax,winnerpms,synxis7,windsurfercrs,synxis2,avvio,ratetiger,gse,synxis1,maestropms,bookingexpert,vaillms,hotelspider,synxis5,webrezpropms,yourvoyager,rategain,blastness,connecterevmax,advantagereserve,ihotelier,hbsi,synxis4,curtisc,rmspms,synxis8,ihotelier1,ihotelier2,ihotelier3,ihotelier4
crsArchiveBucket=g3-dev-crs-data-archive-cluster-01
crsArchiveFolder=${base.g3.drive.path}/G3/Data/CRSData/archive
crsDataFolder=${base.g3.drive.path}/G3/Data/CRSData
crsExtractsFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/extracts
crsIncomingFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming
crsRatesFolder=${base.g3.drive.path}/G3/Data/Rates
dataFolder=${base.g3.drive.path}/G3/Data
dbHost=${env.DB_HOST}
dbInstance=
dbPassword=${env.DB_PASSWORD}
dbUserId=sa
default-jndi-URL=jnp://localhost:1099
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.server.host=aspmailhost.ideasdev.int
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=g3_local_localstandalone
extractArchiveFolder=${base.g3.drive.path}/G3/Data/Extracts/archive
extractErrorFolder=${base.g3.drive.path}/G3/Data/Extracts/error
extractFtpFolder=${base.g3.drive.path}/G3/Temp/ftp
feature.monitoringdashboard.datadogLinkEnabled=false
forecastingRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstRequest.map
g3.internal.loadbalancer.url=http://${env.APP_HOST}:8080
gftServiceXmlIncomingExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlIncomingRemoteExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlMonitoringUrl=http://${env.APP_HOST}:31110/v5iservices-3/servlet/SimpleMonitor
gftServiceXmlOutboundDecisionFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlPath=${base.g3.drive.path}/G3/Data/Config/gftservice.xml
gftServiceXmlUploadBufferFolder=${base.g3.drive.path}/G3/Temp/
ideas.one.embedded.url=http://localhost:4300/local
jndi-URL=jnp://localhost:1099
jobinstance.log.dir=${base.g3.drive.path}/G3/Logs/Jobs
lms.snapshot.location=${base.g3.drive.path}/G3/Deploy/LMS/Snapshots
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:/Program Files/Microsoft SQL Server/MSSQL10_50.G3SQL01/MSSQL/DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
lmsSupplementalScriptLocation=${base.g3.drive.path}/G3/Deploy/LMS/Scripts
localFTPFolder=${base.g3.drive.path}/G3/FTP
oauth.client.id=7ne6thmsh9hpbq7hdldi5p06bg
oauth.client.secret=2ds5or7av70mi08jboi1ba2agqjq0ohgog47mmkfus2539vh4ag
oauth.client.url=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
onlineReportServerLocation=${base.g3.drive.path}/G3/Data/OnlineReport
opera.client.log.dir=${base.g3.drive.path}/G3/Logs/Opera
operapopulationDailyRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operadailypopulation.map
operapopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operapopulation.map
optimizationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/optRequest.map
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
outOfOrderPopulationMap=${base.sas.drive.path}/SAS/Deploy/Maps/type2ooo.map
outOfOrderPopulationPath=${base.sas.drive.path}/SAS/Deploy/DDL/analytics/ddl_scripts
paceBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacebuildrequest.map
paceHistoryBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacehistoryrequest.map
pacman-jnp=jnp://localhost:1099
pacman.base.db.instance=
pacman.base.db.password=${env.DB_PASSWORD}
pacman.base.db.scripts.jar=${base.g3.drive.path}/G3/Deploy/Lib/tenant.jar
pacman.base.db.server.url=jdbc:sqlserver://${env.APP_HOST}:1433
pacman.base.db.username=sa
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.encryption.key=GaQn}?wF8,@#mZ8!^HnHj!:a5hea%Fa%^dfdfdfd1238c3dg
pacman.exceed.lms.private.key=MIIEpAIBAAKCAQEArqfZ1SfuJiQAKlRdlhUuy4Uf46l8DBDVdIWNstsAvSd/oT1ljd/vIHTRmde59HvjCmRwlrS0NfAmWP+B7ZjJ+XkmgV1iKhCkZUWhAR74zC7MPhTwj6plUsDQvD7dKKGgj8w3HgdKO8mPjb6d/xeEej7K8oGmv+ktRK7Ok66n7L/VpfxI8eGdO9gE6Cw0GuZclTwHOP+7Onm3qquiU1pkefQR29yeGfg5kFB8PiufoFYRg0+TPSU+hsOPg+vM0D0Cq76SHNUf/AIsMdWPjk0fxlyklX8QObkRAqLhaIkb4py7lBRAfwCUrGdYtJ2b/1fiq5hLtsP84O+H6rNc1KmJPwIDAQABAoIBABiEl5/EqznPc/Z9QWYAM0F+vqtG75MCCfUBipLBC6mCEfigldEEzpzPyVy/W+tmOMkMsIF3fJTV0YxM0gB4omHTEAS5Bz7Ad1OAIsn1fxequiE870g7s12ak7LZYFKD3fZRxKsCevlJok1TpPpaYs6GYhcI/E+UfDZYE8fHjOs/Bz/aWxsmt/P/1yyjubve98B4YjVfUmCjMEcWGPQt7bbP+7SnZns6L90Wi3spPseAmyQpovtmm3RijD/6O09MYACbNLMqwAklS2DWpg1Q9w7RJZ0pmS6Q63ScSYd01po+a/jbOap7iBlGE/w1Og9nGT5VijvjdPiFWuY61zt0S4ECgYEA5SEILSrSzi/2ykMWxvZ/Z/ZnOu7Iz5h0JxMYtcd4Co6+jmd7dKR/dpBx04gvlOfy1SdiYdZsow5f+1/SXkx6Q/yDBxVnBU2CEqHbsa5sjRwfqZ5uFhdB+EbC8SHhrKrMeFYSVituTLaPq75sSsj+CypUWW/LCdiMBGtzmN36jLMCgYEAwyNoAVtDtAj923deQ7+KeN2aZmXGgpAO44/T7LSnFaspbCyc9Nmghc1hwnxrg8xwhMv9BaE5FZlkAZge58s6FSQDTEx0P4a6TCWDP0RNFZCh69/v2r4CnbMzPvmtRrqRUGVuemsF9z4EuAdMvw1b08rCmE23CIPfMF1Auf5gb0UCgYA1rURWGHckGHZvMcJPQAXe3AmY88GVaHilgsLCKwWDK6eWgsWYMMUviG0yCfBjqObpan2QwAPZOw/fOrZ0ouPts2UarrfgtSSoqsRl3p/Neu4r9VKC2FX7DNC0XqSYp7aY5kkLaMdBL29+43BJIMbq5LK39QmSt+ktS+Lbx/zfIwKBgQCMwz6kM9xrw0y0ArB5+HJ/64MUOHhw+E/1MWwzRMF+bQCtaqUyLsaPnm7Vp4C7Cz9jW+7CH+GYxhSQ3dX1aPEWDOtgH4p/xMUgZrL9cVyVoMZc6yYPSZ+DeKAP3nVzHuPx9akzH36nMhKfWYywCIfkCFBFI1JUDyRxs+McB/YybQKBgQCepZYJA+IdCdBPpjI+pNnPGt3ix8kXaUXTD59dqrVWiFqML5tp0HrevwuySOWSwqEDsqbxo0tf8kvZTs3tMtsiwidk+leHe41UKeXZsih5jfJFtsdu4uI/h1jk8eOnDzaZXYeJOyS4kZAbL9ROYdbDg7hDJ4MjsPDeEYoi7rfIKw==
pacman.fds.admin.url=https://admin.dev.ideasrms.com
pacman.fds.base.url=https://fds.stage.ideasrms.com/api
pacman.fds.client.id=7ne6thmsh9hpbq7hdldi5p06bg
pacman.fds.client.secret=
fds.base.url=https://fds.dev.ideasrms.com/api
fds.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
fds.oauth2.client-secret=
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.integration.opera.agentWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/opera-agent.war
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.secure.help.base.url=https://help-api.dev.ideasrms.com/
pacman.superuser.bypass.openam.login=true
pacman.superuser.password=password
pendo.prefix=G3DevLocal
platform-jnp=jnp://localhost:1099
platform.jboss.as7.management.host=${env.APP_HOST}
platform.jobweb.url=http://${env.APP_HOST}:8080/job-web
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
populationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/poprequest.map
propertyConfigurationsFolder=${base.g3.drive.path}/G3/Data/PropertyConfigurations
purgeRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/purgeRequest.map
ratchetValidationBaseLogPath=${base.sas.drive.path}/SAS/Logs
rateShopperCatchupFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
ratesValidationDataMap=${base.sas.drive.path}/SAS/Deploy/Maps/ratedata.map
ratesValidationMap=${base.sas.drive.path}/SAS/Deploy/Maps/raterequest.map
redis.cache.isEnabled=true
redis.local.instance.enabled=true
redis.cache.max.heap.setting=maxmemory 2G
report.scheduler.mail.sender.host=aspmailhost.ideasdev.int
reportGenRequestBaseOutputPath=${base.sas.drive.path}/SAS/Data/Reports
reportGenRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rptGenRequest.map
revisionRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/revRequest.map
roomClassSyncRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rcSynchRequest.map
rssArchiveFolder=${base.g3.drive.path}/G3/Data/RateShopper/archive
rssDataFolder=${base.g3.drive.path}/G3/Data/RateShopper
rssDropFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
rssIncomingFolder=${base.g3.drive.path}/G3/Data/RateShopper/incoming
runTaskOutputPath=${base.g3.drive.path}/G3/Data/Runtask
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
saleDemoUsersLocation=${base.g3.drive.path}/G3/Deploy/LMS/Sales
sas.db.password=${env.DB_PASSWORD}
sas.db.userid=sa
sas.jdbc.password=${env.DB_PASSWORD}
sasDomainNodes=${env.SAS_HOST}
sasPassword=password
sihot.extractHealth.baseUrl=http://localhost:8093
ssoLoginRedirect=http://${env.APP_HOST}/solutions/c/portal/login
ssoLogoutRedirect=http://${env.APP_HOST}/solutions/web/guest/home
tempFolderBasePathForReports=${base.g3.drive.path}/G3/Data/Report
tetris.gftservicexml.extract.remote.dir=${base.g3.drive.path}/G3/Temp/
typeThreePopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3poprequest.map
typeThreeValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3valrequest.map
typeTwoValidationLogBasePath=${base.sas.drive.path}/SAS/Logs
typeTwoValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/valrequest.map
updateDDLSeedMap=${base.sas.drive.path}/SAS/Deploy/Maps/updateDdlSeedRequest.map
updateDDLSeedPath=${base.sas.drive.path}/SAS/Deploy/DDL/analytics/ddl_scripts
validationRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/validation
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
whatIfResponseFileProp5=${base.g3.drive.path}/G3/Data/Mock/prop5WhatIf.xml
whatIfResponseFileProp6=${base.g3.drive.path}/G3/Data/Mock/prop6WhatIf.xml
g3.springdoc.enabled=true
ngi.set.cloud.migration.stable.job.enabled=true
pacman.fplos.serviceUrl=https://g3-recommendation-internal.stage.ideasrms.com/api/v1/broker
module.dynamicPropertyGroup.group-pricing-evaluation=true
log4j2.formatMsgNoLookups=true
purge.purgeOperaHistoryJob=true
feature.walkme.menu.enabled=true
sendWebRatesToNGI=true
fdsjit.oauth2.resourceserver.client-id=48hld8fmml57p8mu9ioud315m4
lms.hours.until.stale=8
central.rms.data.sync.enabled=true
considerBDEDecisionsInProgressAsComplete=true
edit.config.parameter.enabled=true
jems.retry.autoRetryEnabled=false
platform.jboss.as7.management.profile=services-profile
lms.snapshot.enabled=true
vaadin.shell=true
fdsjit.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
lms.refresh.property.enabled=true
purge.purgeOperaHistoryJobDaysToDelete=7
feature.roiSimulator.externalUsersEnabled=false
chaos.monkey.functionality.enabled=true
fdsjit.enabled=true
lms.single.signon.enabled=true
platform.jboss.as7.management.port=9999
pacman.fds.universal.redirect.uri=http://${env.APP_HOST}/solutions/universallogin/callback
awssqs.enabled=${env.SQS_ENABLED:false}
salesforce.rest.password=IDeaS345
sqs.name=${env.SQS_NAME:sqs-sns-event-bridge-1}
salesforce.capacity.synch.enabled=true
ngi.rest.url=${env.NGI_REST_URL}
salesforce.stage.synch.enabled=true
fetch.salesforce.email.address.disabled=false
salesforce.rest.username=<EMAIL>
salesforce.rest.client.secret=8344249421880090910
salesforce.stage.synch.version=2
salesforce.rest.client.id=3MVG9pHRjzOBdkd_Ob2ZvV.Ohgc13Fey6pu5QFrnY9KlwmzXgHxz05DBs1I5R6yR_j1xBDFCVQNfux8OrqD1L
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.dev.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.dev.ideasrms.com
pacman.feature.global.universal.login.help=true