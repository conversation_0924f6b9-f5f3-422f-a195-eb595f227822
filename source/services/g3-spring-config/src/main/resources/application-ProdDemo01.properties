ahws.newApi.v2.enabled=false
canary.server.url=
central.rms.data.sync.enabled=true
currency.exchange.service.url=http://us2p2ta01.ideasprod.int:20057/CExchange/services/CurrencyExchange?wsdl
dbHost=${env.DB_HOST}
default-jndi-URL=jnp://localhost:1099
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note it make take 15 minutes after receiving the email for the data to be present. Also note that your requested files will be automatically deleted in 72 hours.
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=g3_prod_proddemo01
fds.application.name=G3-DEMO1
feature.roiSimulator.externalUsersEnabled=true
feature.sevenzip.uncompress=false
fetch.salesforce.email.address.disabled=false
g3.internal.loadbalancer.url=http://${env.APP_HOST}:81
g3.link.client=https://g3demo.ideas.com/solutions/
g3.restClient.base64EncodedBasicAuth=************************************
gftServiceXmlMonitoringUrl=http://localhost:31110/v5iservices-3/servlet/SimpleMonitor
isLMSEnabled=true
is.CpDecisionBARNOVRDetails.batchDelete.enabled=true
jndi-URL=jnp://localhost:1099
lms.hours.until.stale=1
lms.refresh.property.enabled=true
lms.single.signon.enabled=true
lms.snapshot.enabled=true
lms.snapshot.location=${base.g3.drive.path}/G3/Snapshots/
lms.walkme.url=https://cdn.walkme.com/users/9470/walkme_9470_https.js
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=/var/opt/mssql/data
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
lmsSupplementalScriptLocation=${base.g3.drive.path}/G3/Deploy/LMS/Scripts
load.extract.cache.on.startup=true
log4j2.formatMsgNoLookups=true
module.dynamicPropertyGroup.group-pricing-evaluation=true
ngi.htng.cloud.migration.parallel.job.limit=5
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.ideasrms.com
ngi.rest.url=http://ngiprodcluster01.ideasprod.int:9090
ngi.set.cloud.migration.stable.job.enabled=true
pacman-jnp=jnp://localhost:1099
pacman.base.db.server.url=*******************************
pacman.database.baselineOnMigrate=true
pacman.database.baselineVersion=5.6.3
pacman.demo.property.daily.load.mappings=StressBox.S60750=StressBox.S60751,StressBox.S60750=StressBox.S60752
pacman.ejb.internal.url=http://localhost:8080/pacman-platformsecurity
pacman.ejb.rest.url=https://g3demo.ideas.com/pacman-platformsecurity/rest
pacman.encryption.key=8Ge86B1WN24sL06SY7zUV35ezyOS60bL7cO2TFoGSxvGImU4BAIanhqPsPE0ZV4ePa3a3x9EB2psKI8DQh2uP3FAgm2jS4EU
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.exceed.lms.url=https://discover.ideas.com/
pacman.faqModule.enabled=false
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
pacman.fds.admin.url=https://admin.ideasrms.com
fds.base.url=https://fds.ideasrms.com/api
fds.oauth2.token-uri=https://fds.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1inhdo4ti68oivbdfmo3lhq6cq
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=1inhdo4ti68oivbdfmo3lhq6cq
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_9VL8MDrxL
well.known.uri=https://fds.ideasrms.com/api/uis/login/.well-known/jwks.json
pacman.feature.agileRatesHierarchyEnabled=true
pacman.feature.agileRatesProductGroupsEnabled=true
pacman.g3.api.rest.url=https://g3demo.ideas.com/api
pacman.help.secure=false
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.integration.opera.stopOnValidationError=true
pacman.parameterByModule.enabled=false
pacman.portal.google.analytics.account=UA-*********-1
pacman.portal.google.analytics.enabled=true
pacman.portal.google.analytics.siteSpeedSampleRate=10
pacman.restcall.for.testing.enabled=false
pacman.server.name=localhost
pacman.user.management.allowed.to.self.modify=false
pendo.prefix=G3ProdDemo1
platform-jnp=jnp://localhost:1099
platform.jboss.as7.management.host=localhost
platform.jboss.as7.management.port=9999
platform.jboss.as7.management.profile=services-profile
platform.jobweb.url=http://localhost/job-web
platform.security.showFullExceptionStackTrace=false
platform.security.ssoURL=http://localhost:8091/sso
platform.server.name=localhost
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.local.instance.enabled=${env.REDIS_LOCAL_ENABLED}
saleDemoUsersLocation=${base.g3.drive.path}/G3/Deploy/LMS/Sales
salesforce.soap.auth.url=https://login.salesforce.com/services/Soap/u/27.0
salesforce.soap.client.url=https://na1-api.salesforce.com/services/Soap/class/PWS_Webservices
salesforce.soap.password=IDeaS345t4Nqt3YMxLDkwkxmnZTrMuPx8
salesforce.soap.username=<EMAIL>
sasDomainNodes=mn5pg3xenvl001.ideasprod.int
sihot.extractHealth.baseUrl=https://data-admin.sis.ideasrms.com
ssoLoginRedirect=http://localhost/solutions/c/portal/login
ssoLogoutRedirect=http://localhost/solutions/web/guest/home
use.cognito.for.internal.users=true
vaadin.internal.url=http://localhost:8080/vaadin
walkme.url=https://cdn.walkme.com/users/9470/walkme_9470_https.js
xapiPassword=e5605bde10f30aea0fa6a18dca00a272f17b8198db09d2b2
xapiUsername=G3Production
extractFtpFolder.linux=/var/opt/mssql/backup/ftp
pacman.fplos.serviceUrl=https://g3-recommendation-internal.ideasrms.com/api/v1/broker
universal.login.url=https://id.ideasrms.com/login
pacman.fds.universal.client.id=fa845f01-6f4e-41a8-aec9-369afe66388a
pacman.fds.universal.client.secret=5ae4743455da68e09c165193e1a8255af7e98433485a908e21b8ba7322d19f50
universal.login.account.settings.url=https://id.ideasrms.com/userprofile
pacman.fds.universal.redirect.uri=https://g3demo.ideas.com/solutions/universallogin/callback
universal.login.logout.url=https://id.ideasrms.com/api/uis/signout
fds.oauth2.authorize.url=https://id.ideasrms.com/api/uis/login/oauth2/authorize
pacman.fds.g3.environment.id=f8151b26-30cf-4eec-9b8f-5f06b3279294
pacman.fds.g3.environment.ids=f8151b26-30cf-4eec-9b8f-5f06b3279294
pacman.fds.uad.environment.id=8227a8ca-c3df-44be-873f-6b06dc3ab517
pacman.fds.navigator.environment.id=59fb3f06-8b1c-4b3a-a584-5b5a977e09b5
pacman.fds.optix.environment.id=09058b24-e23b-46df-902d-573f9ae26d19
pacman.fds.specialevents.environment.id=660bd982-2a7d-4984-a848-4fe7c911c5bf
dbInstance=
linux.data.extract.callback.url=http://${env.APP_HOST}:81
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.ideasrms.com
projectedBookingPace.s3BucketName=projected-booking-pace-us-east-2-prod
emailTemplates.s3BucketName=g3-sfdc-email-templates-prod
oauth.client.id=1inhdo4ti68oivbdfmo3lhq6cq
oauth.client.secret=ep37oiuk8skrmuiibnkpf9495drsg0o4st36l88gbhk2dithhgj
oauth.client.url=https://fds.ideasrms.com/api/uis/internal_m2m/oauth2/token
pacman.fds.base.url=https://fds.ideasrms.com/api
pacman.fds.client.id=1inhdo4ti68oivbdfmo3lhq6cq
pacman.rdl.api.key=ElotrbivoE68cWvA9ZujP4KXrSE90Z1paj8qew0O
pacman.rdl.base.url=https://rdl.ideasrms.com
report.delivery.enabled.on.nonprod=true
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
awssqs.enabled=${env.SQS_ENABLED:false}
sqs.name=${env.SQS_NAME:sqs-sns-event-bridge-1}
