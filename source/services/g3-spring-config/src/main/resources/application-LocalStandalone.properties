SasCatalogsBasePath=${base.sas.drive.path}/SAS/Deploy/Catalogs/
central.rms.data.sync.enabled=true
centralrms.common.alerts.base-uri=http://localhost:9093/api/centralrms-common/G3/alerts/
centralrms.common.base-uri=http://localhost:9093/api/centralrms-common/G3
centralrms.enabled=true
centralrms.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
centralrms.oauth2.client-secret=9m3ap4nhvt6cqld3ql0f2bj3je1ag3avctnlbv623abvo6eokov
centralrms.oauth2.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.resourceserver.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
centralrms.pricing.base-uri=http://localhost:9091/G3/pricing-config/
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.client-id=iokjj0kfso4e2mj5nfjj4plj1
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.security.oauth2.resourceserver.jwt.PRICING.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.security.oauth2.resourceserver.jwt.PRICING.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.fds.base-uri=https://fds.dev.ideasrms.com/api
well.known.uri=https://fds.dev.ideasrms.com/api/uis/login/.well-known/jwks.json
channel.srp.population.enabled=true
considerBDEDecisionsInProgressAsComplete=true
crsArchiveBucket=g3-dev-crs-data-archive-cluster-01
dbHost=${env.APP_HOST}
default-jndi-URL=jnp://${env.APP_HOST}:1099
edit.config.parameter.enabled=true
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.server.host=aspmailhost.ideasdev.int
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=g3_local_localstandalone
feature.monitoringdashboard.datadogLinkEnabled=false
feature.roiSimulator.externalUsersEnabled=false
feature.walkme.menu.enabled=true
g3.internal.loadbalancer.url=http://localhost:8080
g3.link.client=http://${env.APP_HOST}/solutions/
gftServiceXmlMonitoringUrl=http://${env.APP_HOST}:31110/v5iservices-3/servlet/SimpleMonitor
ideas.one.embedded.url=http://localhost:4300/local
jndi-URL=jnp://${env.APP_HOST}:1099
lms.hours.until.stale=8
lms.refresh.property.enabled=true
lms.single.signon.enabled=true
lms.snapshot.enabled=true
lms.snapshot.location=${base.g3.drive.path}/G3/Deploy/LMS/Snapshots
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:\Program Files\Microsoft SQL Server\MSSQL10_50.G3SQL01\MSSQL\DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
lmsSupplementalScriptLocation=${base.g3.drive.path}/G3/Deploy/LMS/Scripts
log4j2.formatMsgNoLookups=true
module.dynamicPropertyGroup.group-pricing-evaluation=true
ngi.htng.cloud.migration.parallel.job.limit=2
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.set.cloud.migration.stable.job.enabled=true
oauth.client.id=7ne6thmsh9hpbq7hdldi5p06bg
oauth.client.secret=2ds5or7av70mi08jboi1ba2agqjq0ohgog47mmkfus2539vh4ag
oauth.client.url=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
pacman-jnp=jnp://${env.APP_HOST}:1099
pacman.base.db.server.url=jdbc:sqlserver://${env.APP_HOST}:1433
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://${env.APP_HOST}/pacman-platformsecurity/rest
pacman.exceed.lms.private.key=MIIEpAIBAAKCAQEArqfZ1SfuJiQAKlRdlhUuy4Uf46l8DBDVdIWNstsAvSd/oT1ljd/vIHTRmde59HvjCmRwlrS0NfAmWP+B7ZjJ+XkmgV1iKhCkZUWhAR74zC7MPhTwj6plUsDQvD7dKKGgj8w3HgdKO8mPjb6d/xeEej7K8oGmv+ktRK7Ok66n7L/VpfxI8eGdO9gE6Cw0GuZclTwHOP+7Onm3qquiU1pkefQR29yeGfg5kFB8PiufoFYRg0+TPSU+hsOPg+vM0D0Cq76SHNUf/AIsMdWPjk0fxlyklX8QObkRAqLhaIkb4py7lBRAfwCUrGdYtJ2b/1fiq5hLtsP84O+H6rNc1KmJPwIDAQABAoIBABiEl5/EqznPc/Z9QWYAM0F+vqtG75MCCfUBipLBC6mCEfigldEEzpzPyVy/W+tmOMkMsIF3fJTV0YxM0gB4omHTEAS5Bz7Ad1OAIsn1fxequiE870g7s12ak7LZYFKD3fZRxKsCevlJok1TpPpaYs6GYhcI/E+UfDZYE8fHjOs/Bz/aWxsmt/P/1yyjubve98B4YjVfUmCjMEcWGPQt7bbP+7SnZns6L90Wi3spPseAmyQpovtmm3RijD/6O09MYACbNLMqwAklS2DWpg1Q9w7RJZ0pmS6Q63ScSYd01po+a/jbOap7iBlGE/w1Og9nGT5VijvjdPiFWuY61zt0S4ECgYEA5SEILSrSzi/2ykMWxvZ/Z/ZnOu7Iz5h0JxMYtcd4Co6+jmd7dKR/dpBx04gvlOfy1SdiYdZsow5f+1/SXkx6Q/yDBxVnBU2CEqHbsa5sjRwfqZ5uFhdB+EbC8SHhrKrMeFYSVituTLaPq75sSsj+CypUWW/LCdiMBGtzmN36jLMCgYEAwyNoAVtDtAj923deQ7+KeN2aZmXGgpAO44/T7LSnFaspbCyc9Nmghc1hwnxrg8xwhMv9BaE5FZlkAZge58s6FSQDTEx0P4a6TCWDP0RNFZCh69/v2r4CnbMzPvmtRrqRUGVuemsF9z4EuAdMvw1b08rCmE23CIPfMF1Auf5gb0UCgYA1rURWGHckGHZvMcJPQAXe3AmY88GVaHilgsLCKwWDK6eWgsWYMMUviG0yCfBjqObpan2QwAPZOw/fOrZ0ouPts2UarrfgtSSoqsRl3p/Neu4r9VKC2FX7DNC0XqSYp7aY5kkLaMdBL29+43BJIMbq5LK39QmSt+ktS+Lbx/zfIwKBgQCMwz6kM9xrw0y0ArB5+HJ/64MUOHhw+E/1MWwzRMF+bQCtaqUyLsaPnm7Vp4C7Cz9jW+7CH+GYxhSQ3dX1aPEWDOtgH4p/xMUgZrL9cVyVoMZc6yYPSZ+DeKAP3nVzHuPx9akzH36nMhKfWYywCIfkCFBFI1JUDyRxs+McB/YybQKBgQCepZYJA+IdCdBPpjI+pNnPGt3ix8kXaUXTD59dqrVWiFqML5tp0HrevwuySOWSwqEDsqbxo0tf8kvZTs3tMtsiwidk+leHe41UKeXZsih5jfJFtsdu4uI/h1jk8eOnDzaZXYeJOyS4kZAbL9ROYdbDg7hDJ4MjsPDeEYoi7rfIKw==
pacman.fds.admin.url=https://admin.dev.ideasrms.com
pacman.fds.base.url=https://fds.stage.ideasrms.com/api
pacman.fds.client.id=7ne6thmsh9hpbq7hdldi5p06bg
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
fds.base.url=https://fds.dev.ideasrms.com/api
fds.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
fds.oauth2.client-secret=
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
pacman.feature.openam.disabled=true
pacman.g3.aws.base.url=https://g3-api.dev.ideasrms.com
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.secure.help.base.url=https://help-api.dev.ideasrms.com/
pacman.server.name=${env.APP_HOST}
pendo.prefix=G3DevLocal
platform-jnp=jnp://${env.APP_HOST}:1099
platform.jboss.as7.management.host=${env.APP_HOST}
platform.jboss.as7.management.port=9999
platform.jboss.as7.management.profile=services-profile
platform.jobweb.url=http://${env.APP_HOST}/job-web
platform.security.noSSO=false
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.server.name=${env.APP_HOST}
purge.purgeOperaHistoryJob=true
purge.purgeOperaHistoryJobDaysToDelete=7
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.local.instance.enabled=${env.REDIS_LOCAL_ENABLED}
report.scheduler.mail.sender.host=aspmailhost.ideasdev.int
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
saleDemoUsersLocation=${base.g3.drive.path}/G3/Deploy/LMS/Sales
sasDomainNodes=${env.APP_HOST}
sendWebRatesToNGI=true
sihot.extractHealth.baseUrl=http://localhost:8093
ssoLoginRedirect=http://${env.APP_HOST}/solutions/c/portal/login
ssoLogoutRedirect=http://${env.APP_HOST}/solutions/web/guest/home
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.shell=true
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
# URLs
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.dev.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.dev.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.dev.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.dev.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.dev.ideasrms.com
g3.springdoc.enabled=true
pacman.fplos.serviceUrl=https://g3-recommendation-internal.dev.ideasrms.com/api/v1/broker
extractFtpFolder.linux=/mnt/c/G3/Temp/ftp
pacman.fds.universal.redirect.uri=http://${env.APP_HOST}/solutions/universallogin/callback
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.dev.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.dev.ideasrms.com
#AWS
sqs.name=sns-sqs-bridge
awssqs.enabled=${env.SQS_ENABLED:false}
spring.profiles.active=${env.SPRING_PROFILE:LocalStandalone}
dbInstance=
pacman.base.db.instance=
projectedBookingPace.s3BucketName=projected-booking-pace-us-east-2-dev
emailTemplates.s3BucketName=g3-sfdc-email-templates-dev
pacman.feature.global.universal.login.help=true