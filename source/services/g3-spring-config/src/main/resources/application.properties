base.g3.drive.path=/opt
base.sas.drive.path=/opt
CommitForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupCommitRequest.map
CreateForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupRequest.map
IdeasDefaultSASStoredProc=ideasSendRequest
SasMapFileLocation=${base.sas.drive.path}/SAS/Deploy/Maps
TETRISGLOBAL_DbName=Global
ahws.newApi.v2.enabled=true
allow.limited.data.extract=true
archiveFolder=${base.g3.drive.path}/G3/Data
awssqs.enabled=${env.SQS_ENABLED:false}
calibrationProc=sendRequest
calibrationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/calibRequest.map
canary.release.enable=false
canary.server.url=http://localhost:7788
catchupRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/catchup
cmt.datasource.dir=
cmt.datasource.enabled=false
commitForecastGroupProc=sendRequest
compress_uncompress_path=C:/Programs/Compress-Uncompress/compress.exe
config.parameters.cache.enabled=true
createForecastGroupProc=sendRequestStreaming
crs.identify.bde.or.cdp.enabled=true
crsArchiveFolder=${base.g3.drive.path}/G3/Data/CRSData/archive
crsDataFolder=${base.g3.drive.path}/G3/Data/CRSData
crsExtractsFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/extracts
crsIncomingFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming
crsRatesFolder=${base.g3.drive.path}/G3/Data/Rates
currency.exchange.service.basic.auth.credential=YWRtaW46YWRtaW4=
currency.exchange.service.url=http://us1s2ta01:10106/CExchange/services/CurrencyExchange?wsdl
dataFolder=${base.g3.drive.path}/G3/Data
dataPurgeProc=sendRequest
datadog.rum.applicationId=${env.DD_RUM_APP_ID:''}
datadog.rum.clientToken=${env.DD_RUM_CLIENT_TOKEN:''}
datadog.rum.enable=${env.DD_RUM_ENABLE:false}
db.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
dbHost=${env.DB_HOST}
dbInstance=G3SQL01
dbName=Global
dbPassword=IDeaS123
dbPort=1433
dbUserId=g3services
default-jndi-URL=jnp://${env.APP_HOST}:1099
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.componentRoom.orphanMapping.change.recipients=<EMAIL>
email.componentRoom.orphanMapping.change.subject=Review Mapping of Orphan Room Types
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.discover.user.create.subject=Welcome to IDeaS Discover - Your Login Details
email.is.production.environment=false
email.sas.datasets.body.fail=We were unable to provide your data extraction at this time for property {0} (id:{1})
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.sas.datasets.subject.fail=Data Extraction Failure
email.sas.datasets.subject.success=Data Extraction Success
email.server.host=aspmailhost.ideasprod.int
email.server.port=25
email.user.bde.body=BDE Daily Processing has completed for {0} properties out of {1}.
email.user.bde.from=<EMAIL>
email.user.bde.subject=BDE Daily Processing Completed Notification
email.user.bde.to=<EMAIL>
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.user.create.from=<EMAIL>
email.user.create.subject=Welcome to IDeaS G3 RMS - Your Login Details
email.user.password.subject=Password reset
email.uses.auth=false
email.uses.ssl=false
environment.cluster=g3_prod_prodstandalone
etlProc=etl_type2_load_stp
experience.api.context.url=http://test.ideas.com/g3/
experience.api.server.url=https://ideas.elmnts.com/lrs/
extract.path.cleanup.from.cache=true
extractArchiveFolder=${base.g3.drive.path}/G3/Data/Extracts/archive
extractErrorFolder=${base.g3.drive.path}/G3/Data/Extracts/error
extractFtpFolder=${base.g3.drive.path}/G3/Temp/ftp
feature.corporate.users=true
feature.monitoringdashboard.datadogLinkEnabled=true
feature.monitoringdashboard.datadogLogBaseURL=https://ideas.datadoghq.com/logs
feature.putCrsAck.multithreaded=true
feature.roatools.phase2=true
feature.role.all.permissions=false
feature.salesforce.enableUS8922=false
feature.sas.dbtool.enabled=true
feature.sevenzip.uncompress=true
feature.sso.useDynamicCookieDomain=false
feature.uploadDecisions.multithreaded=true
feature.user.panacea=true
feature.walkme.enabled=true
fetch.salesforce.email.address.disabled=true
forecastingProc=sendRequest
forecastingRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstRequest.map
ftp.folder.requests=Test_CI
ftp.server.password=3Q6Q1ngQ
ftp.server.username.care.user=g3restore
ftp.server.username=g3dataextraction
ftp.server=ftp-waas.ideasprod.int
g3.link.client=http://${env.APP_HOST}/solutions/
gftServiceExtractProcessorURL=none
gftServiceXmlIncomingExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlIncomingRemoteExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlMonitoringUrl=http://${env.APP_HOST}:31110/v5iservices-3/servlet/SimpleMonitor
gftServiceXmlOutboundDecisionFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlPath=${base.g3.drive.path}/G3/Data/Config/gftservice.xml
gftServiceXmlUploadBufferFolder=${base.g3.drive.path}/G3/Temp/
helloWorldProc=hello_world_stored_process
htng.address.to=http://dev-web.ttaws.com/rt/pmsWS
htng.async.callback.service.password=password
htng.async.callback.service.username=<EMAIL>
htng.client.password=jC+9Spqc+W
htng.client.username=ideasint
htng.endpoint.url=http://dev-web.ttaws.com/rt/pmsWS
infoMgr.popout.enabled=true
isFiscalCalendarUserPreferenceEnabled=true
isLMSEnabled=false
isMultinodeApp=false
java.security.auth.login.config=
jndi-URL=jnp://${env.APP_HOST}:1099
jobinstance.log.dir=${base.g3.drive.path}/G3/Logs/Jobs
lms.walkme.url=https://cdn.walkme.com/users/8b2ca488bac8464dba0a47410629d155/test/walkme_8b2ca488bac8464dba0a47410629d155_https.js
load.extract.cache.on.startup=false
load.property.cache.on.startup=false
localFTPFolder=${base.g3.drive.path}/G3/FTP
loginx.enabled=true
mock.roles.enabled=false
mockSASInteractions=false
ngi.rest.url=${env.NGI_REST_URL:http://localhost:9090}
oauth.client.id=7ne6thmsh9hpbq7hdldi5p06bg
oauth.client.secret=2ds5or7av70mi08jboi1ba2agqjq0ohgog47mmkfus2539vh4ag
oauth.client.url=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
onQArchiveFolder=notneeded
onlineReportServerLocation=${base.g3.drive.path}/G3/Data/OnlineReport
opera.client.log.dir=${base.g3.drive.path}/G3/Logs/Opera
operapopulationDailyRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operadailypopulation.map
operapopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operapopulation.map
optimizationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/optRequest.map
outOfOrderPopulationMap=${base.sas.drive.path}/SAS/Deploy/Maps/type2ooo.map
outOfOrderPopulationPath=${base.sas.drive.path}/SAS/Deploy/DDL/Analytics/ddl_scripts
outOfOrderPopulationProc=ideasSendRequest
paceBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacebuildrequest.map
paceHistoryBuildProc=ideasSendRequest
paceHistoryBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacehistoryrequest.map
pacman-ear=platform-ear
pacman-jnp=jnp://${env.APP_HOST}:1099
pacman.base.db.instance=G3SQL01
pacman.base.db.password=IDeaS123
pacman.base.db.port=1433
pacman.base.db.qualifiers=base,revisions,ddl,config,test,skip
pacman.base.db.scripts.jar=${base.g3.drive.path}/G3/Deploy/Lib/tenant.jar
pacman.base.db.server.url=jdbc:sqlserver://${env.DB_HOST}:1433
pacman.base.db.username=g3services
pacman.checkSystemRequirements=false
pacman.client.property.cache.service.enabled=false
pacman.context.root=pacman-platformsecurity
pacman.db.data.volumes=
pacman.db.transaction.volumes=
pacman.demo.property.daily.load.mappings=Hilton.MEMPR=HiltonTest.HLT1
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://${env.APP_HOST}/pacman-platformsecurity/rest
pacman.encryption.key=GaQn}?wF8,@#mZ8!^HnHj!:a5hea%Fa$sdfd1232sdfsdfsd%^dfdfdfd1238c3dg$sdfdfd88f8df8d8fdfjdjfdjf
pacman.exceed.lms.api.key=V3BocAjhPqYHYXwvdkj8Q1cr
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.exceed.lms.url=https://discoverideassandbox.docebosaas.com/
pacman.extractCaching.enabled=true
pacman.faqModule.enabled=true
pacman.fds.admin.url=https://admin.dev.ideasrms.com
pacman.fds.base.url=https://fds.stage.ideasrms.com/api
pacman.fds.client.id=7ne6thmsh9hpbq7hdldi5p06bg
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
pacman.g3.aws.base.url=https://g3-api.dev.ideasrms.com
pacman.help.flare.url=/help/Default.htm
pacman.informationmanager.ExcessOOOAlertEvaluation.enabled=true
pacman.informationmanager.displayhistorydetails=true
pacman.informationmanager.seperateInstanceDetailsLoading=true
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.integration.opera.agentWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/opera-agent.war
pacman.integration.opera.stopOnValidationError=false
pacman.monitorProcessPropertyRunFrequency=30
pacman.opera.ping.timeout.enabled=false
pacman.parameterByModule.enabled=true
pacman.portal.InfoMgr.isPropertyAndPropertyGroupEnabled=true
pacman.portal.hilton.logo=GRO_Logo_403x32.png
pacman.portal.minify=true
pacman.portal.sandbox.useHiltonLogo=false
pacman.portal.showPageLoadTime=true
pacman.portal.showSystemHealth.DQI=true
pacman.portal.userPreference.isInvalidPreferenceCheckEnabled=true
pacman.pricingmanagement.isOverrideForValidRateEnabled.enabled=true
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.restcall.for.testing.enabled=true
pacman.server.name=${env.APP_HOST}
pacman.server.rest.context.root=rest
pacman.showAlertManager=true
pacman.showSystemRequirementsLink=true
pacman.ui.propertyGroupsEnabled=true
pacman.ui.sessionTimeoutMessage=1200
pacman.ui.sessionTimeoutMessageEnabled=true
pacman.ui.sessionTimeoutWarning=60
pacman.ui.user.context.caching.enabled=false
pacman.useRemCapFix=true
pacman.userDefinedDateFormat.enabled=true
pacman.webrateshopping.marketpostionconstraint.dow.enabled=true
pendo.prefix=G3ProdStandalone
platform-ear=platform-ear
platform-jnp=jnp://${env.APP_HOST}:1099
platform.configparams2.enabled=true
platform.context.root=support
platform.jobweb.url=http://${env.APP_HOST}/job-web
platform.security.containerSecurityRealm=tetris
platform.security.ldapBaseDN=dc=ideas,dc=com
platform.security.ssoCookieDomain=localhost
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.security.writeUserDN=cn=Directory Manager
platform.security.writeUserPass=password
platform.server.name=${env.APP_HOST}
platform.udplogger.enabled=true
platform.ui.logLevel=WARN
populationProc=ideasSendRequest
populationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/popRequest.map
property.estimated.capacity.enabled=true
propertyConfigurationsFolder=${base.g3.drive.path}/G3/Data/PropertyConfigurations
purge.global.enabled=true
purge.property.batch.size=30
purge.property.enabled=true
purge.sas.enabled=true
purge.sas.history.days=365
purgeRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/purgeRequest.map
ratchetValidationBaseLogPath=${base.sas.drive.path}/SAS/Logs
ratchetdbname=Ratchet
rateShopperCatchupFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
ratesValidationDataMap=${base.sas.drive.path}/SAS/Deploy/Maps/ratedata.map
ratesValidationMap=${base.sas.drive.path}/SAS/Deploy/Maps/raterequest.map
rdl.retry.interval=10000
rdl.retry.maxAttempts=4
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.local.instance.enabled=${env.REDIS_LOCAL_ENABLED}
remote.filesystem.batch.size=10
remote.filesystem.max.connections=1
remote.filesystem.multithreaded=true
report.scheduler.mail.sender.from=<EMAIL>
report.scheduler.mail.sender.host=aspmailhost.ideasprod.int
report.scheduler.mail.sender.port=25
report.scheduler.mail.sender.protocol=smtp
reportGenRequestBaseOutputPath=${base.sas.drive.path}/SAS/Data/Reports
reportGenRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rptGenRequest.map
requestMap=C:/Program Files/SASHome/sas_xsl_map/request.map
revisionProc=sendRequest
revisionRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/revRequest.map
roomClassSyncProc=sendRequest
roomClassSyncRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rcSynchRequest.map
rssArchiveFolder=${base.g3.drive.path}/G3/Data/RateShopper/archive
rssDataFolder=${base.g3.drive.path}/G3/Data/RateShopper
rssDropFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
rssIncomingFolder=${base.g3.drive.path}/G3/Data/RateShopper/incoming
runTaskOutputPath=${base.g3.drive.path}/G3/Data/Runtask
runTaskProc=sendRequest
runtask.email.from.address=<EMAIL>
salesforce.capacity.synch.enabled=false
salesforce.error.email.enabled=false
salesforce.error.email.to=undefined
salesforce.error.max.retries=6
salesforce.organization.id=00DQ0000003MHYn
salesforce.pacman.updatePropertyName=false
salesforce.portal.id=0603000000188nx
salesforce.rest.auth.url=https://test.salesforce.com/services/oauth2/token
salesforce.rest.client.id=3MVG9pHRjzOBdkd87KtFsH7PmZ2HiJyC9BeBXeTn3JbWH0qgw09v_g4Wls35D39q_9nPTe3j.sIoQJO.XlQdX
salesforce.rest.client.secret=4523299089254950409
salesforce.rest.password=IDeaS234vd2F8yzmSFxgvWPLO9BQ1qUAN
salesforce.rest.username=<EMAIL>.realign12
salesforce.soap.auth.url=https://test.salesforce.com/services/Soap/u/27.0
salesforce.soap.client.url=https://cs3-api.salesforce.com/services/Soap/class/PWS_Webservices
salesforce.soap.password=IDeaS666RLRC15iM6czB43H3AjiOwV8Ol
salesforce.soap.username=<EMAIL>.g3integ1
salesforce.stage.date.synch.enabled=false
salesforce.stage.synch.enabled=false
salesforce.stage.synch.version=1
salesforce.userauthsynch.enabled=false
sas.db.password=IDeaS123
sas.db.userid=G3SAS
sas.dynamically.allocate.server.add.property.node.enabled=false
sas.jdbc.password=IDeaS123
sas.jdbc.port=8591
sas.jdbc.user=sasspawn
sasDomainNodes=${env.APP_HOST}
sasPassword=IDeaS123
sasPort=8561
sasUserId=sastrust@saspw
sqs.name=sqs-sns-event-bridge-1
ssoLoginRedirect=http://${env.APP_HOST}/solutions/c/portal/login
ssoLogoutRedirect=http://${env.APP_HOST}/solutions/web/guest/home
tempFolderBasePathForReports=${base.g3.drive.path}/G3/Data/Report
tetris.gftservicexml.extract.remote.dir=${base.g3.drive.path}/G3/Temp/
typeThreePopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3poprequest.map
typeThreeValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3valrequest.map
typeTwoValidationLogBasePath=${base.sas.drive.path}/SAS/Logs
typeTwoValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/valrequest.map
updateDDLSeedMap=${base.sas.drive.path}/SAS/Deploy/Maps/updateDdlSeedRequest.map
updateDDLSeedPath=${base.sas.drive.path}/SAS/Deploy/DDL/Analytics/ddl_scripts
updateSeedDDLProc=sendRequest
use.async.webrates=true
use.separate.opera.summary.steps=true
useSasTkForWhatIf=true
useSasTkWhereAvailable=true
vaadin.cdn.enabled=false
vaadin.cdn.url=
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.productionMode.enabled=false
vaadin.service.url=/vaadin
vaadin.theme=tetris
vaadin.version=8.8.2
vaadin.widgetset=com.ideas.tetris.ui.widget.TetrisWidgetset
validationDirectoryName=validation
validationRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/validation
walkme.url=https://cdn.walkme.com/users/9470/test/walkme_9470_https.js
whatIfProc=sendRequestStreaming
whatIfResponseFileProp5=${base.g3.drive.path}/G3/Data/Mock/prop5WhatIf.xml
whatIfResponseFileProp6=${base.g3.drive.path}/G3/Data/Mock/prop6WhatIf.xml
workcontext.session.reaper.interval.millis=300000
workcontext.session.timeout.millis=1800000
pacman.feature.openam.disabled=true
default.temp.location=${base.g3.drive.path}/G3/temp
font.report=DejaVu Sans
font.optimizationsetting=DejaVu Sans
FPLOSServiceStep.timeout=1800
occupancyForecastDecisionPopulationStep.timeout=1800
SASOutputPopulationStreamingStep.timeout=1800
CPDecisionBarOutputPopulationStep.timeout=1800
SASOutputPopulationChunkStep.timeout=1800
config.param.migrated.externalSystem=bookassist,smarthotel,protelioair,synxis,rvng,siteminder,synxis10,cubilis,staah,suite8,hermeshotels,synxis6,newbookpms,hotelnetsolution,leanpms,traveltripper,synxis3,synxis9,synxis11,protel,infor,rezlynx,availpro,anyhtng,groupmax,winnerpms,synxis7,windsurfercrs,synxis2,avvio,ratetiger,gse,synxis1,maestropms,bookingexpert,vaillms,hotelspider,synxis5,webrezpropms,yourvoyager,rategain,blastness,connecterevmax,advantagereserve,ihotelier,hbsi,synxis4,curtisc,rmspms,synxis8,ihotelier1,ihotelier2,ihotelier3,ihotelier4
htng.param.migration.backward.compatibility.enabled=false
FPLOSServiceMigrationStep.timeout=1800
pacman.enable.credentialEncryption=true
jboss.as.management.blocking.timeout=1200
pacman.http.htng.connectTimeout=360000
pacman.http.htng.readTimeout=360000
pacman.http.salesforce.connectTimeout=15000
pacman.http.salesforce.readTimeout=15000
rds.decision.folder.path=${base.g3.drive.path}/rdsDrop
job.autoStopFailedJobStepsStep.enabled=false
job.autoCreateProblemOnFailedJobsStep.enabled=false
projectedBookingPace.s3BucketName=projected-booking-pace
emailTemplates.s3BucketName=g3-sfdc-email-templates-dev
SASOutputPopulationStrategy.isDataPopulationByUpsertEnabled=true
well.known.uri=https://fds.dev.ideasrms.com/api/uis/login/.well-known/jwks.json
job.view.hasActiveInstance.useRelevantStatuses.enabled=true

#### API WAR
security.endpoints.web.cors.allowed.origins=http://localhost:4200,http://localhost:4300,https://one.embedded.dev.ideasrms.com,https://one.embedded.stage.ideasrms.com,https://one.embedded.ideasrms.com
# Secret key
security.jwt.token.secret-key=war-api-key-2021abababa21
security.jwt.refresh.token.secret-key=war-api-refresh-key-2021abababa21
security.jwt.token.expire-length=3600000
security.jwt.refresh.token.expire-length=64800000
security.jwt.token.encoder.pass=G3Token123Refresh567
shared.G3.encryption.key=EaQn}?wF8,@#mZ8!^HnHj!:a5hva%Fa$sdfd1232sdfsdfsd%^dfdfdfa1238c3dg$sdfdfd88f8df4d8fdfjdj6djL
springdoc.swagger-ui.supportedSubmitMethods=

#### JOB WAR

management.endpoint.health.show-details=ALWAYS
management.endpoint.health.status.http-mapping.DOWN=200
management.endpoints.web.exposure.include=health,env,beans,refresh
management.endpoints.web.base-path=/

test.value=in application.properties
#Placeholder value for swapping out the Spring config for com.ideas.infra.tetris.security.cognito.config.CognitoConfig
use.spring.config.for.cognito=This is from the system settings, not Spring config

base.value=In application.properties. Here I am
pacman.node.type=${env.NODE_TYPE:'job_and_ui'}

pace.webrate.differential.data.s3.bucket=competitorrates-rms-normalized-us-east-dev-01