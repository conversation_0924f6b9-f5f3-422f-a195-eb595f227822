CommitForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupCommitRequest.map
CreateForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupRequest.map
SasMapFileLocation=${base.sas.drive.path}/SAS/Deploy/Maps
archiveFolder=${base.g3.drive.path}/G3/Data
aws.cognito.scim.api.token.url=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
awssqs.enabled=${env.AWS_SQS_START_ENABLED:true}
calibrationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/calibRequest.map
canary.server.url=
catchupRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/catchup
central.rms.data.sync.enabled=true
centralrms.common.alerts.base-uri=https://centralrms.dev.ideasrms.com/api/centralrms-common/G3/alerts/
centralrms.common.base-uri=https://centralrms.dev.ideasrms.com/api/centralrms-common/G3
centralrms.enabled=true
centralrms.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
centralrms.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
centralrms.oauth2.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.resourceserver.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
centralrms.pricing.base-uri=https://pricing-config.dev.ideasrms.com/api/central-rms/G3/pricing-config/
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.client-id=iokjj0kfso4e2mj5nfjj4plj1
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.security.oauth2.resourceserver.jwt.PRICING.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.security.oauth2.resourceserver.jwt.PRICING.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.fds.base-uri=https://fds.dev.ideasrms.com/api
well.known.uri=https://fds.dev.ideasrms.com/api/uis/login/.well-known/jwks.json
channel.srp.population.enabled=true
chaos.monkey.functionality.enabled=true
cmt.datasource.dir=${base.g3.drive.path}/G3/CMA
compress_uncompress_path=C:\Programs\Compress-Uncompress\compress.exe
considerBDEDecisionsInProgressAsComplete=true
crsArchiveBucket=g3-stage-crs-data-archive-cluster-01
crsArchiveFolder=${base.g3.drive.path}/G3/Data/CRSData/archive
crsDataFolder=${base.g3.drive.path}/G3/Data/CRSData
crsExtractsFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/extracts
crsIncomingFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming
crsRatesFolder=${base.g3.drive.path}/G3/Data/Rates
dataFolder=${base.g3.drive.path}/G3/Data
dbHost=${env.DB_HOST}
default-jndi-URL=jnp://${env.APP_HOST}:1099
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;\span>&lt;\a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.server.host=aspmailhost.ideasdev.int
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=g3_dev_devcluster01
extractArchiveFolder=${base.g3.drive.path}/G3/Data/Extracts/archive
extractErrorFolder=${base.g3.drive.path}/G3/Data/Extracts/error
extractFtpFolder=${base.g3.drive.path}/G3/Data/Temp/ftp
feature.roiSimulator.externalUsersEnabled=false
feature.sso.useDynamicCookieDomain=true
feature.walkme.menu.enabled=true
fetch.salesforce.email.address.disabled=false
forecastingRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstRequest.map
ftp.folder.requests=DevCluster
g3.internal.loadbalancer.url=http://g3devcluster01.ideasdev.int:81
g3.link.client=https://devg3.ideasdev.int/solutions/
gftServiceXmlIncomingExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlIncomingRemoteExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlMonitoringUrl=http://localhost:31110/v5iservices-3/servlet/SimpleMonitor
gftServiceXmlOutboundDecisionFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlPath=${base.g3.drive.path}/G3/Data/Config/gftservice.xml
gftServiceXmlUploadBufferFolder=${base.g3.drive.path}/G3/Temp/
hazelcast.cloud.group.name=G3-Dev-Cluster01
ideas.one.embedded.url=https://one.embedded.dev.ideasrms.com/LATEST
isMultinodeApp=true
jndi-URL=jnp://${env.APP_HOST}:1099
jobinstance.log.dir=${base.g3.drive.path}/G3/Logs/Jobs
lms.hours.until.stale=8
lms.refresh.property.enabled=false
lms.single.signon.enabled=false
lms.walkme.url=https://cdn.walkme.com/users/75d90fc4cc7d433c8adc89ecd103b30e/test/walkme_75d90fc4cc7d433c8adc89ecd103b30e_https.js
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:\Program Files\Microsoft SQL Server\MSSQL10_50.G3SQL01\MSSQL\DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
localFTPFolder=${base.g3.drive.path}/G3/FTP
log4j2.formatMsgNoLookups=true
module.beta.booking-pace-report=true
module.beta.booking-situation-report=true
module.beta.comparative-booking-pace-report=true
module.beta.data-extraction-report=true
module.beta.forecast-validation-report=true
module.beta.inventory-history-report=true
module.beta.output-override-report=true
module.beta.performance-comparison-report=true
module.beta.pricing-override-history-report=true
module.beta.pricing-pace-report=true
module.beta.pricing-report=true
module.beta.schedule-reports=true
module.dynamicPropertyGroup.group-pricing-evaluation=true
ngi.htng.cloud.migration.parallel.job.limit=2
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.dev.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.dev.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.dev.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.dev.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.dev.ideasrms.com
ngi.rest.url=http://ngidevcluster01.ideasdev.int:9090
ngi.set.cloud.migration.stable.job.enabled=true
oauth.client.id=7ne6thmsh9hpbq7hdldi5p06bg
oauth.client.secret=2ds5or7av70mi08jboi1ba2agqjq0ohgog47mmkfus2539vh4ag
oauth.client.url=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
opera.client.log.dir=${base.g3.drive.path}/G3/Logs/Opera
operapopulationDailyRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operadailypopulation.map
operapopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operapopulation.map
optimizationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/optRequest.map
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
outOfOrderPopulationMap=${base.sas.drive.path}/SAS/Deploy/Maps/type2ooo.map
outOfOrderPopulationPath=${base.sas.drive.path}/SAS/Deploy/DDL/Analytics/ddl_scripts
paceBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacebuildrequest.map
paceHistoryBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacehistoryrequest.map
pacman-jnp=jnp://${env.APP_HOST}:1099
pacman.base.db.scripts.jar=${base.g3.drive.path}/G3/Deploy/Lib/tenant.jar
pacman.base.db.server.url=*************************************************
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.demo.property.daily.load.mappings=StressBox.S60750=StressBox.S60751,StressBox.S60750=StressBox.S60752
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://devg3.ideasdev.int/pacman-platformsecurity/rest
pacman.encryption.key=4iDCjqT01gWzb77Xtn9Bw7JIRXQ6UqROSgTtAEZWD275WzOYqvNYYy7666j8z8H89HEyi17TgoFVdRGJcMwKhE03f9E7gtUq
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.faqModule.enabled=false
pacman.fds.admin.url=https://admin.dev.ideasrms.com
pacman.fds.base.url=https://fds.stage.ideasrms.com/api
pacman.fds.client.id=7ne6thmsh9hpbq7hdldi5p06bg
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
fds.base.url=https://fds.dev.ideasrms.com/api
fds.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
pacman.feature.openam.disabled=true
pacman.g3.api.rest.url=https://devg3.ideasdev.int/api
pacman.g3.aws.base.url=https://g3-api.dev.ideasrms.com
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.integration.opera.agentWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/opera-agent.war
pacman.integration.opera.stopOnValidationError=true
pacman.parameterByModule.enabled=false
pacman.portal.google.analytics.account=UA-********-1
pacman.portal.google.analytics.enabled=true
pacman.portal.google.analytics.siteSpeedSampleRate=30
pacman.portal.showPageLoadTime=false
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.secure.help.base.url=https://help-api.dev.ideasrms.com/
pacman.server.name=${env.APP_HOST}
pendo.prefix=G3DevCluster1
platform-jnp=jnp://${env.APP_HOST}:1099
platform.jobweb.url=http://${env.APP_HOST}:8080/job-web
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.server.name=${env.APP_HOST}
populationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/popRequest.map
propertyConfigurationsFolder=${base.g3.drive.path}/G3/Data/PropertyConfigurations
purge.purgeOperaHistoryJob=true
purge.purgeOperaHistoryJobDaysToDelete=7
purgeRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/purgeRequest.map
ratchetValidationBaseLogPath=${base.sas.drive.path}/SAS/Logs
rateShopperCatchupFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
ratesValidationDataMap=${base.sas.drive.path}/SAS/Deploy/Maps/ratedata.map
ratesValidationMap=${base.sas.drive.path}/SAS/Deploy/Maps/raterequest.map
redis.cache.isCluster=${env.REDIS_CACHE_IS_CLUSTER}
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.cache.node.addresses=${env.REDIS_CACHE_NODE_ADDRESSES}
redis.local.instance.enabled=${env.REDIS_LOCAL_ENABLED}
report.scheduler.mail.sender.host=aspmailhost.ideasdev.int
reportGenRequestBaseOutputPath=${base.sas.drive.path}/SAS/Data/Reports
reportGenRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rptGenRequest.map
requestMap=C:\Program Files\SASHome\sas_xsl_map\request.map
revisionRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/revRequest.map
roomClassSyncRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rcSynchRequest.map
rssArchiveFolder=${base.g3.drive.path}/G3/Data/RateShopper/archive
rssDataFolder=${base.g3.drive.path}/G3/Data/RateShopper
rssDropFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
rssIncomingFolder=${base.g3.drive.path}/G3/Data/RateShopper/incoming
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
salesforce.organization.id=00DQ000000B57If
salesforce.rest.client.id=3MVG9Oe7T3Ol0ea4KLKLCC4McoPLHnyzYMbZICYQw6yg_vwxfq8kEuEJ5wRhdr80VDvrbVMiwitJCzBb_YvdX
salesforce.rest.client.secret=2547210597410575274
salesforce.rest.password=IDeaS234cbe5AxiNwG8Pcm2nyTXhJvch
salesforce.rest.username=<EMAIL>
salesforce.soap.username=<EMAIL>.g3integ2
sas.jdbc.user=<EMAIL>
sasDomainNodes=mn4dg3xsasl011.ideasdev.int,mn4dg3xsasl012.ideasdev.int
should.embed.benefits.measurement=true
sihot.extractHealth.baseUrl=https://data-admin.sis.dev.ideasrms.com
ssoLoginRedirect=http://devg3.ideasdev.int/solutions/c/portal/login
ssoLogoutRedirect=http://devg3.ideasdev.int/solutions/web/guest/home
tetris.gftservicexml.extract.remote.dir=${base.g3.drive.path}/G3/Temp/
typeThreePopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3poprequest.map
typeThreeValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3valrequest.map
typeTwoValidationLogBasePath=${base.sas.drive.path}/SAS/Logs
typeTwoValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/valrequest.map
updateDDLSeedMap=${base.sas.drive.path}/SAS/Deploy/Maps/updateDdlSeedRequest.map
updateDDLSeedPath=${base.sas.drive.path}/SAS/Deploy/DDL/Analytics/ddl_scripts
use.cognito.for.internal.users=true
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.productionMode.enabled=true
vaadin.shell=true
validationRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/validation
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
walkme.url=https://d3b3ehuo35wzeh.cloudfront.net/users/9470/walkme_9470_https.js
whatIfResponseFileProp5=${base.g3.drive.path}/G3/Temp/prop5WhatIf.xml
whatIfResponseFileProp6=${base.g3.drive.path}/G3/Temp/prop6WhatIf.xml
xapiPassword=e5605bde10f30aea0fa6a18dca00a272f17b8198db09d2b2
xapiUsername=G3Production
g3.springdoc.enabled=true
aws.cognito.poolId.external.users.prod.bstn=us-east-2_rjjXehR8v
pacman.fplos.serviceUrl=https://g3-recommendation-internal.dev.ideasrms.com/api/v1/broker
aws.cognito.clientId.external.users.prod.ideas=4pm125vvnhvd86hpfvc0i7ne5r
aws.cognito.userpool.domain.ideas=dev-internal-users
aws.cognito.clientId.external.users.prod.bstn=79t8474r9b4m459lf9eauqai8p
aws.cognito.userpool.domain.bstn=fds-stage-g3test-auth
aws.cognito.clientSecret.external.users.prod.bstn=${env.bstnClientSecret}
aws.cognito.poolId.external.users.prod.ideas=us-east-2_RTEvPQcNs
pacman.fds.universal.redirect.uri=https://devg3.ideasdev.int/solutions/universallogin/callback
dbInstance=
pacman.base.db.instance=
sqs.name=${env.SQS_NAME:sqs-sns-event-bridge-1}
quartz.scheduler.start.enabled=${env.QUARTZ_SCHEDULER_START_ENABLED:true}
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.dev.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.dev.ideasrms.com
projectedBookingPace.s3BucketName=projected-booking-pace-us-east-2-dev
emailTemplates.s3BucketName=g3-sfdc-email-templates-dev
pacman.feature.global.universal.login.help=true

pacman.node.testing.enabled=true