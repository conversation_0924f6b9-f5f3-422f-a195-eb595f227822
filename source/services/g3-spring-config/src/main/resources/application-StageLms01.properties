central.rms.data.sync.enabled=true
channel.srp.population.enabled=true
chaos.monkey.functionality.enabled=true
considerBDEDecisionsInProgressAsComplete=true
crsArchiveBucket=g3-stage-crs-data-archive-cluster-01
dbHost=${env.DB_HOST}
default-jndi-URL=jnp://localhost:1099
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.server.host=aspmailhost.ideasstg.int
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=g3_stage_stagelms01
feature.roiSimulator.externalUsersEnabled=false
feature.walkme.menu.enabled=true
fetch.salesforce.email.address.disabled=false
g3.link.client=http://mn4sg3xenvl008.ideasstg.int/solutions/
gftServiceXmlMonitoringUrl=http://localhost:31110/v5iservices-3/servlet/SimpleMonitor
isLMSEnabled=true
jndi-URL=jnp://localhost:1099
lms.hours.until.stale=8
lms.refresh.property.enabled=true
lms.single.signon.enabled=true
lms.snapshot.enabled=true
lms.snapshot.location=${base.g3.drive.path}/G3/Deploy/LMS/Snapshots
lms.walkme.url=https://cdn.walkme.com/users/9470/test/walkme_9470_https.js
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=/var/opt/mssql/data
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
lmsSupplementalScriptLocation=${base.g3.drive.path}/G3/Deploy/LMS/Scripts
load.extract.cache.on.startup=true
log4j2.formatMsgNoLookups=true
module.dynamicPropertyGroup.group-pricing-evaluation=true
ideas.one.embedded.url=https://one.embedded.dev.ideasrms.com/LATEST
g3.internal.loadbalancer.url=http://${env.APP_HOST}:8080
ngi.htng.cloud.migration.parallel.job.limit=2
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.stage.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.stage.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.stage.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.stage.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.stage.ideasrms.com
ngi.rest.url=http://us1sngiap01.ideasstg.int:9090/
ngi.set.cloud.migration.stable.job.enabled=true
oauth.client.id=7ne6thmsh9hpbq7hdldi5p06bg
oauth.client.secret=2ds5or7av70mi08jboi1ba2agqjq0ohgog47mmkfus2539vh4ag
oauth.client.url=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
pacman-jnp=jnp://localhost:1099
pacman.base.db.server.url=*************************************************
pacman.database.baselineOnMigrate=true
pacman.database.baselineVersion=5.6.3
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.demo.property.daily.load.mappings=StressBox.S60750=StressBox.S60751,StressBox.S60750=StressBox.S60752
pacman.ejb.internal.url=http://localhost:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://mn4sg3xenvl008.ideasstg.int/pacman-platformsecurity/rest
pacman.encryption.key=C7DS13P7Fx61uR9n1kpdDE23BeCt7WPK50yrl9Qa3hjflol78Z7UmTs80gbJN69wV4Lp8AC5v32KO7EChN7qLYKNb2mVnN0T
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.faqModule.enabled=false
pacman.fds.admin.url=https://admin.stage.ideasrms.com
pacman.fds.base.url=https://fds.stage.ideasrms.com/api
pacman.fds.client.id=7ne6thmsh9hpbq7hdldi5p06bg
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
fds.base.url=https://fds.stage.ideasrms.com/api
fds.oauth2.token-uri=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
well.known.uri=https://fds.stage.ideasrms.com/api/uis/login/.well-known/jwks.json
pacman.feature.agileRatesHierarchyEnabled=true
pacman.feature.agileRatesProductGroupsEnabled=true
pacman.feature.openam.disabled=true
pacman.g3.api.rest.url=https://stageg3lms.ideas.com/api
pacman.g3.aws.base.url=https://g3-api.stage.ideasrms.com
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.integration.opera.stopOnValidationError=true
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.secure.help.base.url=https://help-api.stage.ideasrms.com/
pacman.server.name=localhost
pendo.prefix=G3StageLms1
platform-jnp=jnp://localhost:1099
platform.jboss.as7.management.host=localhost
platform.jboss.as7.management.port=9999
platform.jboss.as7.management.profile=services-profile
platform.jobweb.url=http://localhost/job-web
platform.security.showFullExceptionStackTrace=false
platform.security.ssoURL=http://localhost:8091/sso
platform.server.name=localhost
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.local.instance.enabled=${env.REDIS_LOCAL_ENABLED}
report.scheduler.mail.sender.host=aspmailhost.ideasstg.int
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
saleDemoUsersLocation=${base.g3.drive.path}/G3/Deploy/LMS/Sales
sasDomainNodes=mn4sg3xenvl008.ideasstg.int
sihot.extractHealth.baseUrl=https://data-admin.sis.stage.ideasrms.com
ssoLoginRedirect=http://localhost/solutions/c/portal/login
ssoLogoutRedirect=http://localhost/solutions/web/guest/home
vaadin.cdn.url=http://d1udrshf4wgihr.cloudfront.net/vaadin
vaadin.internal.url=http://localhost:8080/vaadin
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
xapiPassword=e5605bde10f30aea0fa6a18dca00a272f17b8198db09d2b2
xapiUsername=G3Production
lmsSqlLinuxDestination=/var/opt/mssql/data/
pacman.fplos.serviceUrl=https://g3-recommendation-internal.stage.ideasrms.com/api/v1/broker
lms.sql.linux=false
pacman.fds.universal.redirect.uri=http://mn4sg3xenvl008.ideasstg.int/solutions/universallogin/callback
pacman.fds.g3.environment.ids=50c4fbf5-2230-4493-ae33-9a7569b62dd9
pacman.fds.g3.environment.id=50c4fbf5-2230-4493-ae33-9a7569b62dd9
pacman.fds.uad.environment.id=44ac98f6-1a66-41a8-b349-d689b36e0a49
pacman.fds.navigator.environment.id=ea0abe47-4316-4f4f-8c98-b000987fb805
pacman.fds.optix.environment.id=3abb21c8-3619-45ab-acae-d05a23681ae3
pacman.fds.specialevents.environment.id=c0cdd3ca-6138-4c84-bf9a-13db8cb9afea
dbInstance=
awssqs.enabled=${env.SQS_ENABLED:false}
pacman.base.db.instance=
sqs.name=${env.SQS_NAME:sqs-sns-event-bridge-1}
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.stage.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.stage.ideasrms.com
projectedBookingPace.s3BucketName=projected-booking-pace-us-east-2-stage
emailTemplates.s3BucketName=g3-sfdc-email-templates-stage
pacman.feature.global.universal.login.help=true