SasCatalogsBasePath=${base.sas.drive.path}/SAS/Deploy/Catalogs/
central.rms.data.sync.enabled=true
centralrms.common.alerts.base-uri=http://localhost:9093/api/centralrms-common/G3/alerts/
centralrms.common.base-uri=http://localhost:9093/api/centralrms-common/G3
centralrms.enabled=true
centralrms.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
centralrms.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
centralrms.oauth2.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.resourceserver.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
centralrms.pricing.base-uri=http://localhost:9091/G3/pricing-config/
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.client-id=iokjj0kfso4e2mj5nfjj4plj1
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.security.oauth2.resourceserver.jwt.PRICING.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.security.oauth2.resourceserver.jwt.PRICING.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.fds.base-uri=https://fds.dev.ideasrms.com/api
well.known.uri=https://fds.dev.ideasrms.com/api/uis/login/.well-known/jwks.json
channel.srp.population.enabled=true
chaos.monkey.functionality.enabled=true
considerBDEDecisionsInProgressAsComplete=true
crsArchiveBucket=g3-stage-crs-data-archive-cluster-01
dbHost=MN4DBLDCISL004.ideasdev.int
dbInstance=
default-jndi-URL=jnp://${env.APP_HOST}:1099
edit.config.parameter.enabled=true
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.server.host=aspmailhost.ideasdev.int
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=g3_dev_devstandalone
fdsjit.enabled=true
fdsjit.oauth2.resourceserver.client-id=48hld8fmml57p8mu9ioud315m4
fdsjit.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
feature.monitoringdashboard.datadogLinkEnabled=false
feature.roiSimulator.externalUsersEnabled=false
feature.walkme.menu.enabled=true
fetch.salesforce.email.address.disabled=false
g3.internal.loadbalancer.url=http://localhost:8080
g3.link.client=http://${env.APP_HOST}/solutions/
gftServiceXmlMonitoringUrl=http://${env.APP_HOST}:31110/v5iservices-3/servlet/SimpleMonitor
jems.retry.autoRetryEnabled=false
jndi-URL=jnp://${env.APP_HOST}:1099
lms.hours.until.stale=8
lms.refresh.property.enabled=true
lms.single.signon.enabled=true
lms.snapshot.enabled=true
lms.snapshot.location=${base.g3.drive.path}/G3/Deploy/LMS/Snapshots
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:\Program Files\Microsoft SQL Server\MSSQL10_50.G3SQL01\MSSQL\DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
lmsSupplementalScriptLocation=${base.g3.drive.path}/G3/Deploy/LMS/Scripts
log4j2.formatMsgNoLookups=true
module.dynamicPropertyGroup.group-pricing-evaluation=true
ngi.htng.cloud.migration.parallel.job.limit=2
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.dev.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.dev.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.dev.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.dev.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.dev.ideasrms.com
ngi.rest.url=${env.NGI_REST_URL}
ngi.set.cloud.migration.stable.job.enabled=true
oauth.client.id=7ne6thmsh9hpbq7hdldi5p06bg
oauth.client.secret=2ds5or7av70mi08jboi1ba2agqjq0ohgog47mmkfus2539vh4ag
oauth.client.url=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
pacman-jnp=jnp://${env.APP_HOST}:1099
pacman.base.db.instance=
pacman.base.db.server.url=*************************************************
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://${env.APP_HOST}/pacman-platformsecurity/rest
pacman.encryption.key=GaQn}?wF8,@#mZ8!^HnHj!:a5hea%Fa%^dfdfdfd1238c3dg
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.fds.admin.url=https://admin.dev.ideasrms.com
pacman.fds.base.url=https://fds.stage.ideasrms.com/api
pacman.fds.client.id=7ne6thmsh9hpbq7hdldi5p06bg
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
fds.base.url=https://fds.dev.ideasrms.com/api
fds.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
pacman.feature.openam.disabled=true
pacman.g3.aws.base.url=https://g3-api.dev.ideasrms.com
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.secure.help.base.url=https://help-api.dev.ideasrms.com/
pacman.server.name=${env.APP_HOST}
pendo.prefix=G3DevLinuxStandalone
platform-jnp=jnp://${env.APP_HOST}:1099
platform.jboss.as7.management.host=${env.APP_HOST}
platform.jboss.as7.management.port=9999
platform.jboss.as7.management.profile=services-profile
platform.jobweb.url=http://${env.APP_HOST}/job-web
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.server.name=${env.APP_HOST}
purge.purgeOperaHistoryJob=true
purge.purgeOperaHistoryJobDaysToDelete=7
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.local.instance.enabled=${env.REDIS_LOCAL_ENABLED}
report.scheduler.mail.sender.host=aspmailhost.ideasdev.int
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
saleDemoUsersLocation=${base.g3.drive.path}/G3/Deploy/LMS/Sales
salesforce.capacity.synch.enabled=true
salesforce.rest.client.id=3MVG9pHRjzOBdkd_Ob2ZvV.Ohgc13Fey6pu5QFrnY9KlwmzXgHxz05DBs1I5R6yR_j1xBDFCVQNfux8OrqD1L
salesforce.rest.client.secret=8344249421880090910
salesforce.rest.password=IDeaS345
salesforce.rest.username=<EMAIL>
salesforce.stage.synch.enabled=true
salesforce.stage.synch.version=2
sasDomainNodes=${env.APP_HOST}
sendWebRatesToNGI=true
sihot.extractHealth.baseUrl=https://data-admin.sis.dev.ideasrms.com
ssoLoginRedirect=http://${env.APP_HOST}/solutions/c/portal/login
ssoLogoutRedirect=http://${env.APP_HOST}/solutions/web/guest/home
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.shell=true
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
g3.springdoc.enabled=true
pacman.fplos.serviceUrl=https://g3-recommendation-internal.stage.ideasrms.com/api/v1/broker
pacman.fds.universal.redirect.uri=http://${env.APP_HOST}/solutions/universallogin/callback
awssqs.enabled=${env.SQS_ENABLED:false}
sqs.name=${env.SQS_NAME:sqs-sns-event-bridge-1}
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.dev.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.dev.ideasrms.com
pacman.feature.global.universal.login.help=true