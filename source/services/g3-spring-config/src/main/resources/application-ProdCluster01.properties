CommitForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupCommitRequest.map
CreateForecastGroupMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstGroupRequest.map
RegulatorServiceAppServerThrottle.ScheduledReportJob=20
RegulatorServiceThrottle.IDPScheduledReportJob=20
RegulatorServiceThrottle.ScheduledReportJob=30
SasMapFileLocation=${base.sas.drive.path}/SAS/Deploy/Maps
archiveFolder=${base.g3.drive.path}/G3/Data
awssqs.enabled=${env.AWS_SQS_START_ENABLED:true}
quartz.scheduler.start.enabled=${env.QUARTZ_SCHEDULER_START_ENABLED:true}
pacman.g3.aws.base.url=https://g3-api.ideasrms.com
calibrationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/calibRequest.map
canary.server.url=
catchupRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/catchup
central.rms.data.sync.enabled=true
centralrms.common.alerts.base-uri=https://centralrms.ideasrms.com/api/centralrms-common/G3/alerts/
centralrms.common.base-uri=https://centralrms.ideasrms.com/api/centralrms-common/G3
centralrms.common.host-url=https://portfolionavigator.ideasrms.com
centralrms.enabled=true
centralrms.oauth2.client-id=1inhdo4ti68oivbdfmo3lhq6cq
centralrms.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
centralrms.oauth2.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_9VL8MDrxL
centralrms.oauth2.resourceserver.client-id=7rgv6n20i9obaqi87n9na58tah
centralrms.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_9VL8MDrxL
centralrms.oauth2.token-uri=https://fds.ideasrms.com/api/uis/internal_m2m/oauth2/token
centralrms.pricing.base-uri=https://pricing-config.ideasrms.com/api/central-rms/G3/pricing-config/
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.client-id=4kfq8o6ieq1o9vn2ttsujr9ih5
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_9VL8MDrxL
centralrms.security.oauth2.resourceserver.jwt.PRICING.client-id=7rgv6n20i9obaqi87n9na58tah
centralrms.security.oauth2.resourceserver.jwt.PRICING.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_9VL8MDrxL
centralrms.room.class.sync.enabled=true
well.known.uri=https://fds.ideasrms.com/api/uis/login/.well-known/jwks.json
channel.srp.population.enabled=true
cmt.datasource.dir=${base.g3.drive.path}/CMA
cmt.datasource.enabled=true
compress_uncompress_path=C:\Programs\Compress-Uncompress\compress.exe
considerBDEDecisionsInProgressAsComplete=false
crsArchiveBucket=g3-prod-crs-data-archive-cluster-01
crsArchiveFolder=${base.g3.drive.path}/G3/Data/CRSData/archive
crsDataFolder=${base.g3.drive.path}/G3/Data/CRSData
crsExtractsFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/extracts
crsIncomingFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming
crsRatesFolder=${base.g3.drive.path}/G3/Data/Rates
currency.exchange.service.url=http://us2p2ta01.ideasprod.int:20057/CExchange/services/CurrencyExchange?wsdl
dataFolder=${base.g3.drive.path}/G3/Data
datadog.rum.applicationId=12807a11-cad9-457d-9750-319ef4ae2950
datadog.rum.clientToken=pub198d1d5c567ada96d175487776be7832
datadog.rum.enable=true
dbHost=MN5PG3XDBSL101.ideasprod.int
default-jndi-URL=jnp://${env.APP_HOST}:1099
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note it make take 15 minutes after receiving the email for the data to be present. Also note that your requested files will be automatically deleted in 72 hours.
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
enable.scim.apis.for.prod=true
enableCreationOfJobThroughRest=true
environment.cluster=g3_prod_prodcluster01
environmentId=1
extractArchiveFolder=${base.g3.drive.path}/G3/Data/Extracts/archive
extractErrorFolder=${base.g3.drive.path}/G3/Data/Extracts/error
extractFtpFolder=${base.g3.drive.path}/G3/Data/Temp/ftp
fds.application.name=G3-PROD1
fds.base.url=https://fds.ideasrms.com/api
pacman.fds.admin.url=https://admin.ideasrms.com
fds.oauth2.token-uri=https://fds.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1inhdo4ti68oivbdfmo3lhq6cq
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=1inhdo4ti68oivbdfmo3lhq6cq
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_9VL8MDrxL
feature.roiSimulator.externalUsersEnabled=false
feature.sevenzip.uncompress=false
feature.sso.useDynamicCookieDomain=true
fetch.salesforce.email.address.disabled=false
forecastingRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/fcstRequest.map
ftp.folder.requests=Prod
ftp.server=ftp.ideasprod.int
g3.app.server.nodes=http://mn5pg3xappl101.ideasprod.int,http://mn5pg3xappl102.ideasprod.int,http://mn5pg3xappl103.ideasprod.int,http://mn5pg3xappl104.ideasprod.int,http://mn5pg3xappl105.ideasprod.int,http://mn5pg3xappl106.ideasprod.int,http://mn5pg3xappl107.ideasprod.int,http://mn5pg3xappl108.ideasprod.int
g3.dynamic.node.selection=true
g3.internal.loadbalancer.url=http://g3prodcluster01-nopersistence.ideasprod.int:81
g3.link.client=https://g3.ideas.com/solutions/
g3.restClient.base64EncodedBasicAuth=************************************
g3.title=Hilton GRO
gftServiceXmlIncomingExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlIncomingRemoteExtractFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlMonitoringUrl=http://localhost:31110/v5iservices-3/servlet/SimpleMonitor
gftServiceXmlOutboundDecisionFolder=${base.g3.drive.path}/G3/Temp/
gftServiceXmlPath=${base.g3.drive.path}/G3/Data/Config/gftservice.xml
gftServiceXmlUploadBufferFolder=${base.g3.drive.path}/G3/Temp/
hazelcast.cloud.cluster.addresses=mn5pg3xutlw101.ideasprod.int:5701,mn5pg3xutlw201.ideasprod.int:5701,mn5pg3xutlw301.ideasprod.int:5701
hazelcast.cloud.group.name=G3-Prod-Cluster01
htng.async.callback.service.password=IDeaS@123
htng.async.callback.service.username=<EMAIL>
isMultinodeApp=true
isPropertyAttachmentEnabled=false
is.CpDecisionBARNOVRDetails.batchDelete.enabled=true
jems.retry.initialInterval=10000
jems.retry.maxInterval=300000
jems.retry.multiplier=5
jndi-URL=jnp://${env.APP_HOST}:1099
jobinstance.log.dir=${base.g3.drive.path}/G3/Data/Logs
lms.hours.until.stale=8
lms.refresh.property.enabled=false
lms.single.signon.enabled=false
lms.walkme.url=https://cdn.walkme.com/users/75d90fc4cc7d433c8adc89ecd103b30e/test/walkme_75d90fc4cc7d433c8adc89ecd103b30e_https.js
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:\Program Files\Microsoft SQL Server\MSSQL10_50.G3SQL01\MSSQL\DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
load.extract.cache.on.startup=true
localFTPFolder=${base.g3.drive.path}/G3/FTP
log4j2.formatMsgNoLookups=true
ngi.htng.cloud.migration.parallel.job.limit=5
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.ideasrms.com
ngi.rest.url=http://ngiprodcluster01.ideasprod.int:9090
ngi.set.cloud.migration.stable.job.enabled=false
opera.client.log.dir=${base.g3.drive.path}/G3/Data/Logs
operapopulationDailyRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operadailypopulation.map
operapopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/operapopulation.map
optimizationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/optRequest.map
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
outOfOrderPopulationMap=${base.sas.drive.path}/SAS/Deploy/Maps/type2ooo.map
outOfOrderPopulationPath=${base.sas.drive.path}/SAS/Deploy/DDL/Analytics/ddl_scripts
paceBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacebuildrequest.map
paceHistoryBuildRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/pacehistoryrequest.map
pacman-jnp=jnp://${env.APP_HOST}:1099
pacman.base.db.scripts.jar=${base.g3.drive.path}/G3/Deploy/Lib/tenant.jar
pacman.base.db.server.url=**************************************************
pacman.configured.outbounds=PCRS,HILSTAR,SYNXIS,TARS,OPERA,iHotelier
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.decisionDailyBAROutputNonHiltonCRS.tableBatch.enabled=true
pacman.demo.property.daily.load.mappings=Hilton.LAXAG=HiltonTest.HLTGI,Hilton.MEMSG=HiltonTest.HMPTN,Hilton.MSPHW=HiltonTest.HOMWD,Hilton.ATLUP=HiltonTest.TATLS,Hilton.PDXVA=HiltonTest.HILTN,Hilton.SANQQ=HiltonTest.CURIO,Hilton.LRMFM=HiltonTest.RSORT,Hilton.ATLWA=HiltonTest.WAATL,Hilton.VPLITLI=HiltonTest.VPLITTL
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=https://g3.ideas.com/pacman-platformsecurity/rest
pacman.encryption.key=8Ge86B1WN24sL06SY7zUV35ezyOS60bL7cO2TFoGSxvGImU4BAIanhqPsPE0ZV4ePa3a3x9EB2psKI8DQh2uP3FAgm2jS4EU
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.exceed.lms.url=https://discover.ideas.com/
pacman.faqModule.enabled=false
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
pacman.g3.api.rest.url=https://g3.ideas.com/api
pacman.independentproductmigration.job.enabled=false
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.integration.opera.agentWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/opera-agent.war
pacman.integration.opera.stopOnValidationError=true
pacman.parameterByModule.enabled=false
pacman.pms.inbound.v2.enabled=true
pacman.portal.google.analytics.account=UA-********-1
pacman.portal.google.analytics.enabled=true
pacman.portal.google.analytics.siteSpeedSampleRate=10
pacman.portal.showPageLoadTime=false
pacman.secure.help.hilton.specific.enabled=true
pacman.server.name=${env.APP_HOST}
pacman.user.management.allowed.to.self.modify=false
pendo.prefix=G3Prod1
platform-jnp=jnp://${env.APP_HOST}:1099
platform.jobweb.url=http://${env.APP_HOST}:8080/job-web
platform.security.showFullExceptionStackTrace=false
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.server.name=${env.APP_HOST}
populationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/popRequest.map
propertyConfigurationsFolder=${base.g3.drive.path}/G3/Data/PropertyConfigurations
propertyId.query.enabled=false
purgeRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/purgeRequest.map
qualifiedDeferredDeliveryRateLoadStep.batchSize=75
qualifiedDeferredDeliveryRateLoadStep.chunkSize=75
qualifiedDeferredDeliveryRateLoadStep.timeout=900
ratchetValidationBaseLogPath=${base.sas.drive.path}/SAS/Logs
rateShopperCatchupFolder=${base.g3.drive.path}/G3/Data/RateShopper/catchup
ratesValidationDataMap=${base.sas.drive.path}/SAS/Deploy/Maps/ratedata.map
ratesValidationMap=${base.sas.drive.path}/SAS/Deploy/Maps/raterequest.map
redis.cache.isCluster=${env.REDIS_CACHE_IS_CLUSTER}
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.cache.node.addresses=${env.REDIS_CACHE_NODE_ADDRESSES}
redis.local.instance.enabled=false
regulator.unblock.after.unthrottle.enabled=false
reportGenRequestBaseOutputPath=${base.sas.drive.path}/SAS/Data/Reports
reportGenRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rptGenRequest.map
requestMap=C:\Program Files\SASHome\sas_xsl_map\request.map
revisionRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/revRequest.map
roomClassSyncRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/rcSynchRequest.map
rssArchiveFolder=${base.g3.drive.path}/G3/Data/RateShopper/archive
rssDataFolder=${base.g3.drive.path}/G3/Data/RateShopper
rssDropFolder=${base.g3.drive.path}/rssDrop
rssIncomingFolder=${base.g3.drive.path}/G3/Data/RateShopper/incoming
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
salesforce.capacity.synch.enabled=true
salesforce.error.email.to=<EMAIL>,<EMAIL>,<EMAIL>
salesforce.organization.id=00D300000000L78
salesforce.pacman.updatePropertyName=true
salesforce.rest.auth.url=https://login.salesforce.com/services/oauth2/token
salesforce.rest.client.id=3MVG99OxTyEMCQ3iIAXaqMupT20ThJUmVYczZvSAIpx0sLyEeJdyh.OAOLYjbAF0UfcfSWSTFCIHDAtcgdMU2
salesforce.rest.client.secret=9029594548902741965
salesforce.rest.password=IDeaS345t4Nqt3YMxLDkwkxmnZTrMuPx8
salesforce.rest.username=<EMAIL>
salesforce.soap.auth.url=https://login.salesforce.com/services/Soap/u/27.0
salesforce.soap.client.url=https://na1-api.salesforce.com/services/Soap/class/PWS_Webservices
salesforce.soap.password=IDeaS345t4Nqt3YMxLDkwkxmnZTrMuPx8
salesforce.soap.username=<EMAIL>
salesforce.stage.date.synch.enabled=true
salesforce.stage.synch.enabled=true
salesforce.stage.synch.version=2
sas.jdbc.password=S@SSp@wn
sas.jdbc.user=<EMAIL>
sas.log.parsing.enabled=false
sasDomainNodes=mn5pg3xsasw119.ideasprod.int,mn5pg3xsasw120.ideasprod.int,mn5pg3xsasw121.ideasprod.int,mn5pg3xsasw122.ideasprod.int,mn5pg3xsasw123.ideasprod.int,mn5pg3xsasw124.ideasprod.int,mn5pg3xsasw125.ideasprod.int,mn5pg3xsasw126.ideasprod.int,mn5pg3xsasw127.ideasprod.int,mn5pg3xsasw128.ideasprod.int,mn5pg3xsasw129.ideasprod.int,mn5pg3xsasw130.ideasprod.int,mn5pg3xsasw131.ideasprod.int,mn5pg3xsasw132.ideasprod.int,mn5pg3xsasw133.ideasprod.int,mn5pg3xsasw134.ideasprod.int,mn5pg3xsasw135.ideasprod.int,mn5pg3xsasw136.ideasprod.int,mn5pg3xsasw137.ideasprod.int,mn5pg3xsasw138.ideasprod.int,mn5pg3xsasw139.ideasprod.int,mn5pg3xsasw140.ideasprod.int,mn5pg3xsasw141.ideasprod.int,mn5pg3xsasw142.ideasprod.int,mn5pg3xsasw143.ideasprod.int,mn5pg3xsasw144.ideasprod.int,mn5pg3xsasw145.ideasprod.int,mn5pg3xsasw146.ideasprod.int,mn5pg3xsasw147.ideasprod.int,mn5pg3xsasw148.ideasprod.int,mn5pg3xsasw149.ideasprod.int,mn5pg3xsasw150.ideasprod.int,mn5pg3xsasw151.ideasprod.int,mn5pg3xsasw152.ideasprod.int,mn5pg3xsasw153.ideasprod.int,mn5pg3xsasw154.ideasprod.int,mn5pg3xsasw191.ideasprod.int,mn5pg3xsasw192.ideasprod.int,mn5pg3xsasw193.ideasprod.int,mn5pg3xsasw194.ideasprod.int,mn5pg3xsasw155.ideasprod.int
sasPassword=S@Strust
sasrestclient.dataset.enabled=false
sihot.extractHealth.baseUrl=https://data-admin.sis.ideasrms.com
ssoLoginRedirect=https://g3.ideas.com/solutions/c/portal/login
ssoLogoutRedirect=https://g3.ideas.com/solutions/web/guest/home
tetris.gftservicexml.extract.remote.dir=${base.g3.drive.path}/G3/Temp/
typeThreePopulationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3poprequest.map
typeThreeValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/type3valrequest.map
typeTwoValidationLogBasePath=${base.sas.drive.path}/SAS/Logs
typeTwoValidationRequestMap=${base.sas.drive.path}/SAS/Deploy/Maps/valrequest.map
updateDDLSeedMap=${base.sas.drive.path}/SAS/Deploy/Maps/updateDdlSeedRequest.map
updateDDLSeedPath=${base.sas.drive.path}/SAS/Deploy/DDL/Analytics/ddl_scripts
use.cognito.for.internal.users=true
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.productionMode.enabled=true
validationRootFolder=${base.g3.drive.path}/G3/Data/CRSData/incoming/validation
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
walkme.url=https://cdn.walkme.com/users/9470/walkme_9470_https.js
webrate.split.largeExtractFile.bucket.size=10000
whatIfResponseFileProp5=${base.g3.drive.path}/G3/Temp/prop5WhatIf.xml
whatIfResponseFileProp6=${base.g3.drive.path}/G3/Temp/prop6WhatIf.xml
xapiPassword=e5605bde10f30aea0fa6a18dca00a272f17b8198db09d2b2
xapiUsername=G3Production
extractFtpFolder.linux=/var/opt/mssql/backup/ftp
config.param.migrated.externalSystem=advantagereserve,agilsysstay,amadeusidpms,anyhtng,availpro,avvio,blastness,bookassist,bookingexpert,connecterevmax,cubilis,curtisc,groupmax,gse,hbsi,hermeshotels,hotelcube,hotelnetsolution,hotelspider,ihotelier,ihotelier1,ihotelier2,ihotelier3,ihotelier4,indratms,infor,inntopiacrs,leanpms,maestropms,mews,newbookpms,nor1,protel,protelioair,rategain,ratetiger,rezlynx,reztripbar,reztriplrv,rmspms,rvng,siteminder,smarthotel,smshost,staah,suite8,synxis,synxis1,synxis10,synxis11,synxis2,synxis3,synxis4,synxis5,synxis6,synxis7,synxis8,synxis9,tarshtng,traveltripper,vaillms,webrezpropms,windsurfercrs,winnerpms,yourvoyager,landalpms,optimabar,optimacontrols,roverpms,oakyupsell,phobscrs,hotelrunner,roomkeypms,hotelkeypms,agilysysvisualone,spectrapms,shijienterpriseplatform,book360,huazhucrs
pacman.enable.uninstall.property.in.fds=true
pacman.enable.move.property.in.fds=true
pacman.fplos.serviceUrl=https://g3-recommendation-internal.ideasrms.com/api/v1/broker
universal.login.url=https://id.ideasrms.com/login
pacman.fds.universal.client.id=fa845f01-6f4e-41a8-aec9-369afe66388a
pacman.fds.universal.client.secret=${env.UNIVERSAL_LOGIN_CLIENT_SECRET}
pacman.feature.global.universalLoginEnabled=${env.UNIVERSAL_LOGIN_ENABLED}
populateRateProtectNonLinkedRatesStep.batchSize=1
universal.login.account.settings.url=https://id.ideasrms.com/userprofile
replay.message.offset.days.rate_plan=1000
populateRateProtectNonLinkedRatesStep.chunkSize=1
pacman.cognito.internal.user.sync.enabled=true
pacman.fds.universal.redirect.uri=https://g3.ideas.com/solutions/universallogin/callback
universal.login.logout.url=https://id.ideasrms.com/api/uis/signout
fds.oauth2.authorize.url=https://id.ideasrms.com/api/uis/login/oauth2/authorize
pacman.fds.g3.environment.id=cdf68eb7-b0d7-43d9-b2e3-d6e0cdc89249
pacman.fds.g3.environment.ids=cdf68eb7-b0d7-43d9-b2e3-d6e0cdc89249,50faa811-7c10-4941-afb3-f89eacb2533d,d1027d75-424a-4fb7-958e-1dbbb8227169,f40e03c0-eff8-4dfc-8db1-3b9bf7d81a23,f71ae540-e546-401c-9127-c9696dc0ee99,d6772668-c221-4b6c-aab9-3685f9c76ccd
pacman.fds.uad.environment.id=8227a8ca-c3df-44be-873f-6b06dc3ab517
pacman.fds.navigator.environment.id=59fb3f06-8b1c-4b3a-a584-5b5a977e09b5
pacman.fds.optix.environment.id=09058b24-e23b-46df-902d-573f9ae26d19
pacman.fds.specialevents.environment.id=660bd982-2a7d-4984-a848-4fe7c911c5bf
internal.role.configuration.environment.role=secondary
ngi.purge.stats.enabled=false
use.rate.details.v2.for.fplos=false
fplos.service.listener=${env.FPLOS_SERVICE_LISTENER:true}
RegulatorServiceAppServerThrottle.NGIDeferredDeliveryJob=60
RegulatorServiceAppServerThrottle.NGICdpDeferredDeliveryJob=60
RegulatorServiceThrottle.NGIDeferredDeliveryJob=480
RegulatorServiceThrottle.NGICdpDeferredDeliveryJob=480
dbInstance=
pacman.base.db.instance=
linux.data.extract.callback.url=http://${env.APP_HOST}:81
purge.db.timeoutTime=1500
purge.property.batch.size=100
purge.purgeTenantMaxDaysToDelete=15
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.ideasrms.com
projectedBookingPace.s3BucketName=projected-booking-pace-us-east-2-prod
emailTemplates.s3BucketName=g3-sfdc-email-templates-prod
oauth.client.id=1inhdo4ti68oivbdfmo3lhq6cq
oauth.client.secret=ep37oiuk8skrmuiibnkpf9495drsg0o4st36l88gbhk2dithhgj
oauth.client.url=https://fds.ideasrms.com/api/uis/internal_m2m/oauth2/token
pacman.fds.base.url=https://fds.ideasrms.com/api
pacman.fds.client.id=1inhdo4ti68oivbdfmo3lhq6cq
pacman.rdl.api.key=ElotrbivoE68cWvA9ZujP4KXrSE90Z1paj8qew0O
pacman.rdl.base.url=https://rdl.ideasrms.com
report.delivery.enabled.on.nonprod=true

