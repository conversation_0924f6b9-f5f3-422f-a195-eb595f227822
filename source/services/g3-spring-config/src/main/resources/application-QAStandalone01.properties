FPLOSQualifiedCdpRecommendationStep.chunkSize=45
FPLOSQualifiedRecommendationStep.chunkSize=45
JasperReportLocation=${base.g3.drive.path}/G3/JasperReports
LRAControlFPLOSDailyBARRecommendationBDEStep.chunkSize=20
LRAControlFPLOSDailyBARRecommendationCDPStep.chunkSize=20
LRAControlMinLOSDailyBARRecommendationBDEStep.chunkSize=20
LRAControlMinLOSDailyBARRecommendationCDPStep.chunkSize=20
RegulatorServiceDBServerThrottle=25
RegulatorServiceSASServerThrottle=40
RegulatorServiceThrottle.AddProperty=1
RegulatorServiceThrottle.BDEPostProcessingIndexRebuildJob=20
RegulatorServiceThrottle.BDEPostProcessingJob=100
RegulatorServiceThrottle.DataExtractionUtilityJob=1
RegulatorServiceThrottle.DeleteProperty=1
RegulatorServiceThrottle.Demand360DataLoadJob=10
RegulatorServiceThrottle.HtngDecisionDeliveryJob=50
RegulatorServiceThrottle.IDPScheduledReportJob=10
RegulatorServiceThrottle.LastGoodDecisionsDeliveryJob=100
RegulatorServiceThrottle.MassLastGoodDecisionsDeliveryJob=5
RegulatorServiceThrottle.NGICdpDeferredDeliveryJob=120
RegulatorServiceThrottle.NGIDeferredDeliveryJob=120
RegulatorServiceThrottle.NGIOperaDecisionDeliveryJob=50
RegulatorServiceThrottle.OnlineReportJob=10
RegulatorServiceThrottle.OperaCdpDataLoad=100
RegulatorServiceThrottle.OperaDataFeedJob=150
RegulatorServiceThrottle.OperaDataLoad=100
RegulatorServiceThrottle.OperaDecisionDeliveryJob=100
RegulatorServiceThrottle.ProcessWebRateExtract=10
RegulatorServiceThrottle.PurgeOldPropertyDataJob=25
RegulatorServiceThrottle.PurgeOldPropertySASDatasetsJob=50
RegulatorServiceThrottle.RefreshLearningDatabaseJob=4
RegulatorServiceThrottle.RevenueStreamsDataLoadJob=5
RegulatorServiceThrottle.ScheduledReportImmediateDeliveryJob=10
RegulatorServiceThrottle.ScheduledReportJob=10
RegulatorServiceThrottle.WebrateRatevalueDisplayBackfillJob=10
SasCatalogsBasePath=${base.sas.drive.path}/SAS/Deploy/Catalogs/
bookingSituationScheduledReportStep.timeout=1800
canary.server.url=
centralrms.common.alerts.base-uri=https://centralrms.dev.ideasrms.com/api/centralrms-common/G3/alerts/
centralrms.common.base-uri=https://centralrms.dev.ideasrms.com/api/centralrms-common/G3
centralrms.enabled=true
centralrms.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
centralrms.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
centralrms.oauth2.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.resourceserver.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.oauth2.resourceserver.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
centralrms.pricing.base-uri=https://pricing-config.dev.ideasrms.com/api/central-rms/G3/pricing-config/
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.client-id=iokjj0kfso4e2mj5nfjj4plj1
centralrms.security.oauth2.resourceserver.jwt.CENTRALRMS_COMMON.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.security.oauth2.resourceserver.jwt.PRICING.client-id=5842l5pfh16cbp4c00qmovskk1
centralrms.security.oauth2.resourceserver.jwt.PRICING.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_akYsPaFAQ
centralrms.fds.base-uri=https://fds.dev.ideasrms.com/api
well.known.uri=https://fds.dev.ideasrms.com/api/uis/login/.well-known/jwks.json
channel.srp.population.enabled=true
chaos.monkey.functionality.enabled=true
considerBDEDecisionsInProgressAsComplete=true
crsArchiveBucket=g3-stage-crs-data-archive-cluster-01
dataExtractionScheduledReportStep.timeout=1800
dbHost=
default-jndi-URL=jnp://:1099
deleteReportsTempFolderStep.chunkSize=5
demand360BookingDataLoadStep.batchSize=5000
demand360BookingDataLoadStep.chunkSize=5000
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.server.host=aspmailhost.ideasdev.int
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
environment.cluster=g3_dev_devstandalone
feature.monitoringdashboard.datadogLinkEnabled=false
feature.roiSimulator.externalUsersEnabled=false
feature.walkme.menu.enabled=true
fetch.salesforce.email.address.disabled=false
forecastValidationScheduledReportStep.timeout=1800
functionSpaceLoadBookingGuestRoomStep.batchSize=1000
functionSpaceLoadBookingGuestRoomStep.chunkSize=500
functionSpaceLoadBookingPaceStep.batchSize=1000
functionSpaceLoadBookingPaceStep.chunkSize=500
functionSpaceLoadBookingStep.batchSize=1000
functionSpaceLoadBookingStep.chunkSize=500
functionSpaceLoadEventStep.batchSize=1000
functionSpaceLoadEventStep.chunkSize=500
g3.link.client=http://${env.APP_HOST}/solutions/
gftServiceXmlMonitoringUrl=http://${env.APP_HOST}:31110/v5iservices-3/servlet/SimpleMonitor
inputOverrideScheduledReportStep.timeout=1800
jasper.internal.loadbalancer.url=http://localhost:8092/jasperserver-pro
jasper.server.url=http://${env.APP_HOST}/jasperserver-pro
jasper.server.version.5.enabled=true
jems.retry.autoRetryEnabled=false
jndi-URL=jnp://:1099
lms.hours.until.stale=8
lms.refresh.property.enabled=true
lms.single.signon.enabled=true
lms.snapshot.enabled=true
lms.snapshot.location=${base.g3.drive.path}/G3/Deploy/LMS/Snapshots
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:\Program Files\Microsoft SQL Server\MSSQL10_50.G3SQL01\MSSQL\DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
lmsSupplementalScriptLocation=${base.g3.drive.path}/G3/Deploy/LMS/Scripts
marketSegmentMappingScheduledReportStep.timeout=1800
module.dynamicPropertyGroup.group-pricing-evaluation=true
ngi.htng.cloud.migration.parallel.job.limit=2
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.stage.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.stage.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.stage.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.stage.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.stage.ideasrms.com
ngi.rest.url=http://mn4qg3xenvw001.ideasdev.int:9090
ngi.set.cloud.migration.stable.job.enabled=true
notificationStartStep.chunkSize=1
oauth.client.id=7ne6thmsh9hpbq7hdldi5p06bg
oauth.client.secret=2ds5or7av70mi08jboi1ba2agqjq0ohgog47mmkfus2539vh4ag
oauth.client.url=https://fds.stage.ideasrms.com/api/uis/internal_m2m/oauth2/token
onlineReportGenerationStep.timeout=1800
operationsScheduledReportStep.timeout=1800
org.xml.sax.driver=org.apache.xerces.parsers.SAXParser
outputOverrideScheduledReportStep.timeout=1800
pacman-jnp=jnp://:1099
pacman.base.db.server.url=jdbc:sqlserver://${env.APP_HOST}:1433
pacman.decision.decisionAckTableBatchingEnabled=true
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://${env.APP_HOST}/pacman-platformsecurity/rest
pacman.encryption.key=GaQn}?wF8,@#mZ8!^HnHj!:a5hea%Fa`$sdfd1232sdfsdfsd%^dfdfdfd1238c3dg`$sdfdfd88f8df8d8fdfjdjfdjf
pacman.exceed.lms.private.key=MIIEpAIBAAKCAQEArqfZ1SfuJiQAKlRdlhUuy4Uf46l8DBDVdIWNstsAvSd/oT1ljd/vIHTRmde59HvjCmRwlrS0NfAmWP+B7ZjJ+XkmgV1iKhCkZUWhAR74zC7MPhTwj6plUsDQvD7dKKGgj8w3HgdKO8mPjb6d/xeEej7K8oGmv+ktRK7Ok66n7L/VpfxI8eGdO9gE6Cw0GuZclTwHOP+7Onm3qquiU1pkefQR29yeGfg5kFB8PiufoFYRg0+TPSU+hsOPg+vM0D0Cq76SHNUf/AIsMdWPjk0fxlyklX8QObkRAqLhaIkb4py7lBRAfwCUrGdYtJ2b/1fiq5hLtsP84O+H6rNc1KmJPwIDAQABAoIBABiEl5/EqznPc/Z9QWYAM0F+vqtG75MCCfUBipLBC6mCEfigldEEzpzPyVy/W+tmOMkMsIF3fJTV0YxM0gB4omHTEAS5Bz7Ad1OAIsn1fxequiE870g7s12ak7LZYFKD3fZRxKsCevlJok1TpPpaYs6GYhcI/E+UfDZYE8fHjOs/Bz/aWxsmt/P/1yyjubve98B4YjVfUmCjMEcWGPQt7bbP+7SnZns6L90Wi3spPseAmyQpovtmm3RijD/6O09MYACbNLMqwAklS2DWpg1Q9w7RJZ0pmS6Q63ScSYd01po+a/jbOap7iBlGE/w1Og9nGT5VijvjdPiFWuY61zt0S4ECgYEA5SEILSrSzi/2ykMWxvZ/Z/ZnOu7Iz5h0JxMYtcd4Co6+jmd7dKR/dpBx04gvlOfy1SdiYdZsow5f+1/SXkx6Q/yDBxVnBU2CEqHbsa5sjRwfqZ5uFhdB+EbC8SHhrKrMeFYSVituTLaPq75sSsj+CypUWW/LCdiMBGtzmN36jLMCgYEAwyNoAVtDtAj923deQ7+KeN2aZmXGgpAO44/T7LSnFaspbCyc9Nmghc1hwnxrg8xwhMv9BaE5FZlkAZge58s6FSQDTEx0P4a6TCWDP0RNFZCh69/v2r4CnbMzPvmtRrqRUGVuemsF9z4EuAdMvw1b08rCmE23CIPfMF1Auf5gb0UCgYA1rURWGHckGHZvMcJPQAXe3AmY88GVaHilgsLCKwWDK6eWgsWYMMUviG0yCfBjqObpan2QwAPZOw/fOrZ0ouPts2UarrfgtSSoqsRl3p/Neu4r9VKC2FX7DNC0XqSYp7aY5kkLaMdBL29+43BJIMbq5LK39QmSt+ktS+Lbx/zfIwKBgQCMwz6kM9xrw0y0ArB5+HJ/64MUOHhw+E/1MWwzRMF+bQCtaqUyLsaPnm7Vp4C7Cz9jW+7CH+GYxhSQ3dX1aPEWDOtgH4p/xMUgZrL9cVyVoMZc6yYPSZ+DeKAP3nVzHuPx9akzH36nMhKfWYywCIfkCFBFI1JUDyRxs+McB/YybQKBgQCepZYJA+IdCdBPpjI+pNnPGt3ix8kXaUXTD59dqrVWiFqML5tp0HrevwuySOWSwqEDsqbxo0tf8kvZTs3tMtsiwidk+leHe41UKeXZsih5jfJFtsdu4uI/h1jk8eOnDzaZXYeJOyS4kZAbL9ROYdbDg7hDJ4MjsPDeEYoi7rfIKw==
pacman.fds.admin.url=https://admin.dev.ideasrms.com
pacman.fds.base.url=https://fds.stage.ideasrms.com/api
pacman.fds.client.id=7ne6thmsh9hpbq7hdldi5p06bg
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
fds.base.url=https://fds.dev.ideasrms.com/api
fds.oauth2.token-uri=https://fds.dev.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1m6gqjqnj6e2urbiot4dn0qvp7
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=7ne6thmsh9hpbq7hdldi5p06bg
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_KOdh0NbXw
pacman.feature.openam.disabled=true
pacman.g3.api.rest.url=https://${env.APP_HOST}/api
pacman.g3.aws.base.url=https://g3-api.dev.ideasrms.com
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.property.by.client.cache.enabled=true
pacman.rdl.api.key=lbF5yG0ugD8SzxgE13mUo6s5iw6LB8V83pKp9ycQ
pacman.rdl.base.url=https://rdl.stage.ideasrms.com
pacman.server.name=
pendo.prefix=G3DevStandalone1
performanceComparisonScheduledReportStep.timeout=1800
pickupChangeScheduledReportStep.timeout=1800
platform-jnp=jnp://:1099
platform.jboss.as7.management.host=
platform.jboss.as7.management.port=9999
platform.jboss.as7.management.profile=services-profile
platform.jobweb.url=http://${env.APP_HOST}/job-web
platform.security.ssoCookieDomain=${env.OPENAM_COOKIE_DOMAIN}
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.server.name=
pricingOverrideHistoryScheduledReportStep.timeout=1800
pricingScheduledReportStep.timeout=1800
purge.purgeOperaHistoryJob=true
purge.purgeOperaHistoryJobDaysToDelete=7
qualifiedDeferredDeliveryRateLoadStep.batchSize=1
qualifiedDeferredDeliveryRateLoadStep.chunkSize=1
ratePlanProductionScheduledReportStep.timeout=1800
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.local.instance.enabled=${env.REDIS_LOCAL_ENABLED}
report.scheduler.mail.sender.host=aspmailhost.ideasdev.int
resilience.enabled.apis=groups
revenueStreamsDetailsDataLoadStep.batchSize=1
roomTypeCurrentActivityDataLoadStep.batchSize=500
roomTypeCurrentActivityDataLoadStep.chunkSize=250
roomTypeHotelMarketSegmentCurrentActivityDataLoadStep.batchSize=500
roomTypeHotelMarketSegmentCurrentActivityDataLoadStep.chunkSize=250
roomTypeHotelMarketSegmentCurrentActivityDataLoadWithClientAPIStep.batchSize=500
roomTypeHotelMarketSegmentCurrentActivityDataLoadWithClientAPIStep.chunkSize=250
roomTypeHotelMarketSegmentPastActivityDataLoadStep.batchSize=500
roomTypeHotelMarketSegmentPastActivityDataLoadStep.chunkSize=250
roomTypeHotelMarketSegmentPastActivityDataLoadWithClientAPIStep.batchSize=500
roomTypeHotelMarketSegmentPastActivityDataLoadWithClientAPIStep.chunkSize=250
roomTypeMarketSegmentCurrentActivityDataLoadStep.batchSize=500
roomTypeMarketSegmentCurrentActivityDataLoadStep.chunkSize=250
roomTypeMarketSegmentCurrentActivityOnlyProvidedDataLoadStep.batchSize=500
roomTypeMarketSegmentCurrentActivityOnlyProvidedDataLoadStep.chunkSize=250
roomTypeMarketSegmentCurrentActivityOnlyProvidedDataLoadWithClientAPIStep.batchSize=500
roomTypeMarketSegmentCurrentActivityOnlyProvidedDataLoadWithClientAPIStep.chunkSize=250
roomTypeMarketSegmentPastActivityDataLoadStep.batchSize=500
roomTypeMarketSegmentPastActivityDataLoadStep.chunkSize=250
roomTypeMarketSegmentPastActivityOnlyProvidedDataLoadStep.batchSize=500
roomTypeMarketSegmentPastActivityOnlyProvidedDataLoadStep.chunkSize=250
roomTypeMarketSegmentPastActivityOnlyProvidedDataLoadWithClientAPIStep.batchSize=500
roomTypeMarketSegmentPastActivityOnlyProvidedDataLoadWithClientAPIStep.chunkSize=250
roomTypeMarketSegmentZeroFillingStep.chunkSize=30
roomTypePastActivityDataLoadStep.batchSize=500
roomTypePastActivityDataLoadStep.chunkSize=250
s3.access.key=${env.S3_ACCESS_KEY}
s3.region=us-east-2
s3.secret.key=${env.S3_SECRET_KEY}
saleDemoUsersLocation=${base.g3.drive.path}/G3/Deploy/LMS/Sales
salesforce.capacity.synch.enabled=true
salesforce.rest.client.id=3MVG9pHRjzOBdkd_Ob2ZvV.Ohgc13Fey6pu5QFrnY9KlwmzXgHxz05DBs1I5R6yR_j1xBDFCVQNfux8OrqD1L
salesforce.rest.client.secret=8344249421880090910
salesforce.rest.password=IDeaS345
salesforce.rest.username=<EMAIL>
salesforce.stage.synch.enabled=true
salesforce.stage.synch.version=2
sasDomainNodes=
scheduledReportImmediateDeliveryStep.timeout=1800
sendWebRatesToNGI=true
ssoLoginRedirect=http://${env.APP_HOST}/solutions/c/portal/login
ssoLogoutRedirect=http://${env.APP_HOST}/solutions/web/guest/home
tetris.rpt.http.url=http://${env.APP_HOST}/jasperserver-pro
tetris.rpt.password=superuser
tetris.rpt.userName=superuser
totalHotelCurrentActivityDataLoadStep.batchSize=500
totalHotelCurrentActivityDataLoadStep.chunkSize=500
totalHotelPastActivityDataLoadStep.batchSize=500
totalHotelPastActivityDataLoadStep.chunkSize=500
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.shell=true
vendors.not.providing.merged.rss.extracts=otainsight,Rubicon
g3.springdoc.enabled=true
pacman.fplos.serviceUrl=https://g3-recommendation-internal.stage.ideasrms.com/api/v1/broker
pacman.fds.universal.redirect.uri=http://${env.APP_HOST}/solutions/universallogin/callback
awssqs.enabled=${env.SQS_ENABLED:false}
dbInstance=
pacman.base.db.instance=
sqs.name=${env.SQS_NAME:sqs-sns-event-bridge-1}
htng.param.migration.backward.compatibility.enabled=
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.dev.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.dev.ideasrms.com
pacman.feature.global.universal.login.help=true