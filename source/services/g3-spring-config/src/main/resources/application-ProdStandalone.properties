SasCatalogsBasePath=${base.sas.drive.path}/SAS/Deploy/Catalogs/
central.rms.data.sync.enabled=true
considerBDEDecisionsInProgressAsComplete=true
dbHost=${env.APP_HOST}
default-jndi-URL=jnp://${env.APP_HOST}:1099
edit.config.parameter.enabled=true
email.componentRoom.orphanMapping.change.body=Hi All,&lt;br>&lt;br>The Component Rooms Configuration wizard was revisited by user for&lt;br>Client: {0}&lt;br>Property: {1}&lt;br>&lt;br>Please review the corresponding mapping of Orphan Room Types:&lt;br>&lt;br>{2}&lt;br>Please get in touch with the techinal team in case of any issues.&lt;br>&lt;br>Regards,&lt;br>G3RMS
email.discover.user.create.body=Hello {0},&lt;br>Welcome to IDeaS Discover, the website that provides the learning materials and instructions on how to use your IDeaS product. Discover includes training on revenue management concepts, configuration, and system use for new users and those who need to refresh their skills.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
email.sas.datasets.body.success=Your data extraction for property {0} (id:{1}) are available.&lt;br>&lt;br>FTP: {2}&lt;br>Username: {3}&lt;br>Password: Please refer to process document&lt;br>Folder: &lt;a href='#' style='color:#000000; text-decoration:none; cursor:text; !important;'>&lt;span style='text-decoration:none;'>{4}&lt;/span>&lt;/a>&lt;br>File: {5}&lt;br>&lt;br>Please note that your requested files will be automatically deleted in 72 hours.
email.user.create.body=Hello {0},&lt;br>Welcome to the IDeaS SAS revenue management system.&lt;br>Your user id is: {1}&lt;br>Your account password is: {2}&lt;br>&lt;a href=&quot;{3}&quot;>You can log in here.&lt;/a>
feature.roiSimulator.externalUsersEnabled=false
feature.walkme.menu.enabled=true
g3.internal.loadbalancer.url=http://localhost:8080
g3.link.client=http://${env.APP_HOST}/solutions/
g3.restClient.base64EncodedBasicAuth=************************************
gftServiceXmlMonitoringUrl=http://${env.APP_HOST}:31110/v5iservices-3/servlet/SimpleMonitor
jndi-URL=jnp://${env.APP_HOST}:1099
is.CpDecisionBARNOVRDetails.batchDelete.enabled=true
lms.hours.until.stale=8
lms.refresh.property.enabled=true
lms.single.signon.enabled=true
lms.snapshot.enabled=true
lms.snapshot.location=${base.g3.drive.path}/G3/Deploy/LMS/Snapshots
lmsSasDestination=${base.sas.drive.path}/SAS/Data/Properties
lmsSasSource=${base.g3.drive.path}/G3/Deploy/LMS/SAS
lmsSqlDestination=C:\Program Files\Microsoft SQL Server\MSSQL10_50.G3SQL01\MSSQL\DATA
lmsSqlSource=${base.g3.drive.path}/G3/Deploy/LMS/SQL
lmsSupplementalScriptLocation=${base.g3.drive.path}/G3/Deploy/LMS/Scripts
log4j2.formatMsgNoLookups=true
module.dynamicPropertyGroup.group-pricing-evaluation=true
ngi.htng.cloud.migration.parallel.job.limit=5
ngi.oxi.cloud.migration.parallel.job.limit=40
ngi.rest.cloud.pmsInbound.url=https://pmsinbound-internal.ideasrms.com
ngi.rest.cloud.fols.url=https://fols-internal.ideasrms.com
ngi.rest.cloud.tars.url=https://tars-internal.ideasrms.com
ngi.rest.cloud.htng.url=https://htng-internal.ideasrms.com
ngi.rest.cloud.oxi.url=https://oxi-internal.ideasrms.com
ngi.set.cloud.migration.stable.job.enabled=true
pacman-jnp=jnp://${env.APP_HOST}:1099
pacman.base.db.server.url=jdbc:sqlserver://${env.APP_HOST}:1433
pacman.ejb.internal.url=http://${env.APP_HOST}:8080/pacman-platformsecurity
pacman.ejb.rest.url=http://${env.APP_HOST}/pacman-platformsecurity/rest
pacman.exceed.lms.private.key=${env.EXCEED_LMS_PRIVATE_KEY}
pacman.fds.client.secret=${env.FDS_CLIENT_SECRET}
pacman.fds.admin.url=https://admin.ideasrms.com
fds.base.url=https://fds.ideasrms.com/api
fds.oauth2.token-uri=https://fds.ideasrms.com/api/uis/internal_m2m/oauth2/token
fds.oauth2.client-id=1inhdo4ti68oivbdfmo3lhq6cq
fds.oauth2.client-secret=${env.OAUTH2_CLIENT_SECRET}
fds.security.oauth2.resourceserver.jwt.FDS_G3.client-id=1inhdo4ti68oivbdfmo3lhq6cq
fds.security.oauth2.resourceserver.jwt.FDS_G3.issuer-uri=https://cognito-idp.us-east-2.amazonaws.com/us-east-2_9VL8MDrxL
well.known.uri=https://fds.ideasrms.com/api/uis/login/.well-known/jwks.json
pacman.integration.opera.agentCustomWarLocation=${base.g3.drive.path}/G3/Deploy/WebApps/custom/opera-agent.war
pacman.server.name=${env.APP_HOST}
pacman.user.management.allowed.to.self.modify=false
platform-jnp=jnp://${env.APP_HOST}:1099
platform.jboss.as7.management.host=${env.APP_HOST}
platform.jboss.as7.management.port=9999
platform.jboss.as7.management.profile=services-profile
platform.jobweb.url=http://${env.APP_HOST}/job-web
platform.security.noSSO=false
platform.security.ssoURL=http://${env.APP_HOST}:8091/sso
platform.server.name=${env.APP_HOST}
purge.purgeOperaHistoryJob=true
purge.purgeOperaHistoryJobDaysToDelete=7
redis.cache.isEnabled=${env.REDIS_CACHE_IS_ENABLED}
redis.local.instance.enabled=${env.REDIS_LOCAL_ENABLED}
saleDemoUsersLocation=${base.g3.drive.path}/G3/Deploy/LMS/Sales
sasDomainNodes=${env.APP_HOST}
sendWebRatesToNGI=true
sihot.extractHealth.baseUrl=https://data-admin.sis.ideasrms.com
ssoLoginRedirect=http://${env.APP_HOST}/solutions/c/portal/login
ssoLogoutRedirect=http://${env.APP_HOST}/solutions/web/guest/home
tetris.rpt.password=superuser
vaadin.internal.url=http://${env.APP_HOST}:8080/vaadin
vaadin.shell=true
extractFtpFolder.linux=/var/opt/mssql/backup/ftp
pacman.fplos.serviceUrl=https://g3-recommendation-internal.ideasrms.com/api/v1/broker
universal.login.url=https://id.ideasrms.com/login
pacman.fds.universal.client.id=fa845f01-6f4e-41a8-aec9-369afe66388a
pacman.fds.universal.client.secret=5ae4743455da68e09c165193e1a8255af7e98433485a908e21b8ba7322d19f50
universal.login.account.settings.url=https://id.ideasrms.com/userprofile
pacman.fds.universal.redirect.uri=https://${env.APP_HOST}/solutions/universallogin/callback
universal.login.logout.url=https://id.ideasrms.com/api/uis/signout
fds.oauth2.authorize.url=https://id.ideasrms.com/api/uis/login/oauth2/authorize
pacman.fds.g3.environment.id=f8151b26-30cf-4eec-9b8f-5f06b3279294
pacman.fds.g3.environment.ids=f8151b26-30cf-4eec-9b8f-5f06b3279294
pacman.fds.uad.environment.id=8227a8ca-c3df-44be-873f-6b06dc3ab517
pacman.fds.navigator.environment.id=59fb3f06-8b1c-4b3a-a584-5b5a977e09b5
pacman.fds.optix.environment.id=09058b24-e23b-46df-902d-573f9ae26d19
pacman.fds.specialevents.environment.id=660bd982-2a7d-4984-a848-4fe7c911c5bf
groups.meetings.events.oneui.redirect.uri=https://groups-meetings-events.ideasrms.com/evaluation/overview
meeting.package.pricing.oneui.redirect.uri=https://meeting-package-pricing.ideasrms.com
oauth.client.id=1inhdo4ti68oivbdfmo3lhq6cq
oauth.client.secret=ep37oiuk8skrmuiibnkpf9495drsg0o4st36l88gbhk2dithhgj
oauth.client.url=https://fds.ideasrms.com/api/uis/internal_m2m/oauth2/token
pacman.fds.base.url=https://fds.ideasrms.com/api
pacman.fds.client.id=1inhdo4ti68oivbdfmo3lhq6cq
pacman.rdl.api.key=ElotrbivoE68cWvA9ZujP4KXrSE90Z1paj8qew0O
pacman.rdl.base.url=https://rdl.ideasrms.com
report.delivery.enabled.on.nonprod=true