<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Change Report - TotalHotel"   pageWidth="500" pageHeight="700" whenNoDataType="NoDataSection" columnWidth="480" leftMargin="10" rightMargin="10" topMargin="5" bottomMargin="5">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.jasperserver.url" value="http://localhost:8280/jasperserver-pro/services/repository"/>
	<template>"jrxmls/customStyles.jrtx"</template>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 7_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#EFF7FF"/>
		</conditionalStyle>
	</style>
	<style name="table 5">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 5_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 5_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 5_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="New Dataset 1">
		<parameter name="param_EndDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_StartDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_Property_ID" class="java.lang.Integer"/>
		<parameter name="param_Business_StartDate" class="java.util.Date"/>
		<parameter name="param_IsOcFcstChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsOOOChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsSpecialEventChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsRoomRevenueChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsADRChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsRevParChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsProfitChecked" class="java.lang.Boolean">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="param_IsProPORChecked" class="java.lang.Boolean">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
        <parameter name="param_IsProPARChecked" class="java.lang.Boolean">
            <defaultValueExpression><![CDATA[]]></defaultValueExpression>
        </parameter>
		<parameter name="param_BaseCurrency" class="java.lang.String">
			<defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsLastRoomValueChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsOvbkChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_IsBARChecked" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="param_isBARByDay" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp1" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp2" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp3" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp4" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp5" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp6" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp7" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp8" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp9" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp10" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp11" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp12" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp13" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp14" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="Comp15" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
		</parameter>
		<parameter name="JNDI_NAME" class="java.lang.String"/>
		<parameter name="param_isRollingDate" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
		</parameter>
		<parameter name="param_RollingBusinessStartDate" class="java.lang.String"/>
		<parameter name="param_Rolling_Start_Date" class="java.lang.String"/>
		<parameter name="param_Rolling_End_Date" class="java.lang.String"/>
		<parameter name="maxLOS" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[7]]></defaultValueExpression>
		</parameter>
		<parameter name="isPhysicalCapacity" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
		</parameter>
		<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="number_formatter" class="java.text.NumberFormat" isForPrompting="false">
			<defaultValueExpression><![CDATA[$P{REPORT_FORMAT_FACTORY}.createNumberFormat( "###0.00", $P{REPORT_LOCALE})]]></defaultValueExpression>
		</parameter>
		<parameter name="isRestrictHighestBarEnabled" class="java.lang.String">
			<defaultValueExpression><![CDATA["false"]]></defaultValueExpression>
		</parameter>
        <parameter name="isReferDifferentialTableForPaceOvrbkAccomEnabled" class="java.lang.Integer">
            <defaultValueExpression><![CDATA[0]]></defaultValueExpression>
        </parameter>
		<parameter name="isUseCompactWebratePaceEnabled" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
		</parameter>
        <queryString>
            <![CDATA[if($P{isReferDifferentialTableForPaceOvrbkAccomEnabled}=1)
                begin
                    exec dbo.usp_hotel_differential_control_report_property $P{param_Property_ID},3,13,$P{param_Business_StartDate},$P{param_StartDate},$P{param_EndDate},$P{Comp1},$P{Comp2},$P{Comp3},$P{Comp4},$P{Comp5},$P{Comp6},$P{Comp7},$P{Comp8},$P{Comp9},$P{Comp10},$P{Comp11},$P{Comp12},$P{Comp13},$P{Comp14},$P{Comp15},$P{param_isRollingDate},$P{param_RollingBusinessStartDate},$P{param_Rolling_Start_Date},$P{param_Rolling_End_Date},$P{isRestrictHighestBarEnabled}, $P{isPhysicalCapacity}, true
                end
                else
                begin
                    exec dbo.usp_hotel_differential_control_report_property $P{param_Property_ID},3,13,$P{param_Business_StartDate},$P{param_StartDate},$P{param_EndDate},$P{Comp1},$P{Comp2},$P{Comp3},$P{Comp4},$P{Comp5},$P{Comp6},$P{Comp7},$P{Comp8},$P{Comp9},$P{Comp10},$P{Comp11},$P{Comp12},$P{Comp13},$P{Comp14},$P{Comp15},$P{param_isRollingDate},$P{param_RollingBusinessStartDate},$P{param_Rolling_Start_Date},$P{param_Rolling_End_Date},$P{isRestrictHighestBarEnabled}, $P{isPhysicalCapacity}, true
            end]]>
        </queryString>

		<field name="dayofarrival" class="java.util.Date"/>
		<field name="dow" class="java.lang.String"/>
		<field name="ooo" class="java.math.BigDecimal"/>
		<field name="specialevent" class="java.lang.String"/>
		<field name="property_id" class="java.lang.Integer"/>
		<field name="roomssoldcurrent" class="java.math.BigDecimal"/>
		<field name="roomssoldchange" class="java.math.BigDecimal"/>
		<field name="occupancyforecastcurrent" class="java.math.BigDecimal"/>
		<field name="occupancyforecastchange" class="java.math.BigDecimal"/>
		<field name="occupancyforecastpercurrent" class="java.math.BigDecimal"/>
		<field name="occupancyforecastperchange" class="java.math.BigDecimal"/>
		<field name="bookedroomrevenuecurrent" class="java.math.BigDecimal"/>
		<field name="bookedroomrevenuechange" class="java.math.BigDecimal"/>
		<field name="fcstedroomrevenuecurrent" class="java.math.BigDecimal"/>
		<field name="fcstedroomrevenuechange" class="java.math.BigDecimal"/>
		<field name="bookedadrcurrent" class="java.math.BigDecimal"/>
		<field name="bookedadrchange" class="java.math.BigDecimal"/>
		<field name="estimatedadrcurrent" class="java.math.BigDecimal"/>
		<field name="estimatedadrchange" class="java.math.BigDecimal"/>
		<field name="bookedrevparcurrent" class="java.math.BigDecimal"/>
		<field name="bookedrevparchange" class="java.math.BigDecimal"/>
		<field name="estimatedrevparcurrent" class="java.math.BigDecimal"/>
		<field name="estimatedrevparchange" class="java.math.BigDecimal"/>
		<field name="master_class_name" class="java.lang.String"/>
		<field name="lrv" class="java.math.BigDecimal"/>
		<field name="lrv_change" class="java.math.BigDecimal"/>
		<field name="overbookingcurrent" class="java.math.BigDecimal"/>
		<field name="overbookingchange" class="java.math.BigDecimal"/>
		<field name="bar_los1" class="java.lang.Double"/>
		<field name="bar_los2" class="java.lang.Double"/>
		<field name="bar_los3" class="java.lang.Double"/>
		<field name="bar_los4" class="java.lang.Double"/>
		<field name="bar_los5" class="java.lang.Double"/>
		<field name="bar_los6" class="java.lang.Double"/>
		<field name="bar_los7" class="java.lang.Double"/>
		<field name="bar_los8" class="java.lang.Double"/>
		<field name="bar_by_day" class="java.lang.Double"/>
		<field name="ratecode_los1" class="java.lang.String"/>
		<field name="ratecode_los2" class="java.lang.String"/>
		<field name="ratecode_los3" class="java.lang.String"/>
		<field name="ratecode_los4" class="java.lang.String"/>
		<field name="ratecode_los5" class="java.lang.String"/>
		<field name="ratecode_los6" class="java.lang.String"/>
		<field name="ratecode_los7" class="java.lang.String"/>
		<field name="ratecode_los8" class="java.lang.String"/>
		<field name="ratecode_los_all" class="java.lang.String"/>
		<field name="bar_los1_change" class="java.math.BigDecimal"/>
		<field name="bar_los2_change" class="java.math.BigDecimal"/>
		<field name="bar_los3_change" class="java.math.BigDecimal"/>
		<field name="bar_los4_change" class="java.math.BigDecimal"/>
		<field name="bar_los5_change" class="java.math.BigDecimal"/>
		<field name="bar_los6_change" class="java.math.BigDecimal"/>
		<field name="bar_los7_change" class="java.math.BigDecimal"/>
		<field name="bar_los8_change" class="java.math.BigDecimal"/>
		<field name="bar_by_day_change" class="java.math.BigDecimal"/>
		<field name="comp1_rate" class="java.math.BigDecimal"/>
		<field name="comp1_name" class="java.lang.String"/>
		<field name="comp2_rate" class="java.math.BigDecimal"/>
		<field name="comp2_name" class="java.lang.String"/>
		<field name="comp3_rate" class="java.math.BigDecimal"/>
		<field name="comp3_name" class="java.lang.String"/>
		<field name="comp4_rate" class="java.math.BigDecimal"/>
		<field name="comp4_name" class="java.lang.String"/>
		<field name="comp5_rate" class="java.math.BigDecimal"/>
		<field name="comp5_name" class="java.lang.String"/>
		<field name="comp6_rate" class="java.math.BigDecimal"/>
		<field name="comp6_name" class="java.lang.String"/>
		<field name="comp7_rate" class="java.math.BigDecimal"/>
		<field name="comp7_name" class="java.lang.String"/>
		<field name="comp8_rate" class="java.math.BigDecimal"/>
		<field name="comp8_name" class="java.lang.String"/>
		<field name="comp9_rate" class="java.math.BigDecimal"/>
		<field name="comp9_name" class="java.lang.String"/>
		<field name="comp10_rate" class="java.math.BigDecimal"/>
		<field name="comp10_name" class="java.lang.String"/>
		<field name="comp11_rate" class="java.math.BigDecimal"/>
		<field name="comp11_name" class="java.lang.String"/>
		<field name="comp12_rate" class="java.math.BigDecimal"/>
		<field name="comp12_name" class="java.lang.String"/>
		<field name="comp13_rate" class="java.math.BigDecimal"/>
		<field name="comp13_name" class="java.lang.String"/>
		<field name="comp14_rate" class="java.math.BigDecimal"/>
		<field name="comp14_name" class="java.lang.String"/>
		<field name="comp15_rate" class="java.math.BigDecimal"/>
		<field name="comp15_name" class="java.lang.String"/>
		<field name="comp1_change" class="java.math.BigDecimal"/>
		<field name="comp2_change" class="java.math.BigDecimal"/>
		<field name="comp3_change" class="java.math.BigDecimal"/>
		<field name="comp4_change" class="java.math.BigDecimal"/>
		<field name="comp5_change" class="java.math.BigDecimal"/>
		<field name="comp6_change" class="java.math.BigDecimal"/>
		<field name="comp7_change" class="java.math.BigDecimal"/>
		<field name="comp8_change" class="java.math.BigDecimal"/>
		<field name="comp9_change" class="java.math.BigDecimal"/>
		<field name="comp10_change" class="java.math.BigDecimal"/>
		<field name="comp11_change" class="java.math.BigDecimal"/>
		<field name="comp12_change" class="java.math.BigDecimal"/>
		<field name="comp13_change" class="java.math.BigDecimal"/>
		<field name="comp14_change" class="java.math.BigDecimal"/>
		<field name="comp15_change" class="java.math.BigDecimal"/>
		<field name="occupancyforecastpercurrent_without_ooo" class="java.math.BigDecimal"/>
		<field name="occupancyforecastperchange_without_ooo" class="java.math.BigDecimal"/>
		<field name="decisionreasontypecurrent" class="java.lang.String"/>
		<field name="decisionreasontypechange" class="java.lang.String"/>
		<field name="decisionreasontypecurrent_los1" class="java.lang.String"/>
		<field name="decisionreasontypechange_los1" class="java.lang.String"/>
		<field name="decisionreasontypecurrent_los2" class="java.lang.String"/>
		<field name="decisionreasontypechange_los2" class="java.lang.String"/>
		<field name="decisionreasontypecurrent_los3" class="java.lang.String"/>
		<field name="decisionreasontypechange_los3" class="java.lang.String"/>
		<field name="decisionreasontypecurrent_los4" class="java.lang.String"/>
		<field name="decisionreasontypechange_los4" class="java.lang.String"/>
		<field name="decisionreasontypecurrent_los5" class="java.lang.String"/>
		<field name="decisionreasontypechange_los5" class="java.lang.String"/>
		<field name="decisionreasontypecurrent_los6" class="java.lang.String"/>
		<field name="decisionreasontypechange_los6" class="java.lang.String"/>
		<field name="decisionreasontypecurrent_los7" class="java.lang.String"/>
		<field name="decisionreasontypechange_los7" class="java.lang.String"/>
		<field name="decisionreasontypecurrent_los8" class="java.lang.String"/>
		<field name="decisionreasontypechange_los8" class="java.lang.String"/>
		<field name="profitcurrent" class="java.math.BigDecimal"/>
		<field name="profitchange" class="java.math.BigDecimal"/>
        <field name="fcstedprofitcurrent" class="java.math.BigDecimal"/>
        <field name="fcstedprofitchange" class="java.math.BigDecimal"/>
        <field name="proporcurrent" class="java.math.BigDecimal"/>
        <field name="proporchange" class="java.math.BigDecimal"/>
        <field name="estimatedproporcurrent" class="java.math.BigDecimal"/>
        <field name="estimatedproporchange" class="java.math.BigDecimal"/>
        <field name="proparcurrent" class="java.math.BigDecimal"/>
        <field name="proparchange" class="java.math.BigDecimal"/>
        <field name="estimatedproparcurrent" class="java.math.BigDecimal"/>
        <field name="estimatedproparchange" class="java.math.BigDecimal"/>
	</subDataset>
	<subDataset name="ExcelHeader">
		<parameter name="param_BaseCurrency" class="java.lang.String">
			<defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
		</parameter>
		<parameter name="param_ComparisonEndDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_AnalysisEndDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_AnalysisStartDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_Property_ID" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[5]]></defaultValueExpression>
		</parameter>
		<parameter name="param_ComparisonStartDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_isRollingDate" class="java.lang.Integer">
			<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
		</parameter>
		<parameter name="param_RollingAnalysisStartDate" class="java.lang.String"/>
		<parameter name="param_RollingAnalysisEndDate" class="java.lang.String">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<parameter name="param_RollingComparisionStartDate" class="java.lang.String"/>
		<parameter name="param_RollingComparisionEndDate" class="java.lang.String"/>
		<parameter name="param_User_ID" class="java.lang.Integer"/>
		<parameter name="JNDI_NAME" class="java.lang.String"/>
		<parameter name="Jasper_custom_formatter" class="com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter" isForPrompting="false">
			<defaultValueExpression><![CDATA[new com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter()]]></defaultValueExpression>
		</parameter>
		<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<queryString>
			<![CDATA[select * from dbo.ufn_get_filter_selection
(
$P{param_Property_ID},
$P{param_User_ID},
$P{param_BaseCurrency},
$P{param_isRollingDate},
'',
'',
$P{param_AnalysisStartDate},
$P{param_AnalysisEndDate},
'',
$P{param_ComparisonStartDate},
$P{param_ComparisonEndDate},
'',
'',
'',
$P{param_RollingAnalysisStartDate},
$P{param_RollingAnalysisEndDate},
'',
$P{param_RollingComparisionStartDate},
$P{param_RollingComparisionEndDate},
''
)]]>
		</queryString>
		<field name="property_name" class="java.lang.String"/>
		<field name="created_by" class="java.lang.String"/>
		<field name="genration_date" class="java.lang.String"/>
		<field name="start_date" class="java.lang.String"/>
		<field name="end_date" class="java.lang.String"/>
		<field name="analysis_start_date" class="java.util.Date"/>
		<field name="analysis_end_date" class="java.util.Date"/>
		<field name="analysis_business_dt" class="java.lang.String"/>
		<field name="comparision_start_date" class="java.util.Date"/>
		<field name="comparision_end_date" class="java.lang.String"/>
		<field name="comparision_business_dt" class="java.lang.String"/>
		<field name="param_BaseCurrency" class="java.lang.String"/>
	</subDataset>
	<parameter name="param_Property_ID" class="java.lang.Integer"/>
	<parameter name="param_StartDate" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_EndDate" class="java.util.Date">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_Business_StartDate" class="java.util.Date"/>
	<parameter name="param_IsOcFcstChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsOOOChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsSpecialEventChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsRoomRevenueChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsADRChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsRevParChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsProfitChecked" class="java.lang.String">
        <defaultValueExpression><![CDATA[]]></defaultValueExpression>
    </parameter>
    <parameter name="param_IsProPORChecked" class="java.lang.String">
        <defaultValueExpression><![CDATA[]]></defaultValueExpression>
    </parameter>
    <parameter name="param_IsProPARChecked" class="java.lang.String">
        <defaultValueExpression><![CDATA[]]></defaultValueExpression>
    </parameter>
	<parameter name="param_BaseCurrency" class="java.lang.String">
		<defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsLastRoomValueChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsOvbkChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_IsBARChecked" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="param_isBARByDay" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp1" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp2" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp3" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp4" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp5" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp6" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp7" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp8" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp9" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp10" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp11" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp12" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp13" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp14" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="Comp15" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[-1]]></defaultValueExpression>
	</parameter>
	<parameter name="JNDI_NAME" class="java.lang.String"/>
	<parameter name="param_isRollingDate" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
	</parameter>
    <parameter name="isReferDifferentialTableForPaceOvrbkAccomEnabled" class="java.lang.Integer">
        <defaultValueExpression><![CDATA[0]]></defaultValueExpression>
    </parameter>
	<parameter name="param_RollingBusinessStartDate" class="java.lang.String"/>
	<parameter name="param_Rolling_Start_Date" class="java.lang.String"/>
	<parameter name="param_Rolling_End_Date" class="java.lang.String"/>
	<parameter name="maxLOS" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[7]]></defaultValueExpression>
	</parameter>
	<parameter name="param_User_ID" class="java.lang.Integer"/>
	<parameter name="Jasper_custom_formatter" class="com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="isPhysicalCapacity" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
	</parameter>
	<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["yyyy-MM-dd"]]></defaultValueExpression>
	</parameter>
	<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["en_US"]]></defaultValueExpression>
	</parameter>
	<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{REPORT_FORMAT_FACTORY}.createDateFormat( $P{userDateFormat}, $P{REPORT_LOCALE}, null )]]></defaultValueExpression>
	</parameter>
	<parameter name="param_SheetForCriteria" class="java.lang.Boolean" isForPrompting="false">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<parameter name="isRestrictHighestBarEnabled" class="java.lang.String">
		<defaultValueExpression><![CDATA["false"]]></defaultValueExpression>
	</parameter>
	<parameter name="isUseCompactWebratePaceEnabled" class="java.lang.Boolean">
			<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT GETDATE() as date]]>
	</queryString>
	<field name="date" class="java.sql.Timestamp"/>
	<title>
		<band height="30">
			<printWhenExpression><![CDATA[$P{IS_IGNORE_PAGINATION} && $P{param_SheetForCriteria}.toString().equalsIgnoreCase("true")?false:true]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="480" height="30">
					<propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{change} +$R{common.report}+ " - " + $R{Hotel}]]></propertyExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="15" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["   "+$R{change.and.differential.control.report.hotel}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="50">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria})==false)?true:false]]></printWhenExpression>
			<componentElement>
				<reportElement key="table 3" style="table 3" stretchType="RelativeToBandHeight" x="0" y="0" width="360" height="50"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="ExcelHeader">
						<datasetParameter name="param_Property_ID">
							<datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_User_ID">
							<datasetParameterExpression><![CDATA[$P{param_User_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_BaseCurrency">
							<datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="JNDI_NAME">
							<datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_AnalysisStartDate">
							<datasetParameterExpression><![CDATA[$P{param_StartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_AnalysisEndDate">
							<datasetParameterExpression><![CDATA[$P{param_EndDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_ComparisonStartDate">
							<datasetParameterExpression><![CDATA[$P{param_Business_StartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_isRollingDate">
							<datasetParameterExpression><![CDATA[$P{param_isRollingDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_RollingAnalysisStartDate">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_Start_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_RollingAnalysisEndDate">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_End_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_RollingComparisionStartDate">
							<datasetParameterExpression><![CDATA[$P{param_RollingBusinessStartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Jasper_custom_formatter">
							<datasetParameterExpression><![CDATA[$P{Jasper_custom_formatter}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
							<datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userLocale">
							<datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="dateFormatter">
							<datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:columnGroup width="630">
						<jr:tableHeader height="30" rowSpan="1">
							<textField>
								<reportElement x="0" y="0" width="630" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="11" isBold="true"/>
								</textElement>
								<textFieldExpression class="java.lang.String"><![CDATA[$R{report.ReportCriteria}]]></textFieldExpression>
							</textField>
						</jr:tableHeader>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{common.propertyName}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.lang.String"><![CDATA[$F{property_name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{analysisStartDateLabel}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.util.Date"><![CDATA[$F{analysis_start_date}]]></textFieldExpression>
										<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{analysis.end.date}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.util.Date"><![CDATA[$F{analysis_end_date}]]></textFieldExpression>
										<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{activity.start.date}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.util.Date"><![CDATA[$F{comparision_start_date}]]></textFieldExpression>
										<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{currency}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.lang.String"><![CDATA[$F{param_BaseCurrency}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{createdBy}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.lang.String"><![CDATA[$F{created_by}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.GeneratedOn}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.lang.String"><![CDATA[$P{Jasper_custom_formatter}.getDateInPropertyTimeZone($F{genration_date}, $P{REPORT_TIME_ZONE}, $P{PROPERTY_TIME_ZONE},$P{userDateFormat}+" HH:mm:ss", $P{REPORT_LOCALE})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="20">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria})==false)?true:false]]></printWhenExpression>
			<staticText>
				<reportElement x="0" y="0" width="90" height="20"/>
				<textElement/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="250" splitType="Stretch">
			<componentElement>
				<reportElement key="table 5" style="table 4" stretchType="RelativeToBandHeight" x="0" y="0" width="480" height="250"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="New Dataset 1">
						<datasetParameter name="param_EndDate">
							<datasetParameterExpression><![CDATA[$P{param_EndDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_StartDate">
							<datasetParameterExpression><![CDATA[$P{param_StartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Property_ID">
							<datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Business_StartDate">
							<datasetParameterExpression><![CDATA[$P{param_Business_StartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsOcFcstChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsOcFcstChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsOOOChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsOOOChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsSpecialEventChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsSpecialEventChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsRoomRevenueChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsRoomRevenueChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsADRChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsADRChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsRevParChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsRevParChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsProfitChecked">
                            <datasetParameterExpression><![CDATA[new Boolean($P{param_IsProfitChecked})]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_IsProPORChecked">
                            <datasetParameterExpression><![CDATA[new Boolean($P{param_IsProPORChecked})]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="param_IsProPARChecked">
                            <datasetParameterExpression><![CDATA[new Boolean($P{param_IsProPARChecked})]]></datasetParameterExpression>
                        </datasetParameter>
						<datasetParameter name="param_BaseCurrency">
							<datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsLastRoomValueChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsLastRoomValueChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsBARChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsBARChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_IsOvbkChecked">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_IsOvbkChecked})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_isBARByDay">
							<datasetParameterExpression><![CDATA[new Boolean($P{param_isBARByDay})]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp1">
							<datasetParameterExpression><![CDATA[$P{Comp1}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp2">
							<datasetParameterExpression><![CDATA[$P{Comp2}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp3">
							<datasetParameterExpression><![CDATA[$P{Comp3}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp4">
							<datasetParameterExpression><![CDATA[$P{Comp4}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp5">
							<datasetParameterExpression><![CDATA[$P{Comp5}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp6">
							<datasetParameterExpression><![CDATA[$P{Comp6}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp7">
							<datasetParameterExpression><![CDATA[$P{Comp7}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp8">
							<datasetParameterExpression><![CDATA[$P{Comp8}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp9">
							<datasetParameterExpression><![CDATA[$P{Comp9}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp10">
							<datasetParameterExpression><![CDATA[$P{Comp10}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp11">
							<datasetParameterExpression><![CDATA[$P{Comp11}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp12">
							<datasetParameterExpression><![CDATA[$P{Comp12}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp13">
							<datasetParameterExpression><![CDATA[$P{Comp13}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp14">
							<datasetParameterExpression><![CDATA[$P{Comp14}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Comp15">
							<datasetParameterExpression><![CDATA[$P{Comp15}]]></datasetParameterExpression>
						</datasetParameter>
                        <datasetParameter name="isReferDifferentialTableForPaceOvrbkAccomEnabled">
                            <datasetParameterExpression><![CDATA[$P{isReferDifferentialTableForPaceOvrbkAccomEnabled}]]></datasetParameterExpression>
                        </datasetParameter>
						<datasetParameter name="JNDI_NAME">
							<datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_isRollingDate">
							<datasetParameterExpression><![CDATA[$P{param_isRollingDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_RollingBusinessStartDate">
							<datasetParameterExpression><![CDATA[$P{param_RollingBusinessStartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Rolling_Start_Date">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_Start_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Rolling_End_Date">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_End_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="isPhysicalCapacity">
							<datasetParameterExpression><![CDATA[$P{isPhysicalCapacity}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
							<datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userLocale">
							<datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="dateFormatter">
							<datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="isRestrictHighestBarEnabled">
							<datasetParameterExpression><![CDATA[$P{isRestrictHighestBarEnabled}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="isUseCompactWebratePaceEnabled">
							<datasetParameterExpression><![CDATA[$P{isUseCompactWebratePaceEnabled}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:columnGroup width="14125">
						<jr:column width="70">
							<jr:columnHeader style="table 5_CH" height="80" rowSpan="3">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="80"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{common.dow}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.lang.String"><![CDATA[str($F{dow}.toLowerCase())]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="75">
							<jr:columnHeader style="table 5_CH" height="80" rowSpan="3">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="80"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{day.of.arrival}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.util.Date"><![CDATA[$F{dayofarrival}]]></textFieldExpression>
										<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<printWhenExpression><![CDATA[new Boolean($P{param_IsSpecialEventChecked})]]></printWhenExpression>
							<jr:columnHeader style="table 5_CH" height="80" rowSpan="3">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="80"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{specialEvent}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30">
										<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
									</reportElement>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.lang.String"><![CDATA[$F{specialevent}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<printWhenExpression><![CDATA[new Boolean($P{param_IsOOOChecked})]]></printWhenExpression>
							<jr:columnHeader style="table 5_CH" height="80" rowSpan="3">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="80"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.hotelOutOfOrder}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{ooo}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{occupancy.on.books}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{roomssoldcurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.0" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{roomssoldchange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{common.occupancyForecast}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsOcFcstChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.0" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{occupancyforecastcurrent} == null ? 0 : $F{occupancyforecastcurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsOcFcstChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.0" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{occupancyforecastchange}.toString().startsWith("-0.0")?new BigDecimal("0.0"):$F{occupancyforecastchange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{occupancyforecast%}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsOcFcstChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{isPhysicalCapacity}==0 ? $F{occupancyforecastpercurrent} == null ? 0 : $F{occupancyforecastpercurrent} : $F{occupancyforecastpercurrent_without_ooo} == null ? 0 : $F{occupancyforecastpercurrent_without_ooo}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsOcFcstChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$P{isPhysicalCapacity}==0 ?
($F{occupancyforecastperchange}.toString().startsWith("-0.00")?new BigDecimal("0.00"):$F{occupancyforecastperchange}) :
($F{occupancyforecastperchange_without_ooo}.toString().startsWith("-0.00")?new BigDecimal("0.00"):$F{occupancyforecastperchange_without_ooo})]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{revenue.on.books}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsRoomRevenueChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{bookedroomrevenuecurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsRoomRevenueChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{bookedroomrevenuechange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[new Boolean($P{param_IsRoomRevenueChecked})]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50">
										<printWhenExpression><![CDATA[new Boolean($P{param_IsRoomRevenueChecked})]]></printWhenExpression>
									</reportElement>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{revenue.forecast}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<pen lineWidth="0.5"/>
											<topPen lineWidth="0.5"/>
											<leftPen lineWidth="0.5"/>
											<bottomPen lineWidth="0.5"/>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{fcstedroomrevenuecurrent} == null ? 0 : $F{fcstedroomrevenuecurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<bottomPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{fcstedroomrevenuechange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{adr.on.books}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsADRChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{bookedadrcurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsADRChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{bookedadrchange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{adr.forecast}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsADRChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{estimatedadrcurrent} == null ? 0 : $F{estimatedadrcurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsADRChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{estimatedadrchange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{revpar.on.books}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsRevParChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{bookedrevparcurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsRevParChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{bookedrevparchange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{revpar.forecast}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsRevParChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{estimatedrevparcurrent} == null ? 0 : $F{estimatedrevparcurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsRevParChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{estimatedrevparchange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
                        	<jr:columnHeader style="table_CH" height="50" rowSpan="1">
                        		<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        			<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
                        			<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        			<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        				<font isBold="true"/>
                        			</textElement>
                        			<textFieldExpression class="java.lang.String"><![CDATA[$R{profit}+ " " + $R{on.books}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
                        		</textField>
                        	</jr:columnHeader>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProfitChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{profitcurrent}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProfitChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{profitchange}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        </jr:columnGroup>
                        <jr:columnGroup width="180">
                        	<printWhenExpression><![CDATA[new Boolean($P{param_IsProfitChecked})]]></printWhenExpression>
                        	<jr:columnHeader style="table_CH" height="50" rowSpan="1">
                        		<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        			<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50">
                        				<printWhenExpression><![CDATA[new Boolean($P{param_IsProfitChecked})]]></printWhenExpression>
                        			</reportElement>
                        			<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        			<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        				<font isBold="true"/>
                        			</textElement>
                        			<textFieldExpression class="java.lang.String"><![CDATA[$R{profit} + " " +$R{forecast}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
                        		</textField>
                        	</jr:columnHeader>
                        	<jr:column width="90">
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box>
                        					<pen lineWidth="0.5"/>
                        					<topPen lineWidth="0.5"/>
                        					<leftPen lineWidth="0.5"/>
                        					<bottomPen lineWidth="0.5"/>
                        					<rightPen lineWidth="0.5"/>
                        				</box>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{fcstedprofitcurrent} == null ? 0 : $F{fcstedprofitcurrent}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        	<jr:column width="90">
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box>
                        					<bottomPen lineWidth="0.5"/>
                        				</box>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{fcstedprofitchange}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        </jr:columnGroup>
                        <jr:columnGroup width="180">
                        	<jr:columnHeader style="table_CH" height="50" rowSpan="1">
                        		<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        			<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
                        			<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        			<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        				<font isBold="true"/>
                        			</textElement>
                        			<textFieldExpression class="java.lang.String"><![CDATA[$R{proPOR}+ " " +$R{on.books}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
                        		</textField>
                        	</jr:columnHeader>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProPORChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{proporcurrent}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProPORChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{proporchange}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        </jr:columnGroup>
                        <jr:columnGroup width="180">
                        	<jr:columnHeader style="table_CH" height="50" rowSpan="1">
                        		<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        			<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
                        			<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        			<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        				<font isBold="true"/>
                        			</textElement>
                        			<textFieldExpression class="java.lang.String"><![CDATA[$R{proPOR}+ " " +$R{forecast}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
                        		</textField>
                        	</jr:columnHeader>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProPORChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{estimatedproporcurrent} == null ? 0 : $F{estimatedproporcurrent}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProPORChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{estimatedproporchange}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        </jr:columnGroup>
                        <jr:columnGroup width="180">
                        	<jr:columnHeader style="table_CH" height="50" rowSpan="1">
                        		<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        			<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
                        			<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        			<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        				<font isBold="true"/>
                        			</textElement>
                        			<textFieldExpression class="java.lang.String"><![CDATA[$R{proPAR}+ " " + $R{on.books}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
                        		</textField>
                        	</jr:columnHeader>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProPARChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{proparcurrent}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProPARChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{proparchange}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        </jr:columnGroup>
                        <jr:columnGroup width="180">
                        	<jr:columnHeader style="table_CH" height="50" rowSpan="1">
                        		<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        			<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
                        			<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        			<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        				<font isBold="true"/>
                        			</textElement>
                        			<textFieldExpression class="java.lang.String"><![CDATA[$R{proPAR}+ " " + $R{forecast}+""+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
                        		</textField>
                        	</jr:columnHeader>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProPARChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{estimatedproparcurrent} == null ? 0 : $F{estimatedproparcurrent}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        	<jr:column width="90">
                        		<printWhenExpression><![CDATA[new Boolean($P{param_IsProPARChecked})]]></printWhenExpression>
                        		<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
                        			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<box topPadding="2" bottomPadding="1" rightPadding="1"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
                        					<font isBold="true"/>
                        				</textElement>
                        				<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
                        			</textField>
                        		</jr:columnHeader>
                        		<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
                        			<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
                        				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
                        				<textElement textAlignment="Center" verticalAlignment="Middle"/>
                        				<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{estimatedproparchange}]]></textFieldExpression>
                        			</textField>
                        		</jr:detailCell>
                        	</jr:column>
                        </jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{last.room.value.for.RC}+" "+$F{master_class_name} +" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsLastRoomValueChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{lrv}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsLastRoomValueChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{lrv_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{common.overbooking}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsOvbkChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{overbookingcurrent}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<printWhenExpression><![CDATA[new Boolean($P{param_IsOvbkChecked})]]></printWhenExpression>
								<jr:columnHeader style="table 5_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{overbookingchange}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[new Boolean($P{param_isBARByDay})  && new Boolean($P{param_IsBARChecked})]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barByDayForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los_all} == null ?"NoBAR":'('+$F{ratecode_los_all}+')'+' '+$P{number_formatter}.format($F{bar_by_day}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_by_day_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[new Boolean($P{param_isBARByDay})  && new Boolean($P{param_IsBARChecked})]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:columnGroup width="180">
								<jr:columnHeader height="0" rowSpan="1"/>
								<jr:column width="90">
									<jr:tableFooter height="0" rowSpan="1"/>
									<jr:columnHeader style="table_CH" height="30" rowSpan="1">
										<textField isStretchWithOverflow="true" isBlankWhenNull="true">
											<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
											<box topPadding="2" bottomPadding="1" rightPadding="1"/>
											<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
												<font isBold="true"/>
											</textElement>
											<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
										</textField>
									</jr:columnHeader>
									<jr:columnFooter height="0" rowSpan="1"/>
									<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
										<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
											<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
											<box>
												<rightPen lineWidth="0.5"/>
											</box>
											<textElement textAlignment="Center" verticalAlignment="Middle"/>
											<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent}]]></textFieldExpression>
										</textField>
									</jr:detailCell>
								</jr:column>
								<jr:column width="90">
									<jr:tableHeader height="0" rowSpan="1"/>
									<jr:tableFooter height="0" rowSpan="1"/>
									<jr:columnHeader style="table_CH" height="30" rowSpan="1">
										<textField isStretchWithOverflow="true" isBlankWhenNull="true">
											<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
											<box topPadding="2" bottomPadding="1" rightPadding="1"/>
											<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
												<font isBold="true"/>
											</textElement>
											<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
										</textField>
									</jr:columnHeader>
									<jr:columnFooter height="0" rowSpan="1"/>
									<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
										<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
											<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
											<box>
												<rightPen lineWidth="0.5"/>
											</box>
											<textElement textAlignment="Center" verticalAlignment="Middle"/>
											<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange}]]></textFieldExpression>
										</textField>
									</jr:detailCell>
								</jr:column>
							</jr:columnGroup>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=1?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barLos1ForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los1} == null ?"NoBAR":'('+$F{ratecode_los1}+')'+' '+$P{number_formatter}.format($F{bar_los1}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_los1_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=2?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barLos2ForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los2} == null ?"NoBAR":'('+$F{ratecode_los2}+')'+' '+$P{number_formatter}.format($F{bar_los2}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_los2_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=3?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barLos3ForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los3} == null ?"NoBAR":'('+$F{ratecode_los3}+')'+' '+$P{number_formatter}.format($F{bar_los3}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_los3_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=4?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barLos4ForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los4} == null ?"NoBAR":'('+$F{ratecode_los4}+')'+' '+$P{number_formatter}.format($F{bar_los4}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_los4_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=5?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barLos5ForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los5} == null ?"NoBAR":'('+$F{ratecode_los5}+')'+' '+$P{number_formatter}.format($F{bar_los5}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_los5_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=6?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barLos6ForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los6} == null ?"NoBAR":'('+$F{ratecode_los6}+')'+' '+$P{number_formatter}.format($F{bar_los6}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_los6_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=7?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barLos7ForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los7} == null ?"NoBAR":'('+$F{ratecode_los7}+')'+' '+$P{number_formatter}.format($F{bar_los7}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_los7_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=8?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.column.barLos8ForRC}+" "+$F{master_class_name}+" (" + $P{param_BaseCurrency}+")"]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{ratecode_los8} == null ?"NoBAR":'('+$F{ratecode_los8}+')'+' '+$P{number_formatter}.format($F{bar_los8}).toString()]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.Double"><![CDATA[$F{bar_los8_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=7?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}+" - "+$R{common.los1}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent_los1}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange_los1}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=7?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}+" - "+$R{common.los2}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent_los2}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange_los2}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=7?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}+" - "+$R{common.los3}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent_los3}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange_los3}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=7?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}+" - "+$R{common.los4}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent_los4}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange_los4}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=7?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}+" - "+$R{common.los5}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent_los5}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange_los5}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=7?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}+" - "+$R{common.los6}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent_los6}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange_los6}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=7?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}+" - "+$R{common.los7}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent_los7}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange_los7}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="180">
							<printWhenExpression><![CDATA[$P{maxLOS}.intValue()>=8?(!new Boolean($P{param_isBARByDay})&& new Boolean($P{param_IsBARChecked}))?true:false:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="180" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{highest.bar.restricted}+"\n"+$R{report.column.forRC}+" "+$F{master_class_name}+" - "+$R{common.los8}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypecurrent_los8}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="90">
								<jr:tableHeader height="0" rowSpan="1"/>
								<jr:tableFooter height="0" rowSpan="1"/>
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:columnFooter height="0" rowSpan="1"/>
								<jr:detailCell style="table_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.lang.String"><![CDATA[$F{decisionreasontypechange_los8}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp1}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp1_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp1_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp1_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp2}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp2_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp2_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp2_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp3}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp3_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp3_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp3_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp4}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp4_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp4_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp4_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp5}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp5_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp5_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp5_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp6}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp6_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp6_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp6_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp7}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp7_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp7_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp7_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp8}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp8_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp8_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp8_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp9}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp9_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp9_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp9_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp10}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp10_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp10_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp10_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp11}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp11_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp11_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp11_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp12}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp12_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp12_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp12_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp13}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp13_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp13_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp13_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp14}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp14_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp14_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp14_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
						<jr:columnGroup width="500">
							<printWhenExpression><![CDATA[$P{Comp15}.intValue()>0?true:false]]></printWhenExpression>
							<jr:columnHeader style="table_CH" height="50" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="500" height="50"/>
									<box topPadding="2" bottomPadding="1" rightPadding="1"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression class="java.lang.String"><![CDATA[$R{report.competitorRateFor}+" "+$F{comp15_name}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{common.current}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp15_rate}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
							<jr:column width="250">
								<jr:columnHeader style="table_CH" height="30" rowSpan="2">
									<textField isStretchWithOverflow="true" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box topPadding="2" bottomPadding="1" rightPadding="1"/>
										<textElement textAlignment="Center" verticalAlignment="Bottom" markup="none">
											<font isBold="true"/>
										</textElement>
										<textFieldExpression class="java.lang.String"><![CDATA[$R{change}]]></textFieldExpression>
									</textField>
								</jr:columnHeader>
								<jr:detailCell style="table 5_TD" height="30" rowSpan="1">
									<textField isStretchWithOverflow="true" pattern="###0.00" isBlankWhenNull="true">
										<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="250" height="30"/>
										<box>
											<rightPen lineWidth="0.5"/>
										</box>
										<textElement textAlignment="Center" verticalAlignment="Middle"/>
										<textFieldExpression class="java.math.BigDecimal"><![CDATA[$F{comp15_change}]]></textFieldExpression>
									</textField>
								</jr:detailCell>
							</jr:column>
						</jr:columnGroup>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
		</band>
		<band height="70">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria}))?true:false]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="38" width="480" height="30">
					<propertyExpression name="net.sf.jasperreports.export.xls.break.before.row"><![CDATA[$P{param_SheetForCriteria}.toString().equalsIgnoreCase("true")?"true":"false"]]></propertyExpression>
					<propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{report.ReportCriteria}]]></propertyExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="15" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["   "+$R{change.and.differential.control.report.hotel}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="480" height="30">
					<propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{change} +$R{common.report}+ " - " + $R{Hotel}]]></propertyExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="15" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["   "]]></textFieldExpression>
			</textField>
		</band>
		<band height="50">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria}))?true:false
]]></printWhenExpression>
			<componentElement>
				<reportElement key="table 3" style="table 3" stretchType="RelativeToBandHeight" x="0" y="0" width="360" height="50"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="ExcelHeader">
						<datasetParameter name="param_Property_ID">
							<datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_User_ID">
							<datasetParameterExpression><![CDATA[$P{param_User_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_BaseCurrency">
							<datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="JNDI_NAME">
							<datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_AnalysisStartDate">
							<datasetParameterExpression><![CDATA[$P{param_StartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_AnalysisEndDate">
							<datasetParameterExpression><![CDATA[$P{param_EndDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_ComparisonStartDate">
							<datasetParameterExpression><![CDATA[$P{param_Business_StartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_isRollingDate">
							<datasetParameterExpression><![CDATA[$P{param_isRollingDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_RollingAnalysisStartDate">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_Start_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_RollingAnalysisEndDate">
							<datasetParameterExpression><![CDATA[$P{param_Rolling_End_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_RollingComparisionStartDate">
							<datasetParameterExpression><![CDATA[$P{param_RollingBusinessStartDate}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Jasper_custom_formatter">
							<datasetParameterExpression><![CDATA[$P{Jasper_custom_formatter}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
							<datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userLocale">
							<datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="dateFormatter">
							<datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:columnGroup width="630">
						<jr:tableHeader height="30" rowSpan="1">
							<textField>
								<reportElement x="0" y="0" width="630" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="11" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{report.ReportCriteria}]]></textFieldExpression>
							</textField>
						</jr:tableHeader>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.propertyName}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{property_name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{analysisStartDateLabel}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{analysis_start_date}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{analysis.end.date}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{analysis_end_date}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{activity.start.date}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{comparision_start_date}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{currency}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{param_BaseCurrency}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{createdBy}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{created_by}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90">
							<jr:tableHeader style="table 3_TH" height="0" rowSpan="1"/>
							<jr:tableFooter style="table 3_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{report.GeneratedOn}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$P{Jasper_custom_formatter}.getDateInPropertyTimeZone($F{genration_date}, $P{REPORT_TIME_ZONE}, $P{PROPERTY_TIME_ZONE},$P{userDateFormat}+" HH:mm:ss", $P{REPORT_LOCALE})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<noData>
		<band height="21">
			<textField>
				<reportElement x="0" y="0" width="400" height="21"/>
				<textElement>
					<font size="15" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$R{nodataavailable}]]></textFieldExpression>
			</textField>
		</band>
	</noData>
</jasperReport>
