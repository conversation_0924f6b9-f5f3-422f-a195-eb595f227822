<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Booking Pace-RC" pageWidth="595" pageHeight="842" orientation="Landscape" columnWidth="575" leftMargin="10" rightMargin="10" topMargin="5" bottomMargin="5" uuid="081e005c-88b8-441f-818f-37b89da5221d">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<template>"jrxmls/customStyles.jrtx"</template>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="New Dataset 1" uuid="14239693-79c9-4fb8-b873-0ad0bfdb1bfd">
		<parameter name="param_Arrival_DT" class="java.util.Date"/>
		<parameter name="param_Property_ID" class="java.lang.Integer"/>
		<parameter name="JNDI_NAME" class="java.lang.String"/>
		<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA["en_US"]]></defaultValueExpression>
		</parameter>
		<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA["yyyy-MM-dd"]]></defaultValueExpression>
		</parameter>
		<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_pace_days" class="java.lang.Integer" isForPrompting="false">
			<defaultValueExpression><![CDATA[30]]></defaultValueExpression>
		</parameter>
        <parameter name="includeInactiveRT" class="java.lang.Integer" isForPrompting="false">
			<defaultValueExpression><![CDATA[1]]></defaultValueExpression>
		</parameter>
        <parameter name="isReferDifferentialTableForPaceOvrbkAccomEnabled" class="java.lang.Integer">
            <defaultValueExpression><![CDATA[0]]></defaultValueExpression>
        </parameter>
        <queryString>
            <![CDATA[if($P{isReferDifferentialTableForPaceOvrbkAccomEnabled}=1)
                begin
                    exec dbo.usp_booking_pace_rc_report_Pace_Ovrbk_Accom_Differential $P{param_Property_ID},$P{param_Arrival_DT},$P{param_pace_days},$P{includeInactiveRT}
                end
                else
                begin
                    exec dbo.usp_booking_pace_rc_report $P{param_Property_ID},$P{param_Arrival_DT},$P{param_pace_days},$P{includeInactiveRT}
                end]]>
        </queryString>
		<field name="Rooms_Sold" class="java.math.BigDecimal"/>
		<field name="SnapShot_DTTM" class="java.util.Date"/>
		<field name="daystoArrival" class="java.lang.Integer"/>
		<field name="dow" class="java.lang.String"/>
		<field name="businessdate" class="java.lang.String"/>
		<field name="Accom_Class_Name" class="java.lang.String"/>
		<field name="Occupancy_NBR" class="java.math.BigDecimal"/>
		<field name="Authorized_Capacity" class="java.math.BigDecimal"/>
		<field name="Effective_Capacity" class="java.math.BigDecimal"/>
	</subDataset>
	<subDataset name="ExcelHeader" uuid="3aab3a8c-a658-42b3-a821-046b75328e75">
		<parameter name="param_StartDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_User_ID" class="java.lang.Integer"/>
		<parameter name="param_EndDate" class="java.util.Date">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="param_BaseCurrency" class="java.lang.String">
			<defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
		</parameter>
		<parameter name="param_Property_ID" class="java.lang.Integer"/>
		<parameter name="param_Arrival_DT" class="java.util.Date"/>
		<parameter name="JNDI_NAME" class="java.lang.String"/>
		<parameter name="Jasper_custom_formatter" class="com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter" isForPrompting="false">
			<defaultValueExpression><![CDATA[new com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter()]]></defaultValueExpression>
		</parameter>
		<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA["en_US"]]></defaultValueExpression>
		</parameter>
		<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA["yyyy-MM-dd"]]></defaultValueExpression>
		</parameter>
		<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<queryString>
			<![CDATA[select * from dbo.ufn_get_filter_selection
(
$P{param_Property_ID},
$P{param_User_ID},
$P{param_BaseCurrency},
'',
$P{param_StartDate},
'',
'',
'',
'',
'',
'',
'',
'',
'',
'',
'',
'',
'',
'',
''
)]]>
		</queryString>
		<field name="property_name" class="java.lang.String"/>
		<field name="created_by" class="java.lang.String"/>
		<field name="genration_date" class="java.lang.String"/>
		<field name="start_date" class="java.lang.String"/>
		<field name="end_date" class="java.lang.String"/>
		<field name="analysis_start_date" class="java.lang.String"/>
		<field name="analysis_end_date" class="java.lang.String"/>
		<field name="analysis_business_dt" class="java.lang.String"/>
		<field name="comparision_start_date" class="java.lang.String"/>
		<field name="comparision_end_date" class="java.lang.String"/>
		<field name="comparision_business_dt" class="java.lang.String"/>
		<field name="param_BaseCurrency" class="java.lang.String"/>
	</subDataset>
	<parameter name="param_Arrival_DT" class="java.util.Date"/>
	<parameter name="param_pace_days" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[30]]></defaultValueExpression>
	</parameter>
    <parameter name="includeInactiveRT" class="java.lang.Integer">
		<defaultValueExpression><![CDATA[1]]></defaultValueExpression>
	</parameter>
    <parameter name="isReferDifferentialTableForPaceOvrbkAccomEnabled" class="java.lang.Integer">
        <defaultValueExpression><![CDATA[0]]></defaultValueExpression>
    </parameter>
	<parameter name="param_Property_ID" class="java.lang.Integer"/>
	<parameter name="JNDI_NAME" class="java.lang.String"/>
	<parameter name="param_User_ID" class="java.lang.Integer"/>
	<parameter name="param_BaseCurrency" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["USD"]]></defaultValueExpression>
	</parameter>
	<parameter name="Jasper_custom_formatter" class="com.ideas.tetris.platform.reports.jasperreports.formatter.JasperCustomFormatter" isForPrompting="false">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="userLocale" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["en_US"]]></defaultValueExpression>
	</parameter>
	<parameter name="PROPERTY_TIME_ZONE" class="java.lang.String" isForPrompting="false">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="userDateFormat" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["yyyy-MM-dd"]]></defaultValueExpression>
	</parameter>
	<parameter name="dateFormatter" class="java.text.DateFormat" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{REPORT_FORMAT_FACTORY}.createDateFormat( $P{userDateFormat}, $P{REPORT_LOCALE}, null)]]></defaultValueExpression>
	</parameter>
	<parameter name="param_SheetForCriteria" class="java.lang.Boolean" isForPrompting="false">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT GETDATE() as dat]]>
	</queryString>
	<field name="dat" class="java.sql.Timestamp"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="31" splitType="Stretch">
			<printWhenExpression><![CDATA[$P{IS_IGNORE_PAGINATION} && $P{param_SheetForCriteria}.toString().equalsIgnoreCase("true")?false:true]]></printWhenExpression>
			<textField>
				<reportElement x="0" y="0" width="366" height="31" uuid="923f0bc3-f9fb-4c79-9e0b-16a7ee132cdc">
					<propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{report.ReportCriteria}]]></propertyExpression>
					<propertyExpression name="net.sf.jasperreports.export.xls.break.before.row"><![CDATA[$P{param_SheetForCriteria}.toString().equalsIgnoreCase("true")?"true":"false"]]></propertyExpression>
					<printWhenExpression><![CDATA[true]]></printWhenExpression>
				</reportElement>
				<box leftPadding="50"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{booking.pace.report.room.classes}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="50">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria})==false)?true:false]]></printWhenExpression>
			<componentElement>
				<reportElement key="table" style="table" x="0" y="0" width="360" height="50" uuid="22204be3-7489-4309-8d28-7f27f2890262"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="ExcelHeader" uuid="66724da9-ae33-4fea-b685-a2b43ef99b43">
						<datasetParameter name="param_Property_ID">
							<datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_User_ID">
							<datasetParameterExpression><![CDATA[$P{param_User_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_BaseCurrency">
							<datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Arrival_DT">
							<datasetParameterExpression><![CDATA[$P{param_Arrival_DT}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="JNDI_NAME">
							<datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Jasper_custom_formatter">
							<datasetParameterExpression><![CDATA[$P{Jasper_custom_formatter}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userLocale">
							<datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
							<datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="dateFormatter">
							<datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:columnGroup width="366" uuid="db24fe2e-8fab-4569-a524-a45791df07cb">
						<jr:tableHeader height="30" rowSpan="1">
							<textField>
								<reportElement x="0" y="0" width="366" height="30" uuid="14c5dc0a-4412-4b6c-8156-3c8512732081"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="11" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{report.ReportCriteria}]]></textFieldExpression>
							</textField>
						</jr:tableHeader>
						<jr:column width="76" uuid="34e5e95f-3d4e-4944-879f-3052e6cb5009">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="76" height="30" uuid="fec3b5e1-b8e6-4840-bf84-d60acdba54b2"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.propertyName}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="76" height="20" uuid="dde404f1-c422-45a8-845b-28363011e44f"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{property_name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="75" uuid="0bc65728-0de9-487a-b755-e1ba3cf94a66">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="30" uuid="4bf31fd0-c1d6-4017-ab5f-10b53058fa2c"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.arrivalDate}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="20" uuid="7c2ceaa8-d5a3-4ada-af9d-f36e9c910ef5"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$P{param_Arrival_DT}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="70" uuid="9df3093d-912a-4bee-90a6-811533f34b65">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="30" uuid="8d9a7255-8c70-401f-a530-dc43157e7160"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{currency}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="20" uuid="962fc011-bdc7-4016-afdd-1750800b00bf"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{param_BaseCurrency}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="75" uuid="30524666-1a1d-4510-8b74-6a1e6427c11a">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="30" uuid="cb961820-e83b-403c-9af8-110829024263"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{createdBy}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="20" uuid="3e344768-aa6b-4763-bdba-ee0f0e18fc07"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{created_by}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="70" uuid="a7d6df8b-b30c-4b32-bfa4-155c87fb816b">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="30" uuid="950c95ea-56e0-4af7-a351-6aa590c3c59b"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{report.GeneratedOn}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="20" uuid="1526a902-db62-4eca-a4ba-c365c8dbf1c6"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$P{Jasper_custom_formatter}.getDateInPropertyTimeZone($F{genration_date}, $P{REPORT_TIME_ZONE}, $P{PROPERTY_TIME_ZONE},$P{userDateFormat}+" HH:mm:ss", $P{REPORT_LOCALE})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="20">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria})==false)?true:false]]></printWhenExpression>
			<staticText>
				<reportElement x="0" y="0" width="76" height="20" uuid="c9d3fdd4-8e89-4614-89c3-ed4d07545310"/>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="114" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="76" height="0" uuid="8f113164-e094-45fb-beeb-d7a6d58cec62">
					<propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{booking.pace.report.room.classes}]]></propertyExpression>
				</reportElement>
				<text><![CDATA[]]></text>
			</staticText>
			<crosstab ignoreWidth="true">
				<reportElement mode="Opaque" x="0" y="0" width="575" height="114" uuid="eac2bc3b-fe35-428e-9a09-258de655edf5"/>
				<crosstabParameter name="userDateFormat">
					<parameterValueExpression><![CDATA[$P{userDateFormat}]]></parameterValueExpression>
				</crosstabParameter>
				<crosstabDataset>
					<dataset>
						<datasetRun subDataset="New Dataset 1" uuid="8ae8de42-f4b5-4369-a806-73630f3662bb">
							<datasetParameter name="param_Arrival_DT">
								<datasetParameterExpression><![CDATA[$P{param_Arrival_DT}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="param_pace_days">
								<datasetParameterExpression><![CDATA[$P{param_pace_days}]]></datasetParameterExpression>
							</datasetParameter>
                            <datasetParameter name="includeInactiveRT">
								<datasetParameterExpression><![CDATA[$P{includeInactiveRT}]]></datasetParameterExpression>
							</datasetParameter>
                            <datasetParameter name="isReferDifferentialTableForPaceOvrbkAccomEnabled">
                                <datasetParameterExpression><![CDATA[$P{isReferDifferentialTableForPaceOvrbkAccomEnabled}]]></datasetParameterExpression>
                            </datasetParameter>
							<datasetParameter name="param_Property_ID">
								<datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="JNDI_NAME">
								<datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="userLocale">
								<datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
								<datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="dateFormatter">
								<datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
							</datasetParameter>
							<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						</datasetRun>
					</dataset>
				</crosstabDataset>
				<crosstabHeaderCell>
					<cellContents>
						<textField>
							<reportElement mode="Opaque" x="0" y="0" width="76" height="86" backcolor="#BFE1FF" uuid="e5eedfcc-8f0e-48c0-b989-9d3d8c122675">
								<property name="net.sf.jasperreports.export.xls.collapse.row.span" value="false"/>
							</reportElement>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$R{daysToArrival}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement mode="Opaque" x="76" y="0" width="75" height="86" backcolor="#BFE1FF" uuid="9342d176-cf30-4160-8108-20c13f8c69c9">
								<property name="net.sf.jasperreports.export.xls.collapse.row.span" value="false"/>
							</reportElement>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$R{report.column.captureDate}]]></textFieldExpression>
						</textField>
						<textField>
							<reportElement mode="Opaque" x="151" y="0" width="70" height="86" backcolor="#BFE1FF" uuid="d743ed8e-fc12-4a2d-bd11-653b2ce1dc66">
								<property name="net.sf.jasperreports.export.xls.collapse.row.span" value="false"/>
							</reportElement>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$R{common.dow}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabHeaderCell>
				<rowGroup name="daystoArrival" width="76">
					<bucket class="java.lang.Integer">
						<bucketExpression><![CDATA[$F{daystoArrival}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#F0F8FF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement mode="Opaque" x="0" y="0" width="76" height="27" uuid="7b72da73-4511-43a5-9b44-7309854faf8d"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" />
								<textFieldExpression><![CDATA[$V{daystoArrival}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="SnapShot_DTTM" width="75">
					<bucket class="java.util.Date">
						<bucketExpression><![CDATA[$F{SnapShot_DTTM}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#F0F8FF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement mode="Opaque" x="0" y="0" width="75" height="27" uuid="8380379c-804f-4909-b0c5-ee43bef5aa3e"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" />
								<textFieldExpression><![CDATA[$V{SnapShot_DTTM}]]></textFieldExpression>
								<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<rowGroup name="dow" width="70">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{dow}]]></bucketExpression>
					</bucket>
					<crosstabRowHeader>
						<cellContents backcolor="#F0F8FF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement mode="Opaque" x="0" y="0" width="70" height="27" uuid="02b490fa-9474-48b6-8993-fbc8f0fb8a92"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" />
								<textFieldExpression><![CDATA[str($V{dow}.toLowerCase())]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabRowHeader>
					<crosstabTotalRowHeader>
						<cellContents/>
					</crosstabTotalRowHeader>
				</rowGroup>
				<columnGroup name="Accom_Class_Name" height="86">
					<bucket class="java.lang.String">
						<bucketExpression><![CDATA[$F{Accom_Class_Name}]]></bucketExpression>
					</bucket>
					<crosstabColumnHeader>
						<cellContents backcolor="#F0F8FF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isStretchWithOverflow="true">
								<reportElement stretchType="RelativeToTallestObject" mode="Opaque" x="0" y="0" width="290" height="45" backcolor="#BFE1FF" uuid="*************-4f7d-9bb1-48e4897254b9"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[""+$V{Accom_Class_Name}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement mode="Opaque" x="75" y="45" width="70" height="41" backcolor="#BFE1FF" uuid="086a67d4-48f4-46f1-b2be-6bfa963434d7"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{common.effective.capacity}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement mode="Opaque" x="220" y="45" width="70" height="41" backcolor="#BFE1FF" uuid="dfcdea16-6a6b-4d9f-9d3f-0948c07bd547"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{forecast}]]></textFieldExpression>
							</textField>
							<textField isBlankWhenNull="true">
								<reportElement mode="Opaque" x="145" y="45" width="75" height="41" backcolor="#BFE1FF" uuid="fa7eacb5-107e-45b4-a601-225e70a48ca3"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" >
									<font isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{occupancyonBooks}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement mode="Opaque" x="0" y="45" width="75" height="41" backcolor="#BFE1FF" uuid="4ea074b4-ae1f-42e9-bdd9-c49d0a736ee6"/>
								<box>
									<pen lineWidth="0.5"/>
									<topPen lineWidth="0.5"/>
									<leftPen lineWidth="0.5"/>
									<bottomPen lineWidth="0.5"/>
									<rightPen lineWidth="0.5"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{common.authorized.capacity}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabColumnHeader>
					<crosstabTotalColumnHeader>
						<cellContents/>
					</crosstabTotalColumnHeader>
				</columnGroup>
				<measure name="Rooms_SoldsMeasure" class="java.math.BigDecimal" calculation="Sum">
					<measureExpression><![CDATA[$F{Rooms_Sold}]]></measureExpression>
				</measure>
				<measure name="measure1" class="java.math.BigDecimal" calculation="Sum">
					<measureExpression><![CDATA[$F{Occupancy_NBR}]]></measureExpression>
				</measure>
				<measure name="Authorized_CapacityMeasure" class="java.math.BigDecimal" calculation="Sum">
					<measureExpression><![CDATA[$F{Authorized_Capacity}]]></measureExpression>
				</measure>
				<measure name="Effective_CapacityMeasure" class="java.math.BigDecimal" calculation="Sum">
					<measureExpression><![CDATA[$F{Effective_Capacity}]]></measureExpression>
				</measure>
				<crosstabCell width="291" height="27">
					<cellContents>
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField pattern="###0.0" isBlankWhenNull="true">
							<reportElement x="220" y="0" width="70" height="27" uuid="281fa111-09c4-4bbe-ad10-dd4f580fa05c"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{measure1}]]></textFieldExpression>
						</textField>
						<textField pattern="###0" isBlankWhenNull="true">
							<reportElement x="0" y="0" width="75" height="27" uuid="d83e1ee9-3301-45e6-90af-b71aa344b530"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{Authorized_CapacityMeasure}]]></textFieldExpression>
						</textField>
						<textField pattern="###0" isBlankWhenNull="true">
							<reportElement x="75" y="0" width="70" height="27" uuid="3d7794d6-e183-4b4b-95fb-389f89be2d94"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{Effective_CapacityMeasure}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement x="145" y="0" width="75" height="27" uuid="c61cead4-8185-44f6-baba-38b0eb28a4f8"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{Rooms_SoldsMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="25" rowTotalGroup="SnapShot_DTTM">
					<cellContents backcolor="#005FB3" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement x="0" y="0" width="50" height="25" forecolor="#FFFFFF" uuid="11f9d3d7-ff66-468a-94e8-557e76386679"/>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{Rooms_SoldsMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell width="50" columnTotalGroup="Accom_Class_Name">
					<cellContents backcolor="#BFE1FF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement x="0" y="0" width="50" height="25" uuid="023de678-68c1-4c3b-bffd-ff2c3747aa08"/>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{Rooms_SoldsMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell rowTotalGroup="SnapShot_DTTM" columnTotalGroup="Accom_Class_Name">
					<cellContents backcolor="#005FB3" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement x="0" y="0" width="50" height="25" forecolor="#FFFFFF" uuid="67185497-bd86-466b-91df-87dc74699dcf"/>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{Rooms_SoldsMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell height="25" rowTotalGroup="dow">
					<cellContents backcolor="#BFE1FF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement x="0" y="0" width="50" height="25" uuid="841f6de4-089a-4173-bbdd-52254d6c0aaf"/>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{Rooms_SoldsMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
				<crosstabCell rowTotalGroup="dow" columnTotalGroup="Accom_Class_Name">
					<cellContents backcolor="#BFE1FF" mode="Opaque">
						<box>
							<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textField>
							<reportElement x="0" y="0" width="50" height="25" uuid="2145d9c2-e41e-4e7c-af8b-0f1f40ac479d"/>
							<textElement textAlignment="Center" />
							<textFieldExpression><![CDATA[$V{Rooms_SoldsMeasure}]]></textFieldExpression>
						</textField>
					</cellContents>
				</crosstabCell>
			</crosstab>
		</band>
		<band height="81">
			<printWhenExpression><![CDATA[($P{IS_IGNORE_PAGINATION} && new Boolean($P{param_SheetForCriteria}))?true:false]]></printWhenExpression>
			<textField>
				<reportElement x="0" y="0" width="366" height="31" uuid="b0ebffcf-6006-4e32-86c4-ab2218aafe45">
					<propertyExpression name="net.sf.jasperreports.export.xls.sheet.name"><![CDATA[$R{report.ReportCriteria}]]></propertyExpression>
					<propertyExpression name="net.sf.jasperreports.export.xls.break.before.row"><![CDATA[$P{param_SheetForCriteria}.toString().equalsIgnoreCase("true")?"true":"false"]]></propertyExpression>
				</reportElement>
				<box leftPadding="50"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["  " + $R{booking.pace.report.room.classes}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement key="table" style="table" x="0" y="31" width="360" height="50" uuid="dfb0a659-c239-4b43-a2d6-c11afd8da948"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="ExcelHeader" uuid="8301ca55-ab21-4a02-9e58-cd57b0846ddd">
						<datasetParameter name="param_Property_ID">
							<datasetParameterExpression><![CDATA[$P{param_Property_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_User_ID">
							<datasetParameterExpression><![CDATA[$P{param_User_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_BaseCurrency">
							<datasetParameterExpression><![CDATA[$P{param_BaseCurrency}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="param_Arrival_DT">
							<datasetParameterExpression><![CDATA[$P{param_Arrival_DT}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="JNDI_NAME">
							<datasetParameterExpression><![CDATA[$P{JNDI_NAME}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="Jasper_custom_formatter">
							<datasetParameterExpression><![CDATA[$P{Jasper_custom_formatter}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userLocale">
							<datasetParameterExpression><![CDATA[$P{userLocale}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="PROPERTY_TIME_ZONE">
							<datasetParameterExpression><![CDATA[$P{PROPERTY_TIME_ZONE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="userDateFormat">
							<datasetParameterExpression><![CDATA[$P{userDateFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="dateFormatter">
							<datasetParameterExpression><![CDATA[$P{dateFormatter}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:columnGroup width="366" uuid="2a8ca805-4d37-4b79-a51d-0c32cf0740f6">
						<jr:tableHeader height="30" rowSpan="1">
							<textField>
								<reportElement x="0" y="0" width="366" height="30" uuid="d3457ef9-4e71-42de-b5e8-3974564eb9e6"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="11" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{report.ReportCriteria}]]></textFieldExpression>
							</textField>
						</jr:tableHeader>
						<jr:column width="76" uuid="836fc148-8d4b-4c4b-aa30-3b6554193382">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="76" height="30" uuid="7157df33-a239-4a59-ae31-ff2c9b67a266"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.propertyName}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="76" height="20" uuid="242fd50b-1a6a-441c-a065-4c1a73a43657"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{property_name}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="75" uuid="67d1e239-73b3-4730-9f27-3d3f62d0be67">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="30" uuid="0d292859-410f-46c6-9853-a01ba4b6f920"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{common.arrivalDate}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="20" uuid="e613aad4-d014-48e5-97ca-dabad53e9931"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$P{param_Arrival_DT}]]></textFieldExpression>
									<patternExpression><![CDATA[$P{userDateFormat}]]></patternExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="70" uuid="82e9e10a-125d-4eca-bdc9-9f36c916e757">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="30" uuid="06b8bcf8-ead9-46a8-a059-11bc7cbcb10c"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{currency}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="20" uuid="0beb0bb5-d161-4450-8d08-6b4dea50d095"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{param_BaseCurrency}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="75" uuid="e5052a9d-7c62-4ed5-9fa5-04fb774f807d">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="30" uuid="10e59103-3e90-4052-b7dc-3e10ebeac481"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{createdBy}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="75" height="20" uuid="c2c4c40e-b6a2-4e50-a5dd-57b5f4404f37"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$F{created_by}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="70" uuid="c1a4dec5-e7d0-4141-97bf-b8f4832b4ee9">
							<jr:tableHeader style="table_TH" height="0" rowSpan="1"/>
							<jr:columnHeader style="table_CH" height="30" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="30" uuid="e9b12482-0546-460e-9501-c320a1d6d8a3"/>
									<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
										<font isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$R{report.GeneratedOn}]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="20" rowSpan="1">
								<textField isStretchWithOverflow="true">
									<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="70" height="20" uuid="050d4cd9-0755-44b4-9d90-3807654002b3"/>
									<textElement textAlignment="Center" verticalAlignment="Middle"/>
									<textFieldExpression><![CDATA[$P{Jasper_custom_formatter}.getDateInPropertyTimeZone($F{genration_date}, $P{REPORT_TIME_ZONE}, $P{PROPERTY_TIME_ZONE},$P{userDateFormat}+" HH:mm:ss", $P{REPORT_LOCALE})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
</jasperReport>
