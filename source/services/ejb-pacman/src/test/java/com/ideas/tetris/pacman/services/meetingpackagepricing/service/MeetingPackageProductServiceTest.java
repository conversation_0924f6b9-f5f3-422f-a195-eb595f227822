package com.ideas.tetris.pacman.services.meetingpackagepricing.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.bestavailablerate.PrettyPricingService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingDigit;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingRule;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PrettyPricingRuleRow;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceObjectMother;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueGroup;
import com.ideas.tetris.pacman.services.functionspace.configuration.packaging.dto.RevenueGroupDto;
import com.ideas.tetris.pacman.services.meetingpackagepricing.dto.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.MeetingPackageProductRepository;
import com.ideas.tetris.pacman.services.meetingpackagepricing.service.mapper.MeetingPackagePricingMapper;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.MinimumIncrementMethod;
import com.ideas.tetris.pacman.services.product.FloorType;
import com.ideas.tetris.pacman.services.product.InvalidReason;
import com.ideas.tetris.pacman.services.product.RoundingRule;
import com.ideas.tetris.pacman.testdatabuilder.MeetingPackageProductBuilder;
import com.ideas.tetris.pacman.testdatabuilder.MeetingPackageProductOffsetBuilder;
import com.ideas.tetris.pacman.testdatabuilder.MeetingPackageProductRateOffsetBuilder;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MeetingPackageProductServiceTest {

    @InjectMocks
    private MeetingPackageProductService meetingPackageProductService;

    @Mock
    MeetingPackageSeasonService seasonService;
    @Mock
    private MeetingPackageProductRepository meetingPackageProductRepository;

    @Mock
    private PacmanConfigParamsService configParamsService;
    @Mock
    private DateService dateService;

    @Mock
    private MeetingPackageDecisionService meetingPackageDecisionService;
    @Mock
    private PrettyPricingService prettyPricingService;

    @Mock
    private SyncEventAggregatorService syncEventAggregatorService;

    @Test
    void testGetMeetingPackageProducts(){
        List<MeetingPackageProduct> list = List.of(getMeetingPackageProduct("MPP_1"), getMeetingPackageProduct("MPP_2"));
        doReturn(list).when(meetingPackageProductRepository).getMeetingPackageProducts();

        List<MeetingPackageProduct> meetingPackageProducts = meetingPackageProductService.getMeetingPackageProducts();

        assertNotNull(meetingPackageProducts);
        assertEquals(2, meetingPackageProducts.size());
        assertEquals("MPP_1", meetingPackageProducts.get(0).getName());
        assertEquals("MPP_2", meetingPackageProducts.get(1).getName());

    }

    @Test
    void testGetBaseMeetingRoom(){
        List<BaseMeetingRoom> baseMeetingRoomList = List.of(getBaseMeetingRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom(), MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN));
        doReturn(baseMeetingRoomList).when(meetingPackageProductRepository).getBaseMeetingRooms();

        List<BaseMeetingRoom> baseMeetingRooms = meetingPackageProductService.getBaseMeetingRooms();

        assertNotNull(baseMeetingRooms);
        assertEquals(1, baseMeetingRooms.size());
        assertEquals("Name", baseMeetingRooms.get(0).getFunctionSpaceFunctionRoom().getName());
        assertEquals(BigDecimal.TEN,baseMeetingRooms.get(0).getMinimumIncrementValue());
    }

    @Test
    void testGetMeetingPackagePricingProducts(){
        List<MeetingPackageProduct> meetingPackageProducts = List.of(getMeetingPackageProduct("MPP_1"), getMeetingPackageProduct("MPP_2"));
        when(meetingPackageProductRepository.getMeetingPackageProducts()).thenReturn(meetingPackageProducts);

        List<MeetingPackageProductDTO> meetingPackagePricingProducts = meetingPackageProductService.getMeetingPackagePricingProducts();

        assertNotNull(meetingPackagePricingProducts);
        assertEquals(2, meetingPackageProducts.size());
        assertEquals("MPP_1", meetingPackagePricingProducts.get(0).getName());
        assertEquals("MPP_2", meetingPackagePricingProducts.get(1).getName());
    }

    @Test
    void testGetMeetingRooms(){
        MeetingPackageProduct mp1 = getMeetingPackageProduct("MP_1");
        mp1.setId(1);
        MeetingPackageProductMeetingRoom meetingRoom = getPackageProductMeetingRoom(mp1);
        List<MeetingPackageProductMeetingRoom> meetingRooms = List.of(meetingRoom);
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(meetingRooms);
        when(meetingPackageProductRepository.getBaseMeetingRooms()).thenReturn(new ArrayList<>());

        List<MeetingRoomDTO> meetingRoomDTOS = meetingPackageProductService.getMeetingRoomsIncludingBase(1);

        assertNotNull(meetingRoomDTOS);
        assertEquals(1, meetingRoomDTOS.size());
    }

    @Test
    void testGetMeetingPackagePricingProductOffset(){
        List<MeetingPackageProductOffset> productOffset = List.of(getMeetingPackageProductOffset("MPP_1", 1), getMeetingPackageProductOffset("MPP_2", 2));
        when(meetingPackageProductRepository.getMPProductOffsets(1)).thenReturn(productOffset);

        MeetingPackageOffsetsResponseDTO dtos = meetingPackageProductService.getMPProductOffsets(1);

        assertNotNull(dtos);
        assertNotNull(dtos.getOffsets());
        assertEquals(2, dtos.getOffsets().size());
    }


    @Test
    void testSaveMeetingPackagePricingProductOffset(){
        MeetingPackageProductOffsetsDTO dto = new MeetingPackageProductOffsetsDTO();
        List<MeetingPackageProductOffsetsDTO> productOffset = List.of(dto);
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        MeetingPackageProductMeetingRoom packageProductMeetingRoom = getPackageProductMeetingRoom(meetingPackageProduct);
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(List.of(packageProductMeetingRoom));
        meetingPackageProductService.saveMeetingRoomsProductOffset(1, productOffset);
        verify(meetingPackageProductRepository).saveMeetingPackageProductOffset(anyList());
    }

    @Test
    void shouldEnableSyncFlagWhenNewOffsetAdded(){
        List<MeetingPackageProductOffset> productOffset = List.of(getMeetingPackageProductOffset("MPP_1", 1),
                getMeetingPackageProductOffset("MPP_2", 2));
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setId(1);
        meetingPackageProductService.checkForSyncFlagForOffsets(1, productOffset);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_INDEPENDENT_PRODUCT_CHANGED);
    }

    @Test
    void shouldNotEnableSyncFlagWhenNoChangeInOffset(){
        MeetingPackageProductOffset mpp1 = getMeetingPackageProductOffset("MPP_1", 1);
        mpp1.setId(11);
        MeetingPackageProductOffset mpp2 = getMeetingPackageProductOffset("MPP_2", 1);
        mpp2.setId(12);
        List<MeetingPackageProductOffset> productOffset = List.of(mpp1, mpp2);
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setId(1);
        when(meetingPackageProductRepository.getMPProductOffsets(1)).thenReturn(productOffset);
        meetingPackageProductService.checkForSyncFlagForOffsets(1, productOffset);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_INDEPENDENT_PRODUCT_CHANGED);
    }


    @Test
    void shouldEnableSyncFlagWhenOffsetValueChanges() {
        MeetingPackageProductOffset mpp1 = getMeetingPackageProductOffset("MPP_1", 1);
        mpp1.setId(11);
        MeetingPackageProductOffset mpp2 = getMeetingPackageProductOffset("MPP_2", 1);
        mpp2.setId(12);
        MeetingPackageProductOffset clone = mpp1.clone();
        clone.setSundayOffsetValue(BigDecimal.valueOf(23));
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setId(1);
        when(meetingPackageProductRepository.getMPProductOffsets(1)).thenReturn(List.of(clone, mpp2));
        meetingPackageProductService.checkForSyncFlagForOffsets(1, List.of(mpp1, mpp2));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_INDEPENDENT_PRODUCT_CHANGED);
    }


    @Test
    void shouldEnableSyncFlagWhenStartDateEndDateChanges() {
        MeetingPackageProductOffset mpp1 = getMeetingPackageProductOffset("MPP_1", 1);
        mpp1.setId(11);
        mpp1.setSeasonName("season1");
        mpp1.setStartDate(LocalDate.of(2024, 10, 12));
        mpp1.setEndDate(LocalDate.of(2024, 10, 19));
        MeetingPackageProductOffset mpp2 = getMeetingPackageProductOffset("MPP_1", 1);
        mpp2.setId(12);
        mpp2.setSeasonName("season1");
        mpp2.setStartDate(LocalDate.of(2024, 10, 12));
        mpp2.setEndDate(LocalDate.of(2024, 10, 19));
        MeetingPackageProductOffset dbDataMock1 = mpp1.clone();
        dbDataMock1.setEndDate(LocalDate.of(2024, 10, 20));
        MeetingPackageProductOffset dbDataMock2 = mpp2.clone();
        dbDataMock2.setEndDate(LocalDate.of(2024, 10, 20));
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setId(1);
        when(meetingPackageProductRepository.getMPProductOffsets(1)).thenReturn(List.of(dbDataMock1, dbDataMock2));
        meetingPackageProductService.checkForSyncFlagForOffsets(1, List.of(mpp1, mpp2));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_INDEPENDENT_PRODUCT_CHANGED);
    }


    @Test
    void getMeetingProductDefinitionDetailsOfLinkedProduct() {
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setDependentProductId(12);
        MeetingProductDayPart dayPart = new MeetingProductDayPart();
        meetingPackageProduct.setMeetingProductDayPart(dayPart);
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        MeetingPackageProductMeetingRoom packageProductMeetingRoom = getPackageProductMeetingRoom(meetingPackageProduct);
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(List.of(packageProductMeetingRoom));
        MeetingPackageElement pkg1 = getMeetingPackageElement(12, "pkg1");
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = new FunctionSpaceRevenueGroup();
        pkg1.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroup);
        MeetingPackageElement pkg2 = getMeetingPackageElement(13, "pkg2");
        pkg2.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroup);
        MeetingPackageProduct dependentProduct = getMeetingPackageProduct("MP_2");
        MeetingPackageProductMeetingRoom dependentProductMeetingRoom = getPackageProductMeetingRoom(meetingPackageProduct);
        when(meetingPackageProductRepository.getMeetingPackageProduct(12)).thenReturn(dependentProduct);
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(12)).thenReturn(List.of(dependentProductMeetingRoom));
        BaseMeetingRoom baseMeetingRoom = getBaseMeetingRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom(), MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN);
        baseMeetingRoom.getFunctionSpaceFunctionRoom().setId(112);
        when(meetingPackageProductRepository.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);
        when(meetingPackageProductRepository.getMeetingPackageElements()).thenReturn(List.of(pkg1, pkg2));
        when(meetingPackageProductRepository.getMeetingPackageElementsForProduct(1)).thenReturn(List.of(getMeetingProductPackageElement(1, pkg1, meetingPackageProduct)));
        MeetingPackageProductDefinitionDTO meetingProductDefinitionDetails = meetingPackageProductService.getMeetingProductDefinitionDetails(1);
        List<MeetingPackageElementDTO> availablePackageElements = meetingProductDefinitionDetails.getAvailablePackageElements();
        List<MeetingPackageElementDTO> selectedPackageElements = meetingProductDefinitionDetails.getSelectedPackageElements();
        assertEquals(1, availablePackageElements.size());
        assertEquals(13, availablePackageElements.get(0).getId());
        assertEquals(1, selectedPackageElements.size());
        assertEquals(12, selectedPackageElements.get(0).getId());
    }

    @Test
    void testSaveMeetingPackagePricingProductSeasonOffset(){
        MeetingPackageProductOffsetsDTO dto = new MeetingPackageProductOffsetsDTO();
        dto.setSeasonName("S1");
        dto.setStartDate(LocalDate.now());
        dto.setEndDate(LocalDate.now());
        List<MeetingPackageProductOffsetsDTO> productOffset = List.of(dto);
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        MeetingPackageProductMeetingRoom packageProductMeetingRoom = getPackageProductMeetingRoom(meetingPackageProduct);
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(List.of(packageProductMeetingRoom));
        meetingPackageProductService.saveMeetingRoomsProductOffset(1, productOffset);
        verify(seasonService).saveSeason(anyList(), any());
    }

    @Test
    void testGetMPPConfiguration() {
        when(configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.MAX_IP_MEETING_PACKAGE_PRODUCT.value())).thenReturn(2);
        when(dateService.getCaughtUpLocalDateTime()).thenReturn(LocalDateTime.of(2011,11,11,20,0, 0));
        MPPConfiguration mppConfigurations = meetingPackageProductService.getMPPConfigurations();
        assertEquals(2, mppConfigurations.getMaxIndependentProduct());
    }

    private BaseMeetingRoom getBaseMeetingRoom(FunctionSpaceFunctionRoom functionSpaceFunctionRoom, MinimumIncrementMethod minimumIncrementMethod, BigDecimal minimumIncrementValue){
        BaseMeetingRoom baseMeetingRoom = new BaseMeetingRoom();
        baseMeetingRoom.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        baseMeetingRoom.setMinimumIncrementMethod(minimumIncrementMethod);
        baseMeetingRoom.setMinimumIncrementValue(minimumIncrementValue);
        baseMeetingRoom.setPriceExcluded(true);
        return baseMeetingRoom;
    }

    private MeetingPackageProduct getMeetingPackageProduct(String productName){
        return MeetingPackageProductBuilder.createIndependentMPProduct(productName, TenantStatusEnum.ACTIVE);
    }

    private MeetingPackageProduct getLinkedMPProduct(String productName, Integer dependentProductId) {
        return MeetingPackageProductBuilder.createLinkedMPProduct(productName, dependentProductId);
    }

    @Test
    void testGetMPPConfigForUsePackageIDTrue() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_MEETING_PACKAGE_PRODUCT_PACKAGE_ID)).thenReturn(true);
        MPPConfiguration mppConfigurations = meetingPackageProductService.getMPPConfigurations();
        assertTrue(mppConfigurations.isUsePackageId());
    }

    @Test
    void testGetMPPConfigForUsePackageIDFalse() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_MEETING_PACKAGE_PRODUCT_PACKAGE_ID)).thenReturn(false);
        MPPConfiguration mppConfigurations = meetingPackageProductService.getMPPConfigurations();
        assertFalse(mppConfigurations.isUsePackageId());
    }

    @Test
    void testSaveCeilingFloor(){
        MeetingPackageProductBaseRoomCeilingFloorDTO dto = new MeetingPackageProductBaseRoomCeilingFloorDTO();
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        List<BaseMeetingRoom> baseMeetingRoomList = List.of(getBaseMeetingRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom(), MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN));
        BaseMeetingRoom baseMeetingRoom = baseMeetingRoomList.get(0);
        dto.setBaseMeetingRoomId(1);

        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        when(meetingPackageProductRepository.getBaseMeetingRoom(1)).thenReturn(baseMeetingRoom);
        meetingPackageProductService.saveMeetingProductCeilingFloor(1, dto);
        verify(meetingPackageProductRepository).saveMeetingPackageProductCeilingFloor(any());
    }

    @Test
    void testSaveMeetingPackagePricingProductSeasonCeilingFloor(){
        MeetingPackageProductBaseRoomCeilingFloorDTO dto = new MeetingPackageProductBaseRoomCeilingFloorDTO();
        dto.setSeasonName("S1");
        dto.setStartDate(LocalDate.now());
        dto.setEndDate(LocalDate.now());

        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        List<BaseMeetingRoom> baseMeetingRoomList = List.of(getBaseMeetingRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom(), MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN));
        BaseMeetingRoom baseMeetingRoom = baseMeetingRoomList.get(0);
        dto.setBaseMeetingRoomId(1);

        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        when(meetingPackageProductRepository.getBaseMeetingRoom(1)).thenReturn(baseMeetingRoom);
        meetingPackageProductService.saveMeetingProductCeilingFloor(1, dto);
        verify(seasonService).saveSeason(anyList(), any());
    }

    @Test
    void testGetMeetingProductCeilingFloor(){
        List<MeetingPackageProductCeilingFloor> meetingPackageProductCeilingFloor = List.of(getMeetingPackageProductCeilingFloor("MPP_1", 1, 1, BigDecimal.TEN, BigDecimal.ONE));
        when(meetingPackageProductRepository.getMeetingPackageProductCeilingFloor(1)).thenReturn(meetingPackageProductCeilingFloor);

        MeetingPackageProductCeilingFloorResponseDTO meetingPackageProductCeilingFloorResponseDTO = meetingPackageProductService.getMeetingProductCeilingFloor(1);

        assertNotNull(meetingPackageProductCeilingFloorResponseDTO);
        assertNotNull(meetingPackageProductCeilingFloorResponseDTO.getCeilingFloor());
        assertEquals(1, meetingPackageProductCeilingFloorResponseDTO.getCeilingFloor().size());
    }

    @Test
    void testDeleteMeetingProductCeilingFloor(){
        meetingPackageProductService.deleteMeetingProductCeilingFloor(1, "season");
        verify(meetingPackageProductRepository).deleteMeetingProductCeilingFloor(1, "season");
    }

    @Test
    void testSaveBaseMeetingRoom(){
        MeetingRoomDTO meetingRoomDTO = new MeetingRoomDTO();
        meetingRoomDTO.setId(10);
        meetingRoomDTO.setName("MR1");

        BaseMeetingRoomDTO baseMeetingRoomDTO = new BaseMeetingRoomDTO();
        baseMeetingRoomDTO.setId(1);
        baseMeetingRoomDTO.setBaseMeetingRoom(meetingRoomDTO);
        baseMeetingRoomDTO.setPriceExcluded(false);
        baseMeetingRoomDTO.setMinimumChangeMethod(new MinChangeMethodDTO("Fixed",MinimumIncrementMethod.FIXED_OFFSET.name()));
        baseMeetingRoomDTO.setMinimumChangeValue(BigDecimal.TEN);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(10);
        functionSpaceFunctionRoom.setName("MR1");

        MeetingPackageProduct product1 = getMeetingPackageProduct("P1");
        product1.setStatus(TenantStatusEnum.ACTIVE);
        MeetingPackageProduct product2 = getMeetingPackageProduct("P2");
        product1.setStatus(TenantStatusEnum.INACTIVE);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom1 = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom1.setId(11);
        functionSpaceFunctionRoom1.setName("MR2");

        BaseMeetingRoom baseMeetingRoom = new BaseMeetingRoom();
        baseMeetingRoom.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom1);
        when(meetingPackageProductRepository.getFunctionSpaceFunctionRoom(baseMeetingRoomDTO.getBaseMeetingRoom().getId())).thenReturn(functionSpaceFunctionRoom);
        when(meetingPackageProductRepository.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);
        when(meetingPackageProductRepository.getMeetingPackageProducts()).thenReturn(Arrays.asList(product1, product2));
        ArgumentCaptor<List<MeetingPackageProduct>> listCaptor = ArgumentCaptor.forClass(List.class);

        meetingPackageProductService.saveBaseMeetingRoom(baseMeetingRoomDTO);

        var captor = ArgumentCaptor.forClass(BaseMeetingRoom.class);
        verify(meetingPackageProductRepository).saveBaseMeetingRoom(captor.capture());

        var savedData = captor.getValue();
        assertEquals(1, savedData.getId());
        assertEquals(10, savedData.getFunctionSpaceFunctionRoom().getId());
        assertFalse(savedData.isPriceExcluded());
        assertEquals(MinimumIncrementMethod.FIXED_OFFSET, savedData.getMinimumIncrementMethod());
        assertEquals(BigDecimal.TEN, savedData.getMinimumIncrementValue());
        verify(meetingPackageProductRepository).saveMeetingPackageProducts(listCaptor.capture());

        List<MeetingPackageProduct> value = listCaptor.getValue();
        assertTrue(value.stream().allMatch(product -> TenantStatusEnum.INVALID.equals(product.getStatus())
                && InvalidReason.INDEPENDENT_PRODUCT_MISSING_CEILING_FLOOR.equals(product.getInvalidReason())));
        verify(syncEventAggregatorService,never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_MINIMUM_PRICE_CHANGED);
    }


    private static Stream<Arguments> provideMinimumIncrementChangeScenarios() {
        return Stream.of(
                // Value changed
                Arguments.of(MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.ONE, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN),
                // Method changed
                Arguments.of(MinimumIncrementMethod.PERCENTAGE, BigDecimal.TEN, MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN)
        );
    }

    @ParameterizedTest
    @MethodSource("provideMinimumIncrementChangeScenarios")
    void testSaveBaseMeetingRoom_WithRegisteringMinimumPriceChangeSyncEvent(
            MinimumIncrementMethod oldMethod,
            BigDecimal oldValue,
            MinimumIncrementMethod newMethod,
            BigDecimal newValue
    ) {
        MeetingRoomDTO meetingRoomDTO = new MeetingRoomDTO();
        meetingRoomDTO.setId(10);
        meetingRoomDTO.setName("MR1");

        BaseMeetingRoomDTO baseMeetingRoomDTO = new BaseMeetingRoomDTO();
        baseMeetingRoomDTO.setId(1);
        baseMeetingRoomDTO.setBaseMeetingRoom(meetingRoomDTO);
        baseMeetingRoomDTO.setPriceExcluded(false);
        baseMeetingRoomDTO.setMinimumChangeMethod(new MinChangeMethodDTO("Fixed", newMethod.name()));
        baseMeetingRoomDTO.setMinimumChangeValue(newValue);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(10);
        functionSpaceFunctionRoom.setName("MR1");

        BaseMeetingRoom existingBaseMeetingRoom = new BaseMeetingRoom();
        existingBaseMeetingRoom.setId(1);
        existingBaseMeetingRoom.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        existingBaseMeetingRoom.setMinimumIncrementMethod(oldMethod);
        existingBaseMeetingRoom.setMinimumIncrementValue(oldValue);

        BaseMeetingRoom updatedBaseMeetingRoom = new BaseMeetingRoom();
        updatedBaseMeetingRoom.setId(1);
        updatedBaseMeetingRoom.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        updatedBaseMeetingRoom.setMinimumIncrementMethod(newMethod);
        updatedBaseMeetingRoom.setMinimumIncrementValue(newValue);

        when(meetingPackageProductRepository.getFunctionSpaceFunctionRoom(baseMeetingRoomDTO.getBaseMeetingRoom().getId())).thenReturn(functionSpaceFunctionRoom);
        when(meetingPackageProductRepository.getBaseMeetingRoom()).thenReturn(existingBaseMeetingRoom);
        when(meetingPackageProductRepository.saveBaseMeetingRoom(updatedBaseMeetingRoom)).thenReturn(updatedBaseMeetingRoom);

        try (MockedStatic<MeetingPackagePricingMapper> mockedMapper = mockStatic(MeetingPackagePricingMapper.class)) {
            mockedMapper.when(() -> MeetingPackagePricingMapper.mapToBaseMeetingRoomEntity(functionSpaceFunctionRoom, baseMeetingRoomDTO)).thenReturn(updatedBaseMeetingRoom);
            mockedMapper.when(() -> MeetingPackagePricingMapper.toBaseMeetingRoomDTO(updatedBaseMeetingRoom)).thenReturn(baseMeetingRoomDTO);

            BaseMeetingRoomDTO savedBaseMeetingRoomDTO = meetingPackageProductService.saveBaseMeetingRoom(baseMeetingRoomDTO);

            assertEquals(updatedBaseMeetingRoom.getId(), savedBaseMeetingRoomDTO.getId());
            assertEquals(updatedBaseMeetingRoom.getFunctionSpaceFunctionRoom().getId(), savedBaseMeetingRoomDTO.getBaseMeetingRoom().getId());
            assertFalse(savedBaseMeetingRoomDTO.isPriceExcluded());
            assertEquals(updatedBaseMeetingRoom.getMinimumIncrementMethod(), MinimumIncrementMethod.valueOf(savedBaseMeetingRoomDTO.getMinimumChangeMethod().getValue()));
            assertEquals(updatedBaseMeetingRoom.getMinimumIncrementValue(), savedBaseMeetingRoomDTO.getMinimumChangeValue());
            verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_MINIMUM_PRICE_CHANGED);
        }
    }

    @Test
    void testDeleteProductOffsets(){
        meetingPackageProductService.deleteMPProductOffsets(1, "season");
        verify(meetingPackageProductRepository).deleteMPProductOffsets(1, "season");
    }

    @Test
    void testLowestFloorForProduct(){
        LocalDate caughtUpDate = LocalDate.of(2025, 10, 10);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        List<MeetingProductConfigurationData> productDetails = List.of(getMeetingPackageProductDetails());
        when(meetingPackageProductRepository.getLowestFloorForEachProduct(caughtUpDate)).thenReturn(productDetails);
        BaseMeetingRoom baseMeetingRoom = getBaseMeetingRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom(),
                MinimumIncrementMethod.PERCENTAGE, BigDecimal.TEN);
        when(meetingPackageProductRepository.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);
        List<MeetingProductConfigurationData> productWithLowestFloor = meetingPackageProductService.getProductWithLowestFloor();
        assertEquals(10d, productWithLowestFloor.get(0).getLowestFloor().doubleValue());
        assertEquals(20d, productWithLowestFloor.get(0).getLowestCeil().doubleValue());
        verify(meetingPackageProductRepository).getLowestFloorForEachProduct(caughtUpDate);
    }

    private static MeetingProductConfigurationData getMeetingPackageProductDetails() {
        MeetingProductConfigurationData detail = new MeetingProductConfigurationData();
        detail.setLowestFloor(BigDecimal.TEN);
        detail.setLowestCeil(BigDecimal.valueOf(20L));
        detail.setProductId(1);
        detail.setProductType("LINKED");
        detail.setProductName("productName");
        detail.setSelectedMeetingRooms(List.of(new MeetingRoomDTO()));
        return detail;
    }

    @Test
    void testSaveProductDefinitionDetails(){
        MeetingProductDayPartDTO meetingProductDayPartDTO = new MeetingProductDayPartDTO();
        meetingProductDayPartDTO.setId(11);
        meetingProductDayPartDTO.setName("FullDay");

        MeetingPackageProductDTO meetingPackageProductDTO = new MeetingPackageProductDTO();
        meetingPackageProductDTO.setId(1);
        meetingPackageProductDTO.setName("MPP1");
        meetingPackageProductDTO.setFloorType(FloorType.RELATIVE_TO_PRIMARY_FLOOR.getId());
        meetingPackageProductDTO.setOffsetMethod(AgileRatesOffsetMethod.FIXED.getId());
        meetingPackageProductDTO.setRoundingRule(RoundingRule.PRICE_ROUNDING.getId());
        meetingPackageProductDTO.setStatusId(1);

        MeetingRoomDTO meetingRoomDTO1 = getMeetingRoomDTO(15, "MR1");
        MeetingRoomDTO meetingRoomDTO2 = getMeetingRoomDTO(16, "MR2");

        MeetingPackageProductDefinitionDTO meetingPackageProductDefinitionDTO = new MeetingPackageProductDefinitionDTO();
        meetingPackageProductDefinitionDTO.setProduct(meetingPackageProductDTO);
        meetingPackageProductDefinitionDTO.setDayPart(meetingProductDayPartDTO);
        meetingPackageProductDefinitionDTO.setAvailableMeetingRooms(List.of(meetingRoomDTO1));
        meetingPackageProductDefinitionDTO.setSelectedMeetingRooms(List.of(meetingRoomDTO2));

        MeetingProductDayPart meetingProductDayPart = new MeetingProductDayPart();
        meetingProductDayPart.setId(11);
        meetingProductDayPart.setName("FullDay");

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(16);
        functionSpaceFunctionRoom.setName("MR2");

        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setMeetingProductDayPart(meetingProductDayPart);

        when(meetingPackageProductRepository.getMeetingProductDayPart(meetingPackageProductDefinitionDTO.getDayPart().getId())).thenReturn(meetingProductDayPart);
        when(meetingPackageProductRepository.getMeetingRooms(List.of(meetingRoomDTO2.getId()))).thenReturn(List.of(functionSpaceFunctionRoom));
        when(meetingPackageProductRepository.saveMeetingPackageProduct(meetingPackageProduct)).thenReturn(meetingPackageProduct);
        MeetingPackageProductDefinitionDTO savedResponse = meetingPackageProductService.saveMeetingProductDefinitionDetails(meetingPackageProductDefinitionDTO);

        assertNotNull(savedResponse);
        assertEquals("MPP1",savedResponse.getProduct().getName());
        var productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        verify(meetingPackageProductRepository, times(2)).saveMeetingPackageProduct(productArgumentCaptor.capture());
        var savedMeetingPackageProduct = productArgumentCaptor.getValue();
        assertEquals("MPP1", savedMeetingPackageProduct.getName());

        var productMeetingRoomArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageProductRepository).saveMeetingProductMeetingRooms(productMeetingRoomArgumentCaptor.capture());
        var savedProductMeetingRooms = productMeetingRoomArgumentCaptor.getValue();
        assertEquals(1,savedProductMeetingRooms.size());
    }

    @Test
    void testSaveProductDefinitionDetailsIfOneMRDeleted(){
        MeetingProductDayPartDTO meetingProductDayPartDTO = new MeetingProductDayPartDTO();
        meetingProductDayPartDTO.setId(11);
        meetingProductDayPartDTO.setName("FullDay");

        MeetingPackageProductDTO meetingPackageProductDTO = new MeetingPackageProductDTO();
        meetingPackageProductDTO.setId(1);
        meetingPackageProductDTO.setName("MPP1");
        meetingPackageProductDTO.setFloorType(FloorType.RELATIVE_TO_PRIMARY_FLOOR.getId());
        meetingPackageProductDTO.setOffsetMethod(AgileRatesOffsetMethod.FIXED.getId());
        meetingPackageProductDTO.setRoundingRule(RoundingRule.PRICE_ROUNDING.getId());
        meetingPackageProductDTO.setStatusId(1);

        MeetingRoomDTO meetingRoomDTO1 = getMeetingRoomDTO(15, "MR1");
        MeetingRoomDTO meetingRoomDTO2 = getMeetingRoomDTO(16, "MR2");

        MeetingPackageProductDefinitionDTO meetingPackageProductDefinitionDTO = new MeetingPackageProductDefinitionDTO();
        meetingPackageProductDefinitionDTO.setProduct(meetingPackageProductDTO);
        meetingPackageProductDefinitionDTO.setDayPart(meetingProductDayPartDTO);
        meetingPackageProductDefinitionDTO.setAvailableMeetingRooms(List.of(meetingRoomDTO1));
        meetingPackageProductDefinitionDTO.setSelectedMeetingRooms(List.of(meetingRoomDTO2));

        MeetingProductDayPart meetingProductDayPart = new MeetingProductDayPart();
        meetingProductDayPart.setId(11);
        meetingProductDayPart.setName("FullDay");

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(16);
        functionSpaceFunctionRoom.setName("MR2");

        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setMeetingProductDayPart(meetingProductDayPart);
        MeetingPackageProductMeetingRoom packageProductMeetingRoom = getPackageProductMeetingRoom(meetingPackageProduct);
        MeetingPackageProduct linkedProduct = getMeetingPackageProduct("MPP2");
        linkedProduct.setId(2);
        linkedProduct.setDependentProductId(1);

        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(List.of(packageProductMeetingRoom));
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(2)).thenReturn(List.of(packageProductMeetingRoom));
        when(meetingPackageProductRepository.getDependentMeetingPackageProducts(1)).thenReturn(List.of(linkedProduct));
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        when(meetingPackageProductRepository.getDefaultMPPAdjustments(2)).thenReturn(2L);

        when(meetingPackageProductRepository.getMeetingProductDayPart(meetingPackageProductDefinitionDTO.getDayPart().getId())).thenReturn(meetingProductDayPart);
        when(meetingPackageProductRepository.getMeetingRooms(List.of(meetingRoomDTO2.getId()))).thenReturn(List.of(functionSpaceFunctionRoom));
        when(meetingPackageProductRepository.saveMeetingPackageProduct(meetingPackageProduct)).thenReturn(meetingPackageProduct);
        MeetingPackageProductDefinitionDTO savedResponse = meetingPackageProductService.saveMeetingProductDefinitionDetails(meetingPackageProductDefinitionDTO);

        assertNotNull(savedResponse);
        assertEquals("MPP1",savedResponse.getProduct().getName());
        var productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        verify(meetingPackageProductRepository, times(2)).saveMeetingPackageProduct(productArgumentCaptor.capture());
        var savedMeetingPackageProduct = productArgumentCaptor.getValue();
        assertEquals("MPP1", savedMeetingPackageProduct.getName());

        var productMeetingRoomArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageProductRepository).saveMeetingProductMeetingRooms(productMeetingRoomArgumentCaptor.capture());
        var savedProductMeetingRooms = productMeetingRoomArgumentCaptor.getValue();
        assertEquals(1,savedProductMeetingRooms.size());

        var deletedMeetingRoomMCapture = ArgumentCaptor.forClass(List.class);
        var productIDCapture = ArgumentCaptor.forClass(Integer.class);
        verify(meetingPackageProductRepository).deleteMeetingPackageProductMeetingRooms(productIDCapture.capture(), deletedMeetingRoomMCapture.capture());
        var deletedMeetingRoomCaptureValue = deletedMeetingRoomMCapture.getValue();
        assertEquals(1, deletedMeetingRoomCaptureValue.size());
        assertEquals(12, deletedMeetingRoomCaptureValue.get(0));

        var deletedDependentMRCapture = ArgumentCaptor.forClass(List.class);
        var dependentProductIdCapture = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageProductRepository).deleteMeetingPackageProductMeetingRooms(dependentProductIdCapture.capture(), deletedDependentMRCapture.capture());
        var deletedMeetingRoomMCaptureValue = deletedDependentMRCapture.getValue();
        assertEquals(1, deletedMeetingRoomMCaptureValue.size());
        assertEquals(12, deletedMeetingRoomMCaptureValue.get(0));
        var productIDs = dependentProductIdCapture.getValue();
        assertEquals(1, productIDs.size());
        assertEquals(2, productIDs.get(0));
    }


    @Test
    void testSaveLinkedProductDefinitionWithTwoNewPackageElmSelected(){
        MeetingProductDayPartDTO meetingProductDayPartDTO = new MeetingProductDayPartDTO();
        meetingProductDayPartDTO.setId(11);
        meetingProductDayPartDTO.setName("FullDay");

        MeetingPackageProductDTO meetingPackageProductDTO = getMeetingPackageProductDTO();
        MeetingRoomDTO meetingRoomDTO1 = getMeetingRoomDTO(15, "MR1");
        MeetingRoomDTO meetingRoomDTO2 = getMeetingRoomDTO(16, "MR2");

        MeetingPackageElementDTO packageElementDTO1 = getMeetingPackageElementDTO(2, "MPE1");
        MeetingPackageElementDTO packageElementDTO2 = getMeetingPackageElementDTO(3, "MPE2");
        MeetingPackageElementDTO packageElementDTO3 = getMeetingPackageElementDTO(4, "MPE3");

        MeetingPackageElement packageElement1 = getMeetingPackageElement(2, "MPE1");
        MeetingPackageElement packageElement2 = getMeetingPackageElement(3, "MPE2");
        MeetingPackageElement packageElement3 = getMeetingPackageElement(4, "MPE3");

        MeetingProductDayPart meetingProductDayPart = new MeetingProductDayPart();
        meetingProductDayPart.setId(11);
        meetingProductDayPart.setName("FullDay");

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(16);
        functionSpaceFunctionRoom.setName("MR2");

        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setMeetingProductDayPart(meetingProductDayPart);

        MeetingPackageProductDefinitionDTO meetingPackageProductDefinitionDTO = new MeetingPackageProductDefinitionDTO();
        meetingPackageProductDefinitionDTO.setProduct(meetingPackageProductDTO);
        meetingPackageProductDefinitionDTO.setDayPart(meetingProductDayPartDTO);
        meetingPackageProductDefinitionDTO.setAvailableMeetingRooms(List.of(meetingRoomDTO1));
        meetingPackageProductDefinitionDTO.setSelectedMeetingRooms(List.of(meetingRoomDTO2));
        meetingPackageProductDefinitionDTO.setAvailablePackageElements(List.of(packageElementDTO1));
        meetingPackageProductDefinitionDTO.setSelectedPackageElements(List.of(packageElementDTO2, packageElementDTO3));

        when(meetingPackageProductRepository.getMeetingPackageElements()).thenReturn(List.of(packageElement1, packageElement2, packageElement3));
        when(meetingPackageProductRepository.getMeetingProductDayPart(meetingPackageProductDefinitionDTO.getDayPart().getId())).thenReturn(meetingProductDayPart);
        when(meetingPackageProductRepository.getMeetingRooms(List.of(meetingRoomDTO2.getId()))).thenReturn(List.of(functionSpaceFunctionRoom));
        when(meetingPackageProductRepository.saveMeetingPackageProduct(meetingPackageProduct)).thenReturn(meetingPackageProduct);
        MeetingPackageProductDefinitionDTO savedResponse = meetingPackageProductService.saveMeetingProductDefinitionDetails(meetingPackageProductDefinitionDTO);

        assertNotNull(savedResponse);
        assertEquals("MPP1",savedResponse.getProduct().getName());
        var productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        verify(meetingPackageProductRepository, times(2)).saveMeetingPackageProduct(productArgumentCaptor.capture());
        var savedMeetingPackageProduct = productArgumentCaptor.getAllValues();
        assertEquals("MPP1", savedMeetingPackageProduct.get(0).getName());

        var productMeetingRoomArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageProductRepository).saveMeetingPackageElementsForProduct(productMeetingRoomArgumentCaptor.capture());
        var savedProductMeetingRooms = productMeetingRoomArgumentCaptor.getValue();
        assertEquals(2,savedProductMeetingRooms.size());
    }

    @Test
    void testSaveLinkedProductDefinitionWithSelectedPkgElmUnselected(){
        MeetingProductDayPartDTO meetingProductDayPartDTO = new MeetingProductDayPartDTO();
        meetingProductDayPartDTO.setId(11);
        meetingProductDayPartDTO.setName("FullDay");

        MeetingPackageProductDTO meetingPackageProductDTO = getMeetingPackageProductDTO();
        MeetingRoomDTO meetingRoomDTO1 = getMeetingRoomDTO(15, "MR1");
        MeetingRoomDTO meetingRoomDTO2 = getMeetingRoomDTO(16, "MR2");

        MeetingProductDayPart meetingProductDayPart = new MeetingProductDayPart();
        meetingProductDayPart.setId(11);
        meetingProductDayPart.setName("FullDay");

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(16);
        functionSpaceFunctionRoom.setName("MR2");

        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setMeetingProductDayPart(meetingProductDayPart);

        MeetingPackageElementDTO packageElementDTO1 = getMeetingPackageElementDTO(2, "MPE1");
        MeetingPackageElementDTO packageElementDTO2 = getMeetingPackageElementDTO(3, "MPE2");
        MeetingPackageElementDTO packageElementDTO3 = getMeetingPackageElementDTO(4, "MPE3");

        MeetingPackageProductDefinitionDTO meetingPackageProductDefinitionDTO = new MeetingPackageProductDefinitionDTO();
        meetingPackageProductDefinitionDTO.setProduct(meetingPackageProductDTO);
        meetingPackageProductDefinitionDTO.setDayPart(meetingProductDayPartDTO);
        meetingPackageProductDefinitionDTO.setAvailableMeetingRooms(List.of(meetingRoomDTO1));
        meetingPackageProductDefinitionDTO.setSelectedMeetingRooms(List.of(meetingRoomDTO2));
        meetingPackageProductDefinitionDTO.setAvailablePackageElements(List.of(packageElementDTO1, packageElementDTO2, packageElementDTO3));
        meetingPackageProductDefinitionDTO.setSelectedPackageElements(List.of());

        when(meetingPackageProductRepository.getMeetingProductDayPart(meetingPackageProductDefinitionDTO.getDayPart().getId())).thenReturn(meetingProductDayPart);
        when(meetingPackageProductRepository.getMeetingRooms(List.of(meetingRoomDTO2.getId()))).thenReturn(List.of(functionSpaceFunctionRoom));
        when(meetingPackageProductRepository.saveMeetingPackageProduct(meetingPackageProduct)).thenReturn(meetingPackageProduct);
        meetingPackageProductService.saveMeetingProductDefinitionDetails(meetingPackageProductDefinitionDTO);

        verify(meetingPackageProductRepository).deleteMeetingProductPackageElements(1);
    }

    @Test
    void testDeleteLinkedProductAdjustmentAndOverridesChanged(){
        MeetingProductDayPartDTO meetingProductDayPartDTO = new MeetingProductDayPartDTO();
        meetingProductDayPartDTO.setId(11);
        meetingProductDayPartDTO.setName("FullDay");

        MeetingPackageProductDTO meetingPackageProductDTO = getMeetingPackageProductDTO();
        MeetingRoomDTO meetingRoomDTO1 = getMeetingRoomDTO(15, "MR1");
        MeetingRoomDTO meetingRoomDTO2 = getMeetingRoomDTO(16, "MR2");

        MeetingProductDayPart meetingProductDayPart = new MeetingProductDayPart();
        meetingProductDayPart.setId(11);
        meetingProductDayPart.setName("FullDay");

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(16);
        functionSpaceFunctionRoom.setName("MR2");

        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setMeetingProductDayPart(meetingProductDayPart);
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        meetingPackageProduct.setStatus(TenantStatusEnum.ACTIVE);

        MeetingPackageProductDefinitionDTO meetingPackageProductDefinitionDTO = new MeetingPackageProductDefinitionDTO();
        meetingPackageProductDefinitionDTO.setProduct(meetingPackageProductDTO);
        meetingPackageProductDefinitionDTO.setDayPart(meetingProductDayPartDTO);
        meetingPackageProductDefinitionDTO.setAvailableMeetingRooms(List.of(meetingRoomDTO1));
        meetingPackageProductDefinitionDTO.setSelectedMeetingRooms(List.of(meetingRoomDTO2));
        meetingPackageProductDefinitionDTO.setSelectedPackageElements(List.of());

        when(meetingPackageProductRepository.getMeetingProductDayPart(meetingPackageProductDefinitionDTO.getDayPart().getId())).thenReturn(meetingProductDayPart);
        when(meetingPackageProductRepository.getMeetingRooms(List.of(meetingRoomDTO2.getId()))).thenReturn(List.of(functionSpaceFunctionRoom));
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        when(meetingPackageProductRepository.saveMeetingPackageProduct(meetingPackageProduct)).thenReturn(meetingPackageProduct);
        LocalDate caughtUpDate = LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);
        meetingPackageProductService.saveMeetingProductDefinitionDetails(meetingPackageProductDefinitionDTO);

        verify(meetingPackageProductRepository).deleteMeetingProductAdjustmentByProductId(1);
        verify(meetingPackageDecisionService).deleteMPDecisionBarOutputsByProduct(meetingPackageProduct, caughtUpDate);
        verify(meetingPackageDecisionService).deleteMPDecisionBarOutputOverridesByProduct(meetingPackageProduct, caughtUpDate);
        verify(meetingPackageDecisionService).deleteMPPaceDecisionBarOutputByProduct(meetingPackageProduct, caughtUpDate);
    }

    @Test
    void shouldInvalidateProductIfMissingBaseProduct(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        meetingPackageProduct.setStatus(TenantStatusEnum.ACTIVE);
        meetingPackageProduct.setDependentProductId(null);
        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertEquals(InvalidReason.MISSING_BASE_PRODUCT, productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, productArgumentCaptor.getValue().getStatus());
    }

    @Test
    void shouldInvalidateProductIfBaseProductIsInvalidate(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        meetingPackageProduct.setStatus(TenantStatusEnum.ACTIVE);
        meetingPackageProduct.setDependentProductId(4);
        MeetingPackageProduct baseProduct = getMeetingPackageProduct("baseProduct");
        baseProduct.setStatus(TenantStatusEnum.INVALID);
        when(meetingPackageProductRepository.getMeetingPackageProduct(4)).thenReturn(baseProduct);
        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertEquals(InvalidReason.INVALID_BASE_PRODUCT, productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, productArgumentCaptor.getValue().getStatus());
    }


    @Test
    void shouldInvalidateProductIfMissingAdjustment(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        meetingPackageProduct.setStatus(TenantStatusEnum.ACTIVE);
        meetingPackageProduct.setDependentProductId(1);
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(List.of(getPackageProductMeetingRoom(meetingPackageProduct)));
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(getMeetingPackageProduct("baseProduct"));
        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        when(meetingPackageProductRepository.getDefaultMPPAdjustments(meetingPackageProduct.getId())).thenReturn(0L);
        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertEquals(InvalidReason.MISSING_RATE_ADJUSTMENT, productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, productArgumentCaptor.getValue().getStatus());
    }

    @Test
    void shouldHaveNullInvalidReasonIdIfValidLinkedProductConfiguration(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setStatus(TenantStatusEnum.ACTIVE);
        meetingPackageProduct.setInvalidReason(InvalidReason.MISSING_RATE_OFFSETS);
        meetingPackageProduct.setDependentProductId(1);
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(getMeetingPackageProduct("baseProduct"));
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(List.of(getPackageProductMeetingRoom(meetingPackageProduct)));
        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        when(meetingPackageProductRepository.getDefaultMPPAdjustments(meetingPackageProduct.getId())).thenReturn(1L);
        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertNull(productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INACTIVE, productArgumentCaptor.getValue().getStatus());
    }

    @Test
    void shouldHaveMissingRoomTypeInvalidReasonForNoRoomTypesSelected(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setStatus(TenantStatusEnum.ACTIVE);
        meetingPackageProduct.setInvalidReason(InvalidReason.MISSING_RATE_OFFSETS);
        meetingPackageProduct.setDependentProductId(1);
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(getMeetingPackageProduct("baseProduct"));
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(List.of());
        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertNotNull(productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(InvalidReason.MISSING_ACCOM_TYPES, productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, productArgumentCaptor.getValue().getStatus());
    }

    @Test
    void shouldInvalidateProductWithoutCeilingFloorForIndependentProduct(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("INDEPENDENTLY");
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        meetingPackageProduct.setStatus(TenantStatusEnum.INACTIVE);
        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);

        when(meetingPackageProductRepository.getMeetingPackageProductCeilingFloor(meetingPackageProduct.getId())).thenReturn(new ArrayList<>());
        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertEquals(InvalidReason.INDEPENDENT_PRODUCT_MISSING_CEILING_FLOOR, productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, productArgumentCaptor.getValue().getStatus());
    }

    @Test
    void shouldInvalidateProductWithoutOffsetForIndependentProduct() {
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("INDEPENDENTLY");
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        meetingPackageProduct.setStatus(TenantStatusEnum.INACTIVE);
        MeetingPackageProductMeetingRoom mr1 = new MeetingPackageProductMeetingRoom();
        MeetingPackageProductMeetingRoom mr2 = new MeetingPackageProductMeetingRoom();

        MeetingPackageProductOffset mpOffset1 = new MeetingPackageProductOffset();
        mpOffset1.setSeasonName("season 1");
        MeetingPackageProductOffset mpOffset2 = new MeetingPackageProductOffset();

        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        MeetingPackageProductCeilingFloor ceilingFloor = getMeetingPackageProductCeilingFloor("MPP_1", 1, 1, BigDecimal.TEN, BigDecimal.ONE);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(LocalDate.of(2025, 6, 6));
        MeetingPackageProductCeilingFloor seasonCeilingFloor = getMeetingPackageProductCeilingFloor("MPP_1", 1, 1, BigDecimal.TEN, BigDecimal.ONE);
        seasonCeilingFloor.setSeasonName("season1");
        seasonCeilingFloor.setStartDate(LocalDate.of(2025, 6, 7));
        seasonCeilingFloor.setEndDate(LocalDate.of(2025, 6, 17));

        when(meetingPackageProductRepository.getMeetingPackageProductCeilingFloor(meetingPackageProduct.getId())).thenReturn(List.of(ceilingFloor, seasonCeilingFloor));
        when(meetingPackageProductRepository.getMeetingProductMeetingRooms(1)).thenReturn(List.of(mr1, mr2));
        when(meetingPackageProductRepository.getMPProductOffsets(1)).thenReturn(List.of(mpOffset1, mpOffset2));

        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertEquals(InvalidReason.MISSING_RATE_OFFSETS, productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, productArgumentCaptor.getValue().getStatus());
    }

    @Test
    void shouldInvalidateProductWithoutSeasonCeilingFloorValuesForIndependentProduct(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("INDEPENDENTLY");
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        meetingPackageProduct.setStatus(TenantStatusEnum.INACTIVE);
        MeetingPackageProductMeetingRoom mr1 = new MeetingPackageProductMeetingRoom();
        MeetingPackageProductMeetingRoom mr2 = new MeetingPackageProductMeetingRoom();

        MeetingPackageProductOffset mpOffset1 = new MeetingPackageProductOffset();
        mpOffset1.setSeasonName("season 1");

        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        MeetingPackageProductCeilingFloor ceilingFloor = getMeetingPackageProductCeilingFloor("MPP_1", 1, 1, BigDecimal.TEN, BigDecimal.ONE);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(LocalDate.of(2025, 6, 6));
        MeetingPackageProductCeilingFloor seasonCeilingFloor = getMeetingPackageProductCeilingFloor("MPP_1", 1, 1, null, null);
        seasonCeilingFloor.setSeasonName("season1");
        seasonCeilingFloor.setStartDate(LocalDate.of(2025, 6, 7));
        seasonCeilingFloor.setEndDate(LocalDate.of(2025, 6, 17));

        when(meetingPackageProductRepository.getMeetingPackageProductCeilingFloor(meetingPackageProduct.getId())).thenReturn(List.of(ceilingFloor, seasonCeilingFloor));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(LocalDate.of(2025, 6, 6));

        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertEquals(InvalidReason.MISSING_SEASON_CEILING_FLOOR, productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID, productArgumentCaptor.getValue().getStatus());
    }

    @Test
    void shouldNotInvalidatedProductWithCeilingFloorAndOffsetForIndependentProduct(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("INDEPENDENTLY");
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        meetingPackageProduct.setStatus(TenantStatusEnum.ACTIVE);
        meetingPackageProduct.setInvalidReason(InvalidReason.INDEPENDENT_PRODUCT_MISSING_CEILING_FLOOR);
        ArgumentCaptor<MeetingPackageProduct> productArgumentCaptor = ArgumentCaptor.forClass(MeetingPackageProduct.class);
        MeetingPackageProductCeilingFloor ceilingFloor = getMeetingPackageProductCeilingFloor("MPP_1", 1, 1, BigDecimal.TEN, BigDecimal.ONE);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(LocalDate.of(2025, 6, 6));
        MeetingPackageProductCeilingFloor seasonCeilingFloor = getMeetingPackageProductCeilingFloor("MPP_1", 1, 1, BigDecimal.TEN, BigDecimal.ONE);
        seasonCeilingFloor.setSeasonName("season1");
        seasonCeilingFloor.setStartDate(LocalDate.of(2025, 6, 7));
        seasonCeilingFloor.setEndDate(LocalDate.of(2025, 6, 17));

        when(meetingPackageProductRepository.getMeetingPackageProductCeilingFloor(meetingPackageProduct.getId())).thenReturn(List.of(ceilingFloor, seasonCeilingFloor));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(LocalDate.of(2025, 6, 6));
        meetingPackageProductService.invalidateProductIfMissingConfiguration(meetingPackageProduct);

        verify(meetingPackageProductRepository).saveMeetingPackageProduct(productArgumentCaptor.capture());
        assertNull(productArgumentCaptor.getValue().getInvalidReason());
        assertEquals(TenantStatusEnum.INACTIVE, productArgumentCaptor.getValue().getStatus());
    }

    private static MeetingPackageProductDTO getMeetingPackageProductDTO() {
        MeetingPackageProductDTO meetingPackageProductDTO = new MeetingPackageProductDTO();
        meetingPackageProductDTO.setId(1);
        meetingPackageProductDTO.setName("MPP1");
        meetingPackageProductDTO.setFloorType(FloorType.RELATIVE_TO_PRIMARY_FLOOR.getId());
        meetingPackageProductDTO.setOffsetMethod(AgileRatesOffsetMethod.FIXED.getId());
        meetingPackageProductDTO.setRoundingRule(RoundingRule.PRICE_ROUNDING.getId());
        meetingPackageProductDTO.setType("LINKED");
        meetingPackageProductDTO.setStatusId(1);
        return meetingPackageProductDTO;
    }

    private static MeetingRoomDTO getMeetingRoomDTO(Integer id, String name) {
        MeetingRoomDTO meetingRoomDTO = new MeetingRoomDTO();
        meetingRoomDTO.setId(id);
        meetingRoomDTO.setName(name);
        return meetingRoomDTO;
    }

    @Test
    void testGetMeetingPackageElements(){
        MeetingPackageElement elm1 = getMeetingPackageElement(1, "Pkg1");
        elm1.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        elm1.setOffsetValue(BigDecimal.TEN);
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = new FunctionSpaceRevenueGroup();
        functionSpaceRevenueGroup.setName("Food");
        functionSpaceRevenueGroup.setProfitPercent(BigDecimal.ONE);
        functionSpaceRevenueGroup.setId(1);
        elm1.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroup);

        MeetingPackageElement elm2 = getMeetingPackageElement(2, "Pkg2");
        elm2.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        elm2.setOffsetValue(BigDecimal.TEN);
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup2 = new FunctionSpaceRevenueGroup();
        functionSpaceRevenueGroup2.setName("AV");
        functionSpaceRevenueGroup2.setProfitPercent(BigDecimal.TEN);
        functionSpaceRevenueGroup2.setId(2);
        elm2.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroup2);

        when(meetingPackageProductRepository.getMeetingPackageElements()).thenReturn(List.of(elm1, elm2));
        List<MeetingPackageElementDTO> packageElements = meetingPackageProductService.getMeetingPackageElements();
        assertEquals(2, packageElements.size());
    }

    private static MeetingPackageElement getMeetingPackageElement(int id, String name) {
        MeetingPackageElement packageElement = new MeetingPackageElement();
        packageElement.setId(id);
        packageElement.setName(name);
        packageElement.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        return packageElement;
    }

    private static MeetingProductMeetingPackageElement getMeetingProductPackageElement(int id, MeetingPackageElement name, MeetingPackageProduct product) {
        MeetingProductMeetingPackageElement elm1 = new MeetingProductMeetingPackageElement();
        elm1.setId(id);
        elm1.setMeetingPackageElement(name);
        elm1.setMeetingPackageProduct(product);
        return elm1;
    }


    private static MeetingPackageElementDTO getMeetingPackageElementDTO(int id, String name) {
        MeetingPackageElementDTO elm = new MeetingPackageElementDTO();
        elm.setId(id);
        elm.setName(name);
        return elm;
    }

    @Test
    void testSaveMeetingProductDayPart(){
        MeetingProductDayPartDTO meetingProductDayPartDTO = new MeetingProductDayPartDTO();
        meetingProductDayPartDTO.setId(1);
        meetingProductDayPartDTO.setName("AM");
        meetingProductDayPartDTO.setStartTime(LocalTime.of(12,0));
        meetingProductDayPartDTO.setEndTime(LocalTime.of(16,0));
        meetingProductDayPartDTO.setStatus(Boolean.TRUE);

        List<MeetingProductDayPart> meetingProductDayPart = MeetingPackagePricingMapper.mapToMeetingProductDayPartEntity(new ArrayList<>(), List.of(meetingProductDayPartDTO));
        when(meetingPackageProductRepository.getMeetingProductDayParts()).thenReturn(emptyList());
        when(meetingPackageProductRepository.saveMeetingProductDayPart(meetingProductDayPart)).thenReturn(meetingProductDayPart);
        List<MeetingProductDayPartDTO> saveMeetingProductDayPart = meetingPackageProductService.saveMeetingProductDayPart(List.of(meetingProductDayPartDTO));

        verify(meetingPackageProductRepository).saveMeetingProductDayPart(any());
        verify(syncEventAggregatorService,never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_MINIMUM_PRICE_CHANGED);
        assertNotNull(saveMeetingProductDayPart);
        assertEquals("AM",saveMeetingProductDayPart.get(0).getName());
        assertEquals(LocalTime.of(12,0), saveMeetingProductDayPart.get(0).getStartTime());
        assertEquals(LocalTime.of(16,0), saveMeetingProductDayPart.get(0).getEndTime());
        assertFalse(saveMeetingProductDayPart.get(0).isUsedInProduct());
    }

    @Test
    void testSaveMeetingProductDayPart_WithRegisteringDayPartsChangedSyncEvent(){
        MeetingProductDayPartDTO meetingProductDayPartDTOFromDB = new MeetingProductDayPartDTO();
        meetingProductDayPartDTOFromDB.setId(1);
        meetingProductDayPartDTOFromDB.setName("AM");
        meetingProductDayPartDTOFromDB.setStartTime(LocalTime.of(10,0));
        meetingProductDayPartDTOFromDB.setEndTime(LocalTime.of(14,0));
        meetingProductDayPartDTOFromDB.setStatus(Boolean.TRUE);
        List<MeetingProductDayPart> savedMeetingProductDayParts = MeetingPackagePricingMapper.mapToMeetingProductDayPartEntity(new ArrayList<>(), List.of(meetingProductDayPartDTOFromDB));

        MeetingProductDayPartDTO meetingProductDayPartIncomingDTO = new MeetingProductDayPartDTO();
        meetingProductDayPartIncomingDTO.setId(1);
        meetingProductDayPartIncomingDTO.setName("AM");
        meetingProductDayPartIncomingDTO.setStartTime(LocalTime.of(8,0));
        meetingProductDayPartIncomingDTO.setEndTime(LocalTime.of(14,0));
        meetingProductDayPartIncomingDTO.setStatus(Boolean.TRUE);
        List<MeetingProductDayPart> meetingProductIncomingDayParts = MeetingPackagePricingMapper.mapToMeetingProductDayPartEntity(new ArrayList<>(), List.of(meetingProductDayPartIncomingDTO));

        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setMeetingProductDayPart(meetingProductIncomingDayParts.get(0));
        when(meetingPackageProductRepository.getMeetingProductDayParts()).thenReturn(savedMeetingProductDayParts);
        when(meetingPackageProductRepository.saveMeetingProductDayPart(meetingProductIncomingDayParts)).thenReturn(meetingProductIncomingDayParts);
        when(meetingPackageProductRepository.getMeetingPackageProductsByDayParts(List.of(1))).thenReturn(List.of(meetingPackageProduct));
        when(meetingPackageProductRepository.getMeetingPackageProductsByAllDayParts(List.of(1))).thenReturn(List.of(meetingPackageProduct));

        List<MeetingProductDayPartDTO> saveMeetingProductDayPart = meetingPackageProductService.saveMeetingProductDayPart(List.of(meetingProductDayPartIncomingDTO));

        assertNotNull(saveMeetingProductDayPart);
        assertEquals("AM",saveMeetingProductDayPart.get(0).getName());
        assertEquals(LocalTime.of(8,0), saveMeetingProductDayPart.get(0).getStartTime());
        assertEquals(LocalTime.of(14,0), saveMeetingProductDayPart.get(0).getEndTime());
        assertTrue(saveMeetingProductDayPart.get(0).isUsedInProduct());
        verify(meetingPackageProductRepository).saveMeetingProductDayPart(any());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_INDEPENDENT_PRODUCT_CHANGED);
    }

    @Test
    void testDeleteMeetingProductDayPart(){
        meetingPackageProductService.deleteMeetingProductDayPart(1);
        verify(meetingPackageProductRepository).deleteMeetingProductDayPart(1);
    }

    @Test
    void testDeleteMeetingPackageProduct(){
        LocalDate caughtUpDate = LocalDate.of(2025,4,1);
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MPP1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setStatus(TenantStatusEnum.ACTIVE);
        MeetingPackageProduct linkedMPProduct1 = getLinkedMPProduct("MP_L_1",meetingPackageProduct.getId());
        MeetingPackageProduct linkedMPProduct2 = getLinkedMPProduct("MP_L_2",meetingPackageProduct.getId());
        List<MeetingPackageProduct> linkedMPProductList = List.of(linkedMPProduct1, linkedMPProduct2);

        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        when(meetingPackageProductRepository.getDependentMeetingPackageProducts(1)).thenReturn(linkedMPProductList);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(caughtUpDate);

        meetingPackageProductService.deleteMeetingPackageProduct(1);

        assertEquals(TenantStatusEnum.DELETED,meetingPackageProduct.getStatus());
        assertEquals(TenantStatusEnum.INVALID,linkedMPProduct1.getStatus());
        assertEquals(InvalidReason.INVALID_BASE_PRODUCT,linkedMPProduct1.getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID,linkedMPProduct2.getStatus());
        assertEquals(InvalidReason.INVALID_BASE_PRODUCT,linkedMPProduct2.getInvalidReason());
        verify(dateService).getCaughtUpJavaLocalDate();
        verify(meetingPackageProductRepository).getMeetingPackageProduct(1);
        verify(meetingPackageDecisionService).deleteMPDecisionBarOutputsByProduct(meetingPackageProduct, caughtUpDate);
        verify(meetingPackageDecisionService).deleteMPDecisionBarOutputOverridesByProduct(meetingPackageProduct, caughtUpDate);
        verify(meetingPackageDecisionService).deleteMPPaceDecisionBarOutputByProduct(meetingPackageProduct, caughtUpDate);
        verify(meetingPackageProductRepository).deleteCeilingFloorMappingsForProduct(meetingPackageProduct);
        verify(meetingPackageProductRepository).deleteOffsetMappingsForProduct(meetingPackageProduct);
        verify(meetingPackageProductRepository).deleteProductNonBaseMeetingRooms(meetingPackageProduct);
        verify(meetingPackageProductRepository).saveMeetingPackageProduct(meetingPackageProduct);
        verify(meetingPackageProductRepository, atMostOnce()).saveMeetingPackageProduct(meetingPackageProduct);
        verify(meetingPackageProductRepository, atMostOnce()).saveMeetingPackageProducts(linkedMPProductList);
    }

    private MeetingPackageProductOffset getMeetingPackageProductOffset(String productName, int productID){
        MeetingPackageProduct mp1 = getMeetingPackageProduct(productName);
        mp1.setId(1);
        MeetingPackageProductMeetingRoom meetingRoom = getPackageProductMeetingRoom(mp1);

        return MeetingPackageProductOffsetBuilder.createMPProductOffset(mp1, meetingRoom,BigDecimal.valueOf(20));
    }

    @Test
    void shouldGetCeilingAndFloorByOccupancyDateAndProductId() {
        LocalDate occupancyDate = LocalDate.of(2024, 4, 17);
        Integer productId = 123;
        CeilingFloor ceilingFloor = new CeilingFloor();
        ceilingFloor.setCeilingRate(BigDecimal.valueOf(230.0));
        ceilingFloor.setFloorRate(BigDecimal.valueOf(100.0));
        ceilingFloor.setMpProductId(123);
        ceilingFloor.setFunctionRoomId(2);
        when(meetingPackageProductRepository.findCeilingAndFloorBy(occupancyDate, productId))
                .thenReturn(ceilingFloor);

        CeilingFloor result = meetingPackageProductService.getCeilingAndFloorByOccupancyDateAndProductId(occupancyDate, productId);

        assertNotNull(result);
        assertEquals(ceilingFloor.getCeilingRate(), result.getCeilingRate());
        assertEquals(ceilingFloor.getFloorRate(), result.getFloorRate());
        verify(meetingPackageProductRepository).findCeilingAndFloorBy(occupancyDate, productId);
    }

    @Test
    void shouldGetCeilingAndFloorForDateRangeAndProductIds() {
        LocalDate startDate = LocalDate.of(2022, 9, 6);
        LocalDate endDate = LocalDate.of(2022, 9, 10);
        List<Integer> productIds = List.of(1, 2, 3, 4);
        when(meetingPackageProductRepository.findCeilingAndFloorBy(startDate, endDate, productIds)).thenReturn(singletonList(new CeilingFloor()));

        List<CeilingFloor> result = meetingPackageProductService.getCeilingAndFloorForDateRangeAndProductIds(startDate, endDate, productIds);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(meetingPackageProductRepository).findCeilingAndFloorBy(startDate, endDate, productIds);
    }

    @Test
    void shouldGetProductRateOffsetByOccupancyDateAndProductId() {
        LocalDate occupancyDate = LocalDate.of(2024, 4, 17);
        Integer productId = 123;
        ProductRateOffset productRateOffset = new ProductRateOffset();
        productRateOffset.setCeilingRate(BigDecimal.valueOf(100.0));
        productRateOffset.setFloorRate(BigDecimal.valueOf(100.0));
        productRateOffset.setMpProductId(123);
        when(meetingPackageProductRepository.getProductRateOffsetsByProductAndOccupancyDate(occupancyDate, productId))
                .thenReturn(productRateOffset);

        ProductRateOffset mpProductOffset = meetingPackageProductService.getProductRateOffsetsByProductAndOccupancyDate(occupancyDate, productId);

        assertNotNull(mpProductOffset);
        assertEquals(productRateOffset.getMpProductId(), mpProductOffset.getMpProductId());
        assertEquals(productRateOffset.getCeilingRate(), mpProductOffset.getCeilingRate());
        assertEquals(productRateOffset.getFloorRate(), mpProductOffset.getFloorRate());
        verify(meetingPackageProductRepository).getProductRateOffsetsByProductAndOccupancyDate(occupancyDate, productId);
    }

    @Test
    void shouldGetRoundingRulesForBarProduct(){
        Integer productId = 1;
        PricingRule pricingRule = buildPricingRule();

        when(prettyPricingService.getPricingRule(productId)).thenReturn(pricingRule);

        RoundingRuleConfiguration roundingRulesForProduct = meetingPackageProductService.getRoundingRulesForProduct(productId);
        assertPricingRoundingRulesForProduct(roundingRulesForProduct);
    }

    @Test
    void shouldSaveMPProductAdjustment() {
        MeetingPackageProductRateOffsetDTO productRateOffsetDTO = new MeetingPackageProductRateOffsetDTO();
        productRateOffsetDTO.setOffsetMethod(1);
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        meetingPackageProductService.saveMPProductAdjustment(1, List.of(productRateOffsetDTO));
        verify(meetingPackageProductRepository).saveMeetingPackageProductRateAdjustments(any());
    }

    @Test
    void shouldGetMPProductAdjustments() {
        List<MeetingPackageProductRateOffset> packageProductRateOffsets = List.of(MeetingPackageProductRateOffsetBuilder.createMPProductRateOffset(getMeetingPackageProduct("MPP_1"),
                BigDecimal.ONE, BigDecimal.TEN));
        when(meetingPackageProductRepository.getMeetingPackageProductRateAdjustments(1)).thenReturn(packageProductRateOffsets);
        MeetingPackageProductRateOffsetResponseDTO rateOffsetResponseDTO = meetingPackageProductService.getMPProductAdjustments(1);
        assertNotNull(rateOffsetResponseDTO);
        assertEquals(1, rateOffsetResponseDTO.getOffsets().size());
    }

    @Test
    void shouldNotEnableSyncIfNoChangesInAdjustmentForSeason() {
        MeetingPackageProductRateOffset productRateOffset1 = getMeetingPackageProductRateOffset("season 2", 1, LocalDate.of(2024, 10, 12), LocalDate.of(2024, 10, 19), BigDecimal.TEN);
        MeetingPackageProductRateOffset productRateOffset2 = getMeetingPackageProductRateOffset("season 1", 2, LocalDate.of(2024, 10, 12), LocalDate.of(2024, 10, 19), BigDecimal.TEN);
        when(meetingPackageProductRepository.getMeetingPackageProductRateAdjustments(1)).thenReturn(List.of(productRateOffset1, productRateOffset2));
        meetingPackageProductService.checkForSyncFlagForAdjustments(1, List.of(productRateOffset1.clone()));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void shouldEnableSyncIfStartDateOrEndDateChanges() {
        MeetingPackageProductRateOffset productRateOffset1 = getMeetingPackageProductRateOffset("season 2", 1, LocalDate.of(2024, 10, 12), LocalDate.of(2024, 10, 19), BigDecimal.TEN);
        MeetingPackageProductRateOffset productRateOffset2 = getMeetingPackageProductRateOffset("season 1", 2, LocalDate.of(2024, 10, 12), LocalDate.of(2024, 10, 19), BigDecimal.TEN);
        when(meetingPackageProductRepository.getMeetingPackageProductRateAdjustments(1)).thenReturn(List.of(productRateOffset1, productRateOffset2));
        MeetingPackageProductRateOffset newData = productRateOffset1.clone();
        newData.setStartDate(LocalDate.of(2024, 10, 11));
        meetingPackageProductService.checkForSyncFlagForAdjustments(1, List.of(newData));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
        newData = productRateOffset1.clone();
        newData.setEndDate(LocalDate.of(2024, 10, 11));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void shouldEnableSyncIfAdjustmentValueChanges() {
        MeetingPackageProductRateOffset productRateOffset1 = getMeetingPackageProductRateOffset("season 2", 1, LocalDate.of(2024, 10, 12), LocalDate.of(2024, 10, 19), BigDecimal.TEN);
        MeetingPackageProductRateOffset productRateOffset2 = getMeetingPackageProductRateOffset("season 1", 2, LocalDate.of(2024, 10, 12), LocalDate.of(2024, 10, 19), BigDecimal.TEN);
        when(meetingPackageProductRepository.getMeetingPackageProductRateAdjustments(1)).thenReturn(List.of(productRateOffset1, productRateOffset2));
        MeetingPackageProductRateOffset newData = productRateOffset1.clone();
        newData.setSaturdayOffsetValueFloor(BigDecimal.ONE);
        newData.setSaturdayOffsetValueCeiling(BigDecimal.ONE);
        meetingPackageProductService.checkForSyncFlagForAdjustments(1, List.of(newData));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void shouldSaveMPProductAdjustmentForSeason() {
        MeetingPackageProductRateOffset productRateOffset = getMeetingPackageProductRateOffset("season 2", 1, LocalDate.of(2024, 10, 12), LocalDate.of(2024, 10, 19), BigDecimal.TEN);
        MeetingPackageProductRateOffsetDTO productRateOffsetDTO = new MeetingPackageProductRateOffsetDTO();
        productRateOffsetDTO.setId(1);
        productRateOffsetDTO.setOffsetMethod(1);
        productRateOffsetDTO.setSeasonName("season1");
        productRateOffsetDTO.setStartDate(LocalDate.now());
        productRateOffsetDTO.setEndDate(LocalDate.now().plusDays(2));
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        when(meetingPackageProductRepository.getMeetingPackageProductRateAdjustments(1)).thenReturn(List.of(productRateOffset));
        meetingPackageProductService.saveMPProductAdjustment(1, List.of(productRateOffsetDTO));
        verify(seasonService).saveSeason(anyList(), any());
    }

    private static MeetingPackageProductRateOffset getMeetingPackageProductRateOffset(String season, Integer id, LocalDate startDate, LocalDate endDate,
                                                                                      BigDecimal adjustment) {
        MeetingPackageProductRateOffset productRateOffset = new MeetingPackageProductRateOffset();
        productRateOffset.setId(id);
        productRateOffset.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        productRateOffset.setSeasonName(season);
        productRateOffset.setStartDate(startDate);
        productRateOffset.setEndDate(endDate);
        productRateOffset.setSaturdayOffsetValueCeiling(adjustment);
        productRateOffset.setSaturdayOffsetValueFloor(adjustment);
        return productRateOffset;
    }

    @Test
    void shouldDeleteMPProductAdjustments() {
        meetingPackageProductService.deleteMeetingProductAdjustment(1, "season");
        verify(meetingPackageProductRepository).deleteMPPRateOffsetByProductIdAndSeason(1, "season");
    }

    @Test
    void testDeleteMeetingPackageElement(){
        meetingPackageProductService.deleteMeetingPackageElement(1);
        verify(meetingPackageProductRepository).deleteMeetingPackageElement(1);
    }

    @Test
    void testProductConfigWhenRequestStatusIsInActiveAndCurrentStatusIsActive(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setId(1);
        MeetingPackageProduct linkedMPProduct1 = getLinkedMPProduct("MP_L_1",meetingPackageProduct.getId());
        linkedMPProduct1.setStatus(TenantStatusEnum.DELETED);
        linkedMPProduct1.setDependentProductId(1);
        MeetingPackageProduct linkedMPProduct2 = getLinkedMPProduct("MP_L_2",meetingPackageProduct.getId());
        linkedMPProduct2.setDependentProductId(1);
        List<MeetingPackageProduct> linkedMPProductList = List.of(linkedMPProduct1, linkedMPProduct2);
        when(meetingPackageProductRepository.getMeetingPackageProduct(1)).thenReturn(meetingPackageProduct);
        when(meetingPackageProductRepository.getDependentMeetingPackageProducts(1)).thenReturn(linkedMPProductList);
        meetingPackageProductService.manageProduct(1,false,false,false);

        verify(meetingPackageProductRepository, atMostOnce()).saveMeetingPackageProducts(linkedMPProductList);
        verify(meetingPackageProductRepository, atMostOnce()).saveMeetingPackageProduct(meetingPackageProduct);

        assertEquals(TenantStatusEnum.INACTIVE,meetingPackageProduct.getStatus());
        assertNull(meetingPackageProduct.getInvalidReason());
        assertEquals(TenantStatusEnum.DELETED,linkedMPProduct1.getStatus());
        assertNull(linkedMPProduct1.getInvalidReason());
        assertEquals(TenantStatusEnum.INVALID,linkedMPProduct2.getStatus());
        assertEquals(InvalidReason.INVALID_BASE_PRODUCT,linkedMPProduct2.getInvalidReason());
    }

    @Test
    void testProductConfigWhenRequestStatusIsActiveAndCurrentStatusIsInActive(){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setStatus(TenantStatusEnum.INACTIVE);
        MeetingPackageProduct linkedMPProduct1 = getLinkedMPProduct("MP_L_1",meetingPackageProduct.getId());
        linkedMPProduct1.setStatus(TenantStatusEnum.INVALID);
        linkedMPProduct1.setInvalidReason(InvalidReason.INVALID_BASE_PRODUCT);
        MeetingPackageProduct linkedMPProduct2 = getLinkedMPProduct("MP_L_2",meetingPackageProduct.getId());
        linkedMPProduct2.setStatus(TenantStatusEnum.INVALID);
        linkedMPProduct2.setInvalidReason(InvalidReason.INVALID_BASE_PRODUCT);
        List<MeetingPackageProduct> linkedMPProductList = List.of(linkedMPProduct1, linkedMPProduct2);
        when(meetingPackageProductRepository.getMeetingPackageProduct(anyInt())).thenReturn(meetingPackageProduct);
        when(meetingPackageProductRepository.getDependentMeetingPackageProducts(any())).thenReturn(linkedMPProductList);
        meetingPackageProductService.manageProduct(1,true,true,true);
        verify(meetingPackageProductRepository, atMostOnce()).saveMeetingPackageProduct(meetingPackageProduct);
        verify(meetingPackageProductRepository, atMostOnce()).saveMeetingPackageProduct(linkedMPProduct1);
        verify(meetingPackageProductRepository, atMostOnce()).saveMeetingPackageProduct(linkedMPProduct2);


        assertEquals(TenantStatusEnum.ACTIVE,meetingPackageProduct.getStatus());
        assertNull(meetingPackageProduct.getInvalidReason());
        assertEquals(TenantStatusEnum.INACTIVE,linkedMPProduct1.getStatus());
        assertNull(linkedMPProduct1.getInvalidReason());
        assertEquals(TenantStatusEnum.INACTIVE,linkedMPProduct2.getStatus());
        assertNull(linkedMPProduct2.getInvalidReason());
    }

    @Test
    void testProductConfigWhenRequestStatusIsInActiveAndCentrallyManagedAsTrue(){

        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        meetingPackageProduct.setCentrallyManaged(false);
        meetingPackageProduct.setUpload(false);
        meetingPackageProduct.setStatus(TenantStatusEnum.INVALID);
        meetingPackageProduct.setInvalidReason(InvalidReason.MISSING_SEASON_OFFSETS);
        when(meetingPackageProductRepository.getMeetingPackageProduct(anyInt())).thenReturn(meetingPackageProduct);
        meetingPackageProductService.manageProduct(1,false,true,true);
        verify(meetingPackageProductRepository, atMostOnce()).saveMeetingPackageProduct(meetingPackageProduct);
        assertTrue(meetingPackageProduct.isCentrallyManaged());
        assertFalse(meetingPackageProduct.isUpload());
    }


    @Test
    void testSaveMeetingPackageElement(){
        MeetingPackageElementDTO meetingPackageElementDTO = new MeetingPackageElementDTO();
        meetingPackageElementDTO.setId(1);
        meetingPackageElementDTO.setName("AM");
        meetingPackageElementDTO.setOffsetValue(BigDecimal.TEN);

        RevenueGroupDto revenueGroupDto = new RevenueGroupDto();
        revenueGroupDto.setId(1);
        revenueGroupDto.setName("Food");
        revenueGroupDto.setProfitPercent(BigDecimal.ONE);

        meetingPackageElementDTO.setRevenueStream(revenueGroupDto);

        List<MeetingPackageElement> meetingPackageElements = MeetingPackagePricingMapper.mapToMeetingPackageElementEntity(new ArrayList<>(), List.of(meetingPackageElementDTO));
        when(meetingPackageProductRepository.getMeetingPackageElements()).thenReturn(emptyList());
        when(meetingPackageProductRepository.saveMeetingPackageElements(meetingPackageElements)).thenReturn(meetingPackageElements);

        List<MeetingPackageElementDTO> saveMeetingPackageElements = meetingPackageProductService.saveMeetingPackageElement(List.of(meetingPackageElementDTO));

        verify(meetingPackageProductRepository).saveMeetingPackageElements(any());
        verify(syncEventAggregatorService,never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
        assertNotNull(saveMeetingPackageElements);
        assertEquals("AM",saveMeetingPackageElements.get(0).getName());
        assertEquals(BigDecimal.TEN, saveMeetingPackageElements.get(0).getOffsetValue());
        assertEquals("Food", saveMeetingPackageElements.get(0).getRevenueStream().getName());
        assertEquals(BigDecimal.ONE, saveMeetingPackageElements.get(0).getRevenueStream().getProfitPercent());
    }

    @Test
    void testSaveMeetingPackageElement_WithRegisteringPkgElementsChangedSyncEvent(){
        MeetingPackageElementDTO meetingPackageElementDTOFromDB = new MeetingPackageElementDTO();
        meetingPackageElementDTOFromDB.setId(1);
        meetingPackageElementDTOFromDB.setName("AM");
        meetingPackageElementDTOFromDB.setOffsetValue(BigDecimal.TEN);
        RevenueGroupDto revenueGroupDto = new RevenueGroupDto();
        revenueGroupDto.setId(1);
        revenueGroupDto.setName("Food");
        revenueGroupDto.setProfitPercent(BigDecimal.ONE);
        meetingPackageElementDTOFromDB.setRevenueStream(revenueGroupDto);
        List<MeetingPackageElement> savedMeetingPackageElements = MeetingPackagePricingMapper.mapToMeetingPackageElementEntity(new ArrayList<>(), List.of(meetingPackageElementDTOFromDB));

        MeetingPackageElementDTO meetingPackageElementIncomingDTO = new MeetingPackageElementDTO();
        meetingPackageElementIncomingDTO.setId(1);
        meetingPackageElementIncomingDTO.setName("AM");
        meetingPackageElementIncomingDTO.setOffsetValue(BigDecimal.ONE);
        RevenueGroupDto revenueGroupIncomingDto = new RevenueGroupDto();
        revenueGroupIncomingDto.setId(1);
        revenueGroupIncomingDto.setName("Food");
        revenueGroupIncomingDto.setProfitPercent(BigDecimal.ONE);
        meetingPackageElementIncomingDTO.setRevenueStream(revenueGroupIncomingDto);
        List<MeetingPackageElement> incomingMeetingPackageElements = MeetingPackagePricingMapper.mapToMeetingPackageElementEntity(new ArrayList<>(), List.of(meetingPackageElementIncomingDTO));

        when(meetingPackageProductRepository.getMeetingPackageElements()).thenReturn(savedMeetingPackageElements);
        when(meetingPackageProductRepository.saveMeetingPackageElements(incomingMeetingPackageElements)).thenReturn(incomingMeetingPackageElements);
        when(meetingPackageProductRepository.getMeetingPackageProductsByPkgElements(List.of(1))).thenReturn(List.of(getMeetingPackageProduct("MPP1")));
        when(meetingPackageProductRepository.getMeetingPackageElements(List.of(1))).thenReturn(List.of(getMeetingPackageElement(1,"pkg1")));

        List<MeetingPackageElementDTO> saveMeetingProductElements = meetingPackageProductService.saveMeetingPackageElement(List.of(meetingPackageElementIncomingDTO));

        assertNotNull(saveMeetingProductElements);
        assertEquals("AM",saveMeetingProductElements.get(0).getName());
        assertEquals(BigDecimal.ONE, saveMeetingProductElements.get(0).getOffsetValue());
        assertTrue(saveMeetingProductElements.get(0).isUsedInProduct());
        verify(meetingPackageProductRepository).saveMeetingPackageElements(any());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void shouldGetMappedMeetingRooms() {
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct("MP_1");
        MeetingPackageProductMeetingRoom packageProductMeetingRoom = getPackageProductMeetingRoom(meetingPackageProduct);
        FunctionSpaceFunctionRoom meetingRoom = packageProductMeetingRoom.getFunctionSpaceFunctionRoom();

        when(meetingPackageProductRepository.getAllDistinctMeetingPackageProductMeetingRooms())
                .thenReturn(List.of(meetingRoom));

        List<MeetingRoomDTO> meetingRoomDTOS = meetingPackageProductService.getMappedMeetingRooms();
        assertEquals(1,meetingRoomDTOS.size());
        assertEquals(meetingRoom.getName(),meetingRoomDTOS.get(0).getName());
        assertEquals(meetingRoom.getId(),meetingRoomDTOS.get(0).getId());
    }

    @Test
    void verifyGetActiveMeetingPackageProducts() {
        List<MeetingPackageProduct> meetingPackageProducts = List.of(new MeetingPackageProduct());
        when(meetingPackageProductRepository.getActiveMeetingPackageProducts()).thenReturn(meetingPackageProducts);

        List<MeetingPackageProduct> activeMeetingPackageProducts = meetingPackageProductService.getActiveMeetingPackageProducts();

        assertEquals(meetingPackageProducts, activeMeetingPackageProducts);
    }

    private void assertPricingRoundingRulesForProduct(RoundingRuleConfiguration roundingRulesForProduct) {
        assertEquals(Collections.emptyList(), roundingRulesForProduct.getTenThousands());
        assertEquals(Collections.emptyList(), roundingRulesForProduct.getThousands());
        assertEquals(Collections.emptyList(), roundingRulesForProduct.getHundreds());
        assertEquals(List.of(8), roundingRulesForProduct.getTens());
        assertEquals(List.of(1,2), roundingRulesForProduct.getOnes());
        assertEquals(9,roundingRulesForProduct.getTenths());
        assertNull(roundingRulesForProduct.getHundredths());
    }

    private PricingRule buildPricingRule() {
        PricingRule pricingRule = new PricingRule();
        pricingRule.addRule(PricingDigit.ONES, getPricingRulesRow(PricingDigit.ONES, "1,2"));
        pricingRule.addRule(PricingDigit.TENS, getPricingRulesRow(PricingDigit.TENS, "8"));
        pricingRule.addRule(PricingDigit.TENTHS, getPricingRulesRow(PricingDigit.TENTHS,"9"));
        return pricingRule;
    }

    private PrettyPricingRuleRow getPricingRulesRow(PricingDigit pricingDigit, String validDigits) {
        PrettyPricingRuleRow prettyPricingRuleRow = new PrettyPricingRuleRow();
        prettyPricingRuleRow.setProductId(1);
        prettyPricingRuleRow.setDigit(pricingDigit);
        prettyPricingRuleRow.setRuleNumbers(validDigits);
        return prettyPricingRuleRow;
    }


    private static MeetingPackageProductMeetingRoom getPackageProductMeetingRoom(MeetingPackageProduct mp1) {
        MeetingPackageProductMeetingRoom meetingRoom = new MeetingPackageProductMeetingRoom();
        FunctionSpaceFunctionRoom functionRoom = new FunctionSpaceFunctionRoom();
        functionRoom.setName("STD");
        functionRoom.setId(12);
        meetingRoom.setMeetingPackageProduct(mp1);
        meetingRoom.setFunctionSpaceFunctionRoom(functionRoom);
        return meetingRoom;
    }

    private MeetingPackageProductCeilingFloor getMeetingPackageProductCeilingFloor(String productName, int productId, int baseMeetingRoomId, BigDecimal ceilingRate, BigDecimal floorRate){
        MeetingPackageProduct meetingPackageProduct = getMeetingPackageProduct(productName);
        meetingPackageProduct.setId(productId);
        BaseMeetingRoom baseMeetingRoom = getBaseMeetingRoom(FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom(), MinimumIncrementMethod.FIXED_OFFSET, BigDecimal.TEN);
        baseMeetingRoom.setId(baseMeetingRoomId);
        MeetingPackageProductCeilingFloor productCeilingFloor = new MeetingPackageProductCeilingFloor();
        productCeilingFloor.setMeetingPackageProduct(meetingPackageProduct);
        productCeilingFloor.setBaseMeetingRoom(baseMeetingRoom);
        productCeilingFloor.setSundayCeilingRate(ceilingRate);
        productCeilingFloor.setSundayFloorRate(floorRate);
        productCeilingFloor.setMondayCeilingRate(ceilingRate);
        productCeilingFloor.setMondayFloorRate(floorRate);
        productCeilingFloor.setTuesdayCeilingRate(ceilingRate);
        productCeilingFloor.setTuesdayFloorRate(floorRate);
        productCeilingFloor.setWednesdayCeilingRate(ceilingRate);
        productCeilingFloor.setWednesdayFloorRate(floorRate);
        productCeilingFloor.setThursdayCeilingRate(ceilingRate);
        productCeilingFloor.setThursdayFloorRate(floorRate);
        productCeilingFloor.setFridayCeilingRate(ceilingRate);
        productCeilingFloor.setFridayFloorRate(floorRate);
        productCeilingFloor.setSaturdayCeilingRate(ceilingRate);
        productCeilingFloor.setSaturdayFloorRate(floorRate);
        productCeilingFloor.setStatus(Status.ACTIVE);
        return  productCeilingFloor;
    }

}
