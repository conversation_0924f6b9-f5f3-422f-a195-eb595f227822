package com.ideas.tetris.pacman.services.partition;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.partition.TenantPartitionEnum;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import org.junit.jupiter.api.Test;

import java.sql.SQLException;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

class TableMetadataServiceTest extends AbstractG3JupiterTest {

    @Test
    void getTableMetadata() throws SQLException {
        // Use the base class methods to get properly configured services
        CrudService tenantCrudService = tenantCrudService();
        JpaJdbcUtil jpaJdbcUtil = new JpaJdbcUtil();

        TableMetadataService tableMetadataService = new TableMetadataService();

        // Inject the dependencies into the service
        inject(tableMetadataService, "tenantCrudService", tenantCrudService);
        inject(tableMetadataService, "jpaJdbcUtil", jpaJdbcUtil);

        var metadata = tableMetadataService.getTableMetadata(TenantPartitionEnum.PACE_ACCOM_ACTIVITY);

        // Verify metadata is not null
        assertNotNull(metadata, "TableMetadata should not be null");

        // Verify primary keys
        assertNotNull(metadata.getPrimaryKeyColumns(), "Primary keys should not be null");

        // Verify foreign keys
        assertNotNull(metadata.getForeignKeys(), "Foreign keys should not be null");
        assertEquals(2, metadata.getForeignKeys().size(), "Should have 2 foreign keys");

        assertTrue(metadata.getForeignKeys().containsKey("FK_PACE_Accom_Activity_File_Metadata"),
                   "Should contain FK_PACE_Accom_Activity_File_Metadata foreign key");
        assertTrue(metadata.getForeignKeys().containsKey("FK_PACE_Accom_Activity_Accom_Type"),
                   "Should contain FK_PACE_Accom_Activity_Accom_Type foreign key");

        // Verify File_Metadata foreign key details
        var fileMetadataFK = metadata.getForeignKeys().get("FK_PACE_Accom_Activity_File_Metadata");
        assertEquals("File_Metadata_ID", fileMetadataFK.get("pkColumn"), "FK File_Metadata pkColumn should be File_Metadata_ID");
        assertEquals("File_Metadata_ID", fileMetadataFK.get("fkColumn"), "FK File_Metadata fkColumn should be File_Metadata_ID");
        assertEquals("File_Metadata", fileMetadataFK.get("pkTable"), "FK File_Metadata pkTable should be File_Metadata");
        assertEquals("NO ACTION", fileMetadataFK.get("deleteRule"), "FK File_Metadata deleteRule should be NO ACTION");
        assertEquals("NO ACTION", fileMetadataFK.get("updateRule"), "FK File_Metadata updateRule should be NO ACTION");

        // Verify Accom_Type foreign key details
        var accomTypeFK = metadata.getForeignKeys().get("FK_PACE_Accom_Activity_Accom_Type");
        assertEquals("Accom_Type_ID", accomTypeFK.get("pkColumn"), "FK Accom_Type pkColumn should be Accom_Type_ID");
        assertEquals("Accom_Type_ID", accomTypeFK.get("fkColumn"), "FK Accom_Type fkColumn should be Accom_Type_ID");
        assertEquals("Accom_Type", accomTypeFK.get("pkTable"), "FK Accom_Type pkTable should be Accom_Type");
        assertEquals("NO ACTION", accomTypeFK.get("deleteRule"), "FK Accom_Type deleteRule should be NO ACTION");
        assertEquals("NO ACTION", accomTypeFK.get("updateRule"), "FK Accom_Type updateRule should be NO ACTION");

        // Verify columns
        assertNotNull(metadata.getColumns(), "Columns should not be null");

        // Verify default constraints
        assertNotNull(metadata.getDefaultConstraints(), "Default constraints should not be null");
        assertEquals(7, metadata.getDefaultConstraints().size(), "Should have 7 default constraints");

        // Verify specific default constraints exist
        assertTrue(metadata.getDefaultConstraints().stream().anyMatch(c -> c.contains("DF_PACE_Accom_Activity_Rooms_Not_Avail_Maint")),
                   "Should contain Rooms_Not_Avail_Maint default constraint");
        assertTrue(metadata.getDefaultConstraints().stream().anyMatch(c -> c.contains("DF_PACE_Accom_Activity_Last_Updated_DTTM")),
                   "Should contain Last_Updated_DTTM default constraint with getdate()");

        // Verify constraints
        assertNotNull(metadata.getConstraints(), "Constraints should not be null");
        assertTrue(metadata.getConstraints().isEmpty(), "Constraints list should be empty");

        // Verify index columns
        assertNotNull(metadata.getIndexColumns(), "Index columns should not be null");
        assertEquals(3, metadata.getIndexColumns().size(), "Should have 3 indexes");

        assertTrue(metadata.getIndexColumns().containsKey("NC_CVRD_PACE_Accom_Activity"),
                   "Should contain NC_CVRD_PACE_Accom_Activity index");
        assertTrue(metadata.getIndexColumns().containsKey("UQ_PACE_Accom_Activity"),
                   "Should contain UQ_PACE_Accom_Activity index");
        assertTrue(metadata.getIndexColumns().containsKey("NC_Business_DT_Property_Id_PACE_Accom_Activity"),
                   "Should contain NC_Business_DT_Property_Id_PACE_Accom_Activity index");

        // Verify unique indexes
        assertNotNull(metadata.getUniqueIndexes(), "Unique indexes should not be null");
        assertEquals(3, metadata.getUniqueIndexes().size(), "Should have 3 unique index entries");

        assertTrue(metadata.getUniqueIndexes().get("UQ_PACE_Accom_Activity"),
                   "UQ_PACE_Accom_Activity should be a unique index");
        assertFalse(metadata.getUniqueIndexes().get("NC_CVRD_PACE_Accom_Activity"),
                    "NC_CVRD_PACE_Accom_Activity should not be a unique index");
        assertFalse(metadata.getUniqueIndexes().get("NC_Business_DT_Property_Id_PACE_Accom_Activity"),
                    "NC_Business_DT_Property_Id_PACE_Accom_Activity should not be a unique index");

        // Verify hot/cold boundary
        assertNotNull(metadata.getHotColdBoundary(), "Hot/cold boundary should not be null");
        // Don't assert exact date as it may change over time - just verify it's reasonable
        assertTrue(metadata.getHotColdBoundary().isAfter(LocalDate.now().minusYears(1)),
                   "Hot/cold boundary should be within the last year");
        assertTrue(metadata.getHotColdBoundary().isBefore(LocalDate.now().plusYears(2)),
                   "Hot/cold boundary should be within the next 2 years");

        // Verify cold dates
        assertNotNull(metadata.getColdDates(), "Cold dates should not be null");
        assertFalse(metadata.getColdDates().isEmpty(), "Cold dates should not be empty");

        // Verify cold dates are all in the past (more flexible than checking specific dates)
        assertTrue(metadata.getColdDates().stream().allMatch(date -> date.isBefore(metadata.getHotColdBoundary())),
                   "All cold dates should be before the hot/cold boundary");

        // Verify we have a reasonable number of cold dates (instead of checking specific ones)
        assertTrue(metadata.getColdDates().size() > 50,
                   "Should have a reasonable number of cold dates (historical data)");

    }
}