package com.ideas.tetris.pacman.services.meetingpackagepricing.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.PrettyPricingService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingDigit;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingRule;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PrettyPricingRuleRow;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.eventaggregator.StalenessFlag;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceObjectMother;
import com.ideas.tetris.pacman.services.meetingpackagepricing.MeetingPackageBarDecisionBuilder;
import com.ideas.tetris.pacman.services.meetingpackagepricing.dto.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.dto.MeetingPackageDecisionOverrideHistory;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.criteria.MeetingPackagePaceSearchCriteria;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.criteria.MeetingPackageSearchCriteria;
import com.ideas.tetris.pacman.services.meetingpackagepricing.service.mapper.MeetingPackagePricingMapper;
import com.ideas.tetris.pacman.services.meetingpackagepricing.transformer.MeetingPackagePricingOverrideTransformer;
import com.ideas.tetris.pacman.services.ngi.dto.meetingroompricing.MeetingPackagePricing;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.RoundingRule;
import com.ideas.tetris.pacman.services.sasoptimization.dto.OptimizationWindowDto;
import com.ideas.tetris.pacman.services.sasoptimization.service.OptimizationWindowService;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncFlagService;
import com.ideas.tetris.pacman.testdatabuilder.MeetingPackageProductBuilder;
import com.ideas.tetris.pacman.testdatabuilder.MeetingPackageProductOffsetBuilder;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.entity.DecisionUploadType;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum.ACTIVE;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.fromDate;
import static com.ideas.tetris.platform.common.time.LocalDateUtils.toDate;
import static java.math.BigDecimal.*;
import static java.time.LocalDate.of;
import static java.util.Collections.emptyList;
import static java.util.Collections.singleton;
import static java.util.stream.Collectors.toCollection;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MeetingPackageDecisionServiceTest {

    private static final String CLIENT_BSTN = "BSTN";
    private static final String PROPERTY_H1 = "H1";

    @Spy
    @InjectMocks
    private MeetingPackageDecisionService meetingPackageDecisionService;

    @Mock
    private MeetingProductBarDecisionRepository meetingProductBarDecisionRepository;

    @Mock
    private MeetingPackagePaceBarDecisionRepository meetingPackagePaceBarDecisionRepository;

    @Mock
    private MeetingPackageProductRepository meetingPackageProductRepository;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    private SpecialEventService specialEventService;

    @Mock
    private DateService dateService;

    @Mock
    private MeetingPackageBarDecisionOverrideRepository meetingPackageBarDecisionOverrideRepository;

    @Mock
    private MeetingPackageProductRateOffsetOverrideRepository meetingPackageProductRateOffsetOverrideRepository;
    @Mock
    private MeetingPackagePricingOverrideTransformer meetingPackagePricingOverrideTransformer;

    @Mock
    private DecisionService decisionService;

    @Mock
    private MeetingPackageProductService meetingPackageProductService;
    @Mock
    private SyncEventAggregatorService syncEventAggregatorService;

    @Mock
    protected OptimizationWindowService optimizationWindowService;

    @Mock
    private PrettyPricingService prettyPricingService;

    @Mock
    private SyncFlagService syncFlagService;

    @Mock
    private MeetingPackageOptimalPriceBARService meetingPackageOptimalPriceBARService;

    @Mock
    private MeetingPackageProductRateOffsetRepository meetingPackageProductRateOffsetRepository;

    @BeforeEach
    public void setUp() {
        PacmanWorkContextHelper.setWorkContext(CLIENT_BSTN, 5);
        PacmanWorkContextHelper.setUserId("1");
        PacmanWorkContextHelper.setPropertyCode(PROPERTY_H1);
    }

    @Test
    void shouldGetMeetingPackagePricings() {
        Date startDate = new Date();
        Date endDate = new Date();
        Date occupancyDate1 = new Date();
        Date occupancyDate2 = new Date();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        String correlationId = UUID.randomUUID().toString();
        MeetingPackageBarDecision meetingPackageBarDecision1 = createMeetingPackageBarDecision(occupancyDate1, meetingPackageProduct, functionSpaceFunctionRoom);
        MeetingPackageBarDecision meetingPackageBarDecision2 = createMeetingPackageBarDecision(occupancyDate2, meetingPackageProduct, functionSpaceFunctionRoom);
        when(meetingProductBarDecisionRepository.getMeetingProductBarDecisionsByOccupancyDateRange(startDate, endDate))
                .thenReturn(List.of(meetingPackageBarDecision1, meetingPackageBarDecision2));
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn("USD");

        List<MeetingPackagePricing> meetingPackagePricings = meetingPackageDecisionService.getMeetingPackagePricings(startDate, endDate, null, correlationId);

        assertEquals(2, meetingPackagePricings.size());
        assertEquals(CLIENT_BSTN, meetingPackagePricings.get(0).getClientCode());
        assertEquals(PROPERTY_H1, meetingPackagePricings.get(0).getPropertyCode());
        assertEquals("MP_Product", meetingPackagePricings.get(0).getPackageId());
        assertEquals("Meeting_Room", meetingPackagePricings.get(0).getFunctionRoomId());
        assertEquals(occupancyDate1, meetingPackagePricings.get(0).getOccupancyDate());
        assertEquals(TEN, meetingPackagePricings.get(0).getAmount());
        assertEquals("USD", meetingPackagePricings.get(0).getCurrencyCode());
        assertEquals(correlationId, meetingPackagePricings.get(0).getCorrelationId());
    }

    @Test
    void shouldGetMeetingProductBarDifferentialDecisionsForUploadByOccupancyDateRange() {
        Date startDate = getDate("2025-02-26");
        Date endDate = getDate("2025-02-27");
        Date lastUploadDate = getDate("2025-02-25");
        Date occupancyDate1 = getDate("2025-02-26");
        Date occupancyDate2 = getDate("2025-02-27");
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        String correlationId = UUID.randomUUID().toString();
        MeetingPackageBarDecision meetingPackagePaceBarDecision1 = createMeetingPackageBarDecision(occupancyDate1, meetingPackageProduct, functionSpaceFunctionRoom);
        MeetingPackageBarDecision meetingPackagePaceBarDecision2 = createMeetingPackageBarDecision(occupancyDate2, meetingPackageProduct, functionSpaceFunctionRoom);
        when(meetingProductBarDecisionRepository.getMeetingProductBarDifferentialDecisionsForUploadByOccupancyDateRange(startDate, endDate, lastUploadDate))
                .thenReturn(List.of(meetingPackagePaceBarDecision1, meetingPackagePaceBarDecision2));
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn("USD");

        List<MeetingPackagePricing> meetingPackagePricings = meetingPackageDecisionService.getMeetingPackagePricings(startDate, endDate, lastUploadDate, correlationId);

        assertEquals(2, meetingPackagePricings.size());
        assertEquals(CLIENT_BSTN, meetingPackagePricings.get(0).getClientCode());
        assertEquals(PROPERTY_H1, meetingPackagePricings.get(0).getPropertyCode());
        assertEquals("MP_Product", meetingPackagePricings.get(0).getPackageId());
        assertEquals("Meeting_Room", meetingPackagePricings.get(0).getFunctionRoomId());
        assertEquals(occupancyDate1, meetingPackagePricings.get(0).getOccupancyDate());
        assertEquals(TEN, meetingPackagePricings.get(0).getAmount());
        assertEquals("USD", meetingPackagePricings.get(0).getCurrencyCode());
        assertEquals(correlationId, meetingPackagePricings.get(0).getCorrelationId());
    }

    @Test
    void shouldReturnEmptyListWhenDateRangeIsIncorrect() {
        Date now = new Date();
        List<MeetingPackagePricing> meetingPackagePricings = meetingPackageDecisionService.getMeetingPackagePricings(now, DateUtil.addDaysToDate(now, 1), null, null);
        assertEquals(0, meetingPackagePricings.size());
        verify(configParamsService, never()).getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
    }

    @Test
    void shouldGetMeetingPackagePricingsWhenUsePackageIDToggleEnabled() {
        Date startDate = new Date();
        Date endDate = new Date();
        Date occupancyDate1 = new Date();
        Date occupancyDate2 = new Date();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setPackageId("PackageID-1");
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        String correlationId = UUID.randomUUID().toString();
        MeetingPackageBarDecision meetingPackageBarDecision1 = createMeetingPackageBarDecision(occupancyDate1, meetingPackageProduct, functionSpaceFunctionRoom);
        MeetingPackageBarDecision meetingPackageBarDecision2 = createMeetingPackageBarDecision(occupancyDate2, meetingPackageProduct, functionSpaceFunctionRoom);
        when(meetingProductBarDecisionRepository.getMeetingProductBarDecisionsByOccupancyDateRange(startDate, endDate))
                .thenReturn(List.of(meetingPackageBarDecision1, meetingPackageBarDecision2));
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn("USD");
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_MEETING_PACKAGE_PRODUCT_PACKAGE_ID)).thenReturn(true);

        List<MeetingPackagePricing> meetingPackagePricings = meetingPackageDecisionService.getMeetingPackagePricings(startDate, endDate, null, correlationId);

        assertEquals(2, meetingPackagePricings.size());
        assertEquals(CLIENT_BSTN, meetingPackagePricings.get(0).getClientCode());
        assertEquals(PROPERTY_H1, meetingPackagePricings.get(0).getPropertyCode());
        assertEquals("PackageID-1", meetingPackagePricings.get(0).getPackageId());
        assertEquals("Meeting_Room", meetingPackagePricings.get(0).getFunctionRoomId());
        assertEquals(occupancyDate1, meetingPackagePricings.get(0).getOccupancyDate());
        assertTrue(BigDecimalUtil.equals(BigDecimal.TEN, meetingPackagePricings.get(0).getAmount()));
        assertEquals("USD", meetingPackagePricings.get(0).getCurrencyCode());
        assertEquals(correlationId, meetingPackagePricings.get(0).getCorrelationId());
    }

    @Test
    void shouldThrowExceptionWhileGettingMeetingPackagePricingsWhenPackageIdMissingAndUsePackageIDToggleEnabled() {
        Date startDate = new Date();
        Date endDate = new Date();
        Date occupancyDate1 = new Date();
        Date occupancyDate2 = new Date();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        String correlationId = UUID.randomUUID().toString();
        MeetingPackageBarDecision meetingPackageBarDecision1 = createMeetingPackageBarDecision(occupancyDate1, meetingPackageProduct, functionSpaceFunctionRoom);
        MeetingPackageBarDecision meetingPackageBarDecision2 = createMeetingPackageBarDecision(occupancyDate2, meetingPackageProduct, functionSpaceFunctionRoom);
        when(meetingProductBarDecisionRepository.getMeetingProductBarDecisionsByOccupancyDateRange(startDate, endDate))
                .thenReturn(List.of(meetingPackageBarDecision1, meetingPackageBarDecision2));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_MEETING_PACKAGE_PRODUCT_PACKAGE_ID)).thenReturn(true);

        TetrisException thrownException = assertThrows(TetrisException.class, () -> meetingPackageDecisionService.getMeetingPackagePricings(startDate, endDate, null, correlationId));
        assertEquals("Package ID is missing for the meeting package product: MP_Product", thrownException.getBaseMessage());
    }

    @Test
    void shouldBuildSearchCriteriaCorrectly() {
        Date startDate = new Date();
        Date endDate = new Date();
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = Arrays.asList(10, 20);

        MeetingPackageSearchCriteria searchCriteria = meetingPackageDecisionService.buildSearchCriteria(startDate, endDate, meetingRoomIds, productIds);

        assertNotNull(searchCriteria);
        assertEquals(startDate, searchCriteria.getStartDate());
        assertEquals(endDate, searchCriteria.getEndDate());
        assertEquals(meetingRoomIds, searchCriteria.getMeetingRoomIds());
        assertEquals(productIds, searchCriteria.getProductIds());
    }

    @Test
    void shouldHandleNullValuesInSearchCriteria() {
        MeetingPackageSearchCriteria searchCriteria = meetingPackageDecisionService.buildSearchCriteria(null, null, null, null);

        assertNotNull(searchCriteria);
        assertNull(searchCriteria.getStartDate());
        assertNull(searchCriteria.getEndDate());
        assertNull(searchCriteria.getMeetingRoomIds());
        assertNull(searchCriteria.getProductIds());
    }

    @Test
    void shouldReturnMeetingPackageBarDecisionsWhenDataIsAvailable() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = Arrays.asList(1);
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = new ArrayList<>();
        List<MeetingPackagePaceBarDecision> meetingPackagePaceBarDecisions = new ArrayList<>();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        meetingRoom.setId(1);
        meetingRoom.setName("Meeting_Room");
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(startDate, meetingPackageProduct, meetingRoom);
        MeetingPackagePaceBarDecision meetingPackagePaceBarDecision = createMeetingPackagePaceBarDecision(startDate, meetingPackageProduct, meetingRoom);
        meetingPackageBarDecisions.add(meetingPackageBarDecision);
        meetingPackagePaceBarDecisions.add(meetingPackagePaceBarDecision);
        Map<String, List<PropertySpecialEvent>> specialEventsMap = new HashMap<>();
        specialEventsMap.put("event1", List.of(createSpecialEventForDate()));
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(meetingPackageBarDecisions);
        when(meetingPackagePaceBarDecisionRepository.searchByFilterCriteria(any(MeetingPackagePaceSearchCriteria.class)))
                .thenReturn(meetingPackagePaceBarDecisions);
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(specialEventsMap);
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);

        List<MeetingPackagePricingPerDay> meetingPackagePricePerDay = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        MeetingRoomPrice meetingRoomPrice = meetingPackagePricePerDay.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0);
        assertMeetingPackagePricingDetails(meetingPackagePricePerDay);
        assertEquals(valueOf(15.00), meetingRoomPrice.getTotalPackagePrice());
    }

    @Test
    void shouldReturnTotalPackagePriceSameAsFinalBarWhenNoPackageElementsAssociatedWithProduct() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = List.of(1);
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = new ArrayList<>();
        List<MeetingPackagePaceBarDecision> meetingPackagePaceBarDecisions = new ArrayList<>();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        meetingRoom.setId(1);
        meetingRoom.setName("Meeting_Room");
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(startDate, meetingPackageProduct, meetingRoom);
        MeetingPackagePaceBarDecision meetingPackagePaceBarDecision = createMeetingPackagePaceBarDecision(startDate, meetingPackageProduct, meetingRoom);
        meetingPackageBarDecisions.add(meetingPackageBarDecision);
        meetingPackagePaceBarDecisions.add(meetingPackagePaceBarDecision);
        Map<String, List<PropertySpecialEvent>> specialEventsMap = new HashMap<>();
        specialEventsMap.put("event1", List.of(createSpecialEventForDate()));
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(meetingPackageBarDecisions);
        when(meetingPackagePaceBarDecisionRepository.searchByFilterCriteria(any(MeetingPackagePaceSearchCriteria.class)))
                .thenReturn(meetingPackagePaceBarDecisions);
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(specialEventsMap);
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);

        List<MeetingPackagePricingPerDay> meetingPackagePricePerDay = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        MeetingRoomPrice meetingRoomPrice = meetingPackagePricePerDay.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0);
        assertMeetingPackagePricingDetails(meetingPackagePricePerDay);
        assertNull(meetingRoomPrice.getAdjustmentValue());
        assertEquals(BigDecimal.valueOf(15.00), meetingRoomPrice.getTotalPackagePrice());
    }

    @Test
    void shouldFetchParentProductDecisionsIfMissingInInitialResults() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = List.of(1);
        List<Integer> productIds = List.of(100);
        MeetingPackageProduct linkedProduct = createMeetingPackageProduct();
        linkedProduct.setId(100);
        linkedProduct.setType("LINKED");
        linkedProduct.setDependentProductId(200);
        FunctionSpaceFunctionRoom room = createFunctionSpaceFunctionRoom();
        room.setId(1);
        room.setName("Room A");
        MeetingPackageBarDecision linkedDecision = createMeetingPackageBarDecision(startDate, linkedProduct, room);
        MeetingPackageProduct parentProduct = createMeetingPackageProduct();
        parentProduct.setId(200);
        parentProduct.setType("INDEPENDENT");
        MeetingPackageBarDecision baseDecision = createMeetingPackageBarDecision(startDate, parentProduct, room);
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any()))
                .thenReturn(List.of(linkedDecision))
                .thenReturn(List.of(baseDecision));
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate)).thenReturn(Map.of());
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);

        List<MeetingPackagePricingPerDay> results = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        assertFalse(results.isEmpty());
        verify(meetingProductBarDecisionRepository, times(2)).searchByFilterCriteria(any());
        assertEquals("MP_Product",results.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0).getBaseProduct());
        assertEquals(TEN,results.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0).getBaseProductPrice());
    }

    @Test
    void shouldUseExistingParentProductDecisionsIfAlreadyPresent() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = List.of(1);
        List<Integer> productIds = List.of(100);
        MeetingPackageProduct linkedProduct = createMeetingPackageProduct();
        linkedProduct.setId(100);
        linkedProduct.setType("LINKED");
        linkedProduct.setDependentProductId(200);
        MeetingPackageProduct baseProduct = createMeetingPackageProduct();
        baseProduct.setId(200);
        baseProduct.setType("INDEPENDENT");
        FunctionSpaceFunctionRoom room = createFunctionSpaceFunctionRoom();
        room.setId(1);
        room.setName("Room A");
        MeetingPackageBarDecision linkedDecision = createMeetingPackageBarDecision(startDate, linkedProduct, room);
        MeetingPackageBarDecision baseDecision = createMeetingPackageBarDecision(startDate, baseProduct, room);
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any()))
                .thenReturn(List.of(linkedDecision, baseDecision));
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate)).thenReturn(Map.of());
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);
        List<MeetingPackagePricingPerDay> results = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        assertFalse(results.isEmpty());
        verify(meetingProductBarDecisionRepository, times(1)).searchByFilterCriteria(any());
    }


    @Test
    void shouldNotCallForParentProductDecisionsWhenNoLinkedProducts() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = List.of(1);
        List<Integer> productIds = List.of(1);
        MeetingPackageProduct product = createMeetingPackageProduct();
        product.setId(1);
        product.setType("INDEPENDENT");
        MeetingPackageBarDecision decision = createMeetingPackageBarDecision(startDate, product, createFunctionSpaceFunctionRoom());
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any()))
                .thenReturn(List.of(decision));
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate)).thenReturn(Map.of());
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);

        List<MeetingPackagePricingPerDay> results = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

        assertFalse(results.isEmpty());
        verify(meetingProductBarDecisionRepository, times(1)).searchByFilterCriteria(any());
    }


    private void assertMeetingPackagePricingDetails(List<MeetingPackagePricingPerDay> meetingPackagePricePerDay) {
        assertEquals(1, meetingPackagePricePerDay.size());
        assertEquals(getDate("2025-04-10"), meetingPackagePricePerDay.get(0).getDate());
        assertEquals(1, meetingPackagePricePerDay.get(0).getSpecialEvents().size());
        assertEquals(1, meetingPackagePricePerDay.get(0).getPriceDetails().size());
        MeetingPackagePriceDetail meetingPackagePriceDetail = meetingPackagePricePerDay.get(0).getPriceDetails().get(0);
        assertEquals(1, meetingPackagePriceDetail.getProductId());
        assertEquals("MP_Product", meetingPackagePriceDetail.getProductName());
        MeetingRoomPrice meetingRoomPrice = meetingPackagePriceDetail.getMeetingRoomPrices().get(0);
        assertEquals(1, meetingRoomPrice.getMeetingRoomId());
        assertEquals("Meeting_Room", meetingRoomPrice.getMeetingRoomName());
        assertEquals(TEN, meetingRoomPrice.getMeetingRoomPrice());
        assertTrue(meetingRoomPrice.getBaseMeetingRoom());
    }

    private PropertySpecialEvent createSpecialEventForDate() {
        PropertySpecialEvent propertySpecialEvent = new PropertySpecialEvent();
        Date startDate = getDate("2025-04-01");
        Date endDate = getDate("2025-04-31");
        propertySpecialEvent.setStartDate(startDate);
        propertySpecialEvent.setEndDate(endDate);
        return propertySpecialEvent;
    }


    @Test
    void shouldReturnEmptyListWhenDecisionsNotPresentForSelectedCriteria() {
        Date startDate = new Date();
        Date endDate = new Date();
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(emptyList());

        try (MockedStatic<MeetingPackagePricingMapper> mockedMapper = mockStatic(MeetingPackagePricingMapper.class)) {
            mockedMapper.when(() -> MeetingPackagePricingMapper.toPricingPerDay(eq(emptyList()), any(), eq(Collections.emptyMap()), eq(Collections.emptyMap()), eq(Collections.emptyMap()), eq(Collections.emptyMap()), eq(emptyList())))
                    .thenReturn(emptyList());

            List<MeetingPackagePricingPerDay> result = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, meetingRoomIds);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void shouldReturnEmptyListWhenSpecialEventsAreEmpty() {
        Date startDate = new Date();
        Date endDate = new Date();
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = Arrays.asList(1);
        List<MeetingPackageBarDecision> mockDecisions = new ArrayList<>();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(startDate, meetingPackageProduct, meetingRoom);
        mockDecisions.add(meetingPackageBarDecision);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(mockDecisions);
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(Collections.emptyMap());
        try (MockedStatic<MeetingPackagePricingMapper> mockedMapper = mockStatic(MeetingPackagePricingMapper.class)) {
            mockedMapper.when(() -> MeetingPackagePricingMapper.toPricingPerDay(eq(mockDecisions), any(), eq(Collections.emptyMap()), eq(Collections.emptyMap()), eq(Collections.emptyMap()), eq(Collections.emptyMap()), eq(emptyList())))
                    .thenReturn(emptyList());

            List<MeetingPackagePricingPerDay> result = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void shouldReturnAdjustmentValueForLinkedProductWhenOverrideExists() {
        Date startDate = getDate("2025-04-10");
        Date endDate = getDate("2025-04-31");
        List<Integer> meetingRoomIds = Arrays.asList(1, 2);
        List<Integer> productIds = List.of(1);
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = new ArrayList<>();
        List<MeetingPackagePaceBarDecision> meetingPackagePaceBarDecisions = new ArrayList<>();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(1);
        meetingPackageProduct.setType("LINKED");
        FunctionSpaceFunctionRoom meetingRoom = createFunctionSpaceFunctionRoom();
        meetingRoom.setId(1);
        meetingRoom.setName("Meeting_Room");
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(startDate, meetingPackageProduct, meetingRoom);
        MeetingPackagePaceBarDecision meetingPackagePaceBarDecision = createMeetingPackagePaceBarDecision(startDate, meetingPackageProduct, meetingRoom);
        meetingPackageBarDecisions.add(meetingPackageBarDecision);
        meetingPackagePaceBarDecisions.add(meetingPackagePaceBarDecision);
        Map<String, List<PropertySpecialEvent>> specialEventsMap = new HashMap<>();
        specialEventsMap.put("event1", List.of(createSpecialEventForDate()));
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class)))
                .thenReturn(meetingPackageBarDecisions);
        when(meetingPackagePaceBarDecisionRepository.searchByFilterCriteria(any(MeetingPackagePaceSearchCriteria.class)))
                .thenReturn(meetingPackagePaceBarDecisions);
        when(specialEventService.getAllSpecialEventsByDateRange(startDate, endDate))
                .thenReturn(specialEventsMap);
        BaseMeetingRoomDTO baseMeetingRoom = new BaseMeetingRoomDTO();
        MeetingRoomDTO meetingRoomDto = new MeetingRoomDTO();
        meetingRoomDto.setId(1);
        meetingRoomDto.setBase(true);
        meetingRoomDto.setName("Meeting_Room");
        baseMeetingRoom.setId(1);
        baseMeetingRoom.setBaseMeetingRoom(meetingRoomDto);
        baseMeetingRoom.setPriceExcluded(true);
        MeetingPackageProductRateOffset meetingPackageProductRateOffset = getMeetingPackageProductRateOffset(meetingPackageProduct, null, null, null);
        MeetingPackageProductRateOffsetOverride override = new MeetingPackageProductRateOffsetOverride();
        override.setOccupancyDate(startDate);
        override.setProduct(meetingPackageProduct);
        override.setOffsetValue(TEN);
        when(meetingPackageProductService.getBaseMeetingRoom()).thenReturn(baseMeetingRoom);
        when(meetingPackageProductRateOffsetRepository.getMeetingPackageProductRateAdjustments(productIds)).thenReturn(List.of(meetingPackageProductRateOffset));
        when(meetingPackageProductRateOffsetOverrideRepository.findActiveOverridesForProductOccupancyPairs(
                anySet(), anySet(), eq(ACTIVE)))
                .thenReturn(List.of(override));

        List<MeetingPackagePricingPerDay> meetingPackagePricePerDay = meetingPackageDecisionService.getMeetingPackageBarDecisions(startDate, endDate, meetingRoomIds, productIds);
        MeetingPackagePriceDetail priceDetail = meetingPackagePricePerDay.get(0).getPriceDetails().get(0);
        MeetingRoomPrice meetingRoomPrice = meetingPackagePricePerDay.get(0).getPriceDetails().get(0).getMeetingRoomPrices().get(0);
        assertMeetingPackagePricingDetails(meetingPackagePricePerDay);
        assertEquals(override.getOffsetValue(), meetingRoomPrice.getAdjustmentValue());
        assertEquals(valueOf(15.00), meetingRoomPrice.getTotalPackagePrice());
        assertEquals(TEN, priceDetail.getAdjustment());
    }

    private ProductRateOffset getProductRateOffset() {
        ProductRateOffset productRateOffset = new ProductRateOffset();
        productRateOffset.setCeilingRate(valueOf(100.0));
        productRateOffset.setFloorRate(valueOf(100.0));
        productRateOffset.setMpProductId(1);
        return productRateOffset;
    }

    @Test
    void shouldGetActiveOverridesForLinkedProducts() throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        ArgumentCaptor<Set<Integer>> productIdsCaptor = ArgumentCaptor.forClass(Set.class);
        ArgumentCaptor<Set<Date>> occupancyDatesCaptor = ArgumentCaptor.forClass(Set.class);
        var room1 = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Room 1");
        var room2 = FunctionSpaceObjectMother.buildFunctionSpaceFunctionRoom("Room 2");
        var mpp1 = MeetingPackageProductBuilder.createIndependentMPProduct("IP1", ACTIVE);
        mpp1.setId(10);
        var mpp2 = MeetingPackageProductBuilder.createLinkedMPProduct("LP1", mpp1.getId());
        mpp2.setId(11);
        mpp2.setType(MeetingPackageProduct.PRODUCT_TYPE_LINKED);
        MeetingPackageBarDecision decision0 = MeetingPackageBarDecisionBuilder.createBarDecision(1L, mpp1, room1, dateFormat.parse("2022-09-01"), TEN);
        MeetingPackageBarDecision decision1 = MeetingPackageBarDecisionBuilder.createBarDecision(1L, mpp2, room1, dateFormat.parse("2022-09-01"), TEN);
        MeetingPackageBarDecision decision2 = MeetingPackageBarDecisionBuilder.createBarDecision(1L, mpp2, room2, dateFormat.parse("2022-09-01"), TEN);
        MeetingPackageBarDecision decision3 = MeetingPackageBarDecisionBuilder.createBarDecision(1L, mpp2, room1, dateFormat.parse("2022-09-02"), TEN);

        meetingPackageDecisionService.getActiveOverridesForLinkedProducts(List.of(decision0, decision1, decision2, decision3));

        verify(meetingPackageProductRateOffsetOverrideRepository).findActiveOverridesForProductOccupancyPairs(
                productIdsCaptor.capture(), occupancyDatesCaptor.capture(), eq(ACTIVE));
        assertEquals(1, productIdsCaptor.getValue().size());
        assertEquals(mpp2.getId(), productIdsCaptor.getValue().iterator().next());
        assertEquals(2, occupancyDatesCaptor.getValue().size());
        assertEquals(Set.of(dateFormat.parse("2022-09-01"), dateFormat.parse("2022-09-02")), occupancyDatesCaptor.getValue());
    }

    @Test
    void shouldGetActiveOverridesForLinkedProductsWhenNoDecisionsArePassed() {
        var result = meetingPackageDecisionService.getActiveOverridesForLinkedProducts(emptyList());

        verify(meetingPackageProductRateOffsetOverrideRepository, never()).findActiveOverridesForProductOccupancyPairs(anySet(), anySet(), any());
        assertTrue(result.isEmpty());
    }

    @Test
    void shouldPopulatePaceMeetingPackageUploadDifferentialDataForBDE() {
        Date startDate = DateUtil.getDate(20, 2, 2025);
        Date endDate = DateUtil.getDate(20, 2, 2026);
        OptimizationWindowDto optimizationWindowDto = new OptimizationWindowDto();
        optimizationWindowDto.setStartDate(startDate);
        optimizationWindowDto.setEndDate(endDate);
        when(optimizationWindowService.getWindowForType("BDE")).thenReturn(optimizationWindowDto);
        when(meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate)).thenReturn(100);

        int resultCount = meetingPackageDecisionService.populatePaceMeetingPackageUploadDifferentialData("BDE");

        assertEquals(100, resultCount);
    }

    @Test
    void shouldPopulatePaceMeetingPackageUploadDifferentialDataForManualUpload() {
        Date startDate = DateUtil.getDate(20, 2, 2025);
        Date endDate = DateUtil.getDate(21, 6, 2025);
        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(endDate);
        when(meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate)).thenReturn(4);

        int resultCount = meetingPackageDecisionService.populatePaceMeetingPackageUploadDifferentialDetailsForManualUpload();

        assertEquals(4, resultCount);
    }

    @Test
    void shouldPopulatePaceMeetingPackageUploadDifferentialDataForCDP() {
        Date startDate = DateUtil.getDate(20, 2, 2025);
        Date endDate = DateUtil.getDate(20, 2, 2026);
        OptimizationWindowDto optimizationWindowDto = new OptimizationWindowDto();
        optimizationWindowDto.setStartDate(startDate);
        optimizationWindowDto.setEndDate(endDate);
        when(optimizationWindowService.getWindowForType("CDP")).thenReturn(optimizationWindowDto);
        when(meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate)).thenReturn(50);

        int resultCount = meetingPackageDecisionService.populatePaceMeetingPackageUploadDifferentialData("CDP");

        assertEquals(50, resultCount);
    }

    @Test
    void shouldReturnTrueWhenMeetingPackagePricingDecisionTypeConfiguredAsDifferential() {
        when(configParamsService.getValue("pacman.BSTN.H1", "pacman.integration.Hilstar.meetingPackagePricing.uploadtype"))
                .thenReturn(DecisionUploadType.DIFFERENTIAL.getConfigParamValue());

        assertTrue(meetingPackageDecisionService.isMeetingPackagePricingDecisionTypeConfigured("Hilstar"));
    }

    @Test
    void shouldReturnTrueWhenMeetingPackagePricingDecisionTypeConfiguredAsFull() {
        when(configParamsService.getValue("pacman.BSTN.H1", "pacman.integration.Hilstar.meetingPackagePricing.uploadtype"))
                .thenReturn(DecisionUploadType.FULL.getConfigParamValue());

        assertTrue(meetingPackageDecisionService.isMeetingPackagePricingDecisionTypeConfigured("Hilstar"));
    }

    @Test
    void shouldReturnFalseWhenMeetingPackagePricingDecisionTypeNotConfigured() {
        when(configParamsService.getValue("pacman.BSTN.H1", "pacman.integration.Hilstar.meetingPackagePricing.uploadtype"))
                .thenReturn(DecisionUploadType.NONE.getConfigParamValue());

        assertFalse(meetingPackageDecisionService.isMeetingPackagePricingDecisionTypeConfigured("Hilstar"));
    }

    @Test
    void shouldReturnPricingOverrideDetailWhenOverridesArePresent() {
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = new MeetingPackageBarDecisionOverride();
        meetingPackageBarDecisionOverride.setId(1L);
        PricingOverrideDetail pricingOverrideDetail = new PricingOverrideDetail();
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageBarDecisionOverrideBy(1L))
                .thenReturn(meetingPackageBarDecisionOverride);
        when(meetingPackagePricingOverrideTransformer.convert(meetingPackageBarDecisionOverride)).thenReturn(pricingOverrideDetail);

        PricingOverrideDetail overrideDetail = meetingPackageDecisionService.getPricingOverrideDetails(1L).get();

        assertEquals(pricingOverrideDetail, overrideDetail);
        verify(meetingPackageBarDecisionOverrideRepository).getMeetingPackageBarDecisionOverrideBy(1L);
        verify(meetingPackagePricingOverrideTransformer).convert(meetingPackageBarDecisionOverride);
    }

    @Test
    void shouldReturnNullWhenOverridesAreAbsent() {
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageBarDecisionOverrideBy(1L))
                .thenReturn(null);

        Optional<PricingOverrideDetail> overrideDetail = meetingPackageDecisionService.getPricingOverrideDetails(1L);

        assertFalse(overrideDetail.isPresent());
        verify(meetingPackageBarDecisionOverrideRepository).getMeetingPackageBarDecisionOverrideBy(1L);
        verify(meetingPackagePricingOverrideTransformer, never()).convert(any());
    }

    @Test
    void shouldSavePricingOverrideForSpecificOverrideWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setSpecificOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertEquals(ONE, overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(ONE, meetingPackageBarDecision.getUserSpecifiedRate());
        assertEquals(DecisionOverrideType.USER, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    void shouldSavePricingOverrideForSpecificOverrideWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setSpecificOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertEquals(ONE, overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(ONE, meetingPackageBarDecision.getUserSpecifiedRate());
        assertEquals(DecisionOverrideType.USER, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    void shouldNotTriggerSpecificOverrideSyncEventWhenSpecificOverrideAppliedWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);

        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);

        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));

        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setSpecificOverride(ONE);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecision));

        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());

        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(meetingPackageBarDecisionOverrideRepository).saveAll(anyList());

        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    void shouldTriggerSpecificOverrideSyncChangeEventWhenSpecificOverrideAppliedWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);

        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);

        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));

        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setSpecificOverride(ONE);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecision));

        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());

        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(meetingPackageBarDecisionOverrideRepository).saveAll(anyList());

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    void shouldTriggerFloorOverrideSyncEventWhenFloorOverrideAppliedWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);

        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);

        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(
                occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));

        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setFloorOverride(ONE);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecision));

        when(meetingPackageBarDecisionOverrideRepository
                .getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());

        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(meetingPackageBarDecisionOverrideRepository).saveAll(anyList());

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldTriggerFloorOverrideSyncEventWhenFloorOverrideAppliedWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);

        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);

        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(
                occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));

        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setFloorOverride(ONE);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecision));

        when(meetingPackageBarDecisionOverrideRepository
                .getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());

        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(meetingPackageBarDecisionOverrideRepository).saveAll(anyList());

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldTriggerCeilingOverrideSyncEventWhenCeilingOverrideAppliedWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);

        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);

        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(
                occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));

        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setCeilingOverride(ONE);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecision));

        when(meetingPackageBarDecisionOverrideRepository
                .getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(meetingPackageBarDecisionOverrideRepository).saveAll(anyList());

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldTriggerCeilingOverrideSyncEventWhenCeilingOverrideAppliedWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);

        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);

        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(
                occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));

        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setCeilingOverride(ONE);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecision));

        when(meetingPackageBarDecisionOverrideRepository
                .getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());

        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(meetingPackageBarDecisionOverrideRepository).saveAll(anyList());

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldTriggerFloorCeilingOverrideSyncEventWhenFloorCeilingOverrideAppliedWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);

        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);

        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(
                occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));

        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setFloorOverride(ONE);
        pricingOverride.setCeilingOverride(ONE);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecision));

        when(meetingPackageBarDecisionOverrideRepository
                .getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());

        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(meetingPackageBarDecisionOverrideRepository).saveAll(anyList());

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldTriggerFloorCeilingOverrideSyncEventWhenFloorCeilingOverrideAppliedWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);

        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);

        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);

        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(
                occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));

        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setFloorOverride(ONE);
        pricingOverride.setCeilingOverride(ONE);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecision));

        when(meetingPackageBarDecisionOverrideRepository
                .getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());

        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(meetingPackageBarDecisionOverrideRepository).saveAll(anyList());

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldRegisterSpecificOverrideSyncEventWhenRemovedPricingOverrideDetailsWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        MeetingPackageProduct product = createMeetingPackageProduct();
        product.setId(2);
        FunctionSpaceFunctionRoom room = createFunctionSpaceFunctionRoom();
        room.setId(3);
        MeetingPackageBarDecision decision = createMeetingPackageBarDecision(occupancyDate, product, room);
        decision.setDecisionOverrideType(DecisionOverrideType.USER);

        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        MeetingPackageBarDecisionOverride override = createMeetingPackageBarDecisionOverride(occupancyDate, product, room, TEN, ZERO);
        override.setId(1L);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(eq(2), eq(3), anyList()))
                .thenReturn(List.of(decision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(eq(2), eq(3), anyList()))
                .thenReturn(List.of(override));
        when(decisionService.createMeetingPackagePricingOverrideDecision())
                .thenReturn(createNewDecision(1));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    void shouldRegisterSpecificOverrideSyncEventWhenRemovedPricingOverrideDetailsWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        MeetingPackageProduct product = createMeetingPackageProduct();
        product.setId(2);
        FunctionSpaceFunctionRoom room = createFunctionSpaceFunctionRoom();
        room.setId(3);
        MeetingPackageBarDecision decision = createMeetingPackageBarDecision(occupancyDate, product, room);
        decision.setDecisionOverrideType(DecisionOverrideType.USER);

        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        MeetingPackageBarDecisionOverride override = createMeetingPackageBarDecisionOverride(occupancyDate, product, room, TEN, ZERO);
        override.setId(1L);

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(eq(2), eq(3), anyList()))
                .thenReturn(List.of(decision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(eq(2), eq(3), anyList()))
                .thenReturn(List.of(override));
        when(decisionService.createMeetingPackagePricingOverrideDecision())
                .thenReturn(createNewDecision(1));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    void shouldRegisterFloorAndCeilingSyncEventWhenRemovedPricingOverrideDetailsWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        MeetingPackageProduct product = createMeetingPackageProduct();
        product.setId(2);
        FunctionSpaceFunctionRoom room = createFunctionSpaceFunctionRoom();
        room.setId(3);
        MeetingPackageBarDecision decision = createMeetingPackageBarDecision(occupancyDate, product, room);
        decision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);

        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        MeetingPackageBarDecisionOverride override = createMeetingPackageBarDecisionOverride(occupancyDate, product, room, TEN, ZERO);
        override.setId(1L);
        override.setNewFloorRate(valueOf(14));
        override.setNewCeilRate(valueOf(22));

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(eq(2), eq(3), anyList()))
                .thenReturn(List.of(decision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(eq(2), eq(3), anyList()))
                .thenReturn(List.of(override));
        when(decisionService.createMeetingPackagePricingOverrideDecision())
                .thenReturn(createNewDecision(1));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldRegisterFloorAndCeilingSyncEventWhenRemovedPricingOverrideDetailsWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        MeetingPackageProduct product = createMeetingPackageProduct();
        product.setId(2);
        FunctionSpaceFunctionRoom room = createFunctionSpaceFunctionRoom();
        room.setId(3);
        MeetingPackageBarDecision decision = createMeetingPackageBarDecision(occupancyDate, product, room);
        decision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);

        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        MeetingPackageBarDecisionOverride override = createMeetingPackageBarDecisionOverride(occupancyDate, product, room, TEN, ZERO);
        override.setId(1L);
        override.setNewFloorRate(valueOf(14));
        override.setNewCeilRate(valueOf(22));

        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(eq(2), eq(3), anyList()))
                .thenReturn(List.of(decision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(eq(2), eq(3), anyList()))
                .thenReturn(List.of(override));
        when(decisionService.createMeetingPackagePricingOverrideDecision())
                .thenReturn(createNewDecision(1));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSavePricingOverrideForFloorOverrideWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setFloorOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOOR, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(ONE, meetingPackageBarDecision.getFloorRate());
        assertEquals(DecisionOverrideType.FLOOR, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSavePricingOverrideForFloorOverrideWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setFloorOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOOR, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(ONE, meetingPackageBarDecision.getFloorRate());
        assertEquals(DecisionOverrideType.FLOOR, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSavePricingOverrideForCeilOverrideWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setCeilingOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.CEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewFloorRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(ONE, meetingPackageBarDecision.getCeilRate());
        assertEquals(DecisionOverrideType.CEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSavePricingOverrideForCeilOverrideWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, ONE, occupancyDate);
        pricingOverride.setCeilingOverride(ONE);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.CEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertNull(overrides.get(0).getNewFloorRate());
        assertNotNull(overrides.get(0).getCreateDate());
        assertEquals(ONE, meetingPackageBarDecision.getFinalBar());
        assertEquals(ONE, meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(ONE, meetingPackageBarDecision.getCeilRate());
        assertEquals(DecisionOverrideType.CEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, List.of(occupancyDate));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }


    @Test
    void shouldCreateNewPricingOverrideDetailsWhenExistingOverrideIsUpdatedWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(23.45));
        pricingOverride.setCeilingOverride(valueOf(32.45));
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(23.45), overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(32.45), overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(12, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(decisionService, times(1)).createMeetingPackagePricingOverrideDecision();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldCreateNewPricingOverrideDetailsWhenExistingOverrideIsUpdateWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(23.45));
        pricingOverride.setCeilingOverride(valueOf(32.45));
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(23.45), overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(32.45), overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(12, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(decisionService, times(1)).createMeetingPackagePricingOverrideDecision();
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldDeactivateOverrideWhenLinkedProductAndAdjustmentIsNullWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        meetingPackageProduct.setType(MeetingPackageProduct.PRODUCT_TYPE_LINKED);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setAdjustment(null);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void shouldDeactivateOverrideWhenLinkedProductAndAdjustmentIsNullWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        meetingPackageProduct.setType(MeetingPackageProduct.PRODUCT_TYPE_LINKED);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setAdjustment(null);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void shouldSetExistingActiveOverridesToInactiveWhenRemovingOverridesWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        meetingPackageProduct.setType(MeetingPackageProduct.PRODUCT_TYPE_LINKED);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setAdjustment(null);
        MeetingPackageProductRateOffsetOverride meetingPackageHistory = new MeetingPackageProductRateOffsetOverride();
        meetingPackageHistory.setStatus(ACTIVE);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(meetingPackageProductRateOffsetOverrideRepository.findActiveByProductAndOccupancyDate(meetingPackageProduct, occupancyDate)).thenReturn(
                List.of(meetingPackageHistory)
        );
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);

    }

    @Test
    void shouldSetExistingActiveOverridesToInactiveWhenRemovingOverridesWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        meetingPackageProduct.setType(MeetingPackageProduct.PRODUCT_TYPE_LINKED);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setAdjustment(null);
        MeetingPackageProductRateOffsetOverride meetingPackageHistory = new MeetingPackageProductRateOffsetOverride();
        meetingPackageHistory.setStatus(ACTIVE);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(12);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(meetingPackageProductRateOffsetOverrideRepository.findActiveByProductAndOccupancyDate(meetingPackageProduct, occupancyDate)).thenReturn(
                List.of(meetingPackageHistory)
        );
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(12, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);

    }

    @Test
    void shouldSavePricingOverrideForFloorAndCeilingOverrideWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(23.45));
        pricingOverride.setCeilingOverride(valueOf(32.45));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(14);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(14, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(1, overrides.get(0).getUserId());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(23.45), overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(32.45), overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(14, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSavePricingOverrideForFloorAndCeilingOverrideWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.NONE);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(23.45));
        pricingOverride.setCeilingOverride(valueOf(32.45));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of());
        Decision overrideDecision = createNewDecision(14);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(14, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(1, overrides.get(0).getUserId());
        assertEquals(DecisionOverrideType.NONE, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getNewOverrideType());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(valueOf(23.45), overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertEquals(valueOf(32.45), overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(14, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(23.45), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSaveRemovedPricingOverrideDetailsWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.USER);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, overrides.get(0).getNewOverrideType());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(120), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.NONE, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
    }

    @Test
    void shouldSaveRemovedPricingOverrideDetailsWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.USER);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, overrides.get(0).getNewOverrideType());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(120), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.NONE, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSaveRemovedFloorAndCeilingPricingOverrideDetailsWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, overrides.get(0).getNewOverrideType());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(120), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.NONE, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSaveRemovedFloorAndCeilingPricingOverrideDetailsWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, overrides.get(0).getNewOverrideType());
        assertNull(overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(120), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.NONE, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
    }

    @Test
    void shouldSaveRemovedFloorAndCeilingAndAddSpecificPricingOverrideDetailsWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        pricingOverride.setSpecificOverride(valueOf(16));

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getNewOverrideType());
        assertEquals(valueOf(16), overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(120), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.USER, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    void shouldSaveRemovedFloorAndCeilingAndAddSpecificPricingOverrideDetailsWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        pricingOverride.setSpecificOverride(valueOf(16));

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageBarDecisionOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageBarDecisionOverride> overrides = overridesCaptor.getValue();
        assertEquals(1, overrides.size());
        assertNull(overrides.get(0).getId());
        assertEquals(1, overrides.get(0).getDecisionId());
        assertEquals(2, overrides.get(0).getMeetingPackageProduct().getId());
        assertEquals(3, overrides.get(0).getFunctionSpaceFunctionRoom().getId());
        assertEquals(occupancyDate, overrides.get(0).getOccupancyDate());
        assertEquals(DecisionOverrideType.FLOORANDCEIL, overrides.get(0).getOldOverrideType());
        assertEquals(DecisionOverrideType.USER, overrides.get(0).getNewOverrideType());
        assertEquals(valueOf(16), overrides.get(0).getNewBar());
        assertEquals(TEN, overrides.get(0).getOldBar());
        assertNull(overrides.get(0).getNewFloorRate());
        assertEquals(valueOf(14), overrides.get(0).getOldFloorRate());
        assertNull(overrides.get(0).getNewCeilRate());
        assertEquals(valueOf(22), overrides.get(0).getOldCeilRate());
        assertEquals(1, meetingPackageBarDecision.getDecisionId());
        assertEquals(valueOf(120), meetingPackageBarDecision.getFinalBar());
        assertEquals(valueOf(120), meetingPackageBarDecision.getTotalPackagePrice());
        assertEquals(DecisionOverrideType.USER, meetingPackageBarDecision.getDecisionOverrideType());
        verify(meetingProductBarDecisionRepository).getMeetingPackageBarDecisionsBy(2, 3, occupancyDates);
        verify(meetingPackageBarDecisionOverrideRepository).getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }


    private static Decision createNewDecision(int decisionId) {
        Decision overrideDecision = new Decision();
        overrideDecision.setId(decisionId);
        return overrideDecision;
    }

    @Test
    public void shouldGetEmptyListWhenNoOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(emptyList());

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertTrue(actualOverrideHistory.isEmpty());
    }

    @Test
    public void shouldGeOverrideHistoryWhenSpecificOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.NONE.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.USER.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(valueOf(30.00));
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(null);
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertEquals(30.0, overrideDetails.getSpecificOverride());
        assertNull(overrideDetails.getFloorOverride());
        assertNull(overrideDetails.getCeilingOverride());
        assertEquals(false, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenOverrideRemoved() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.USER.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.PENDING.name());
        overrideHistory1.setOldBar(valueOf(10.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertEquals(10.0, overrideDetails.getSpecificOverride());
        assertNull(overrideDetails.getFloorOverride());
        assertNull(overrideDetails.getCeilingOverride());
        assertEquals(true, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenFloorOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.NONE.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.FLOOR.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(valueOf(10.00));
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertEquals(10.0, overrideDetails.getFloorOverride());
        assertNull(overrideDetails.getCeilingOverride());
        assertEquals(false, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenFloorOverrideRemoved() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.FLOOR.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.PENDING.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(valueOf(10.00));
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertEquals(10.0, overrideDetails.getFloorOverride());
        assertNull(overrideDetails.getCeilingOverride());
        assertEquals(true, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenCeilingOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.NONE.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.CEIL.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(valueOf(30.00));
        overrideHistory1.setOriginalDecision(null);
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertNull(overrideDetails.getFloorOverride());
        assertEquals(30.0, overrideDetails.getCeilingOverride());
        assertEquals(false, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenCeilingOverrideRemoved() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.CEIL.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.PENDING.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(valueOf(30.00));
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertNull(overrideDetails.getFloorOverride());
        assertEquals(30.0, overrideDetails.getCeilingOverride());
        assertEquals(true, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenFloorAndCeilingOverride() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.NONE.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.FLOORANDCEIL.name());
        overrideHistory1.setOldBar(valueOf(20.00));
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(null);
        overrideHistory1.setNewFloorRate(valueOf(10.00));
        overrideHistory1.setOldCeilingRate(null);
        overrideHistory1.setNewCeilingRate(valueOf(30.00));
        overrideHistory1.setOriginalDecision(null);
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertEquals(10.0, overrideDetails.getFloorOverride());
        assertEquals(30.0, overrideDetails.getCeilingOverride());
        assertEquals(false, overrideDetails.getRemoved());

    }

    @Test
    public void shouldGetUserOverrideHistoryWhenFloorAndCeilingOverrideRemoved() {
        Integer productId = 1;
        Integer functionRoomId = 5;
        Date occupancyDt = toDate(of(2025, 3, 1));
        LocalDateTime createDate = LocalDateTime.of(2025, 2, 20, 10, 15);
        MeetingPackageDecisionOverrideHistory overrideHistory1 = new MeetingPackageDecisionOverrideHistory();
        overrideHistory1.setOldOverride(DecisionOverrideType.FLOORANDCEIL.name());
        overrideHistory1.setNewOverride(DecisionOverrideType.PENDING.name());
        overrideHistory1.setOldBar(null);
        overrideHistory1.setNewBar(null);
        overrideHistory1.setOldFloorRate(valueOf(10.00));
        overrideHistory1.setNewFloorRate(null);
        overrideHistory1.setOldCeilingRate(valueOf(30.00));
        overrideHistory1.setNewCeilingRate(null);
        overrideHistory1.setOriginalDecision(valueOf(20.00));
        overrideHistory1.setUserName("SSO User");
        overrideHistory1.setCreateDate(createDate);
        when(meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt)).thenReturn(List.of(overrideHistory1));
        when(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(createDate))).thenReturn("20-Feb-2025 10:15:00 IST");

        List<PricingOverrideHistory> actualOverrideHistory = meetingPackageDecisionService.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDt);

        assertNotNull(actualOverrideHistory);
        assertFalse(actualOverrideHistory.isEmpty());
        PricingOverrideHistory pricingOverrideHistory = actualOverrideHistory.get(0);
        assertEquals(20.0, pricingOverrideHistory.getOriginalPrice());
        assertEquals("SSO User", pricingOverrideHistory.getUpdatedBy());
        assertEquals("20-Feb-2025 10:15:00 IST", pricingOverrideHistory.getUpdatedOn());
        assertNotNull(pricingOverrideHistory.getOverrideDetails());
        OverrideDetails overrideDetails = pricingOverrideHistory.getOverrideDetails();
        assertNull(overrideDetails.getSpecificOverride());
        assertEquals(10.0, overrideDetails.getFloorOverride());
        assertEquals(30.0, overrideDetails.getCeilingOverride());
        assertEquals(true, overrideDetails.getRemoved());

    }

    @Test
    void shouldCreateLastGoodDecisionsWhenLastGoodDecisionAndNewDecisionIdAreKnown() {
        Date startDate = toDate(of(2024, 7, 22));
        Date endDate = toDate(of(2025, 7, 22));
        Integer newDecisionId = 398;
        Integer lastGoodDecisionId = 392;
        when(dateService.getOptimizationWindowStartDate()).thenReturn(startDate);
        when(dateService.getDecisionUploadWindowEndDate()).thenReturn(endDate);
        when(meetingProductBarDecisionRepository.updateMeetingPackageBarDecisionWithLastKnownGoodDecisions(
                startDate, endDate, newDecisionId, lastGoodDecisionId)).thenReturn(5);
        when(meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(
                startDate, endDate)).thenReturn(3);

        meetingPackageDecisionService.createLastGoodDecisions(lastGoodDecisionId, newDecisionId);

        verify(meetingProductBarDecisionRepository, times(1))
                .updateMeetingPackageBarDecisionWithLastKnownGoodDecisions(startDate, endDate, newDecisionId, lastGoodDecisionId);
        verify(meetingPackagePaceBarDecisionRepository, times(1))
                .insertPaceMeetingPackageUploadDifferentialData(startDate, endDate);
    }

    @Test
    void shouldReturnNewMeetingRoomPriceAfterApplyingSpecificOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setSpecificOverride(valueOf(34));

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(valueOf(34), pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void shouldNotModifyMeetingRoomPriceWhenFloorOverrideIsLessThanMeetingRoomPrice() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(20));

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(valueOf(23.45), pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void shouldReturnNewMeetingRoomPriceAfterApplyingFloorOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setFloorOverride(valueOf(34));

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(valueOf(34), pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void shouldNotModifyMeetingRoomPriceWhenCeilingOverrideIsGreaterThanMeetingRoomPrice() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setCeilingOverride(valueOf(35));

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(valueOf(23.45), pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void shouldReturnNewMeetingRoomPriceAfterApplyingCeilingOverride() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025);
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(23.45), occupancyDate);
        pricingOverride.setCeilingOverride(TEN);

        List<PricingOverrideResult> pricingOverrideResults = meetingPackageDecisionService.applyPricingOverride(List.of(pricingOverride));

        assertEquals(1, pricingOverrideResults.size());
        PricingOverrideResult pricingOverrideResult = pricingOverrideResults.get(0);
        assertEquals(2, pricingOverrideResult.getProductId());
        assertEquals(LocalDateUtils.toLocalDate(occupancyDate), pricingOverrideResult.getOccupancyDate());
        assertEquals(3, pricingOverrideResult.getMeetingRoomId());
        assertEquals(TEN, pricingOverrideResult.getMeetingRoomPrice());
    }

    @Test
    void verifyThatMeetingPackageLinkedProductChangeSyncGetsTriggeredWhenAdjustmentOverrideWhenRecalculateOverrideIsEnabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        pricingOverride.setAdjustment(valueOf(30));

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(true);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageProductRateOffsetOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageProductRateOffsetOverride> rateOffsetOverridesToSave = overridesCaptor.getValue();
        assertNotNull(rateOffsetOverridesToSave);
        assertEquals(1, rateOffsetOverridesToSave.size());
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void verifyThatMeetingPackageLinkedProductChangeSyncGetsTriggeredWhenAdjustmentOverrideWhenRecalculateOverrideIsDisabled() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        List<Date> occupancyDates = List.of(occupancyDate);
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct();
        meetingPackageProduct.setId(2);
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(3);
        MeetingPackageBarDecision meetingPackageBarDecision = createMeetingPackageBarDecision(occupancyDate, meetingPackageProduct, functionSpaceFunctionRoom);
        meetingPackageBarDecision.setDecisionId(5L);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setDecisionOverrideType(DecisionOverrideType.FLOORANDCEIL);
        meetingPackageBarDecision.setFloorRate(valueOf(14));
        meetingPackageBarDecision.setCeilRate(valueOf(22));
        PricingOverride pricingOverride = getPricingOverride(2, 3, valueOf(120), occupancyDate);
        pricingOverride.setAdjustment(valueOf(30));

        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = createMeetingPackageBarDecisionOverride(occupancyDate,
                meetingPackageProduct, functionSpaceFunctionRoom, TEN, ZERO);
        meetingPackageBarDecisionOverride.setId(1L);
        meetingPackageBarDecisionOverride.setNewFloorRate(valueOf(14));
        meetingPackageBarDecisionOverride.setNewCeilRate(valueOf(22));
        when(meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(2, 3,
                occupancyDates)).thenReturn(List.of(meetingPackageBarDecision));
        when(meetingPackageBarDecisionOverrideRepository.getLatestMeetingPackageBarDecisionOverridesBy(2, 3, occupancyDates))
                .thenReturn(List.of(meetingPackageBarDecisionOverride));
        Decision overrideDecision = createNewDecision(1);
        when(decisionService.createMeetingPackagePricingOverrideDecision()).thenReturn(overrideDecision);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE)).thenReturn(false);

        meetingPackageDecisionService.savePricingOverrides(List.of(pricingOverride));

        ArgumentCaptor<List> overridesCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingPackageProductRateOffsetOverrideRepository).saveAll(overridesCaptor.capture());
        List<MeetingPackageProductRateOffsetOverride> rateOffsetOverridesToSave = overridesCaptor.getValue();
        assertNotNull(rateOffsetOverridesToSave);
        assertEquals(1, rateOffsetOverridesToSave.size());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
    }

    @Test
    void shouldRecalculateSpecificOverridesForNonBaseMeetingRoomsWithDefaultOffsets() {
        Date occupancyDate = DateUtil.getDate(20, 2, 2025, 0, 0, 0);
        PricingOverride baseOverride = getPricingOverride(2, 1, valueOf(120), occupancyDate);
        baseOverride.setSpecificOverride(BigDecimal.valueOf(200));
        PricingOverride nonBaseOverride = getPricingOverride(2, 3, valueOf(110), occupancyDate);
        nonBaseOverride.setSpecificOverride(BigDecimal.valueOf(200));
        Product primaryProduct = createPrimaryProduct();
        Map<Integer, PricingRule> pricingRuleMap = buildPrettyPricingRules("9", "9", "9");
        when(meetingPackageProductRepository.getBaseMeetingRoom()).thenReturn(createBaseMeetingRoom());
        when(meetingPackageProductRepository.getMPProductOffsetsForProducts(singleton(2))).thenReturn(createDefaultProductOffsets());
        when(meetingPackageProductRepository.getPrimaryPricingProduct()).thenReturn(primaryProduct);
        when(prettyPricingService.getPricingRules()).thenReturn(pricingRuleMap);
        when(syncFlagService.getStalenessFlags()).thenReturn(emptyList());
        ArgumentCaptor<List<PricingOverride>> overridesCaptor = ArgumentCaptor.forClass(List.class);
        doNothing().when(meetingPackageDecisionService).savePricingOverrides(anyList());
        when(meetingPackageOptimalPriceBARService.applyProductRoundingRule(primaryProduct.getId(), primaryProduct.getRoundingRule(), BigDecimal.valueOf(204.0), pricingRuleMap)).thenReturn(BigDecimal.valueOf(199.99));

        meetingPackageDecisionService.saveRecalculatedPricingOverrides(List.of(baseOverride, nonBaseOverride));

        verify(meetingPackageDecisionService).savePricingOverrides(overridesCaptor.capture());
        List<PricingOverride> updatedOverrides = overridesCaptor.getValue();
        assertEquals(1, updatedOverrides.get(0).getMeetingRoomId());
        assertEquals(new BigDecimal("200"), updatedOverrides.get(0).getSpecificOverride());
        assertEquals(3, updatedOverrides.get(1).getMeetingRoomId());
        assertEquals(new BigDecimal("199.99"), updatedOverrides.get(1).getSpecificOverride());
    }

    @Test
    void shouldRecalculateSpecificOverridesForNonBaseMeetingRoomsWithSeasonalOffsets() {
        // months start at 0 for Java util Calender, 1 is February
        Date occupancyDate = DateUtil.getDate(20, 1, 2025, 0, 0, 0);
        PricingOverride baseOverride = getPricingOverride(2, 1, valueOf(120), occupancyDate);
        baseOverride.setSpecificOverride(BigDecimal.valueOf(200));
        PricingOverride nonBaseOverride = getPricingOverride(2, 3, valueOf(110), occupancyDate);
        nonBaseOverride.setSpecificOverride(BigDecimal.valueOf(200));
        Product primaryProduct = createPrimaryProduct();
        Map<Integer, PricingRule> pricingRuleMap = buildPrettyPricingRules("9", "9", "9");
        when(meetingPackageProductRepository.getBaseMeetingRoom()).thenReturn(createBaseMeetingRoom());
        when(meetingPackageProductRepository.getMPProductOffsetsForProducts(singleton(2))).thenReturn(createSeasonalProductOffsets());
        when(meetingPackageProductRepository.getPrimaryPricingProduct()).thenReturn(createPrimaryProduct());
        when(prettyPricingService.getPricingRules()).thenReturn(pricingRuleMap);
        when(syncFlagService.getStalenessFlags()).thenReturn(emptyList());
        ArgumentCaptor<List<PricingOverride>> overridesCaptor = ArgumentCaptor.forClass(List.class);
        doNothing().when(meetingPackageDecisionService).savePricingOverrides(anyList());
        when(meetingPackageOptimalPriceBARService.applyProductRoundingRule(primaryProduct.getId(), primaryProduct.getRoundingRule(), new BigDecimal("214.00000"), pricingRuleMap)).thenReturn(BigDecimal.valueOf(209.99));

        meetingPackageDecisionService.saveRecalculatedPricingOverrides(List.of(baseOverride, nonBaseOverride));

        verify(meetingPackageDecisionService).savePricingOverrides(overridesCaptor.capture());
        List<PricingOverride> updatedOverrides = overridesCaptor.getValue();
        assertEquals(1, updatedOverrides.get(0).getMeetingRoomId());
        assertEquals(new BigDecimal("200"), updatedOverrides.get(0).getSpecificOverride());
        assertEquals(3, updatedOverrides.get(1).getMeetingRoomId());
        assertEquals(new BigDecimal("209.99"), updatedOverrides.get(1).getSpecificOverride());
    }

    @Test
    void shouldSkipChildLinkedProductPriceRecalculationIfNoBaseProductOverrides() {
        Product primaryProduct = createPrimaryProduct();
        Map<Integer, PricingRule> pricingRuleMap = buildPrettyPricingRules("9", "9", "9");

        meetingPackageDecisionService.recalculateChildProductPricesOfOverriddenBaseProduct(emptyList(), pricingRuleMap, primaryProduct);

        verifyNoInteractions(syncFlagService);
        verifyNoInteractions(meetingPackageProductService);
        verifyNoInteractions(meetingPackageOptimalPriceBARService);
        verifyNoInteractions(meetingProductBarDecisionRepository);
        verifyNoInteractions(meetingPackageProductRateOffsetOverrideRepository);
        verifyNoInteractions(meetingPackageProductRateOffsetRepository);

    }

    @Test
    void shouldSkipChildLinkedProductPriceRecalculationWhenBaseProductOverridesButSyncRequired() {
        Date occupancyDate = toDate(LocalDate.of(2025, 1, 20));
        Product primaryProduct = createPrimaryProduct();
        PricingOverride baseOverride = getPricingOverride(1, 1, valueOf(120), occupancyDate);
        baseOverride.setSpecificOverride(BigDecimal.valueOf(200));
        List<PricingOverride> baseOverrides = List.of(baseOverride);
        Map<Integer, PricingRule> pricingRuleMap = buildPrettyPricingRules("9", "9", "9");
        when(syncFlagService.getStalenessFlags()).thenReturn(List.of(new StalenessFlag("TEST_SYNC", "TestSync", true)));

        meetingPackageDecisionService.recalculateChildProductPricesOfOverriddenBaseProduct(baseOverrides, pricingRuleMap, primaryProduct);

        verify(syncFlagService).getStalenessFlags();
        verifyNoInteractions(meetingPackageProductService);
        verifyNoInteractions(meetingPackageOptimalPriceBARService);
        verifyNoInteractions(meetingProductBarDecisionRepository);
        verifyNoInteractions(meetingPackageProductRateOffsetOverrideRepository);
        verifyNoInteractions(meetingPackageProductRateOffsetRepository);

    }

    @Test
    void verifyChildLinkedProductPriceRecalculationWhenBaseProductHasNoChildren() {
        Date occupancyDate = toDate(LocalDate.of(2025, 1, 20));
        Product primaryProduct = createPrimaryProduct();
        MeetingPackageProduct meetingPackageProduct = createMeetingPackageProduct(1);
        PricingOverride baseOverride = getPricingOverride(meetingPackageProduct.getId(), 1, valueOf(120), occupancyDate);
        baseOverride.setSpecificOverride(BigDecimal.valueOf(200));
        List<PricingOverride> baseOverrides = List.of(baseOverride);
        Map<Integer, PricingRule> pricingRuleMap = buildPrettyPricingRules("9", "9", "9");
        when(syncFlagService.getStalenessFlags()).thenReturn(emptyList());
        when(meetingPackageProductService.getActiveMeetingPackageProducts()).thenReturn(List.of(meetingPackageProduct));
        when(meetingPackageOptimalPriceBARService.getProductIdToTotalOffsetMap(List.of(meetingPackageProduct.getId()))).thenReturn(Collections.emptyMap());

        meetingPackageDecisionService.recalculateChildProductPricesOfOverriddenBaseProduct(baseOverrides, pricingRuleMap, primaryProduct);

        ArgumentCaptor<List<MeetingPackageBarDecision>> mpBarDecisionsCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingProductBarDecisionRepository).saveAll(mpBarDecisionsCaptor.capture());
        assertTrue(mpBarDecisionsCaptor.getValue().isEmpty());
        verify(meetingProductBarDecisionRepository, never()).searchByFilterCriteria(any(MeetingPackageSearchCriteria.class));
        verifyNoInteractions(meetingPackageProductRateOffsetOverrideRepository);
        verifyNoInteractions(meetingPackageProductRateOffsetRepository);

    }

    @Test
    void verifyChildLinkedProductPriceRecalculationWithAdjustmentWhenBaseProductOverrides() {
        Date occupancyDate = toDate(LocalDate.of(2025, 1, 20));
        Product primaryProduct = createPrimaryProduct();
        Map<Integer, PricingRule> pricingRuleMap = buildPrettyPricingRules("9", "9", "9");
        MeetingPackageProduct independentMPProduct1 = createIdependentMeetingPackageProduct(1, "Full Day");
        MeetingPackageProduct linkedMPProduct1 = createLinkedMeetingPackageProduct(2, "Full Day F&B", independentMPProduct1.getId());
        BigDecimal baseOverridePrice1 = valueOf(200);
        PricingOverride baseOverride = getPricingOverride(independentMPProduct1.getId(), 1, valueOf(120), occupancyDate);
        baseOverride.setSpecificOverride(baseOverridePrice1);
        List<PricingOverride> baseOverrides = List.of(baseOverride);
        Map<Integer, BigDecimal> totalPackageElementOffsetMap = Map.of(linkedMPProduct1.getId(), valueOf(10));
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom(1, "101", "FR1");
        MeetingPackageBarDecision mpBarDecision1 = createMeetingPackageBarDecision(occupancyDate, independentMPProduct1, functionSpaceFunctionRoom);
        mpBarDecision1.setFinalBar(baseOverridePrice1);
        mpBarDecision1.setDecisionId(101L);
        MeetingPackageBarDecision mpBarDecision2 = createMeetingPackageBarDecision(occupancyDate, linkedMPProduct1, functionSpaceFunctionRoom);
        List<MeetingPackageBarDecision> mpDecisions = List.of(mpBarDecision1, mpBarDecision2);
        MeetingPackageProductRateOffset mpProductRateOffset = getMeetingPackageProductRateOffset(linkedMPProduct1, null, null, null);
        MeetingPackageProductRateOffset mpProductRateOffsetSeason = getMeetingPackageProductRateOffset(linkedMPProduct1, "Season 1", LocalDate.of(2025, 4, 1), LocalDate.of(2025, 4, 15));
        List<MeetingPackageProductRateOffset> mpProductRateOffsets = List.of(mpProductRateOffset, mpProductRateOffsetSeason);
        BigDecimal updatedPrice = BigDecimal.valueOf(210);
        BigDecimal prettyPrice = BigDecimal.valueOf(209);
        BigDecimal finalPrice = BigDecimal.valueOf(209);
        BigDecimal totalPackagePrice = BigDecimal.valueOf(219);
        when(syncFlagService.getStalenessFlags()).thenReturn(emptyList());
        when(meetingPackageProductService.getActiveMeetingPackageProducts()).thenReturn(List.of(independentMPProduct1, linkedMPProduct1));
        when(meetingPackageOptimalPriceBARService.getProductIdToTotalOffsetMap(List.of(independentMPProduct1.getId(), linkedMPProduct1.getId()))).thenReturn(totalPackageElementOffsetMap);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class))).thenReturn(mpDecisions);
        when(meetingPackageProductRateOffsetOverrideRepository.findActiveOverridesForProductOccupancyPairs(eq(Set.of(linkedMPProduct1.getId())), eq(Set.of(occupancyDate)), eq(TenantStatusEnum.ACTIVE))).thenReturn(emptyList());
        when(meetingPackageProductRateOffsetRepository.getMeetingPackageProductRateAdjustments(eq(List.of(linkedMPProduct1.getId())))).thenReturn(mpProductRateOffsets);
        when(meetingPackageOptimalPriceBARService.applyProductRoundingRule(primaryProduct.getId(), primaryProduct.getRoundingRule(), updatedPrice, pricingRuleMap)).thenReturn(finalPrice);
        when(meetingPackageOptimalPriceBARService.checkPrettyPriceAgainstFloorRate(eq(finalPrice), eq(linkedMPProduct1))).thenReturn(finalPrice);
        when(meetingPackageOptimalPriceBARService.getTotalPackagePrice(eq(finalPrice), eq(linkedMPProduct1.getId()), eq(totalPackageElementOffsetMap))).thenReturn(totalPackagePrice);

        meetingPackageDecisionService.recalculateChildProductPricesOfOverriddenBaseProduct(baseOverrides, pricingRuleMap, primaryProduct);

        ArgumentCaptor<List<MeetingPackageBarDecision>> mpBarDecisionsCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingProductBarDecisionRepository).saveAll(mpBarDecisionsCaptor.capture());
        List<MeetingPackageBarDecision> actualMPBarDecisions = mpBarDecisionsCaptor.getValue();
        assertFalse(actualMPBarDecisions.isEmpty());
        MeetingPackageBarDecision meetingPackageBarDecision = actualMPBarDecisions.get(0);
        assertEquals(linkedMPProduct1.getId(), meetingPackageBarDecision.getMeetingPackageProduct().getId());
        assertEquals(101L, meetingPackageBarDecision.getDecisionId());
        assertEquals(prettyPrice, meetingPackageBarDecision.getPrettyBar());
        assertEquals(finalPrice, meetingPackageBarDecision.getFinalBar());
        assertEquals(totalPackagePrice, meetingPackageBarDecision.getTotalPackagePrice());

    }

    @Test
    void verifyChildLinkedProductPriceRecalculationWithAdjustmentSeasonWhenBaseProductOverrides() {
        Date occupancyDate = toDate(LocalDate.of(2025, 1, 20));
        Product primaryProduct = createPrimaryProduct();
        Map<Integer, PricingRule> pricingRuleMap = buildPrettyPricingRules("5", "9", "9");
        MeetingPackageProduct independentMPProduct1 = createIdependentMeetingPackageProduct(1, "Full Day");
        MeetingPackageProduct linkedMPProduct1 = createLinkedMeetingPackageProduct(2, "Full Day F&B", independentMPProduct1.getId());
        BigDecimal baseOverridePrice1 = valueOf(200);
        PricingOverride baseOverride = getPricingOverride(independentMPProduct1.getId(), 1, valueOf(120), occupancyDate);
        baseOverride.setSpecificOverride(baseOverridePrice1);
        List<PricingOverride> baseOverrides = List.of(baseOverride);
        Map<Integer, BigDecimal> totalPackageElementOffsetMap = Map.of(linkedMPProduct1.getId(), valueOf(10));
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom(1, "101", "FR1");
        MeetingPackageBarDecision mpBarDecision1 = createMeetingPackageBarDecision(occupancyDate, independentMPProduct1, functionSpaceFunctionRoom);
        mpBarDecision1.setFinalBar(baseOverridePrice1);
        mpBarDecision1.setDecisionId(101L);
        MeetingPackageBarDecision mpBarDecision2 = createMeetingPackageBarDecision(occupancyDate, linkedMPProduct1, functionSpaceFunctionRoom);
        List<MeetingPackageBarDecision> mpDecisions = List.of(mpBarDecision1, mpBarDecision2);
        MeetingPackageProductRateOffset mpProductRateOffset = getMeetingPackageProductRateOffset(linkedMPProduct1, null, null, null);
        MeetingPackageProductRateOffset mpProductRateOffsetSeason = getMeetingPackageProductRateOffset(linkedMPProduct1, "Season 1", LocalDate.of(2025, 1, 18), LocalDate.of(2025, 1, 25));
        mpProductRateOffsetSeason.setMondayOffsetValueFloor(BigDecimal.valueOf(5));
        mpProductRateOffsetSeason.setMondayOffsetValueCeiling(BigDecimal.valueOf(5));
        List<MeetingPackageProductRateOffset> mpProductRateOffsets = List.of(mpProductRateOffset, mpProductRateOffsetSeason);
        BigDecimal updatedPrice = BigDecimal.valueOf(205);
        BigDecimal prettyPrice = BigDecimal.valueOf(205);
        BigDecimal finalPrice = BigDecimal.valueOf(205);
        BigDecimal totalPackagePrice = BigDecimal.valueOf(215);
        when(syncFlagService.getStalenessFlags()).thenReturn(emptyList());
        when(meetingPackageProductService.getActiveMeetingPackageProducts()).thenReturn(List.of(independentMPProduct1, linkedMPProduct1));
        when(meetingPackageOptimalPriceBARService.getProductIdToTotalOffsetMap(List.of(independentMPProduct1.getId(), linkedMPProduct1.getId()))).thenReturn(totalPackageElementOffsetMap);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class))).thenReturn(mpDecisions);
        when(meetingPackageProductRateOffsetOverrideRepository.findActiveOverridesForProductOccupancyPairs(eq(Set.of(linkedMPProduct1.getId())), eq(Set.of(occupancyDate)), eq(TenantStatusEnum.ACTIVE))).thenReturn(emptyList());
        when(meetingPackageProductRateOffsetRepository.getMeetingPackageProductRateAdjustments(eq(List.of(linkedMPProduct1.getId())))).thenReturn(mpProductRateOffsets);
        when(meetingPackageOptimalPriceBARService.applyProductRoundingRule(primaryProduct.getId(), primaryProduct.getRoundingRule(), updatedPrice, pricingRuleMap)).thenReturn(finalPrice);
        when(meetingPackageOptimalPriceBARService.checkPrettyPriceAgainstFloorRate(eq(finalPrice), eq(linkedMPProduct1))).thenReturn(finalPrice);
        when(meetingPackageOptimalPriceBARService.getTotalPackagePrice(eq(finalPrice), eq(linkedMPProduct1.getId()), eq(totalPackageElementOffsetMap))).thenReturn(totalPackagePrice);

        meetingPackageDecisionService.recalculateChildProductPricesOfOverriddenBaseProduct(baseOverrides, pricingRuleMap, primaryProduct);

        ArgumentCaptor<List<MeetingPackageBarDecision>> mpBarDecisionsCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingProductBarDecisionRepository).saveAll(mpBarDecisionsCaptor.capture());
        List<MeetingPackageBarDecision> actualMPBarDecisions = mpBarDecisionsCaptor.getValue();
        assertFalse(actualMPBarDecisions.isEmpty());
        MeetingPackageBarDecision meetingPackageBarDecision = actualMPBarDecisions.get(0);
        assertEquals(linkedMPProduct1.getId(), meetingPackageBarDecision.getMeetingPackageProduct().getId());
        assertEquals(101L, meetingPackageBarDecision.getDecisionId());
        assertEquals(prettyPrice, meetingPackageBarDecision.getPrettyBar());
        assertEquals(finalPrice, meetingPackageBarDecision.getFinalBar());
        assertEquals(totalPackagePrice, meetingPackageBarDecision.getTotalPackagePrice());

    }

    @Test
    void verifyChildLinkedProductPriceRecalculationWithAdjustmentOverrideWhenBaseProductOverrides() {
        Date occupancyDate = toDate(LocalDate.of(2025, 1, 20));
        Product primaryProduct = createPrimaryProduct();
        Map<Integer, PricingRule> pricingRuleMap = buildPrettyPricingRules("0", "0", "0");
        MeetingPackageProduct independentMPProduct1 = createIdependentMeetingPackageProduct(1, "Full Day");
        MeetingPackageProduct linkedMPProduct1 = createLinkedMeetingPackageProduct(2, "Full Day F&B", independentMPProduct1.getId());
        BigDecimal baseOverridePrice1 = valueOf(200);
        PricingOverride baseOverride = getPricingOverride(independentMPProduct1.getId(), 1, valueOf(120), occupancyDate);
        baseOverride.setSpecificOverride(baseOverridePrice1);
        List<PricingOverride> baseOverrides = List.of(baseOverride);
        Map<Integer, BigDecimal> totalPackageElementOffsetMap = Map.of(linkedMPProduct1.getId(), valueOf(10));
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom(1, "101", "FR1");
        MeetingPackageBarDecision mpBarDecision1 = createMeetingPackageBarDecision(occupancyDate, independentMPProduct1, functionSpaceFunctionRoom);
        mpBarDecision1.setFinalBar(baseOverridePrice1);
        mpBarDecision1.setDecisionId(101L);
        MeetingPackageBarDecision mpBarDecision2 = createMeetingPackageBarDecision(occupancyDate, linkedMPProduct1, functionSpaceFunctionRoom);
        List<MeetingPackageBarDecision> mpDecisions = List.of(mpBarDecision1, mpBarDecision2);
        MeetingPackageProductRateOffset mpProductRateOffset = getMeetingPackageProductRateOffset(linkedMPProduct1, null, null, null);
        MeetingPackageProductRateOffset mpProductRateOffsetSeason = getMeetingPackageProductRateOffset(linkedMPProduct1, "Season 1", LocalDate.of(2025, 1, 18), LocalDate.of(2025, 1, 25));
        mpProductRateOffsetSeason.setMondayOffsetValueFloor(BigDecimal.valueOf(5));
        mpProductRateOffsetSeason.setMondayOffsetValueCeiling(BigDecimal.valueOf(5));
        List<MeetingPackageProductRateOffset> mpProductRateOffsets = List.of(mpProductRateOffset, mpProductRateOffsetSeason);
        BigDecimal adjustmentOverride = BigDecimal.valueOf(-10);
        MeetingPackageProductRateOffsetOverride productRateOffsetOverride = getMeetingPackageProductRateOffsetOverride(occupancyDate, linkedMPProduct1, adjustmentOverride);
        List<MeetingPackageProductRateOffsetOverride> productRateOffsetOverrides = List.of(productRateOffsetOverride);
        BigDecimal updatedPrice = BigDecimal.valueOf(190);
        BigDecimal prettyPrice = BigDecimal.valueOf(190);
        BigDecimal finalPrice = BigDecimal.valueOf(190);
        BigDecimal totalPackagePrice = BigDecimal.valueOf(200);
        when(syncFlagService.getStalenessFlags()).thenReturn(emptyList());
        when(meetingPackageProductService.getActiveMeetingPackageProducts()).thenReturn(List.of(independentMPProduct1, linkedMPProduct1));
        when(meetingPackageOptimalPriceBARService.getProductIdToTotalOffsetMap(List.of(independentMPProduct1.getId(), linkedMPProduct1.getId()))).thenReturn(totalPackageElementOffsetMap);
        when(meetingProductBarDecisionRepository.searchByFilterCriteria(any(MeetingPackageSearchCriteria.class))).thenReturn(mpDecisions);
        when(meetingPackageProductRateOffsetOverrideRepository.findActiveOverridesForProductOccupancyPairs(eq(Set.of(linkedMPProduct1.getId())), eq(Set.of(occupancyDate)), eq(TenantStatusEnum.ACTIVE))).thenReturn(productRateOffsetOverrides);
        when(meetingPackageProductRateOffsetRepository.getMeetingPackageProductRateAdjustments(eq(List.of(linkedMPProduct1.getId())))).thenReturn(mpProductRateOffsets);
        when(meetingPackageOptimalPriceBARService.applyProductRoundingRule(primaryProduct.getId(), primaryProduct.getRoundingRule(), updatedPrice, pricingRuleMap)).thenReturn(finalPrice);
        when(meetingPackageOptimalPriceBARService.checkPrettyPriceAgainstFloorRate(eq(finalPrice), eq(linkedMPProduct1))).thenReturn(finalPrice);
        when(meetingPackageOptimalPriceBARService.getTotalPackagePrice(eq(finalPrice), eq(linkedMPProduct1.getId()), eq(totalPackageElementOffsetMap))).thenReturn(totalPackagePrice);

        meetingPackageDecisionService.recalculateChildProductPricesOfOverriddenBaseProduct(baseOverrides, pricingRuleMap, primaryProduct);

        ArgumentCaptor<List<MeetingPackageBarDecision>> mpBarDecisionsCaptor = ArgumentCaptor.forClass(List.class);
        verify(meetingProductBarDecisionRepository).saveAll(mpBarDecisionsCaptor.capture());
        List<MeetingPackageBarDecision> actualMPBarDecisions = mpBarDecisionsCaptor.getValue();
        assertFalse(actualMPBarDecisions.isEmpty());
        MeetingPackageBarDecision meetingPackageBarDecision = actualMPBarDecisions.get(0);
        assertEquals(linkedMPProduct1.getId(), meetingPackageBarDecision.getMeetingPackageProduct().getId());
        assertEquals(101L, meetingPackageBarDecision.getDecisionId());
        assertEquals(prettyPrice, meetingPackageBarDecision.getPrettyBar());
        assertEquals(finalPrice, meetingPackageBarDecision.getFinalBar());
        assertEquals(totalPackagePrice, meetingPackageBarDecision.getTotalPackagePrice());

    }

    @Test
    void shouldGetAdjustmentOverrideHistoryWhenActiveAdjustmentOverride() {
        Integer productId = 101;
        Date occupancyDate = new Date();
        MeetingPackageProductRateOffsetOverride override = new MeetingPackageProductRateOffsetOverride();
        override.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        override.setOffsetValue(new BigDecimal("30.0"));
        override.setStatus(ACTIVE);
        override.setLastUpdatedByUserId(1);
        override.setLastUpdatedDate(new Date());
        List<MeetingPackageProductRateOffsetOverride> overrides = List.of(override);
        when(meetingPackageProductRateOffsetOverrideRepository.findAllAdjustmentsByProductAndOccupancyDate(productId, occupancyDate))
                .thenReturn(overrides);
        when(dateService.formatDateToPropertyTimeZone(LocalDateUtils.toDate(override.getLastUpdatedDate())))
                .thenReturn("26-May-2025 10:31:02 MST");
        User user = new User();
        user.setId(1);
        user.setName("SSO User");
        MeetingPackageDecisionService spyService = spy(meetingPackageDecisionService);
        doReturn(List.of(user)).when(spyService).getUsers(overrides);

        List<MeetingPackageAdjustmentOverrideHistory> result =
                spyService.getMeetingPackageAdjustmentOverrideHistory(productId, occupancyDate);

        assertEquals(1, result.size());
        MeetingPackageAdjustmentOverrideHistory overrideHistory = result.get(0);
        assertEquals("Fixed", overrideHistory.getOffsetType());
        assertEquals(valueOf(30.00).setScale(2), overrideHistory.getAdjustedOffset());
        assertEquals("SSO User", overrideHistory.getUpdatedBy());
        assertFalse(overrideHistory.isRemoved());
        assertNotNull(overrideHistory.getUpdatedOn());
    }

    @Test
    void shouldGetAdjustmentOverrideHistoryWhenOverridesAddedAndRemoved() {
        Integer productId = 101;
        Date occupancyDate = new Date();
        Date createDate = new Date(System.currentTimeMillis() - 10_000);
        Date lastUpdatedDate = new Date(System.currentTimeMillis());
        MeetingPackageProductRateOffsetOverride override = new MeetingPackageProductRateOffsetOverride();
        override.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        override.setOffsetValue(new BigDecimal("25.5"));
        override.setStatus(TenantStatusEnum.DELETED);
        override.setLastUpdatedByUserId(1);
        override.setCreatedByUserId(2);
        override.setCreateDate(createDate);
        override.setLastUpdatedDate(lastUpdatedDate);
        List<MeetingPackageProductRateOffsetOverride> overrides = List.of(override);
        when(meetingPackageProductRateOffsetOverrideRepository.findAllAdjustmentsByProductAndOccupancyDate(productId, occupancyDate))
                .thenReturn(overrides);
        when(dateService.formatDateToPropertyTimeZone(LocalDateUtils.toDate(override.getLastUpdatedDate())))
                .thenReturn("26-May-2025 10:35:02 MST");
        when(dateService.formatDateToPropertyTimeZone(LocalDateUtils.toDate(override.getCreateDate())))
                .thenReturn("25-May-2025 10:31:02 MST");
        User user1 = new User();
        user1.setId(1);
        user1.setName("Remover");
        User user2 = new User();
        user2.setId(2);
        user2.setName("Creator");
        MeetingPackageDecisionService spyService = spy(meetingPackageDecisionService);
        doReturn(List.of(user1, user2)).when(spyService).getUsers(overrides);

        List<MeetingPackageAdjustmentOverrideHistory> result =
                spyService.getMeetingPackageAdjustmentOverrideHistory(productId, occupancyDate);

        assertEquals(2, result.size());
        MeetingPackageAdjustmentOverrideHistory removed = result.get(0);
        MeetingPackageAdjustmentOverrideHistory created = result.get(1);
        assertEquals("Fixed", removed.getOffsetType());
        assertEquals(valueOf(25.50).setScale(2), removed.getAdjustedOffset());
        assertTrue(removed.isRemoved());
        assertEquals("Remover", removed.getUpdatedBy());
        assertEquals("Fixed", created.getOffsetType());
        assertEquals(valueOf(25.50).setScale(2), created.getAdjustedOffset());
        assertFalse(created.isRemoved());
        assertEquals("Creator", created.getUpdatedBy());
    }

    @Test
    public void shouldCreateSingleEntryWhenOnlyOneDecisionExists() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date, 500.00);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1));

        assertEquals(1, result.size());
        assertEquals(10, result.values().iterator().next().getDecisionId());
    }

    @Test
    public void shouldGetHigherDecisionIdWhenKeysAreDuplicate() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date, 500.00);
        MeetingPackagePaceBarDecision d2 = createDecision(20, 1, 1, date, 500.00);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(1, result.size());
        assertEquals(20, result.values().iterator().next().getDecisionId());
    }

    @Test
    public void shouldIgnoreLowerDecisionIdWhenKeysAreDuplicate() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(20, 1, 1, date, 500.00);
        MeetingPackagePaceBarDecision d2 = createDecision(10, 1, 1, date, 500.00);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(1, result.size());
        assertEquals(20, result.values().iterator().next().getDecisionId());
    }

    @Test
    public void shouldCreateTwoEntriesForDifferentProductsSameRoomAndDate() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date, 500.00);
        MeetingPackagePaceBarDecision d2 = createDecision(15, 2, 1, date, 500.00);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(2, result.size());
    }

    @Test
    public void shouldCreateTwoEntriesForDifferentRoomsSameProductAndDate() {
        Date date = getDate("2025-07-01");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date, 500.00);
        MeetingPackagePaceBarDecision d2 = createDecision(15, 1, 2, date, 500.00);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(2, result.size());
    }

    @Test
    public void shouldCreateTwoEntriesForSameProductAndRoomButDifferentDates() {
        Date date1 = getDate("2025-07-01");
        Date date2 = getDate("2025-07-02");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date1, 500.00);
        MeetingPackagePaceBarDecision d2 = createDecision(15, 1, 1, date2, 500.00);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(2, result.size());
    }

    @Test
    public void shouldKeepFirstEntryWhenExactDuplicatesExist() {
        Date date = getDate("2025-02-25");
        MeetingPackagePaceBarDecision d1 = createDecision(10, 1, 1, date, 500.00);
        MeetingPackagePaceBarDecision d2 = createDecision(10, 1, 1, date, 500.00);

        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> result =
                meetingPackageDecisionService.getMeetingPackagePaceBarDecisionMap(List.of(d1, d2));

        assertEquals(1, result.size());
        assertEquals(10, result.values().iterator().next().getDecisionId());
    }

    @Test
    void shouldGetChangesSinceDecisionId() throws ParseException {
        Date changesSinceDate = DateUtil.parseDate("2025-07-20 14:00:00", DateUtil.DATE_TIME_FORMAT);
        when(decisionService.getLastDecisionIdSinceDate(changesSinceDate)).thenReturn(1);

        Optional<Integer> result = meetingPackageDecisionService.getChangesSinceDecisionId("changesSince", changesSinceDate);

        assertTrue(result.isPresent());
        assertEquals(1, result.get());
        verify(decisionService).getLastDecisionIdSinceDate(changesSinceDate);
    }

    @Test
    void shouldReturnEmptyWhenChangesSinceDateIsNotSent() {
        Optional<Integer> result = meetingPackageDecisionService.getChangesSinceDecisionId("changesSince", null);

        assertFalse(result.isPresent());
        verify(decisionService, never()).getLastDecisionIdSinceDate(any(Date.class));
    }

    @Test
    void shouldGetChangesSinceBDEDecisionId() {
        when(decisionService.getLastBDEDecisionId()).thenReturn(1);

        Optional<Integer> result = meetingPackageDecisionService.getChangesSinceDecisionId("changesSinceLastBDE", null);

        assertTrue(result.isPresent());
        assertEquals(1, result.get());
        verify(decisionService).getLastBDEDecisionId();
    }

    @Test
    void shouldGetChangesSinceIDPDecisionId() {
        when(decisionService.getLastCDPDecisionId()).thenReturn(1);

        Optional<Integer> result = meetingPackageDecisionService.getChangesSinceDecisionId("changesSinceLastIDP", null);

        assertTrue(result.isPresent());
        assertEquals(1, result.get());
        verify(decisionService).getLastCDPDecisionId();
    }

    @Test
    void shouldReturnEmptyWhenIncorrectDatesWithFilterIsSent() throws ParseException {
        Date changesSinceDate = DateUtil.parseDate("2025-07-20 14:00:00", DateUtil.DATE_TIME_FORMAT);

        Optional<Integer> result = meetingPackageDecisionService.getChangesSinceDecisionId("changesSinceRandom", changesSinceDate);

        assertFalse(result.isPresent());
        verify(decisionService, never()).getLastDecisionIdSinceDate(any(Date.class));
        verify(decisionService, never()).getLastBDEDecisionId();
        verify(decisionService, never()).getLastCDPDecisionId();
    }

    @Test
    void shouldGetMeetingPackagePaceDecisions() {
        var productIds = List.of(1);
        var meetingRoomsIds = List.of(1, 2);
        Date startDate = getDate("2025-07-02");
        LocalDate localStartDate = fromDate(startDate);
        Date endDate = getDate("2025-07-05");
        LocalDate localEndDate = fromDate(endDate);
        int decisionId = 12583;
        when(meetingPackagePaceBarDecisionRepository.getLatestDecisionUptoDecisionId(localStartDate, localEndDate,
                productIds, meetingRoomsIds, decisionId)).thenReturn(List.of(new MeetingPackagePaceBarDecision()));

        List<MeetingPackagePaceBarDecision> result = meetingPackageDecisionService.getMeetingPackagePaceDecisions(
                startDate, endDate, meetingRoomsIds, productIds, decisionId);

        assertEquals(1, result.size());
        verify(meetingPackagePaceBarDecisionRepository).getLatestDecisionUptoDecisionId(localStartDate, localEndDate,
                productIds, meetingRoomsIds, decisionId);
    }

    @Test
    void shouldFilterUnchangedPrices() {
        int productId = 1;
        int meetingRoomId1 = 1;
        int meetingRoomId2 = 2;
        var date = getDate("2025-02-25");
        var meetingPackagePricingPerDay = createMeetingPackagePricingPerDay(date, new ArrayList<>(List.of(
                createMeetingPackagePriceDetail(productId, new ArrayList<>(List.of(
                        createMeetingRoomPrice(meetingRoomId1, 785.00),
                        createMeetingRoomPrice(meetingRoomId2, 785.00)))))));
        var decision1 = createDecision(10, 1, meetingRoomId1, date, 799.00);
        var decision2 = createDecision(10, 1, meetingRoomId2, date, 785.00);

        meetingPackageDecisionService.filterUnchangedPrices(Stream.of(meetingPackagePricingPerDay).collect(toCollection(ArrayList::new)),
                Stream.of(decision1, decision2).collect(toCollection(ArrayList::new)));

        assertEquals(1, meetingPackagePricingPerDay.getPriceDetails().size());
        assertEquals(1, meetingPackagePricingPerDay.getPriceDetails().get(0).getMeetingRoomPrices().size());
    }

    @Test
    void shouldGetProductAdjustmentsFromProductRateOffsetsByProductAndOccupancyDate() {
        Date occupancyDate = toDate(LocalDate.of(2025, 1, 20));
        MeetingPackageProduct independentMPProduct1 = createIdependentMeetingPackageProduct(1, "Full Day");
        MeetingPackageProduct linkedMPProduct1 = createLinkedMeetingPackageProduct(2, "Full Day F&B", independentMPProduct1.getId());
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = createFunctionSpaceFunctionRoom(1, "101", "FR1");
        MeetingPackageBarDecision mpBarDecision1 = createMeetingPackageBarDecision(occupancyDate, independentMPProduct1, functionSpaceFunctionRoom);
        MeetingPackageBarDecision mpBarDecision2 = createMeetingPackageBarDecision(occupancyDate, linkedMPProduct1, functionSpaceFunctionRoom);
        List<MeetingPackageBarDecision> mpDecisions = List.of(mpBarDecision1, mpBarDecision2);
        MeetingPackageProductRateOffset mpProductRateOffset = getMeetingPackageProductRateOffset(linkedMPProduct1, null, null, null);
        MeetingPackageProductRateOffset mpProductRateOffsetSeason = getMeetingPackageProductRateOffset(linkedMPProduct1, "Season 1", LocalDate.of(2025, 4, 1), LocalDate.of(2025, 4, 15));
        List<MeetingPackageProductRateOffset> mpProductRateOffsets = List.of(mpProductRateOffset, mpProductRateOffsetSeason);
        List<Integer> productIds = List.of(independentMPProduct1.getId(), linkedMPProduct1.getId());
        when(meetingPackageProductRateOffsetRepository.getMeetingPackageProductRateAdjustments(productIds)).thenReturn(mpProductRateOffsets);

        Map<ProductOccupancyKey, BigDecimal> productAdjustmentsMap = meetingPackageDecisionService.getProductAdjustmentsFromProductRateOffsets(mpDecisions, productIds);

        assertFalse(productAdjustmentsMap.isEmpty());
        assertEquals(1, productAdjustmentsMap.size());
        assertEquals(TEN, productAdjustmentsMap.get(new ProductOccupancyKey(2, occupancyDate)));

    }

    private static Date getDate(String dateString) {
        try {
            return DateUtil.parseDate(dateString, DateUtil.DEFAULT_DATE_FORMAT);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private static PricingOverride getPricingOverride(Integer productId, Integer meetingRoomId,
                                                      BigDecimal meetingRoomPrice, Date occupancyDate) {
        PricingOverride pricingOverrideDetail = new PricingOverride();
        pricingOverrideDetail.setProductId(productId);
        pricingOverrideDetail.setMeetingRoomId(meetingRoomId);
        pricingOverrideDetail.setMeetingRoomPrice(meetingRoomPrice);
        pricingOverrideDetail.setStartDate(LocalDateUtils.toJavaLocalDate(occupancyDate));
        pricingOverrideDetail.setEndDate(LocalDateUtils.toJavaLocalDate(occupancyDate));
        return pricingOverrideDetail;
    }

    private static MeetingPackageBarDecisionOverride createMeetingPackageBarDecisionOverride(Date occupancyDate,
                                                                                             MeetingPackageProduct meetingPackageProduct,
                                                                                             FunctionSpaceFunctionRoom functionSpaceFunctionRoom,
                                                                                             BigDecimal oldBar, BigDecimal newBar) {
        MeetingPackageBarDecisionOverride meetingPackageBarDecision = new MeetingPackageBarDecisionOverride();
        meetingPackageBarDecision.setMeetingPackageProduct(meetingPackageProduct);
        meetingPackageBarDecision.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        meetingPackageBarDecision.setOccupancyDate(occupancyDate);
        meetingPackageBarDecision.setOldBar(oldBar);
        meetingPackageBarDecision.setNewBar(newBar);
        return meetingPackageBarDecision;
    }

    private static MeetingPackageBarDecision createMeetingPackageBarDecision(Date occupancyDate,
                                                                             MeetingPackageProduct meetingPackageProduct,
                                                                             FunctionSpaceFunctionRoom functionSpaceFunctionRoom) {
        MeetingPackageBarDecision meetingPackageBarDecision = new MeetingPackageBarDecision();
        meetingPackageBarDecision.setMeetingPackageProduct(meetingPackageProduct);
        meetingPackageBarDecision.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        meetingPackageBarDecision.setOccupancyDate(occupancyDate);
        meetingPackageBarDecision.setFinalBar(TEN);
        meetingPackageBarDecision.setTotalPackagePrice(BigDecimal.valueOf(15.00));
        return meetingPackageBarDecision;
    }

    private static MeetingPackagePaceBarDecision createMeetingPackagePaceBarDecision(Date occupancyDate,
                                                                                     MeetingPackageProduct meetingPackageProduct,
                                                                                     FunctionSpaceFunctionRoom functionSpaceFunctionRoom) {
        MeetingPackagePaceBarDecision meetingPackagePaceBarDecision = new MeetingPackagePaceBarDecision();
        meetingPackagePaceBarDecision.setMeetingPackageProduct(meetingPackageProduct);
        meetingPackagePaceBarDecision.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        meetingPackagePaceBarDecision.setOccupancyDate(occupancyDate);
        meetingPackagePaceBarDecision.setFinalBar(TEN);
        meetingPackagePaceBarDecision.setTotalPackagePrice(BigDecimal.valueOf(15.00));
        return meetingPackagePaceBarDecision;
    }

    private static MeetingPackageProduct createMeetingPackageProduct() {
        MeetingPackageProduct meetingPackageProduct = new MeetingPackageProduct();
        meetingPackageProduct.setName("MP_Product");
        return meetingPackageProduct;
    }

    private static FunctionSpaceFunctionRoom createFunctionSpaceFunctionRoom() {
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = new FunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setSalesCateringIdentifier("Meeting_Room");
        return functionSpaceFunctionRoom;
    }

    private static FunctionSpaceFunctionRoom createFunctionSpaceFunctionRoom(Integer functionRoomId, String scIdentifier, String name) {
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = new FunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setId(functionRoomId);
        functionSpaceFunctionRoom.setSalesCateringIdentifier(scIdentifier);
        functionSpaceFunctionRoom.setName(name);
        functionSpaceFunctionRoom.setDiaryName(name);
        return functionSpaceFunctionRoom;
    }

    private BaseMeetingRoom createBaseMeetingRoom() {
        BaseMeetingRoom baseMeetingRoom = new BaseMeetingRoom();
        FunctionSpaceFunctionRoom functionRoom = new FunctionSpaceFunctionRoom();
        functionRoom.setId(1);
        baseMeetingRoom.setFunctionSpaceFunctionRoom(functionRoom);
        baseMeetingRoom.setPriceExcluded(true);
        return baseMeetingRoom;
    }

    private MeetingPackageProduct createMeetingPackageProduct(int id) {
        MeetingPackageProduct product = new MeetingPackageProduct();
        product.setId(id);
        return product;
    }

    private FunctionSpaceFunctionRoom createFunctionRoom(int id) {
        FunctionSpaceFunctionRoom room = new FunctionSpaceFunctionRoom();
        room.setId(id);
        room.setName("Room_" + id);
        return room;
    }

    private MeetingPackagePaceBarDecision createDecision(int decisionId, int productId, int roomId, Date date, Double finalBar) {
        MeetingPackagePaceBarDecision decision = new MeetingPackagePaceBarDecision();
        decision.setDecisionId(decisionId);
        decision.setOccupancyDate(date);
        decision.setMeetingPackageProduct(createMeetingPackageProduct(productId));
        decision.setFunctionSpaceFunctionRoom(createFunctionRoom(roomId));
        decision.setFinalBar(BigDecimal.valueOf(finalBar));
        return decision;
    }

    private static MeetingPackagePricingPerDay createMeetingPackagePricingPerDay(Date date, List<MeetingPackagePriceDetail> priceDetails) {
        MeetingPackagePricingPerDay meetingPackagePricingPerDay = new MeetingPackagePricingPerDay();
        meetingPackagePricingPerDay.setDate(date);
        meetingPackagePricingPerDay.setPriceDetails(priceDetails);
        return meetingPackagePricingPerDay;
    }

    private static MeetingPackagePriceDetail createMeetingPackagePriceDetail(int productId, List<MeetingRoomPrice> meetingRoomPrices) {
        MeetingPackagePriceDetail meetingPackagePriceDetail = new MeetingPackagePriceDetail();
        meetingPackagePriceDetail.setProductId(productId);
        meetingPackagePriceDetail.setMeetingRoomPrices(meetingRoomPrices);
        return meetingPackagePriceDetail;
    }

    private static MeetingRoomPrice createMeetingRoomPrice(Integer meetingRoomId, Double meetingRoomPrice) {
        MeetingRoomPrice meetingRoomDetail = new MeetingRoomPrice();
        meetingRoomDetail.setMeetingRoomId(meetingRoomId);
        meetingRoomDetail.setMeetingRoomPrice(BigDecimal.valueOf(meetingRoomPrice));
        return meetingRoomDetail;
    }

    private List<MeetingPackageProductOffset> createSeasonalProductOffsets() {
        MeetingPackageProductOffset offset = createDefaultProductOffsets().get(0);
        offset.setStartDate(LocalDate.of(2025, 2, 15));
        offset.setEndDate(LocalDate.of(2025, 2, 25));
        offset.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        offset.setThursdayOffsetValue(BigDecimal.valueOf(7.00));
        return List.of(createDefaultProductOffsets().get(0), offset);
    }

    private List<MeetingPackageProductOffset> createDefaultProductOffsets() {
        MeetingPackageProduct product = new MeetingPackageProduct();
        product.setId(2);
        FunctionSpaceFunctionRoom room = new FunctionSpaceFunctionRoom();
        room.setId(3);
        MeetingPackageProductMeetingRoom productMeetingRoom = new MeetingPackageProductMeetingRoom();
        productMeetingRoom.setFunctionSpaceFunctionRoom(room);
        MeetingPackageProductOffset offset = MeetingPackageProductOffsetBuilder.createMPProductOffset(product, productMeetingRoom, valueOf(4.00));
        return List.of(offset);
    }

    private static Map<Integer, PricingRule> buildPrettyPricingRules(String onesPlace, String tenthsPlace, String hundredthsPlace) {
        PricingRule rule = new PricingRule();
        PrettyPricingRuleRow row1 = new PrettyPricingRuleRow();
        row1.setRuleNumbers(onesPlace);
        rule.addRule(PricingDigit.ONES, row1);
        PrettyPricingRuleRow row2 = new PrettyPricingRuleRow();
        row2.setRuleNumbers(tenthsPlace);
        rule.addRule(PricingDigit.TENTHS, row2);
        PrettyPricingRuleRow row3 = new PrettyPricingRuleRow();
        row3.setRuleNumbers(hundredthsPlace);
        rule.addRule(PricingDigit.HUNDREDTHS, row3);
        return Map.of(1, rule);
    }

    private Product createPrimaryProduct() {
        Product barProduct = new Product();
        barProduct.setId(1);
        barProduct.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        return barProduct;
    }


    private MeetingPackageProductRateOffsetOverride getMeetingPackageProductRateOffsetOverride(Date occupancyDate, MeetingPackageProduct linkedMPProduct1, BigDecimal adjustmentOverride) {
        MeetingPackageProductRateOffsetOverride productRateOffsetOverride = new MeetingPackageProductRateOffsetOverride();
        productRateOffsetOverride.setProduct(linkedMPProduct1);
        productRateOffsetOverride.setOccupancyDate(occupancyDate);
        productRateOffsetOverride.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        productRateOffsetOverride.setOffsetValue(adjustmentOverride);
        productRateOffsetOverride.setOffsetValueCeil(adjustmentOverride);
        productRateOffsetOverride.setOffsetValueFloor(adjustmentOverride);
        productRateOffsetOverride.setStatus(ACTIVE);
        return productRateOffsetOverride;
    }

    private MeetingPackageProductRateOffset getMeetingPackageProductRateOffset(MeetingPackageProduct linkedMPProduct1, String seasonName, LocalDate startDate, LocalDate endDate) {
        MeetingPackageProductRateOffset mpProductRateOffsetSeason = new MeetingPackageProductRateOffset();
        mpProductRateOffsetSeason.setMeetingPackageProduct(linkedMPProduct1);
        mpProductRateOffsetSeason.setSeasonName(seasonName);
        mpProductRateOffsetSeason.setStartDate(startDate);
        mpProductRateOffsetSeason.setEndDate(endDate);
        mpProductRateOffsetSeason.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        mpProductRateOffsetSeason.setSundayOffsetValueCeiling(TEN);
        mpProductRateOffsetSeason.setSundayOffsetValueFloor(TEN);
        mpProductRateOffsetSeason.setMondayOffsetValueCeiling(TEN);
        mpProductRateOffsetSeason.setMondayOffsetValueFloor(TEN);
        mpProductRateOffsetSeason.setTuesdayOffsetValueCeiling(TEN);
        mpProductRateOffsetSeason.setTuesdayOffsetValueFloor(TEN);
        mpProductRateOffsetSeason.setWednesdayOffsetValueCeiling(TEN);
        mpProductRateOffsetSeason.setWednesdayOffsetValueFloor(TEN);
        mpProductRateOffsetSeason.setThursdayOffsetValueCeiling(TEN);
        mpProductRateOffsetSeason.setThursdayOffsetValueFloor(TEN);
        mpProductRateOffsetSeason.setFridayOffsetValueCeiling(TEN);
        mpProductRateOffsetSeason.setFridayOffsetValueFloor(TEN);
        mpProductRateOffsetSeason.setSaturdayOffsetValueCeiling(TEN);
        mpProductRateOffsetSeason.setSundayOffsetValueFloor(TEN);
        return mpProductRateOffsetSeason;
    }

    private MeetingPackageProduct createIdependentMeetingPackageProduct(Integer productId, String name) {
        MeetingPackageProduct meetingPackageProduct = new MeetingPackageProduct();
        meetingPackageProduct.setId(productId);
        meetingPackageProduct.setName(name);
        meetingPackageProduct.setType("INDEPENDENTLY");
        meetingPackageProduct.setCode("MP_INDEPENDENT");
        meetingPackageProduct.setStatus(ACTIVE);
        return meetingPackageProduct;
    }

    private MeetingPackageProduct createLinkedMeetingPackageProduct(Integer productId, String name, Integer dependentProductId) {
        MeetingPackageProduct meetingPackageProduct = new MeetingPackageProduct();
        meetingPackageProduct.setId(productId);
        meetingPackageProduct.setName(name);
        meetingPackageProduct.setType("LINKED");
        meetingPackageProduct.setCode("MP_AGILE_RATES");
        meetingPackageProduct.setStatus(ACTIVE);
        meetingPackageProduct.setDependentProductId(dependentProductId);
        return meetingPackageProduct;
    }
}