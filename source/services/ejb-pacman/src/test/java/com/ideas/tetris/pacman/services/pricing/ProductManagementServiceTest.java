package com.ideas.tetris.pacman.services.pricing;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.*;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesObjectMother;
import com.ideas.tetris.pacman.services.bestavailablerate.*;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.*;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyDataDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyIndicatorDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisSpecialEventDto;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.extendedstay.ratemanagement.dto.ProductDTO;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.pricing.dto.*;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingConfigurationObjectMother;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.Supplement;
import com.ideas.tetris.pacman.services.product.*;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.rdl.entity.dto.WebrateTypeProductDTO;
import com.ideas.tetris.pacman.services.rms.dto.RmsAuditInfo;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorRateInfo;
import com.ideas.tetris.pacman.services.webrate.dto.DCMPCGenericValuesDTO;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateDefaultChannel;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideChannel;
import com.ideas.tetris.pacman.services.webrate.service.CompetitorRateInfoService;
import com.ideas.tetris.pacman.services.webrate.service.DynamicCMPCService;
import com.ideas.tetris.pacman.testdatabuilder.ProductBuilder;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.entity.IdAware;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.MapUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.ws.rs.core.Response;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import static com.ideas.tetris.pacman.services.pricing.ProductManagementService.*;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toJavaLocalDate;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertJavaToJodaLocalDate;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static java.util.List.of;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
public class ProductManagementServiceTest extends AbstractG3JupiterTest {
    @Spy
    CrudService crudService;

    @Mock
    private CompetitorRateInfoService competitorRateInfoService;

    @Mock
    private DateService dateService;

    @Mock
    CPDecisionContext cpDecisionContext;

    @Mock
    PricingConfigurationService pricingConfigurationService;

    @Mock
    DecisionService decisionService;

    @Mock
    SyncEventAggregatorService syncEventAggregatorService;

    @Mock
    BarDecisionService barDecisionService;

    @Mock
    BusinessAnalysisDashboardService businessAnalysisDashboardService;

    @Mock
    PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    AccomTypeSupplementService accomTypeSupplementService;

    @Mock
    TaxService taxService;

    @Mock
    AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock
    AccommodationService accommodationService;

    @Captor
    ArgumentCaptor<Object> overrideCaptor;

    @Captor
    private ArgumentCaptor<String> queryCaptor;

    @Captor
    private ArgumentCaptor<Map<String, Object>> parametersCaptor;

    @InjectMocks
    ProductManagementService productManagementService;

    @Spy
    @InjectMocks
    ProductManagementService spyService;

    @Mock
    private DynamicCMPCService dynamicCMPCService;

    @Mock
    PrettyPricingService prettyPricingService;

    @Mock
    private RoundingRulesDTO roundingRulesDTO;
    @Mock
    private HierarchyDTO hierarchyDTO;
    @Mock
    private FloorCeilDetailsDTO floorCeilDetailsDTO;
    @Mock
    private OffsetDetailsDTO offsetDetailsDTO;
    @Mock
    private SupplementDTO supplementDTO;
    @Mock
    private PriceChangeRangeDTO priceChangeRangeDTO;
    @Mock
    private LinkProductsDTO linkProductsDTO;
    @Mock
    private ProductHierarchy productHierarchy;
    @Mock
    private ProductMinPriceDiff productMinPriceDiff;
    AbstractMultiPropertyCrudService multiPropertyCrudService = multiPropertyCrudService();

    CrudService tenantCrudService = tenantCrudService();

    private static final Set<DayOfWeek> DAY_OF_WEEKS = new HashSet<>() {
        {
            add(DayOfWeek.TUESDAY);
            add(DayOfWeek.THURSDAY);
        }
    };

    private static final LocalDate START_DATE_RANGE = new LocalDate(2016, 11, 1);
    private static final LocalDate END_DATE_RANGE = new LocalDate(2016, 11, 3);

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        productManagementService.multiPropertyCrudService = multiPropertyCrudService;
    }

    @Test
    public void getDecisionBarOutputForDate() {
        productManagementService.getDecisionBarOutputForDate(new LocalDate("2018-09-01"));
        verify(crudService, times(1)).findByNamedQuery(any(), anyMap());
    }

    @Test
    public void testGetCompetitorRatesForAccomClassList() {
        List<AccomClass> roomClasses = new ArrayList<>();
        roomClasses.add(createAccomClass(1, "accomClass1"));
        roomClasses.add(createAccomClass(2, "accomClass2"));
        Date occupancyDate = setDataForCompetitorRates(roomClasses);
        List<CompetitorRateInfo> rateInfoList = productManagementService.getCompetitorRates(occupancyDate, -1, false, new ArrayList<>());
        assertEquals(3, rateInfoList.size());
        rateInfoList = productManagementService.getCompetitorRatesForDateRange(occupancyDate, occupancyDate, -1, false);
        assertEquals(0, rateInfoList.size());
    }

    @Test
    public void testGetCompetitorRatesForSingleAccomClass() {
        List<AccomClass> roomClasses = new ArrayList<>();
        roomClasses.add(createAccomClass(1, "accomClass1"));
        Date occupancyDate = setDataForCompetitorRates(roomClasses);
        List<CompetitorRateInfo> rateInfoList = productManagementService.getCompetitorRates(occupancyDate, 1, false, new ArrayList<>());
        assertEquals(3, rateInfoList.size());
        assertEquals("Competitor1", rateInfoList.get(0).getCompetitorName());
        assertEquals(new BigDecimal("10.00"), rateInfoList.get(0).getRate());
        assertEquals(new BigDecimal("10.00"), rateInfoList.get(1).getRate());
        assertEquals(0, rateInfoList.get(2).getRate().intValue());
        assertEquals("Competitor2", rateInfoList.get(1).getCompetitorName());
        assertEquals("Competitor3", rateInfoList.get(2).getCompetitorName());
    }

    @Test
    public void testGetCompetitorRatesForAccomClassListWithDCMPC() {
        List<AccomClass> roomClasses = new ArrayList<>();
        roomClasses.add(createAccomClass(1, "accomClass1"));
        roomClasses.add(createAccomClass(2, "accomClass2"));
        Date occupancyDate = setDataForCompetitorRates(roomClasses, true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTING_ON_COMPETITOR_UI)).thenReturn(true);
        DCMPCGenericValuesDTO dto = new DCMPCGenericValuesDTO();
        dto.setDowId(Byte.valueOf(""+JavaLocalDateUtils.getDayOfWeek(occupancyDate)));
        dto.setMaxPercentile(BigDecimal.valueOf(30));
        dto.setOnBooksThresholdPercent(BigDecimal.valueOf(20));
        dto.setAccomClassName("accomClass1");
        when(dynamicCMPCService.fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(anyList(), anyList(), any(), any())).thenReturn(List.of(dto));
        List<CompetitorRateInfo> rateInfoList = productManagementService.getCompetitorRates(occupancyDate, -1, true, new ArrayList<>());
        assertEquals(3, rateInfoList.size());
        assertEquals(BigDecimal.valueOf(20), rateInfoList.get(0).getDcmpcOnBooksThresholdPercent());
        assertEquals(BigDecimal.valueOf(20), rateInfoList.get(1).getDcmpcOnBooksThresholdPercent());
        assertEquals(BigDecimal.valueOf(20), rateInfoList.get(2).getDcmpcOnBooksThresholdPercent());
        assertEquals(BigDecimal.valueOf(30), rateInfoList.get(0).getDcmpcMaxMarketPercentile());
        assertEquals(BigDecimal.valueOf(30), rateInfoList.get(1).getDcmpcMaxMarketPercentile());
        assertEquals(BigDecimal.valueOf(30), rateInfoList.get(2).getDcmpcMaxMarketPercentile());
        rateInfoList = productManagementService.getCompetitorRatesForDateRange(occupancyDate, occupancyDate, -1, true);
        assertEquals(0, rateInfoList.size());
    }

    @Test
    public void testGetCompetitorRatesForSingleAccomClassWithOccupancyBasedCMPC() {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTING_ON_COMPETITOR_UI)).thenReturn(true);
        List<AccomClass> roomClasses = new ArrayList<>();
        roomClasses.add(createAccomClass(1, "accomClass1"));
        Date occupancyDate = setDataForCompetitorRates(roomClasses);
        DCMPCGenericValuesDTO dto = new DCMPCGenericValuesDTO();
        dto.setDowId(Byte.valueOf(""+JavaLocalDateUtils.getDayOfWeek(occupancyDate)));
        dto.setMaxPercentile(BigDecimal.valueOf(30));
        dto.setOnBooksThresholdPercent(BigDecimal.valueOf(20));
        dto.setAccomClassName("accomClass1");
        when(dynamicCMPCService.fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(anyList(), anyList(), any(java.time.LocalDate.class), any())).thenReturn(List.of(dto));
        List<CompetitorRateInfo> rateInfoList = productManagementService.getCompetitorRates(occupancyDate, 1, false, new ArrayList<>());
        assertEquals(3, rateInfoList.size());
        assertEquals("Competitor1", rateInfoList.get(0).getCompetitorName());
        assertEquals(new BigDecimal("10.00"), rateInfoList.get(0).getRate());
        assertEquals(new BigDecimal("10.00"), rateInfoList.get(1).getRate());
        assertEquals(0, rateInfoList.get(2).getRate().intValue());
        assertEquals("Competitor2", rateInfoList.get(1).getCompetitorName());
        assertEquals("Competitor3", rateInfoList.get(2).getCompetitorName());
        assertEquals(BigDecimal.valueOf(30), rateInfoList.get(0).getDcmpcMaxMarketPercentile());
        assertEquals(BigDecimal.valueOf(30), rateInfoList.get(1).getDcmpcMaxMarketPercentile());
        assertEquals(BigDecimal.valueOf(30), rateInfoList.get(2).getDcmpcMaxMarketPercentile());
        assertEquals(BigDecimal.valueOf(20), rateInfoList.get(0).getDcmpcOnBooksThresholdPercent());
        assertEquals(BigDecimal.valueOf(20), rateInfoList.get(1).getDcmpcOnBooksThresholdPercent());
        assertEquals(BigDecimal.valueOf(20), rateInfoList.get(2).getDcmpcOnBooksThresholdPercent());

    }

    @Test
    void testRemoveOverride_WithPercentageSupplement_overides_in_ext_window() {
        BigDecimal supplementValue = BigDecimal.valueOf(50);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        decisionBARCPOutput.setOptimalBAR(BigDecimal.valueOf(150));
        decisionBARCPOutput.setPropertyId(1);
        decisionBARCPOutput.setDecisionId(2);
        decisionBARCPOutput.setAccomType(new AccomType());
        decisionBARCPOutput.setProduct(new Product());
        decisionBARCPOutput.setOverrideType(DecisionOverrideType.CEIL);
        decisionBARCPOutput.setSpecificOverride(BigDecimal.valueOf(50));
        decisionBARCPOutput.setCeilingOverride(BigDecimal.valueOf(60));
        decisionBARCPOutput.setFloorOverride(BigDecimal.valueOf(40));
        decisionBARCPOutput.setLengthOfStay(1);
        decisionBARCPOutput.setArrivalDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.of(2023, 1, 1)));

        // Supplement Value is 50%
        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setOffsetMethod(OffsetMethod.PERCENTAGE);
        accomTypeSupplementValue.setValue(supplementValue);

        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplementValue);
        when(cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput)).thenReturn(accomTypeSupplementValue);

        Mockito.when(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(200));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        when(pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(toJavaLocalDate(decisionBARCPOutput.getArrivalDate()), dateService.getCaughtUpJavaLocalDate())).thenReturn(true);

        PricingRule pricingRule = new PricingRule();
        expectFloorCeiling(pricingRule, decisionBARCPOutput, new BigDecimal("10"), new BigDecimal("200"), supplementValue);

        productManagementService.removeOverride(cpDecisionContext, decisionBARCPOutput, 1, 12, false, true, true);

        assertNull(decisionBARCPOutput.getSpecificOverride());
        assertNull(decisionBARCPOutput.getFloorOverride());
        assertNull(decisionBARCPOutput.getCeilingOverride());
        assertEquals(BigDecimal.valueOf(200), decisionBARCPOutput.getFinalBAR());

        //Casting is necessary here to appease the compiler
        verify(crudService, times(2)).save((CPDecisionBAROutputOverride) overrideCaptor.capture());
        verify(cpDecisionContext, times(2)).getAccomTypeSupplementValue(decisionBARCPOutput);
        verify(cpDecisionContext, times(4)).getSupplement(decisionBARCPOutput);

        //verify sync flags are thrown
        verify(syncEventAggregatorService, times(0)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, times(0)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(toJavaLocalDate(decisionBARCPOutput.getArrivalDate()), java.time.LocalDate.parse("2023-01-01"));

        List<Object> values = overrideCaptor.getAllValues();

        assertEquals(decisionBARCPOutput, values.get(0));

        CPDecisionBAROutputOverride override = (CPDecisionBAROutputOverride) values.get(1);

        assertEquals(Integer.valueOf(1), override.getDecisionId());
        assertEquals(override.getDecisionId(), decisionBARCPOutput.getDecisionId());
        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(BigDecimal.valueOf(33.33333), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(BigDecimal.valueOf(26.66667), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(BigDecimal.valueOf(40.0), override.getOldCeilingRate().setScale(1));
        assertNull(override.getNewCeilingRate());
        assertEquals(decisionBARCPOutput.getPropertyId(), override.getPropertyId());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());
        assertEquals(decisionBARCPOutput.getAccomType(), override.getAccomType());
        assertEquals(decisionBARCPOutput.getLengthOfStay(), override.getLengthOfStay());
        assertEquals(decisionBARCPOutput.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
    }

    @Test
    void testRemoveOverride_WithPercentageSupplement_overrides_in_std_window() {
        BigDecimal supplementValue = BigDecimal.valueOf(50);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        decisionBARCPOutput.setOptimalBAR(BigDecimal.valueOf(150));
        decisionBARCPOutput.setPropertyId(1);
        decisionBARCPOutput.setDecisionId(2);
        decisionBARCPOutput.setAccomType(new AccomType());
        decisionBARCPOutput.setProduct(new Product());
        decisionBARCPOutput.setOverrideType(DecisionOverrideType.CEIL);
        decisionBARCPOutput.setSpecificOverride(BigDecimal.valueOf(50));
        decisionBARCPOutput.setCeilingOverride(BigDecimal.valueOf(60));
        decisionBARCPOutput.setFloorOverride(BigDecimal.valueOf(40));
        decisionBARCPOutput.setLengthOfStay(1);
        decisionBARCPOutput.setArrivalDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.of(2023, 1, 1)));

        // Supplement Value is 50%
        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setOffsetMethod(OffsetMethod.PERCENTAGE);
        accomTypeSupplementValue.setValue(supplementValue);

        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplementValue);
        when(cpDecisionContext.getAccomTypeSupplementValue(decisionBARCPOutput)).thenReturn(accomTypeSupplementValue);

        Mockito.when(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(200));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        when(pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(toJavaLocalDate(decisionBARCPOutput.getArrivalDate()), dateService.getCaughtUpJavaLocalDate())).thenReturn(false);

        PricingRule pricingRule = new PricingRule();
        expectFloorCeiling(pricingRule, decisionBARCPOutput, new BigDecimal("10"), new BigDecimal("200"), supplementValue);

        productManagementService.removeOverride(cpDecisionContext, decisionBARCPOutput, 1, 12, false, true, true);

        assertNull(decisionBARCPOutput.getSpecificOverride());
        assertNull(decisionBARCPOutput.getFloorOverride());
        assertNull(decisionBARCPOutput.getCeilingOverride());
        assertEquals(BigDecimal.valueOf(200), decisionBARCPOutput.getFinalBAR());

        //Casting is necessary here to appease the compiler
        verify(crudService, times(2)).save((CPDecisionBAROutputOverride) overrideCaptor.capture());
        verify(cpDecisionContext, times(2)).getAccomTypeSupplementValue(decisionBARCPOutput);
        verify(cpDecisionContext, times(4)).getSupplement(decisionBARCPOutput);

        //verify sync flags are thrown
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(toJavaLocalDate(decisionBARCPOutput.getArrivalDate()), java.time.LocalDate.parse("2023-01-01"));

        List<Object> values = overrideCaptor.getAllValues();

        assertEquals(decisionBARCPOutput, values.get(0));

        CPDecisionBAROutputOverride override = (CPDecisionBAROutputOverride) values.get(1);

        assertEquals(Integer.valueOf(1), override.getDecisionId());
        assertEquals(override.getDecisionId(), decisionBARCPOutput.getDecisionId());
        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(BigDecimal.valueOf(33.33333), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(BigDecimal.valueOf(26.66667), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(BigDecimal.valueOf(40.0), override.getOldCeilingRate().setScale(1));
        assertNull(override.getNewCeilingRate());
        assertEquals(decisionBARCPOutput.getPropertyId(), override.getPropertyId());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());
        assertEquals(decisionBARCPOutput.getAccomType(), override.getAccomType());
        assertEquals(decisionBARCPOutput.getLengthOfStay(), override.getLengthOfStay());
        assertEquals(decisionBARCPOutput.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
    }

    @Test
    public void removeOverride() {
        BigDecimal supplementValue = BigDecimal.TEN;

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        decisionBARCPOutput.setOptimalBAR(BigDecimal.valueOf(150));
        decisionBARCPOutput.setPropertyId(1);
        decisionBARCPOutput.setDecisionId(2);
        decisionBARCPOutput.setAccomType(new AccomType());
        decisionBARCPOutput.setArrivalDate(new LocalDate());
        decisionBARCPOutput.setProduct(new Product());
        decisionBARCPOutput.setOverrideType(DecisionOverrideType.CEIL);
        decisionBARCPOutput.setSpecificOverride(BigDecimal.valueOf(45));
        decisionBARCPOutput.setCeilingOverride(BigDecimal.valueOf(46));
        decisionBARCPOutput.setFloorOverride(BigDecimal.valueOf(47));
        decisionBARCPOutput.setLengthOfStay(1);

        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplementValue);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));

        Mockito.when(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(200));

        PricingRule pricingRule = new PricingRule();
        expectFloorCeiling(pricingRule, decisionBARCPOutput, new BigDecimal("10"), new BigDecimal("200"), supplementValue);

        productManagementService.removeOverride(cpDecisionContext, decisionBARCPOutput, 1, 12, false, true, true);

        assertNull(decisionBARCPOutput.getSpecificOverride());
        assertNull(decisionBARCPOutput.getFloorOverride());
        assertNull(decisionBARCPOutput.getCeilingOverride());
        assertEquals(BigDecimal.valueOf(200), decisionBARCPOutput.getFinalBAR());

        //Casting is necessary here to appease the compiler
        verify(crudService, times(2)).save((CPDecisionBAROutputOverride) overrideCaptor.capture());
        verify(cpDecisionContext, times(2)).getAccomTypeSupplementValue(decisionBARCPOutput);
        verify(cpDecisionContext, times(4)).getSupplement(decisionBARCPOutput);

        //verify sync flags are thrown
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);

        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(toJavaLocalDate(decisionBARCPOutput.getArrivalDate()), java.time.LocalDate.parse("2023-01-01"));

        List<Object> values = overrideCaptor.getAllValues();

        assertEquals(decisionBARCPOutput, values.get(0));

        CPDecisionBAROutputOverride override = (CPDecisionBAROutputOverride) values.get(1);

        assertEquals(Integer.valueOf(1), override.getDecisionId());
        assertEquals(override.getDecisionId(), decisionBARCPOutput.getDecisionId());
        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(BigDecimal.valueOf(45).subtract(supplementValue), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(BigDecimal.valueOf(47).subtract(supplementValue), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(BigDecimal.valueOf(46).subtract(supplementValue), override.getOldCeilingRate());
        assertNull(override.getNewCeilingRate());
        assertEquals(decisionBARCPOutput.getPropertyId(), override.getPropertyId());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());
        assertEquals(decisionBARCPOutput.getAccomType(), override.getAccomType());
        assertEquals(decisionBARCPOutput.getArrivalDate(), override.getArrivalDate());
        assertEquals(decisionBARCPOutput.getLengthOfStay(), override.getLengthOfStay());
        assertEquals(decisionBARCPOutput.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
    }

    @Test
    public void removeOverride_cascade() {
        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        decisionBARCPOutput.setOptimalBAR(BigDecimal.valueOf(150));
        decisionBARCPOutput.setPropertyId(1);
        decisionBARCPOutput.setDecisionId(2);
        AccomType baseRoomType = new AccomType();
        AccomClass baseRoomClass = new AccomClass();
        baseRoomType.setAccomClass(baseRoomClass);
        decisionBARCPOutput.setAccomType(baseRoomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);
        decisionBARCPOutput.setProduct(new Product());
        decisionBARCPOutput.setOverrideType(DecisionOverrideType.CEIL);
        decisionBARCPOutput.setSpecificOverride(BigDecimal.valueOf(45));
        decisionBARCPOutput.setCeilingOverride(BigDecimal.valueOf(46));
        decisionBARCPOutput.setFloorOverride(BigDecimal.valueOf(47));

        BigDecimal supplementValue = BigDecimal.TEN;
        when(cpDecisionContext.getSupplement(decisionBARCPOutput)).thenReturn(supplementValue);

        PricingRule pricingRule = new PricingRule();

        Mockito.when(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(190));

        CPDecisionBAROutput otherDecision1 = new CPDecisionBAROutput();
        AccomType otherDecision1RoomType = new AccomType();
        AccomClass otherDecision1RoomClass = new AccomClass();
        otherDecision1RoomType.setAccomClass(otherDecision1RoomClass);
        otherDecision1.setAccomType(otherDecision1RoomType);
        CPDecisionBAROutput otherDecision2 = new CPDecisionBAROutput();
        AccomType otherDecision2RoomType = new AccomType();
        otherDecision2RoomType.setAccomClass(baseRoomClass);
        otherDecision2.setAccomType(otherDecision2RoomType);
        otherDecision2.setOptimalBAR(BigDecimal.valueOf(100));
        otherDecision2.setPropertyId(3);
        otherDecision2.setDecisionId(4);
        otherDecision2.setArrivalDate(arrivalDate);
        otherDecision2.setProduct(new Product());
        otherDecision2.setOverrideType(DecisionOverrideType.CEIL);
        otherDecision2.setSpecificOverride(BigDecimal.valueOf(35).subtract(supplementValue));
        otherDecision2.setCeilingOverride(BigDecimal.valueOf(36).subtract(supplementValue));
        otherDecision2.setFloorOverride(BigDecimal.valueOf(37).subtract(supplementValue));

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);

        List<CPDecisionBAROutput> decisionsForDay = asList(decisionBARCPOutput, otherDecision1, otherDecision2);
        when(crudService.<CPDecisionBAROutput>findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, arrivalDate, arrivalDate)))
                .thenReturn(decisionsForDay);

        expectFloorCeiling(pricingRule, decisionBARCPOutput, new BigDecimal("10"), new BigDecimal("200"), supplementValue);

        //when
        productManagementService.removeOverride(cpDecisionContext, decisionBARCPOutput, 1, 12, true, true, true);

        //then
        assertNull(decisionBARCPOutput.getSpecificOverride());
        assertNull(decisionBARCPOutput.getFloorOverride());
        assertNull(decisionBARCPOutput.getCeilingOverride());
        assertEquals(BigDecimal.valueOf(190), decisionBARCPOutput.getFinalBAR());
        assertEquals(Integer.valueOf(1), decisionBARCPOutput.getDecisionId());

        //Casting is necessary here to appease the compiler
        verify(crudService, times(4)).save((CPDecisionBAROutputOverride) overrideCaptor.capture());
        verify(cpDecisionContext, times(2)).getAccomTypeSupplementValue(decisionBARCPOutput);
        verify(cpDecisionContext, times(4)).getSupplement(decisionBARCPOutput);

        List<Object> values = overrideCaptor.getAllValues();

        assertEquals(decisionBARCPOutput, values.get(0));

        CPDecisionBAROutputOverride override = (CPDecisionBAROutputOverride) values.get(1);

        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(BigDecimal.valueOf(45).subtract(supplementValue), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(BigDecimal.valueOf(47).subtract(supplementValue), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(BigDecimal.valueOf(46).subtract(supplementValue), override.getOldCeilingRate());
        assertNull(override.getNewCeilingRate());
        assertEquals(decisionBARCPOutput.getPropertyId(), override.getPropertyId());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());
        assertEquals(decisionBARCPOutput.getAccomType(), override.getAccomType());
        assertEquals(decisionBARCPOutput.getArrivalDate(), override.getArrivalDate());
        assertEquals(decisionBARCPOutput.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());

        assertEquals(otherDecision2, values.get(2));

        override = (CPDecisionBAROutputOverride) values.get(3);

        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(BigDecimal.valueOf(35).subtract(supplementValue), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(BigDecimal.valueOf(37).subtract(supplementValue), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(BigDecimal.valueOf(36).subtract(supplementValue), override.getOldCeilingRate());
        assertNull(override.getNewCeilingRate());
        assertEquals(otherDecision2.getPropertyId(), override.getPropertyId());
        assertEquals(otherDecision2.getDecisionId(), override.getDecisionId());
        assertEquals(otherDecision2.getAccomType(), override.getAccomType());
        assertEquals(otherDecision2.getArrivalDate(), override.getArrivalDate());
        assertEquals(otherDecision2.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
    }

    @Test
    public void saveOverrideForCPDecision_containsSpecificOverride() throws Exception {
        //setup
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setAccomType(buildAccomType());
        override.setNewUserOverride(overridePrice);
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setAccomType(buildAccomType());
        output.setFinalBAR(originalPrice);
        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));

        //when
        productManagementService.saveOverrideForCPDecision(cpDecisionContext, override, output, false);

        //then
        assertEquals(overridePrice, output.getPrettyBAR());
        assertEquals(overridePrice, output.getFinalBAR());
        assertEquals(overridePrice, output.getSpecificOverride());
        assertEquals(floorOverridePrice, output.getFloorOverride());
        assertEquals(ceilingOverridePrice, output.getCeilingOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(toJavaLocalDate(override.getArrivalDate()), java.time.LocalDate.parse("2023-01-01"));
    }

    @Test
    public void saveOverrideForCPDecision_cascadeOverrides() throws Exception {
        //setup
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewUserOverride(overridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        LocalDate arrivalDate = new LocalDate();
        output.setArrivalDate(arrivalDate);
        AccomType baseRoomType = new AccomType();
        AccomClass baseRoomClass = new AccomClass();
        baseRoomType.setAccomClass(baseRoomClass);
        output.setAccomType(baseRoomType);

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));

        CPDecisionBAROutput otherDecision1 = new CPDecisionBAROutput();
        AccomType otherDecision1RoomType = new AccomType();
        AccomClass otherDecision1RoomClass = new AccomClass();
        otherDecision1RoomType.setAccomClass(otherDecision1RoomClass);
        otherDecision1.setAccomType(otherDecision1RoomType);
        CPDecisionBAROutput otherDecision2 = new CPDecisionBAROutput();
        AccomType otherDecision2RoomType = new AccomType();
        otherDecision2RoomType.setId(123);
        otherDecision2RoomType.setAccomClass(baseRoomClass);
        otherDecision2.setAccomType(otherDecision2RoomType);
        otherDecision2.setOptimalBAR(BigDecimal.valueOf(100));
        otherDecision2.setPropertyId(3);
        otherDecision2.setDecisionId(4);
        otherDecision2.setArrivalDate(arrivalDate);
        otherDecision2.setProduct(new Product());
        otherDecision2.setOverrideType(DecisionOverrideType.CEIL);
        otherDecision2.setSpecificOverride(BigDecimal.valueOf(35));

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(pricingConfigurationService.getCPDecisionContext(arrivalDate, arrivalDate)).thenReturn(cpDecisionContext);

        List<CPDecisionBAROutput> decisionsForDay = asList(output, otherDecision1, otherDecision2);
        when(crudService.<CPDecisionBAROutput>findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, arrivalDate, arrivalDate)))
                .thenReturn(decisionsForDay);

        CPConfigMergedOffset offset1 = new CPConfigMergedOffset();
        offset1.setId(new CPConfigMergedOffsetPK());
        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        CPConfigMergedOffsetPK id = new CPConfigMergedOffsetPK();
        id.setAccomTypeId(123);
        id.setOccupancyType(OccupancyType.SINGLE);
        offset2.setId(id);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(BigDecimal.valueOf(20));
        List<CPConfigMergedOffset> offsets = asList(offset1, offset2);
        when(cpDecisionContext.applyOffset(otherDecision2, override.getNewUserOverride())).thenReturn(BigDecimalUtil.add(override.getNewUserOverride(), offset2.getOffsetValue()));

        //when
        productManagementService.saveOverrideForCPDecision(cpDecisionContext, override, output, true);

        //then
        assertEquals(overridePrice, output.getPrettyBAR());
        assertEquals(overridePrice, output.getFinalBAR());
        assertEquals(overridePrice, output.getSpecificOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        ArgumentCaptor<IdAware> captor = ArgumentCaptor.forClass(IdAware.class);
        verify(crudService, times(4)).save(captor.capture());

        List<IdAware> allValues = captor.getAllValues();
        assertEquals(output, allValues.get(0));
        assertEquals(override, allValues.get(1));
        assertEquals(otherDecision2, allValues.get(2));
        CPDecisionBAROutputOverride otherOverride = (CPDecisionBAROutputOverride) allValues.get(3);

        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherOverride.getNewUserOverride().setScale(2));
        assertEquals(DecisionOverrideType.CEIL, otherOverride.getNewOverrideType());
        assertEquals(barOverrideDecision.getId(), otherOverride.getDecisionId());

        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getPrettyBAR().setScale(2));
        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getFinalBAR().setScale(2));
        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getSpecificOverride().setScale(2));
        assertEquals(DecisionOverrideType.CEIL, otherDecision2.getOverrideType());
        verify(pricingConfigurationLTBDEService, atLeastOnce()).enableOverrideForExtendedWindowIfApplicable(toJavaLocalDate(arrivalDate), java.time.LocalDate.parse("2023-01-01"));
    }


    @Test
    public void saveOverrideForCPDecision_cascadeOverridesWithImprovedPerformance() throws Exception {
        //setup
        Date today = new Date();
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewUserOverride(overridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        LocalDate arrivalDate = new LocalDate();
        output.setArrivalDate(arrivalDate);
        AccomType baseRoomType = new AccomType();
        AccomClass baseRoomClass = new AccomClass();
        baseRoomType.setAccomClass(baseRoomClass);
        output.setAccomType(baseRoomType);

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision(today, today, today, today)).thenReturn(barOverrideDecision);

        CPDecisionBAROutput otherDecision1 = new CPDecisionBAROutput();
        AccomType otherDecision1RoomType = new AccomType();
        AccomClass otherDecision1RoomClass = new AccomClass();
        otherDecision1RoomType.setAccomClass(otherDecision1RoomClass);
        otherDecision1.setAccomType(otherDecision1RoomType);
        CPDecisionBAROutput otherDecision2 = new CPDecisionBAROutput();
        AccomType otherDecision2RoomType = new AccomType();
        otherDecision2RoomType.setId(123);
        otherDecision2RoomType.setAccomClass(baseRoomClass);
        otherDecision2.setAccomType(otherDecision2RoomType);
        otherDecision2.setOptimalBAR(BigDecimal.valueOf(100));
        otherDecision2.setPropertyId(3);
        otherDecision2.setDecisionId(4);
        otherDecision2.setArrivalDate(arrivalDate);
        otherDecision2.setProduct(new Product());
        otherDecision2.setOverrideType(DecisionOverrideType.CEIL);
        otherDecision2.setSpecificOverride(BigDecimal.valueOf(35));

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(pricingConfigurationService.getCPDecisionContext(arrivalDate, arrivalDate)).thenReturn(cpDecisionContext);

        List<CPDecisionBAROutput> decisionsForDay = asList(output, otherDecision1, otherDecision2);
        when(crudService.<CPDecisionBAROutput>findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, arrivalDate, arrivalDate)))
                .thenReturn(decisionsForDay);

        CPConfigMergedOffset offset1 = new CPConfigMergedOffset();
        offset1.setId(new CPConfigMergedOffsetPK());
        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        CPConfigMergedOffsetPK id = new CPConfigMergedOffsetPK();
        id.setAccomTypeId(123);
        id.setOccupancyType(OccupancyType.SINGLE);
        offset2.setId(id);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(BigDecimal.valueOf(20));
        List<CPConfigMergedOffset> offsets = asList(offset1, offset2);
        when(cpDecisionContext.applyOffset(otherDecision2, override.getNewUserOverride())).thenReturn(BigDecimalUtil.add(override.getNewUserOverride(), offset2.getOffsetValue()));

        //when

        productManagementService.saveOverrideForCPDecision(cpDecisionContext, override, output, true, today, today, today, today);

        //then
        assertEquals(overridePrice, output.getPrettyBAR());
        assertEquals(overridePrice, output.getFinalBAR());
        assertEquals(overridePrice, output.getSpecificOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        ArgumentCaptor<IdAware> captor = ArgumentCaptor.forClass(IdAware.class);
        verify(crudService, times(4)).save(captor.capture());

        List<IdAware> allValues = captor.getAllValues();
        assertEquals(output, allValues.get(0));
        assertEquals(override, allValues.get(1));
        assertEquals(otherDecision2, allValues.get(2));
        CPDecisionBAROutputOverride otherOverride = (CPDecisionBAROutputOverride) allValues.get(3);

        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherOverride.getNewUserOverride().setScale(2));
        assertEquals(DecisionOverrideType.CEIL, otherOverride.getNewOverrideType());
        assertEquals(barOverrideDecision.getId(), otherOverride.getDecisionId());

        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getPrettyBAR().setScale(2));
        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getFinalBAR().setScale(2));
        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getSpecificOverride().setScale(2));
        assertEquals(DecisionOverrideType.CEIL, otherDecision2.getOverrideType());
    }


    @Test
    public void saveOverrideForCPDecision_noSpecificOverride() throws Exception {
        //setup
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        BigDecimal optimalPrice = BigDecimal.valueOf(210);
        BigDecimal prettiedPrice = BigDecimal.valueOf(300);
        BigDecimal oldCeilingOverride = BigDecimal.valueOf(500);
        BigDecimal oldFloorOverride = BigDecimal.valueOf(150);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        output.setOptimalBAR(optimalPrice);
        output.setCeilingOverride(oldCeilingOverride);
        output.setFloorOverride(oldFloorOverride);
        output.setPropertyId(1);
        output.setAccomType(new AccomType());

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);

        Mockito.when(cpDecisionContext.calculateRoundedRate(output)).thenReturn(prettiedPrice);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));

        //when
        productManagementService.saveOverrideForCPDecision(cpDecisionContext, override, output, false);

        //then
        assertEquals(prettiedPrice, output.getPrettyBAR());
        assertEquals(prettiedPrice, output.getFinalBAR());
        assertNull(output.getSpecificOverride());
        assertEquals(floorOverridePrice, output.getFloorOverride());
        assertEquals(ceilingOverridePrice, output.getCeilingOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(toJavaLocalDate(override.getArrivalDate()), java.time.LocalDate.parse("2023-01-01"));
    }

    @Test
    void testSaveOverride_WithPercentageSupplement() {
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        BigDecimal optimalPrice = BigDecimal.valueOf(210);
        BigDecimal prettiedPrice = BigDecimal.valueOf(300);
        BigDecimal oldCeilingOverride = BigDecimal.valueOf(500);
        BigDecimal oldFloorOverride = BigDecimal.valueOf(150);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        output.setOptimalBAR(optimalPrice);
        output.setCeilingOverride(oldCeilingOverride);
        output.setFloorOverride(oldFloorOverride);
        output.setPropertyId(1);
        output.setAccomType(new AccomType());

        // Supplement Value is 10%
        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setOffsetMethod(OffsetMethod.PERCENTAGE);
        accomTypeSupplementValue.setValue(BigDecimal.valueOf(10));

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);

        Mockito.when(cpDecisionContext.calculateRoundedRate(output)).thenReturn(prettiedPrice);
        Mockito.when(cpDecisionContext.getAccomTypeSupplementValue(output)).thenReturn(accomTypeSupplementValue);
        Mockito.when(cpDecisionContext.getSupplement(output)).thenReturn(BigDecimal.valueOf(10));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        when(pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(toJavaLocalDate(output.getArrivalDate()), dateService.getCaughtUpJavaLocalDate())).thenReturn(false);

        //when
        productManagementService.saveOverrideForCPDecision(cpDecisionContext, override, output, barOverrideDecision.getId());

        //then
        assertEquals(prettiedPrice, output.getPrettyBAR());
        assertEquals(prettiedPrice, output.getFinalBAR());
        assertNull(output.getSpecificOverride());
        assertEquals(floorOverridePrice.setScale(1), output.getFloorOverride().setScale(1));
        assertEquals(ceilingOverridePrice.setScale(1), output.getCeilingOverride().setScale(1));
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(toJavaLocalDate(override.getArrivalDate()), java.time.LocalDate.parse("2023-01-01"));
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    public void saveOverrideForCPDecisions_WithPercentageSupplement_massUpdateOverridesAndSave() throws Exception {
        //setup
        Date today = new Date();
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewUserOverride(overridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        override.setNewCeilingRate(BigDecimal.valueOf(100));
        override.setNewFloorRate(BigDecimal.valueOf(90));
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        AccomType baseRoomType = new AccomType();
        AccomClass baseRoomClass = new AccomClass();
        baseRoomType.setAccomClass(baseRoomClass);
        output.setAccomType(baseRoomType);

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecisions(today, today, today, today, 1)).thenReturn(of(barOverrideDecision));

        CPDecisionBAROutput otherDecision1 = new CPDecisionBAROutput();
        AccomType otherDecision1RoomType = new AccomType();
        AccomClass otherDecision1RoomClass = new AccomClass();
        otherDecision1RoomType.setAccomClass(otherDecision1RoomClass);
        otherDecision1.setAccomType(otherDecision1RoomType);
        CPDecisionBAROutput otherDecision2 = new CPDecisionBAROutput();
        AccomType otherDecision2RoomType = new AccomType();
        otherDecision2RoomType.setId(123);
        otherDecision2RoomType.setAccomClass(baseRoomClass);
        otherDecision2.setAccomType(otherDecision2RoomType);
        otherDecision2.setOptimalBAR(BigDecimal.valueOf(100));
        otherDecision2.setPropertyId(3);
        otherDecision2.setDecisionId(4);
        otherDecision2.setProduct(new Product());
        otherDecision2.setOverrideType(DecisionOverrideType.CEIL);
        otherDecision2.setSpecificOverride(BigDecimal.valueOf(35));

        // Supplement Value is 10%
        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setOffsetMethod(OffsetMethod.PERCENTAGE);
        accomTypeSupplementValue.setValue(BigDecimal.valueOf(10));

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        Mockito.when(cpDecisionContext.getAccomTypeSupplementValue(output)).thenReturn(accomTypeSupplementValue);
        Mockito.when(cpDecisionContext.getSupplement(output)).thenReturn(BigDecimal.valueOf(10));
        Mockito.when(cpDecisionContext.calculateRoundedRate(output)).thenReturn(overridePrice);

        CPConfigMergedOffset offset1 = new CPConfigMergedOffset();
        offset1.setId(new CPConfigMergedOffsetPK());
        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        CPConfigMergedOffsetPK id = new CPConfigMergedOffsetPK();
        id.setAccomTypeId(123);
        id.setOccupancyType(OccupancyType.SINGLE);
        offset2.setId(id);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(BigDecimal.valueOf(20));
        when(cpDecisionContext.applyOffset(otherDecision2, override.getNewUserOverride())).thenReturn(BigDecimalUtil.add(override.getNewUserOverride(), offset2.getOffsetValue()));
        when(pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(toJavaLocalDate(output.getArrivalDate()), dateService.getCaughtUpJavaLocalDate())).thenReturn(false);

        //when
        HashMap<CPDecisionBAROutput, CPDecisionBAROutputOverride> overrideMap = new HashMap<>();
        overrideMap.put(output, override);
        productManagementService.saveOverrideForCPDecisions(cpDecisionContext, overrideMap, today, today, today, today);

        //then
        assertEquals(overridePrice.add(Supplement.calculateSupplementInPercentage(overridePrice, accomTypeSupplementValue.getValue())), output.getPrettyBAR());
        assertEquals(overridePrice.add(Supplement.calculateSupplementInPercentage(overridePrice, accomTypeSupplementValue.getValue())), output.getFinalBAR());
        assertEquals(overridePrice.add(Supplement.calculateSupplementInPercentage(overridePrice, accomTypeSupplementValue.getValue())), output.getSpecificOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        verify(crudService, times(2)).save(anyCollection());
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    public void saveOverrideForCPDecision_duplicateKeyExceptionFix() throws Exception {
        //setup
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        BigDecimal optimalPrice = BigDecimal.valueOf(210);
        BigDecimal prettiedPrice = BigDecimal.valueOf(300);
        BigDecimal oldCeilingOverride = BigDecimal.valueOf(500);
        BigDecimal oldFloorOverride = BigDecimal.valueOf(150);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        output.setOptimalBAR(optimalPrice);
        output.setCeilingOverride(oldCeilingOverride);
        output.setFloorOverride(oldFloorOverride);
        output.setPropertyId(1);
        output.setAccomType(new AccomType());

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);

        CPDecisionBAROutput newCPDecisionBarOutput = new CPDecisionBAROutput();
        newCPDecisionBarOutput.setId(Long.valueOf(10));
        when(crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISION_BAR_OUTPUT,
                CPDecisionBAROutput.params(output.getProduct(), output.getArrivalDate(), output.getAccomType()))).thenReturn(newCPDecisionBarOutput);
        Mockito.when(cpDecisionContext.calculateRoundedRate(output)).thenReturn(prettiedPrice);

        //when
        productManagementService.saveOverrideForCPDecision(cpDecisionContext, override, output, false);

        //then
        assertEquals(newCPDecisionBarOutput.getId(), output.getId());
        assertEquals(prettiedPrice, output.getPrettyBAR());
        assertEquals(prettiedPrice, output.getFinalBAR());
        assertNull(output.getSpecificOverride());
        assertEquals(floorOverridePrice, output.getFloorOverride());
        assertEquals(ceilingOverridePrice, output.getCeilingOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
    }

    @Test
    public void search() throws Exception {
        //setup
        List<CPDecisionBAROutput> expectedResults = asList(new CPDecisionBAROutput(), new CPDecisionBAROutput());
        PricingManagementCPSearchCriteria searchCriteria = new PricingManagementCPSearchCriteria();
        searchCriteria.setPropertyId(6);
        when(crudService.findByCriteria(searchCriteria)).thenReturn(expectedResults);

        //when
        List<CPDecisionBAROutput> actual = productManagementService.search(searchCriteria);

        //then
        assertEquals(expectedResults, actual);
    }

    @Test
    public void addLrvAndCompetotirRateInfoToBaseRoomType() throws Exception {
        LocalDate startLocalDate = new LocalDate(2017, 4, 1);
        Date startDate = startLocalDate.toDate();
        List<CPDecisionBAROutput> barOutputs = new ArrayList<>();

        CPDecisionBAROutput barOutput1 = new CPDecisionBAROutput();
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomType1.setAccomClass(accomClass1);
        barOutput1.setAccomType(accomType1);
        barOutput1.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput1);

        CPDecisionBAROutput barOutput2 = new CPDecisionBAROutput();
        AccomType accomType2 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomType2.setAccomClass(accomClass2);
        barOutput2.setAccomType(accomType2);
        barOutput2.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput2);

        CPDecisionBAROutput barOutput3 = new CPDecisionBAROutput();
        AccomType accomType3 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomType3.setAccomClass(accomClass3);
        barOutput3.setAccomType(accomType3);
        barOutput3.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput3);

        LastRoomValue lrv1 = createLastRoomValue(1, startDate, new BigDecimal(70.00));
        LastRoomValue lrv2 = createLastRoomValue(2, startDate, new BigDecimal(80.00));
        LastRoomValue lrv3 = createLastRoomValue(3, startDate, new BigDecimal(90.00));

        Map<Date, CompetitorInfo> rateMap1 = new HashMap<>();
        CompetitorInfo info1 = new CompetitorInfo();
        info1.setCompetitorPrice("79.99");
        rateMap1.put(startDate, info1);

        Map<Date, CompetitorInfo> rateMap2 = new HashMap<>();
        CompetitorInfo info2 = new CompetitorInfo();
        info2.setCompetitorPrice("89.99");
        rateMap2.put(startDate, info2);

        Map<Date, CompetitorInfo> rateMap3 = new HashMap<>();
        CompetitorInfo info3 = new CompetitorInfo();
        info3.setCompetitorPrice("99.99");
        rateMap3.put(startDate, info3);

        Map<Integer, Map<Date, CompetitorInfo>> rateMap = new HashMap<>();
        rateMap.put(1, rateMap1);
        rateMap.put(2, rateMap2);
        rateMap.put(3, rateMap3);
        when(barDecisionService.getCompetitorInfoMap(1, startDate, startDate)).thenReturn(rateMap);

        productManagementService.addLrvAndCompetitorRateInfoToBaseRoomType(barOutputs, startDate, startDate, Arrays.asList(lrv1, lrv2, lrv3));

        assertEquals(new BigDecimal(70.00), barOutput1.getLrv());
        assertEquals("79.99", barOutput1.getCompetitorRate());

        assertEquals(new BigDecimal(80.00), barOutput2.getLrv());
        assertEquals("89.99", barOutput2.getCompetitorRate());

        assertEquals(new BigDecimal(90.00), barOutput3.getLrv());
        assertEquals("99.99", barOutput3.getCompetitorRate());
    }

    @Test
    public void addLRVInfoToDecisions() throws Exception {
        LocalDate startLocalDate = new LocalDate(2017, 4, 1);
        Date startDate = startLocalDate.toDate();
        List<CPDecisionBAROutput> barOutputs = new ArrayList<>();

        CPDecisionBAROutput barOutput1 = new CPDecisionBAROutput();
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomType1.setAccomClass(accomClass1);
        barOutput1.setAccomType(accomType1);
        barOutput1.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput1);

        CPDecisionBAROutput barOutput2 = new CPDecisionBAROutput();
        AccomType accomType2 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomType2.setAccomClass(accomClass2);
        barOutput2.setAccomType(accomType2);
        barOutput2.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput2);

        CPDecisionBAROutput barOutput3 = new CPDecisionBAROutput();
        AccomType accomType3 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomType3.setAccomClass(accomClass3);
        barOutput3.setAccomType(accomType3);
        barOutput3.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput3);

        LastRoomValue lrv1 = createLastRoomValue(1, startDate, new BigDecimal(70.00));
        LastRoomValue lrv2 = createLastRoomValue(2, startDate, new BigDecimal(80.00));
        LastRoomValue lrv3 = createLastRoomValue(3, startDate, new BigDecimal(90.00));

        productManagementService.addLRVInfoToDecisions(barOutputs, Arrays.asList(lrv1, lrv2, lrv3), new HashSet<>(Arrays.asList(1, 2, 3)));

        assertEquals(new BigDecimal(70.00), barOutput1.getLrv());
        assertEquals(new BigDecimal(80.00), barOutput2.getLrv());
        assertEquals(new BigDecimal(90.00), barOutput3.getLrv());
    }

    @Test
    public void addCompetitorRateInfoToDecisions() throws Exception {
        LocalDate startLocalDate = new LocalDate(2017, 4, 1);
        Date startDate = startLocalDate.toDate();
        List<CPDecisionBAROutput> barOutputs = new ArrayList<>();

        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(true);

        CPDecisionBAROutput barOutput1 = new CPDecisionBAROutput();
        barOutput1.setProduct(product);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomType1.setAccomClass(accomClass1);
        barOutput1.setAccomType(accomType1);
        barOutput1.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput1);

        CPDecisionBAROutput barOutput2 = new CPDecisionBAROutput();
        barOutput2.setProduct(product);
        AccomType accomType2 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomType2.setAccomClass(accomClass2);
        barOutput2.setAccomType(accomType2);
        barOutput2.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput2);

        CPDecisionBAROutput barOutput3 = new CPDecisionBAROutput();
        barOutput3.setProduct(product);
        AccomType accomType3 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomType3.setAccomClass(accomClass3);
        barOutput3.setAccomType(accomType3);
        barOutput3.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput3);

        Map<Date, Map<Integer, CompetitorInfo>> rateMap1 = new HashMap<>();
        CompetitorInfo info1 = new CompetitorInfo();
        info1.setProductId(BigInteger.ONE);
        info1.setCompetitorPrice("79.99");
        Map<Integer, CompetitorInfo> competitorMap1 = new HashMap<>();
        competitorMap1.put(1, info1);
        rateMap1.put(startDate, competitorMap1);

        Map<Date, Map<Integer, CompetitorInfo>> rateMap2 = new HashMap<>();
        CompetitorInfo info2 = new CompetitorInfo();
        info2.setProductId(BigInteger.ONE);
        info2.setCompetitorPrice("89.99");
        Map<Integer, CompetitorInfo> competitorMap2 = new HashMap<>();
        competitorMap2.put(1, info2);
        rateMap2.put(startDate, competitorMap2);

        Map<Date, Map<Integer, CompetitorInfo>> rateMap3 = new HashMap<>();
        CompetitorInfo info3 = new CompetitorInfo();
        info3.setProductId(BigInteger.ONE);
        info3.setCompetitorPrice("99.99");
        Map<Integer, CompetitorInfo> competitorMap3 = new HashMap<>();
        competitorMap3.put(1, info3);
        rateMap3.put(startDate, competitorMap3);

        Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> rateMap = new HashMap<>();
        rateMap.put(1, rateMap1);
        rateMap.put(2, rateMap2);
        rateMap.put(3, rateMap3);

        when(barDecisionService.getCompetitorInfoMapDayCardLayout(1, startDate, startDate)).thenReturn(rateMap);

        productManagementService.addCompetitorRateInfoToDecisions(barOutputs, startDate, startDate, new HashSet<>(Arrays.asList(1, 2, 3)));

        assertEquals("79.99", barOutput1.getCompetitorRate());
        assertEquals("89.99", barOutput2.getCompetitorRate());
        assertEquals("99.99", barOutput3.getCompetitorRate());
    }

    @Test
    public void addCompetitorRateInfoToDecisions_IndependentProducts() throws Exception {
        LocalDate startLocalDate = new LocalDate(2017, 4, 1);
        Date startDate = startLocalDate.toDate();
        List<CPDecisionBAROutput> barOutputs = new ArrayList<>();

        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(true);
        product.setRateShoppingLOSMin(-1);

        CPDecisionBAROutput barOutput1 = new CPDecisionBAROutput();
        barOutput1.setProduct(product);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomType1.setAccomClass(accomClass1);
        barOutput1.setAccomType(accomType1);
        barOutput1.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput1);

        CPDecisionBAROutput barOutput2 = new CPDecisionBAROutput();
        barOutput2.setProduct(product);
        AccomType accomType2 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomType2.setAccomClass(accomClass2);
        barOutput2.setAccomType(accomType2);
        barOutput2.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput2);

        CPDecisionBAROutput barOutput3 = new CPDecisionBAROutput();
        barOutput3.setProduct(product);
        AccomType accomType3 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomType3.setAccomClass(accomClass3);
        barOutput3.setAccomType(accomType3);
        barOutput3.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput3);

        Map<Date, Map<Integer, CompetitorInfo>> rateMap1 = new HashMap<>();
        CompetitorInfo info1 = new CompetitorInfo();
        info1.setProductId(BigInteger.ONE);
        info1.setCompetitorPrice("79.99");
        Map<Integer, CompetitorInfo> competitorMap1 = new HashMap<>();
        competitorMap1.put(1, info1);
        rateMap1.put(startDate, competitorMap1);

        Map<Date, Map<Integer, CompetitorInfo>> rateMap2 = new HashMap<>();
        CompetitorInfo info2 = new CompetitorInfo();
        info2.setProductId(BigInteger.ONE);
        info2.setCompetitorPrice("89.99");
        Map<Integer, CompetitorInfo> competitorMap2 = new HashMap<>();
        competitorMap2.put(1, info2);
        rateMap2.put(startDate, competitorMap2);

        Map<Date, Map<Integer, CompetitorInfo>> rateMap3 = new HashMap<>();
        CompetitorInfo info3 = new CompetitorInfo();
        info3.setProductId(BigInteger.ONE);
        info3.setCompetitorPrice("99.99");
        Map<Integer, CompetitorInfo> competitorMap3 = new HashMap<>();
        competitorMap3.put(1, info3);
        rateMap3.put(startDate, competitorMap3);

        Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> rateMap = new HashMap<>();
        rateMap.put(1, rateMap1);
        rateMap.put(2, rateMap2);
        rateMap.put(3, rateMap3);

        when(barDecisionService.getCompetitorInfoMapDayCardLayout(new HashSet<>(of(1)), startDate, startDate)).thenReturn(rateMap);

        productManagementService.addCompetitorRateInfoToDecisions(barOutputs, startDate, startDate, new HashSet<>(Arrays.asList(1, 2, 3)), new HashSet<>(of(1)));

        assertEquals("79.99", barOutput1.getCompetitorRate());
        assertEquals("89.99", barOutput2.getCompetitorRate());
        assertEquals("99.99", barOutput3.getCompetitorRate());
    }

    @Test
    public void searchForDTO() throws Exception {
        //setup
        LocalDate currentDate = new LocalDate(2018, 9, 1);
        LocalDate oneMonth = currentDate.plusDays(30);
        LocalDate tomorrow = currentDate.plusDays(1);
        LocalDate dayAfter = tomorrow.plusDays(1);

        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        AccomClass roomClass = new AccomClass();
        roomClass.setId(12345);

        CPDecisionBAROutput decision1 = new CPDecisionBAROutput();
        decision1.setArrivalDate(currentDate);
        decision1.setId(1L);
        decision1.setAccomType(accomType1);

        CPDecisionBAROutput decision2 = new CPDecisionBAROutput();
        decision2.setArrivalDate(currentDate);
        decision2.setId(Long.valueOf(2));
        decision2.setAccomType(accomType2);

        CPDecisionBAROutput decision3 = new CPDecisionBAROutput();
        decision3.setArrivalDate(tomorrow);
        decision3.setId(Long.valueOf(3));
        decision3.setAccomType(accomType1);

        CPDecisionBAROutput decision4 = new CPDecisionBAROutput();
        decision4.setArrivalDate(tomorrow);
        decision4.setId(Long.valueOf(4));
        decision4.setAccomType(accomType2);

        CPDecisionBAROutput decision5 = new CPDecisionBAROutput();
        decision5.setArrivalDate(dayAfter);
        decision5.setId(Long.valueOf(5));
        decision5.setAccomType(accomType1);

        CPDecisionBAROutput decision6 = new CPDecisionBAROutput();
        decision6.setArrivalDate(dayAfter);
        decision6.setId(Long.valueOf(6));
        decision6.setAccomType(accomType2);

        PricingManagementCPSearchCriteria searchCriteria = new PricingManagementCPSearchCriteria();
        searchCriteria.setPropertyId(6);
        searchCriteria.setStartDate(currentDate);
        searchCriteria.setEndDate(oneMonth);
        searchCriteria.setRoomClass(roomClass);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(false);
        when(crudService.findByCriteria(searchCriteria)).thenReturn(asList(decision1, decision2, decision3, decision4, decision5, decision6));

        PricingAccomClass masterPricingAccomClass = new PricingAccomClass();
        AccomClass masterAccomClass = new AccomClass();
        masterAccomClass.setMasterClass(1);
        masterPricingAccomClass.setAccomClass(masterAccomClass);
        BusinessAnalysisDailyDataDto dataDto1 = new BusinessAnalysisDailyDataDto();
        dataDto1.setDate(DateParameter.fromDate(currentDate.toDate()));
        dataDto1.setOccupancyForecast(BigDecimal.TEN);
        dataDto1.setOccupancyForecastPerc(BigDecimal.ONE);
        dataDto1.setOutOfOrder((long) 2);
        dataDto1.setOnBooks((long) 50);
        dataDto1.setLrv(BigDecimal.valueOf(25));
        dataDto1.setCapacity((long) 75);

        when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(currentDate.toDate(), oneMonth.toDate(), masterPricingAccomClass, dateService.getCaughtUpDate())).thenReturn(singletonList(dataDto1));

        BusinessAnalysisDailyIndicatorDto indicatorDto1 = new BusinessAnalysisDailyIndicatorDto();
        indicatorDto1.setDate(DateParameter.fromDate(currentDate.toDate()));
        indicatorDto1.setSpecialEventImpactFCST(true);
        BusinessAnalysisDailyIndicatorDto indicatorDto2 = new BusinessAnalysisDailyIndicatorDto();
        indicatorDto2.setDate(DateParameter.fromDate(tomorrow.toDate()));
        indicatorDto2.setSpecialEventInfoOnly(true);
        when(businessAnalysisDashboardService.getBusinessAnalysisDailyIndicatorDtos(currentDate.toDate(), oneMonth.toDate())).thenReturn(asList(indicatorDto1, indicatorDto2));
        List<BusinessAnalysisSpecialEventDto> todaysEvents = asList(new BusinessAnalysisSpecialEventDto(), new BusinessAnalysisSpecialEventDto());
        List<BusinessAnalysisSpecialEventDto> tomorrowsEvents = of(new BusinessAnalysisSpecialEventDto());
        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(currentDate.toDate())).thenReturn(todaysEvents);
        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(tomorrow.toDate())).thenReturn(tomorrowsEvents);

        Map<Date, CompetitorInfo> competitorInfoMap = new HashMap<>();
        CompetitorInfo competitorInfo1 = new CompetitorInfo();
        competitorInfo1.setCompetitorPrice("50.99");
        competitorInfoMap.put(currentDate.toDate(), competitorInfo1);

        LastRoomValue lastRoomValue = new LastRoomValue();
        lastRoomValue.setOccupancyDate(currentDate.toDate());
        lastRoomValue.setValue(new BigDecimal("28"));
        when(barDecisionService.getLastRoomValues(12345, currentDate.toDate(), oneMonth.toDate())).thenReturn(of(lastRoomValue));
        when(barDecisionService.getCompetitorInfo(12345, 1, currentDate.toDate(), oneMonth.toDate())).thenReturn(competitorInfoMap);

        //when
        List<CPBARDecisionDTO> actual = productManagementService.searchForBarDecisionDTO(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value()), searchCriteria, false, masterPricingAccomClass, dateService.getCaughtUpDate());

        //then
        assertEquals(3, actual.size());

        CPBARDecisionDTO dto1 = actual.get(0);

        assertEquals(currentDate, dto1.getDate());
        assertEquals(asList(decision1, decision2), dto1.getDecisions());
        assertEquals(BigDecimal.TEN, dto1.getOccupancyForecast());
        assertEquals(BigDecimal.ONE, dto1.getOccupancyForecastPercentage());
        assertEquals(Long.valueOf(2), dto1.getOutOfOrder());
        assertEquals(Long.valueOf(50), dto1.getRoomsOnBooks());
        assertEquals(BigDecimal.valueOf(28), dto1.getLrv());
        assertEquals(todaysEvents, dto1.getSpecialEvents());
        assertEquals("50.99", dto1.getCompetitorRate());
        assertEquals(Optional.of(dataDto1.getCapacity() - dataDto1.getOutOfOrder() - dataDto1.getOverbookings() - dataDto1.getOnBooks()).get(), dto1.getAvailableCapacityToSell());
        assertEquals(dataDto1.getEffectiveCapacity(), dto1.getEffectiveCapacity());
        assertEquals(dataDto1.getAuthorizedCapacity(), dto1.getAuthorizedCapacity());

        CPBARDecisionDTO dto2 = actual.get(1);

        assertEquals(tomorrow, dto2.getDate());
        assertEquals(asList(decision3, decision4), dto2.getDecisions());
        assertEquals(tomorrowsEvents, dto2.getSpecialEvents());

        CPBARDecisionDTO dto3 = actual.get(2);

        assertEquals(dayAfter, dto3.getDate());
        assertEquals(asList(decision5, decision6), dto3.getDecisions());
    }

    @Test
    public void searchForDTO_baseRoomTypeOnly() throws Exception {
        LocalDate today = new LocalDate();
        LocalDate oneMonth = today.plusDays(30);
        LocalDate tomorrow = today.plusDays(1);

        AccomType baseRoomType1 = new AccomType();
        baseRoomType1.setId(1);
        AccomClass roomClass1 = new AccomClass();
        roomClass1.setId(1);
        baseRoomType1.setAccomClass(roomClass1);

        CPDecisionBAROutput decision1 = new CPDecisionBAROutput();
        decision1.setArrivalDate(today);
        decision1.setId(Long.valueOf(1));
        decision1.setAccomType(baseRoomType1);

        PricingManagementCPSearchCriteria searchCriteria = new PricingManagementCPSearchCriteria();
        searchCriteria.setPropertyId(6);
        searchCriteria.setRoomClass(roomClass1);
        searchCriteria.setStartDate(today);
        searchCriteria.setEndDate(oneMonth);
        searchCriteria.setBaseRoomTypes(singletonList(baseRoomType1));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        when(crudService.findByCriteria(searchCriteria)).thenReturn(singletonList(decision1));
        PricingAccomClass masterPricingAccomClass = new PricingAccomClass();
        AccomClass masterAccomClass = new AccomClass();
        masterAccomClass.setMasterClass(1);
        masterPricingAccomClass.setAccomClass(masterAccomClass);
        BusinessAnalysisDailyDataDto dataDto1 = new BusinessAnalysisDailyDataDto();
        dataDto1.setDate(DateParameter.fromDate(today.toDate()));
        dataDto1.setOccupancyForecast(BigDecimal.TEN);
        dataDto1.setOccupancyForecastPerc(BigDecimal.ONE);
        dataDto1.setOutOfOrder((long) 2);
        dataDto1.setOnBooks((long) 50);
        dataDto1.setLrv(BigDecimal.valueOf(25));
        dataDto1.setCapacity((long) 75);

        when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(today.toDate(), oneMonth.toDate(), masterPricingAccomClass, dateService.getCaughtUpDate())).thenReturn(singletonList(dataDto1));

        BusinessAnalysisDailyIndicatorDto indicatorDto1 = new BusinessAnalysisDailyIndicatorDto();
        indicatorDto1.setDate(DateParameter.fromDate(today.toDate()));
        indicatorDto1.setSpecialEventImpactFCST(true);
        BusinessAnalysisDailyIndicatorDto indicatorDto2 = new BusinessAnalysisDailyIndicatorDto();
        indicatorDto2.setDate(DateParameter.fromDate(tomorrow.toDate()));
        indicatorDto2.setSpecialEventInfoOnly(true);

        when(businessAnalysisDashboardService.getBusinessAnalysisDailyIndicatorDtos(today.toDate(), oneMonth.toDate())).thenReturn(asList(indicatorDto1, indicatorDto2));

        List<BusinessAnalysisSpecialEventDto> todaysEvents = asList(new BusinessAnalysisSpecialEventDto(), new BusinessAnalysisSpecialEventDto());
        List<BusinessAnalysisSpecialEventDto> tomorrowsEvents = singletonList(new BusinessAnalysisSpecialEventDto());

        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(today.toDate())).thenReturn(todaysEvents);
        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(tomorrow.toDate())).thenReturn(tomorrowsEvents);

        Map<Date, CompetitorInfo> competitorInfoMapForRoomClass1 = new HashMap<>();
        CompetitorInfo competitorInfo1 = new CompetitorInfo();
        competitorInfo1.setCompetitorPrice("50.99");
        competitorInfoMapForRoomClass1.put(today.toDate(), competitorInfo1);

        LastRoomValue lrv1 = createLastRoomValue(1, today.toDate(), new BigDecimal("28"));
        when(barDecisionService.getLastRoomValues(1, today.toDate(), oneMonth.toDate())).thenReturn(of(lrv1));

        Map<Integer, Map<Date, CompetitorInfo>> rateMap = new HashMap<>();
        rateMap.put(1, competitorInfoMapForRoomClass1);
        when(barDecisionService.getCompetitorInfoMap(1, today.toDate(), oneMonth.toDate())).thenReturn(rateMap);

        List<CPBARDecisionDTO> actual = productManagementService.searchForBarDecisionDTO(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value()), searchCriteria, false, masterPricingAccomClass, dateService.getCaughtUpDate());

        assertEquals(1, actual.size());
        CPBARDecisionDTO dto1 = actual.get(0);
        CPDecisionBAROutput output1 = dto1.getDecisions().get(0);

        assertEquals(today, dto1.getDate());
        assertEquals(singletonList(decision1), dto1.getDecisions());
        assertEquals(BigDecimal.TEN, dto1.getOccupancyForecast());
        assertEquals(BigDecimal.ONE, dto1.getOccupancyForecastPercentage());
        assertEquals(Long.valueOf(2), dto1.getOutOfOrder());
        assertEquals(Long.valueOf(50), dto1.getRoomsOnBooks());
        assertEquals(todaysEvents, dto1.getSpecialEvents());
        assertEquals(BigDecimal.valueOf(28), output1.getLrv());
        assertEquals("50.99", output1.getCompetitorRate());
        assertEquals(Optional.of(dataDto1.getCapacity() - dataDto1.getOutOfOrder() - dataDto1.getOverbookings() - dataDto1.getOnBooks()).get(), dto1.getAvailableCapacityToSell());
        assertEquals(dataDto1.getEffectiveCapacity(), dto1.getEffectiveCapacity());
        assertEquals(dataDto1.getAuthorizedCapacity(), dto1.getAuthorizedCapacity());
    }

    @Test
    public void getRoomClassDetailsWithOptimizedSp() {
        Integer roomClassId = 3;
        LocalDate startDate = new LocalDate(2018, 12, 1);
        LocalDate endDate = startDate.plusDays(6);

        List<CPRoomClassDTO> roomClassDTOS = buildCPRoomClassDTOs(6, startDate, 1);
        when(crudService.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(roomClassDTOS);

        Map<LocalDate, CPRoomClassDTO> dtos = productManagementService.getRoomClassDetails(roomClassId, startDate, endDate);

        assertTrue(MapUtils.isNotEmpty(dtos));
        assertEquals(7, dtos.size());

        verify(crudService).findByNativeQuery(eq("[dbo].[usp_pricing_get_by_rc_optimized] :propertyId, :roomClassId, :startDate, :endDate, :includeZeroCapacityRT"), anyMap(), any(RowMapper.class));
    }

    @Test
    public void getAllRoomClassDetails() {
        String roomClassId = "2,3";
        LocalDate startDate = new LocalDate(2018, 12, 1);
        LocalDate endDate = startDate.plusDays(6);

        List<CPRoomClassDTO> roomClassDTOS = buildCPRoomClassDTOs(6, startDate, 1);
        roomClassDTOS.addAll(buildCPRoomClassDTOs(6, startDate, 21));
        when(crudService.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(roomClassDTOS);

        Map<LocalDate, Map<Integer, CPRoomClassDTO>> dtos = productManagementService.getAllRoomClassDetails(roomClassId, startDate, endDate);

        assertTrue(MapUtils.isNotEmpty(dtos));
        assertEquals(7, dtos.size());
        dtos.forEach((date, innerMap) -> {
            assertTrue(innerMap.containsKey(1));
            assertTrue(innerMap.containsKey(21));
        });
        verify(crudService).findByNativeQuery(eq("[dbo].[usp_pricing_get_by_all_rc] :propertyId, :roomClassId, :startDate, :endDate"), anyMap(), any(RowMapper.class));
    }

    @Test
    public void shouldReturnGroupedRoomTypeDetailsByDateAndAccomTypeId() {
        Integer roomClassId = 1;
        LocalDate startDate = new LocalDate(2018, 12, 1);
        LocalDate endDate = startDate.plusDays(5);

        List<CPRoomClassDTO> sampleDtos = new ArrayList<>();
        sampleDtos.addAll(buildRoomTypeDTOs(6, startDate, 101));
        sampleDtos.addAll(buildRoomTypeDTOs(6, startDate, 102));

        when(crudService.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class)))
                .thenReturn(sampleDtos);

        Map<LocalDate, Map<Integer, CPRoomClassDTO>> result = productManagementService.getAllRoomTypeDetails(roomClassId, startDate, endDate);

        assertNotNull(result);
        assertEquals(7, result.size());

        result.forEach((date, innerMap) -> {
            assertTrue(innerMap.containsKey(101));
            assertTrue(innerMap.containsKey(102));
        });

        verify(crudService).findByNativeQuery(eq("[dbo].[usp_pricing_get_by_all_rt] :propertyId, :roomClassId, :startDate, :endDate"), anyMap(), any(RowMapper.class));
    }

    private List<CPRoomClassDTO> buildRoomTypeDTOs(Integer numberOfDays, LocalDate startDate, Integer accomTypeId) {
        List<CPRoomClassDTO> dtos = buildCPRoomClassDTOs(numberOfDays, startDate, 1);
        dtos.forEach(dto -> dto.setAccomTypeId(accomTypeId));
        return dtos;
    }

    @Test
    public void saveProductRateOffsetOverride_NonOptimizedOverride() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setOptimized(false);

        when(crudService.save(Collections.singletonList(override))).thenReturn(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        productManagementService.saveProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);

        productManagementService.saveProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, times(2)).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }
    @Test
    public void saveProductRateOffsetOverride_NonOptimizedOverrideWithNewSyncFlag() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setOptimized(false);

        when(crudService.save(Collections.singletonList(override))).thenReturn(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_OVERRIDE_IDP_FLOW_BASED_SYNC_ENABLED)).thenReturn(true);
        productManagementService.saveProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService,never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);

        productManagementService.saveProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        verify(syncEventAggregatorService, times(2)).registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);
    }

    @Test
    public void saveProductRateOffsetOverride_OptimizedOverride() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setOptimized(true);

        when(crudService.save(Collections.singletonList(override))).thenReturn(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        productManagementService.saveProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);

        productManagementService.saveProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }
    @Test
    public void saveProductRateOffsetOverride_OptimizedOverrideWithNewSyncFlag() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setOptimized(true);

        when(crudService.save(Collections.singletonList(override))).thenReturn(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_OVERRIDE_IDP_FLOW_BASED_SYNC_ENABLED)).thenReturn(true);
        productManagementService.saveProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        productManagementService.saveProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);
    }

    @Test
    public void deleteProductRateOffsetOverride_NonOptimizedOverride() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setOptimized(false);

        doNothing().when(crudService).delete(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        override.setStatusId(1);

        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, times(2)).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }
    @Test
    public void deleteProductRateOffsetOverride_NonOptimizedOverrideWithNewSyncFlag() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setOptimized(false);

        doNothing().when(crudService).delete(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_OVERRIDE_IDP_FLOW_BASED_SYNC_ENABLED)).thenReturn(true);
        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        override.setStatusId(1);

        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        verify(syncEventAggregatorService, times(2)).registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);
    }

    @Test
    public void deleteProductRateOffsetOverride_OptimizedOverride() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setOptimized(true);

        doNothing().when(crudService).delete(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        override.setStatusId(1);

        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }
    @Test
    public void deleteProductRateOffsetOverride_OptimizedOverrideWithNewSyncFlag() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setOptimized(true);

        doNothing().when(crudService).delete(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_OVERRIDE_IDP_FLOW_BASED_SYNC_ENABLED)).thenReturn(true);
        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        override.setStatusId(1);

        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.AGILE_RATES_OVERRIDE_CHANGED);
    }

    @Test
    public void shouldAgileRatesSyncBeFire() {
        ProductRateOffsetOverride optimizedOverride = AgileRatesObjectMother.buildProductRateOverride();
        optimizedOverride.getProduct().setOptimized(true);

        ProductRateOffsetOverride nonOptimizedOverride = AgileRatesObjectMother.buildProductRateOverride();
        nonOptimizedOverride.getProduct().setOptimized(false);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_NON_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(null));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(new ArrayList<>()));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(of(optimizedOverride)));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(of(nonOptimizedOverride)));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(Arrays.asList(optimizedOverride, nonOptimizedOverride)));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_NON_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);

        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(null));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(new ArrayList<>()));
        assertTrue(productManagementService.shouldAgileRatesSyncBeFire(of(optimizedOverride)));
        assertTrue(productManagementService.shouldAgileRatesSyncBeFire(of(nonOptimizedOverride)));
        assertTrue(productManagementService.shouldAgileRatesSyncBeFire(Arrays.asList(optimizedOverride, nonOptimizedOverride)));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_NON_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);

        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(null));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(new ArrayList<>()));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(of(optimizedOverride)));
        assertTrue(productManagementService.shouldAgileRatesSyncBeFire(of(nonOptimizedOverride)));
        assertTrue(productManagementService.shouldAgileRatesSyncBeFire(Arrays.asList(optimizedOverride, nonOptimizedOverride)));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_NON_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(null));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(new ArrayList<>()));
        assertTrue(productManagementService.shouldAgileRatesSyncBeFire(of(optimizedOverride)));
        assertFalse(productManagementService.shouldAgileRatesSyncBeFire(of(nonOptimizedOverride)));
        assertTrue(productManagementService.shouldAgileRatesSyncBeFire(Arrays.asList(optimizedOverride, nonOptimizedOverride)));
    }

    @Test
    public void isPricingDisableAgileRatesSyncForOptimizedProductOverrides() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        assertTrue(productManagementService.isPricingDisableAgileRatesSyncForOptimizedProductOverrides());

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        assertFalse(productManagementService.isPricingDisableAgileRatesSyncForOptimizedProductOverrides());
    }

    @Test
    public void isPricingDisableAgileRatesSyncForNonOptimizedProductOverrides() {
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_NON_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        assertTrue(productManagementService.isPricingDisableAgileRatesSyncForNonOptimizedProductOverrides());

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_NON_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        assertFalse(productManagementService.isPricingDisableAgileRatesSyncForNonOptimizedProductOverrides());
    }

    @Test
    public void saveProductRateOffsetOverrideForNonOptimized() {
        setUpServiceCalls(false);

        productManagementService.saveNonOptimizedAgileProductOverrides(7, 3, "2020-05-20", "2020-05-20", 5);

        verify(crudService, times(2)).save(anyList());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }

    private void setUpServiceCalls(boolean isOptimized) {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setId(7);
        override.getProduct().setOptimized(isOptimized);
        override.setOccupancyDate(LocalDate.parse("2020-05-20"));
        AccomClass accomClass = new AccomClass();
        accomClass.setId(3);
        when(crudService.find(AccomClass.class, 3)).thenReturn(accomClass);
        when(crudService.find(Product.class, 7)).thenReturn(override.getProduct());
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        when(agileRatesConfigurationService.findRoomTypesByProduct(override.getProduct())).thenReturn(Collections.singletonList(accomType));
        when(agileRatesConfigurationService.findAllDTARanges()).thenReturn(Collections.singletonList(override.getAgileRatesDTARange()));
        when(agileRatesConfigurationService.getAdjustedDTARanges(override.getProduct(), Collections.singletonList(override.getAgileRatesDTARange()))).thenReturn(Collections.singletonList(override.getAgileRatesDTARange()));
    }

    @Test
    public void saveProductRateOffsetOverrideForOptimized() {
        setUpServiceCalls(true);

        productManagementService.saveOptimizedAgileProductOverrides(7, 3, "2020-05-20", "2020-05-20", 5, 10);

        verify(crudService, times(2)).save(anyList());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }

    @Test
    public void deleteProductRateOffsetOverride1() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();

        doNothing().when(crudService).delete(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);

        productManagementService.deleteProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService).save(Collections.singletonList(override));
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }


    @Test
    public void isPriceBetweenFloorAndCeilingMultiDay() {
        AccomType accomType = buildAccomType();
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        Map<LocalDate, Tax> taxesByDate = new HashMap<>();
        taxesByDate.put(START_DATE_RANGE, tax);
        taxesByDate.put(START_DATE_RANGE.plusDays(1), tax);
        taxesByDate.put(END_DATE_RANGE, tax);
        when(taxService.findTaxesForDateRange(START_DATE_RANGE, END_DATE_RANGE)).thenReturn(taxesByDate);
        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(true);
        when(productManagementService.findDemandForecastBetweenDatesForAccomClass(product, accomType.getAccomClass(), START_DATE_RANGE, END_DATE_RANGE))
                .thenReturn(buildCPUnqualifiedDemandForecastPrice(START_DATE_RANGE, END_DATE_RANGE));

        assertFalse(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE, accomType,
                        BigDecimal.valueOf(10), BigDecimal.valueOf(10), DAY_OF_WEEKS),
                "10 is not between max floor and min ceiling");

        assertTrue(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                        accomType, BigDecimal.valueOf(30), BigDecimal.valueOf(30), DAY_OF_WEEKS),
                "30 is between max floor and min ceiling");

        assertTrue(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                        accomType, BigDecimal.valueOf(40), BigDecimal.valueOf(40), DAY_OF_WEEKS),
                "40 is between max floor and min ceiling");

        assertFalse(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                        accomType, BigDecimal.valueOf(130), BigDecimal.valueOf(130), DAY_OF_WEEKS),
                "130 is not between max floor and min ceiling");

        verify(taxService, times(4)).findTaxesForDateRange(START_DATE_RANGE, END_DATE_RANGE);
    }

    @Test
    public void isPriceBetweenFloorAndCeilingMultiDayWithPreCalculatedData_ApplyRounding() {
        AccomType accomType = buildAccomType();
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        Map<LocalDate, Tax> taxesByDate = new HashMap<>();
        taxesByDate.put(START_DATE_RANGE, tax);
        taxesByDate.put(START_DATE_RANGE.plusDays(1), tax);
        taxesByDate.put(END_DATE_RANGE, tax);
        java.time.LocalDate startDate = java.time.LocalDate.now();
        List<CPUnqualifedDemandForecastPrice> unqualifiedDemandForecastPrice = buildCPUnqualifiedDemandForecastPrice(START_DATE_RANGE, END_DATE_RANGE);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE)).thenReturn(cpDecisionContext);
        BigDecimal ceiling = new BigDecimal(126.50000).setScale(5);
        BigDecimal floor = new BigDecimal(27.50000).setScale(5);
        when(cpDecisionContext.calculatePrettyPrice(1, ceiling)).thenReturn(ceiling);
        when(cpDecisionContext.calculatePrettyPrice(1, floor)).thenReturn(floor);

        assertFalse(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(unqualifiedDemandForecastPrice, 1, accomType,
                        BigDecimal.valueOf(10), BigDecimal.valueOf(10), DAY_OF_WEEKS, taxesByDate, new HashMap<>(), new HashMap<>(), startDate, startDate),
                "10 is not between max floor and min ceiling");

        assertTrue(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(unqualifiedDemandForecastPrice, 1, accomType,
                        BigDecimal.valueOf(30), BigDecimal.valueOf(30), DAY_OF_WEEKS, taxesByDate, new HashMap<>(), new HashMap<>(), startDate, startDate),
                "30 is between max floor and min ceiling");

        assertTrue(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(unqualifiedDemandForecastPrice, 1, accomType,
                        BigDecimal.valueOf(40), BigDecimal.valueOf(40), DAY_OF_WEEKS, taxesByDate, new HashMap<>(), new HashMap<>(), startDate, startDate),
                "40 is between max floor and min ceiling");

        assertFalse(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(unqualifiedDemandForecastPrice, 1, accomType,
                        BigDecimal.valueOf(130), BigDecimal.valueOf(130), DAY_OF_WEEKS, taxesByDate, new HashMap<>(), new HashMap<>(), startDate, startDate),
                "126 is not between max floor and min ceiling");

        verify(taxService, never()).findTaxesForDateRange(START_DATE_RANGE, END_DATE_RANGE);
        verify(pricingConfigurationService, times(4)).getBaseOccupancyType();
        verify(pricingConfigurationService, times(4)).getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE);
    }

    @Test
    public void isPriceBetweenFloorAndCeilingMultiDayWithPreCalculatedData_NoRounding() {
        AccomType accomType = buildAccomType();
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        Map<LocalDate, Tax> taxesByDate = new HashMap<>();
        taxesByDate.put(START_DATE_RANGE, tax);
        taxesByDate.put(START_DATE_RANGE.plusDays(1), tax);
        taxesByDate.put(END_DATE_RANGE, tax);
        java.time.LocalDate startDate = java.time.LocalDate.now();
        List<CPUnqualifedDemandForecastPrice> unqualifiedDemandForecastPrice = buildCPUnqualifiedDemandForecastPrice(START_DATE_RANGE, END_DATE_RANGE);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.EXTRA_ADULT);

        assertFalse(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(unqualifiedDemandForecastPrice, 1, accomType,
                        BigDecimal.valueOf(10), BigDecimal.valueOf(10), DAY_OF_WEEKS, taxesByDate, new HashMap<>(), new HashMap<>(), startDate, startDate),
                "10 is not between max floor and min ceiling");

        assertTrue(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(unqualifiedDemandForecastPrice, 1, accomType,
                        BigDecimal.valueOf(30), BigDecimal.valueOf(30), DAY_OF_WEEKS, taxesByDate, new HashMap<>(), new HashMap<>(), startDate, startDate),
                "30 is between max floor and min ceiling");

        assertTrue(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(unqualifiedDemandForecastPrice, 1, accomType,
                        BigDecimal.valueOf(40), BigDecimal.valueOf(40), DAY_OF_WEEKS, taxesByDate, new HashMap<>(), new HashMap<>(), startDate, startDate),
                "40 is between max floor and min ceiling");

        assertFalse(productManagementService.isPriceBetweenFloorAndCeilingMultiDay(unqualifiedDemandForecastPrice, 1, accomType,
                        BigDecimal.valueOf(130), BigDecimal.valueOf(130), DAY_OF_WEEKS, taxesByDate, new HashMap<>(), new HashMap<>(), startDate, startDate),
                "126 is not between max floor and min ceiling");

        verify(taxService, never()).findTaxesForDateRange(START_DATE_RANGE, END_DATE_RANGE);
        verify(pricingConfigurationService, times(4)).getBaseOccupancyType();
        verify(pricingConfigurationService, never()).getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false);
    }

    @Test
    public void getSupplement() {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(false);
        assertNull(productManagementService.getSupplement(new LocalDate(), new LocalDate()));

        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(true);
        when(accomTypeSupplementService.getSupplementValueMap(new LocalDate(), new LocalDate())).thenReturn(new HashMap<>());

        assertTrue(productManagementService.getSupplement(new LocalDate(), new LocalDate()).isEmpty());
    }

    @Test
    public void getSupplementValue() {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(true);

        LocalDate arrivalDate = new LocalDate();
        Integer accomTypeId = 1;
        OccupancyType occupancyType = OccupancyType.SINGLE;
        HashMap<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = new HashMap<>();

        assertEquals(BigDecimal.ZERO, productManagementService.getSupplementValue(1, map, arrivalDate, accomTypeId, occupancyType));

        AccomTypeSupplementValuePK valuePK = new AccomTypeSupplementValuePK(1, arrivalDate, accomTypeId, occupancyType);
        AccomTypeSupplementValue value = new AccomTypeSupplementValue();
        value.setValue(BigDecimal.TEN);
        map.put(valuePK, value);

        assertEquals(BigDecimal.TEN, productManagementService.getSupplementValue(1, map, arrivalDate, accomTypeId, occupancyType));
    }

    @Test
    public void applyOffsetTaxAndSupplement() {
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(true);

        OccupancyType baseOccupancyType = OccupancyType.SINGLE;
        Tax tax = new Tax();
        HashMap<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplementValueMap = new HashMap<>();
        CPConfigMergedOffset offset = new CPConfigMergedOffset();
        CPUnqualifedDemandForecastPrice forecastPrice = new CPUnqualifedDemandForecastPrice();
        Integer accomTypeId = 1;
        LocalDate arrivalDate = new LocalDate();

        forecastPrice.setRate(BigDecimal.ONE);
        forecastPrice.setArrivalDate(arrivalDate);
        offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset.setOffsetValue(BigDecimal.valueOf(2));
        tax.setRoomTaxRate(BigDecimal.valueOf(100));

        AccomTypeSupplementValuePK valuePK = new AccomTypeSupplementValuePK(1, arrivalDate, accomTypeId, baseOccupancyType);
        AccomTypeSupplementValue value = new AccomTypeSupplementValue();
        value.setValue(BigDecimal.TEN);
        supplementValueMap.put(valuePK, value);

        assertEquals(14.00000, productManagementService.applyOffsetTaxAndSupplement(1, baseOccupancyType, tax, supplementValueMap, offset, forecastPrice, accomTypeId).doubleValue(), 0);
    }

    @Test
    public void applyOffsetTaxandSupplementIfSupplementTypeIsPercentage() throws Exception {
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        OccupancyType baseOccupancyType = OccupancyType.SINGLE;
        Tax tax = new Tax();
        HashMap<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplementValueMap = new HashMap<>();
        CPUnqualifedDemandForecastPrice forecastPrice = new CPUnqualifedDemandForecastPrice();
        Integer accomTypeId = 1;
        java.time.LocalDate arrivalDate = java.time.LocalDate.parse("2024-10-10");

        forecastPrice.setRate(new BigDecimal(80));
        forecastPrice.setArrivalDate(convertJavaToJodaLocalDate(arrivalDate));
        tax.setRoomTaxRate(BigDecimal.valueOf(7));

        AccomTypeSupplementValuePK valuePK = new AccomTypeSupplementValuePK(1, convertJavaToJodaLocalDate(arrivalDate), accomTypeId, baseOccupancyType);
        AccomTypeSupplementValue value = new AccomTypeSupplementValue();
        value.setValue(BigDecimal.TEN);
        value.setOffsetMethod(OffsetMethod.PERCENTAGE);
        supplementValueMap.put(valuePK, value);
        assertEquals(94.16, productManagementService.applyOffsetTaxAndSupplement(1, baseOccupancyType, tax, supplementValueMap, null, forecastPrice, accomTypeId).doubleValue(), 0);

    }

    @Test
    public void findRoomTypesByProduct() {
        Product product = new Product();
        product.setId(1);

        AccomClass accomClass = new AccomClass();
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);

        when(agileRatesConfigurationService.findRoomTypesByProduct(product)).thenReturn(Collections.singletonList(accomType));
        assertEquals(Collections.singleton(accomClass), productManagementService.findRoomClassesByProduct(product));
    }

    @Test
    public void getDecisionsFromLastBDEWithDifferentialPace() {
        Product product = new Product();
        product.setId(1);
        LocalDate localDate = new LocalDate();
        List<AccomType> accomTypes = of(buildAccomType());
        when(accommodationService.getAllAccomTypes()).thenReturn(accomTypes);
        when(crudService.findByNamedQuerySingleResult(Decision.GET_LAST_BDE, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(1);
        Set<Product> products = new HashSet<>(of(product));

        productManagementService.getDecisionsFromLastBDE(products, localDate, localDate, accomTypes);

        verify(crudService).findByNamedQuerySingleResult(Decision.GET_LAST_BDE, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(crudService).findByNativeQuery(eq(CPPaceDecisionBAROutputDifferential.GET_BY_SELECTED_DATES), anyMap(), any(RowMapper.class));
    }

    @Test
    public void getDecisionsFromLastCDPDifferentialPace() {
        Product product = new Product();
        product.setId(1);
        LocalDate localDate = new LocalDate();
        AccomType accomType = buildAccomType();
        when(crudService.findByNamedQuerySingleResult(Decision.GET_LAST_CDP, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(1);
        Set<Product> products = new HashSet<>(of(product));
        productManagementService.getDecisionsFromLastCDP(products, localDate, localDate, of(accomType));
        verify(crudService, times(1)).findByNamedQuerySingleResult(Decision.GET_LAST_CDP, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(crudService).findByNativeQuery(eq(CPPaceDecisionBAROutputDifferential.GET_BY_SELECTED_DATES), anyMap(), any(RowMapper.class));
    }

    @Test
    public void getDecisionsFromLastCDPDifferentialPaceWithNullDecision() {
        Product product = new Product();
        product.setId(1);
        LocalDate localDate = new LocalDate();
        AccomType accomType = buildAccomType();
        when(crudService.findByNamedQuerySingleResult(Decision.GET_LAST_CDP, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(null);
        Set<Product> products = new HashSet<>(of(product));
        productManagementService.getDecisionsFromLastCDP(products, localDate, localDate, of(accomType));
        verify(crudService, times(1)).findByNamedQuerySingleResult(Decision.GET_LAST_CDP, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        verify(crudService).findByNativeQuery(eq(CPPaceDecisionBAROutputDifferential.GET_BY_SELECTED_DATES), anyMap(), any(RowMapper.class));
    }

    @Test
    public void getBaseAccomTypes() {
        PricingAccomClass pricingAccomClass = PricingConfigurationObjectMother.buildPricingAccomClass();
        when(crudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(singletonList(pricingAccomClass));

        List<AccomType> baseAccomTypes = productManagementService.getBaseAccomTypes();

        assertEquals(pricingAccomClass.getAccomType(), baseAccomTypes.get(0));
        verify(crudService).findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @Test
    public void getOptimizedLevel() {
        //given
        when(agileRatesConfigurationService.getOptimizationLevel()).thenReturn(OptimizationLevel.PER_ROOM_CLASS);

        //when
        OptimizationLevel optimizationLevel = productManagementService.getOptimizationLevel();

        //then
        assertEquals(OptimizationLevel.PER_ROOM_CLASS, optimizationLevel);
        verify(agileRatesConfigurationService).getOptimizationLevel();
    }

    private AccomClass createAccomClass(int id, String name) {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(id);
        accomClass.setName(name);
        return accomClass;
    }

    private Date setDataForCompetitorRates(List<AccomClass> roomClasses) {
        return setDataForCompetitorRates(roomClasses, false);
    }

    private Date setDataForCompetitorRates(List<AccomClass> roomClasses, boolean cmpcEnabled) {
        Date occupancyDate = Date.from(java.time.LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
        DateParameter lastUpdatedDate = new DateParameter(1, 1, 2017);
        List<CompetitorRateInfo> competitorRateInfoList = new ArrayList<>();
        CompetitorRateInfo rateInfo1 = new CompetitorRateInfo();
        setCompetitorData(lastUpdatedDate, competitorRateInfoList, rateInfo1, "Competitor1", BigDecimal.TEN, roomClasses.get(0).getName());
        CompetitorRateInfo rateInfo2 = new CompetitorRateInfo();
        setCompetitorData(lastUpdatedDate, competitorRateInfoList, rateInfo2, "Competitor2", BigDecimal.TEN, roomClasses.get(0).getName());
        setCompetitorData(lastUpdatedDate, competitorRateInfoList, new CompetitorRateInfo(), "Competitor3", null, roomClasses.get(0).getName());
        List<Integer> accomClasses = roomClasses.stream().map(AccomClass::getId).collect(Collectors.toList());
        when(competitorRateInfoService.getAccomClassesHavingWebrateAccomTypes()).thenReturn(roomClasses);
        List<Integer> productIds = new ArrayList<>();
        when(competitorRateInfoService.getCompetitorRatesForAccomClassList(occupancyDate, accomClasses, cmpcEnabled, productIds)).thenReturn(competitorRateInfoList);
        when(dateService.getUserPreferredDateFormat()).thenReturn("dd-mm-yyyy");
        return occupancyDate;
    }

    private void setCompetitorData(DateParameter lastUpdatedDate, List<CompetitorRateInfo> competitorRateInfoList, CompetitorRateInfo rateInfo2, String competitor2, BigDecimal rateValue, String accomClassName) {
        rateInfo2.setCompetitorName(competitor2);
        rateInfo2.setWebrateGenerationDate(lastUpdatedDate);
        rateInfo2.setRate(rateValue);
        rateInfo2.setAccomClassName(accomClassName);
        competitorRateInfoList.add(rateInfo2);
    }

    private void expectFloorCeiling(PricingRule pricingRule, CPDecisionBAROutput output, BigDecimal floor, BigDecimal ceiling, BigDecimal supplement) {
        CPConfigMergedCeilingAndFloor ceilingAndFloor = new CPConfigMergedCeilingAndFloor();
        ceilingAndFloor.setFloorRate(floor);
        ceilingAndFloor.setCeilingRate(ceiling);

        CPConfigMergedCeilingAndFloorPK cpConfigMergedCeilingAndFloorPK = new CPConfigMergedCeilingAndFloorPK();
        cpConfigMergedCeilingAndFloorPK.setArrivalDate(output.getArrivalDate());
        cpConfigMergedCeilingAndFloorPK.setAccomTypeId(output.getAccomType().getId());
        ceilingAndFloor.setId(cpConfigMergedCeilingAndFloorPK);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloors = new HashMap<>();
        ceilingAndFloors.put(ceilingAndFloor.getId(), ceilingAndFloor);

        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(output.getArrivalDate(), output.getArrivalDate(), true)).thenReturn(ceilingAndFloors);
    }

    private AccomType buildAccomType() {
        AccomType accomType = new AccomType();
        accomType.setId(1);
        accomType.setAccomClass(buildAccomClass());
        return accomType;
    }

    private AccomClass buildAccomClass() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        return accomClass;
    }

    private CPDecisionBAROutput prepareCPDecisionBAROutput() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setId(Long.valueOf(9130));
        cpDecisionBAROutput.setDecisionId(193);
        Product product = new Product();
        product.setId(1);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setDecisionReasonTypeId(1);
        cpDecisionBAROutput.setAccomType(getAccomType());
        cpDecisionBAROutput.setArrivalDate(LocalDate.parse("2015-09-05"));
        cpDecisionBAROutput.setLengthOfStay(-1);
        cpDecisionBAROutput.setOptimalBAR(new BigDecimal("367.00"));
        cpDecisionBAROutput.setPrettyBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setRoomsOnlyBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setFinalBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        cpDecisionBAROutput.setSpecificOverride(new BigDecimal("255"));

        return cpDecisionBAROutput;
    }

    private AccomType getAccomType() {
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCode("CQ");
        AccomClass accomClass = new AccomClass();
        accomClass.setId(3);
        accomClass.setName("A");
        accomType.setAccomClass(accomClass);
        return accomType;
    }

    private CPDecisionBAROutputOverride prepareCPDecisionBAROutputOverride() {
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = new CPDecisionBAROutputOverride();
        cpDecisionBAROutputOverride.setDecisionId(193);
        Product product = new Product();
        product.setId(1);
        cpDecisionBAROutputOverride.setProduct(product);
        AccomType accomType = getAccomType();
        cpDecisionBAROutputOverride.setAccomType(accomType);
        cpDecisionBAROutputOverride.setArrivalDate(LocalDate.parse("2015-09-05"));
        cpDecisionBAROutputOverride.setLengthOfStay(-1);
        cpDecisionBAROutputOverride.setOldOverrideType(DecisionOverrideType.NONE);
        cpDecisionBAROutputOverride.setNewOverrideType(DecisionOverrideType.USER);
        cpDecisionBAROutputOverride.setOldUserOverride(new BigDecimal("255.00"));
        cpDecisionBAROutputOverride.setNewUserOverride(new BigDecimal("255"));
        return cpDecisionBAROutputOverride;
    }

    private CPBarOverride prepareCPBarOverride(boolean applyOverridesAcrossRoomTypes) {
        BigDecimal floorRate = new BigDecimal("159");
        BigDecimal ceilingRate = new BigDecimal("900");
        CPBarOverride cpBarOverride = new CPBarOverride(prepareCPDecisionBAROutput(), floorRate, ceilingRate, false);
        cpBarOverride.setApplyOverrideAcrossRoomTypes(applyOverridesAcrossRoomTypes);
        return cpBarOverride;
    }

    private CPDecisionBAROutput prepareCPDecisionBAROutputForCeilingAndFloor(boolean isCeiling) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setId(Long.valueOf(9130));
        cpDecisionBAROutput.setDecisionId(193);
        Product product = new Product();
        product.setId(1);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setDecisionReasonTypeId(1);
        cpDecisionBAROutput.setAccomType(getAccomType());
        cpDecisionBAROutput.setArrivalDate(LocalDate.parse("2015-09-05"));
        cpDecisionBAROutput.setLengthOfStay(-1);
        cpDecisionBAROutput.setOptimalBAR(new BigDecimal("367.00"));
        cpDecisionBAROutput.setPrettyBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setRoomsOnlyBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setFinalBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        if (isCeiling)
            cpDecisionBAROutput.setCeilingOverride(new BigDecimal("250"));
        else {
            cpDecisionBAROutput.setCeilingOverride(new BigDecimal("250"));
            cpDecisionBAROutput.setFloorOverride(new BigDecimal("210"));
        }

        return cpDecisionBAROutput;
    }

    private CPBarOverride prepareCPBarOverrideForCeilingAndFloorOverride(boolean applyOverridesAcrossRoomTypes, boolean isCeiling) {
        BigDecimal floorRate = new BigDecimal("159");
        BigDecimal ceilingRate = new BigDecimal("900");
        CPBarOverride cpBarOverride = new CPBarOverride(prepareCPDecisionBAROutputForCeilingAndFloor(isCeiling), floorRate, ceilingRate, false);
        cpBarOverride.setApplyOverrideAcrossRoomTypes(applyOverridesAcrossRoomTypes);
        return cpBarOverride;
    }

    private List<CPUnqualifedDemandForecastPrice> buildCPUnqualifiedDemandForecastPrice(LocalDate occupancyDate, LocalDate date) {
        List<CPUnqualifedDemandForecastPrice> cpUnqualifedDemandForecastPriceList = new ArrayList<>();
        LocalDate tempDate = new LocalDate(occupancyDate.toDate());
        while (tempDate.compareTo(date) < 1) {
            for (int i = 0; i < 10; i++) {
                cpUnqualifedDemandForecastPriceList.add(createDemandForeCastPrice(tempDate, i));
            }
            tempDate = tempDate.plusDays(1);

        }
        return cpUnqualifedDemandForecastPriceList;

    }

    private CPUnqualifedDemandForecastPrice createDemandForeCastPrice(LocalDate arrivalDate, int rate) {
        CPUnqualifedDemandForecastPrice demandForecastPrice = new CPUnqualifedDemandForecastPrice();
        Product product = new Product();
        product.setId(1);
        demandForecastPrice.setProduct(product);
        demandForecastPrice.setArrivalDate(arrivalDate);
        demandForecastPrice.setRate(BigDecimal.valueOf(25 + rate * 10));
        return demandForecastPrice;
    }

    private List<CPRoomClassDTO> buildCPRoomClassDTOs(Integer numberOfDays, LocalDate startDate, Integer accomClassId) {
        List<CPRoomClassDTO> dtos = new ArrayList<>();

        for (int i = 0; i <= numberOfDays; i++) {
            CPRoomClassDTO dto = new CPRoomClassDTO();
            dto.setDate(startDate.plusDays(i));
            dto.setOccupancyForecast(BigDecimal.valueOf(i + 10));
            dto.setRoomsOnBooks(BigDecimal.TEN);
            dto.setOverbooking(BigDecimal.valueOf(i));
            dto.setOutOfOrder(BigDecimal.valueOf(i));
            dto.setCapacity(BigDecimal.valueOf(100));
            dto.setAccomClassId(accomClassId);
            dtos.add(dto);
        }

        return dtos;
    }


    private LastRoomValue createLastRoomValue(int accomClassId, Date occupancyDate, BigDecimal value) {
        LastRoomValue entity = new LastRoomValue();
        entity.setAccomClassID(accomClassId);
        entity.setOccupancyDate(occupancyDate);
        entity.setValue(value);
        entity.setDeltaValue(value);
        entity.setCeilingValue(value);
        entity.setCreateDate(occupancyDate);
        return entity;
    }

    @Test
    public void getMasterPricingAccomClass() {
        AccomClass accomClass = buildAccomClass();
        accomClass.setMasterClass(1);
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        when(businessAnalysisDashboardService.getPricingAccomClass(accomClass)).thenReturn(pricingAccomClass);
        when(businessAnalysisDashboardService.getMasterAccomClass()).thenReturn(accomClass);

        PricingAccomClass masterPricingAccomClass = productManagementService.getMasterPricingAccomClass();

        assertEquals(pricingAccomClass, masterPricingAccomClass);
        assertEquals(accomClass, masterPricingAccomClass.getAccomClass());
        verify(businessAnalysisDashboardService).getMasterAccomClass();
        verify(businessAnalysisDashboardService).getPricingAccomClass(accomClass);
    }

    @Test
    public void getProductDiscountsFor() {
        Set<Product> products = Collections.singleton(new Product());

        productManagementService.getProductDiscountsFor(products);

        verify(agileRatesConfigurationService).findProductRateOffsetFor(products);
    }

    @Test
    public void getProductDiscount() {
        Product product = new Product();

        productManagementService.getProductDiscount(product);

        verify(agileRatesConfigurationService).findProductRateOffsetsByProduct(product);
    }

    @Test
    public void saveOverrideForCPDecisions_massUpdateOverridesAndSave() throws Exception {
        //setup
        Date today = new Date();
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewUserOverride(overridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        LocalDate arrivalDate = new LocalDate();
        output.setArrivalDate(arrivalDate);
        AccomType baseRoomType = new AccomType();
        AccomClass baseRoomClass = new AccomClass();
        baseRoomType.setAccomClass(baseRoomClass);
        output.setAccomType(baseRoomType);

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecisions(today, today, today, today, 1)).thenReturn(of(barOverrideDecision));

        CPDecisionBAROutput otherDecision1 = new CPDecisionBAROutput();
        AccomType otherDecision1RoomType = new AccomType();
        AccomClass otherDecision1RoomClass = new AccomClass();
        otherDecision1RoomType.setAccomClass(otherDecision1RoomClass);
        otherDecision1.setAccomType(otherDecision1RoomType);
        CPDecisionBAROutput otherDecision2 = new CPDecisionBAROutput();
        AccomType otherDecision2RoomType = new AccomType();
        otherDecision2RoomType.setId(123);
        otherDecision2RoomType.setAccomClass(baseRoomClass);
        otherDecision2.setAccomType(otherDecision2RoomType);
        otherDecision2.setOptimalBAR(BigDecimal.valueOf(100));
        otherDecision2.setPropertyId(3);
        otherDecision2.setDecisionId(4);
        otherDecision2.setArrivalDate(arrivalDate);
        otherDecision2.setProduct(new Product());
        otherDecision2.setOverrideType(DecisionOverrideType.CEIL);
        otherDecision2.setSpecificOverride(BigDecimal.valueOf(35));

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(pricingConfigurationService.getCPDecisionContext(arrivalDate, arrivalDate)).thenReturn(cpDecisionContext);

        List<CPDecisionBAROutput> decisionsForDay = asList(output, otherDecision1, otherDecision2);
        when(crudService.<CPDecisionBAROutput>findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, arrivalDate, arrivalDate)))
                .thenReturn(decisionsForDay);

        CPConfigMergedOffset offset1 = new CPConfigMergedOffset();
        offset1.setId(new CPConfigMergedOffsetPK());
        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        CPConfigMergedOffsetPK id = new CPConfigMergedOffsetPK();
        id.setAccomTypeId(123);
        id.setOccupancyType(OccupancyType.SINGLE);
        offset2.setId(id);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(BigDecimal.valueOf(20));
        List<CPConfigMergedOffset> offsets = asList(offset1, offset2);
        when(cpDecisionContext.applyOffset(otherDecision2, override.getNewUserOverride())).thenReturn(BigDecimalUtil.add(override.getNewUserOverride(), offset2.getOffsetValue()));
        when(pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(toJavaLocalDate(output.getArrivalDate()), dateService.getCaughtUpJavaLocalDate())).thenReturn(true);

        //when
        HashMap<CPDecisionBAROutput, CPDecisionBAROutputOverride> overrideMap = new HashMap<>();
        overrideMap.put(output, override);
        productManagementService.saveOverrideForCPDecisions(cpDecisionContext, overrideMap, today, today, today, today);

        //then
        assertEquals(overridePrice, output.getPrettyBAR());
        assertEquals(overridePrice, output.getFinalBAR());
        assertEquals(overridePrice, output.getSpecificOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        ArgumentCaptor<IdAware> captor = ArgumentCaptor.forClass(IdAware.class);
        verify(crudService, times(2)).save(anyCollection());
        verify(syncEventAggregatorService, times(0)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
    }

    @Test
    public void getPricingCompetitorRates() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate().plusDays(1);
        String accomClassIdStr = "2,3";
        String accomTypeIdStr = "1,2,3";
        List<Integer> competitorIds = Arrays.asList(1, 2, -1, -1, -1, -1, -1);

        ArgumentCaptor<String> firstArgument = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map> secondArgument = ArgumentCaptor.forClass(Map.class);

        productManagementService.getPricingCompetitorRates(startDate, endDate, accomClassIdStr, accomTypeIdStr, competitorIds);

        verify(crudService).findByNativeQuery(firstArgument.capture(), secondArgument.capture(), any(RowMapper.class));

        String sql = "select * from dbo.ufn_get_pricing_competitor_rates (:property_id, :start_date, :end_date, :comp_id_1,:comp_id_2,:comp_id_3,:comp_id_4,:comp_id_5,:comp_id_6,:comp_id_7,:RoomClasses, :RoomTypes) order by Arrival_DT, Accom_Class_Name, Accom_Type_Name";
        assertEquals(sql, firstArgument.getAllValues().get(0));

        Map parameters = secondArgument.getAllValues().get(0);
        assertEquals(startDate.toDate(), parameters.get("start_date"));
        assertEquals(endDate.toDate(), parameters.get("end_date"));
        assertEquals(accomClassIdStr, parameters.get("RoomClasses"));
        assertEquals(accomTypeIdStr, parameters.get("RoomTypes"));
        assertEquals(1, parameters.get("comp_id_1"));
        assertEquals(2, parameters.get("comp_id_2"));
        assertEquals(-1, parameters.get("comp_id_3"));
        assertEquals(-1, parameters.get("comp_id_4"));
        assertEquals(-1, parameters.get("comp_id_5"));
        assertEquals(-1, parameters.get("comp_id_6"));
        assertEquals(-1, parameters.get("comp_id_7"));
    }

    @Test
    public void findAllDTARanges() {
        productManagementService.findAllDTARanges();
        verify(agileRatesConfigurationService).findAllDTARanges();
    }

    @Test
    public void findAllProductsForWebRate() {
        String product_1 = "p1";
        Product product1 = buildProduct(1, product_1);
        String product_2 = "p2";
        Product product2 = buildProduct(10, product_2);
        List<Product> products = asList(product1, product2);
        Map<Integer, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        List<Object> objectsArr = new ArrayList<>();
        int webRateId = 1001;
        objectsArr.add(buildObjectOfWebRateAndProduct(webRateId, BigInteger.ONE));
        objectsArr.add(buildObjectOfWebRateAndProduct(webRateId, BigInteger.TEN));
        when(crudService.findByNativeQuery(VW_QUERY_TO_FETCH_PRODUCT_ID, QueryParameter.with(SELECTED_DATE, Mockito.any())
                .and(PRODUCT_IDS, Mockito.any()).parameters())).thenReturn(objectsArr);
        Map<Integer, Set<String>> webRateToProductMap = productManagementService.getWebRateToProductMap(productMap, new Date());
        assertEquals(1, webRateToProductMap.size());
        assertEquals(1, webRateToProductMap.keySet().size());
        assertEquals(2, webRateToProductMap.get(webRateId).size());
        assertTrue(webRateToProductMap.keySet().contains(webRateId));
        assertTrue(webRateToProductMap.get(webRateId).contains(product_1));
        assertTrue(webRateToProductMap.get(webRateId).contains(product_2));
    }

    private Object[] buildObjectOfWebRateAndProduct(Integer webRateId, BigInteger productId) {
        Object[] el1 = new Object[2];
        el1[0] = webRateId;
        el1[1] = productId;
        return el1;
    }

    private Product buildProduct(int id, String name) {
        Product product = new Product();
        product.setId(id);
        product.setName(name);
        return product;
    }

    @Test
    public void finalDTARangesForProduct() {
        Product product = new Product();
        AgileRatesDTARange agileRatesDTARange1 = new AgileRatesDTARange();
        AgileRatesDTARange agileRatesDTARange2 = new AgileRatesDTARange();
        when(agileRatesConfigurationService.findAllDTARanges()).thenReturn(Arrays.asList(agileRatesDTARange1, agileRatesDTARange2));

        productManagementService.finalDTARangesForProduct(product);
        verify(agileRatesConfigurationService).findAllDTARanges();
        verify(agileRatesConfigurationService).getAdjustedDTARanges(product, Arrays.asList(agileRatesDTARange1, agileRatesDTARange2));
    }

    @Test
    public void getRoomTypesForSelectedProduct() {
        Product product = new Product();
        product.setId(1);
        AccomType accomType = new AccomType();
        accomType.setId(3);

        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setProduct(product);
        productAccomType.setAccomType(accomType);

        List<ProductAccomType> productAccomTypeList = of(productAccomType);
        when(agileRatesConfigurationService.findProductRoomTypesByProduct(product)).thenReturn(productAccomTypeList);
        assertEquals(of(accomType), productManagementService.getRoomTypesForSelectedProduct(product));
    }

    @Test
    public void saveSmallGroupProductRateOffsetOverride() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setCode(Product.GROUP_PRODUCT_CODE);

        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        when(crudService.save(Collections.singletonList(override))).thenReturn(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        productManagementService.saveSmallGroupProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(of(toJavaLocalDate(override.getOccupancyDate())), java.time.LocalDate.parse("2023-01-01"));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);

        productManagementService.saveSmallGroupProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
    }
    @Test
    public void saveSmallGroupProductRateOffsetOverrideWithNewSyncFlag() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setCode(Product.GROUP_PRODUCT_CODE);

        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        when(crudService.save(Collections.singletonList(override))).thenReturn(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_OVERRIDE_IDP_FLOW_BASED_SYNC_ENABLED)).thenReturn(true);
        productManagementService.saveSmallGroupProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.GROUP_PRODUCT_OVERRIDE_CHANGED);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(of(toJavaLocalDate(override.getOccupancyDate())), java.time.LocalDate.parse("2023-01-01"));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);

        productManagementService.saveSmallGroupProductRateOffsetOverride(Collections.singletonList(override));

        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.GROUP_PRODUCT_OVERRIDE_CHANGED);
    }

    @Test
    public void deleteSmallGroupProductRateOffsetOverride() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setCode(Product.GROUP_PRODUCT_CODE);

        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        doNothing().when(crudService).delete(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        productManagementService.deleteSmallGroupProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(of(toJavaLocalDate(override.getOccupancyDate())), java.time.LocalDate.parse("2023-01-01"));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        override.setStatusId(1);

        productManagementService.deleteSmallGroupProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
    }
    @Test
    public void deleteSmallGroupProductRateOffsetOverrideWithNewSyncFlag() {
        ProductRateOffsetOverride override = AgileRatesObjectMother.buildProductRateOverride();
        override.getProduct().setCode(Product.GROUP_PRODUCT_CODE);

        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        doNothing().when(crudService).delete(Collections.singletonList(override));
        doReturn(true).when(syncEventAggregatorService).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_OVERRIDE_IDP_FLOW_BASED_SYNC_ENABLED)).thenReturn(true);
        productManagementService.deleteSmallGroupProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(1)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.GROUP_PRODUCT_OVERRIDE_CHANGED);
        verify(pricingConfigurationLTBDEService).enableOverrideForExtendedWindowIfApplicable(of(toJavaLocalDate(override.getOccupancyDate())), java.time.LocalDate.parse("2023-01-01"));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);
        override.setStatusId(1);

        productManagementService.deleteSmallGroupProductRateOffsetOverride(Collections.singletonList(override));

        assertEquals(2, override.getStatusId().intValue());
        verify(crudService, times(2)).save(Collections.singletonList(override));
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.GROUP_PRODUCT_OVERRIDE_CHANGED);
    }

    @Test
    public void shouldSmallGroupSyncBeFire() {
        ProductRateOffsetOverride optimizedOverride = AgileRatesObjectMother.buildProductRateOverride();
        optimizedOverride.getProduct().setCode(Product.GROUP_PRODUCT_CODE);
        optimizedOverride.getProduct().setOptimized(true);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(true);

        assertFalse(productManagementService.shouldSmallGroupSyncBeFire(null));
        assertFalse(productManagementService.shouldSmallGroupSyncBeFire(new ArrayList<>()));
        assertFalse(productManagementService.shouldSmallGroupSyncBeFire(of(optimizedOverride)));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PRICING_DISABLE_AGILE_RATES_SYNC_FOR_OPTIMIZED_PRODUCT_OVERRIDES)).thenReturn(false);

        assertFalse(productManagementService.shouldSmallGroupSyncBeFire(null));
        assertFalse(productManagementService.shouldSmallGroupSyncBeFire(new ArrayList<>()));
        assertTrue(productManagementService.shouldSmallGroupSyncBeFire(of(optimizedOverride)));
    }

    @Test
    void listSmallGroupProducOnly() {
        createAllProducts();
        List<Product> products = productManagementService.findActiveSmallGroupProducts(5);
        assertNotNull(products);
        assertEquals(2, products.size());
    }

    @Test
    void shouldReturnTrueWhenChangeInStandardOptimizationWindow() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        when(pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(toJavaLocalDate(cpDecisionBAROutput.getArrivalDate()), dateService.getCaughtUpJavaLocalDate())).thenReturn(false);

        assertTrue(productManagementService.isDateInOptimizationWindow(cpDecisionBAROutput, dateService.getCaughtUpJavaLocalDate()));

    }

    @Test
    void shouldReturnFalseWhenChangeIsBeyondStandardOptimizationWindow() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.parse("2023-01-01"));
        when(configParamsService.getIntegerParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.getParameterName())).thenReturn(365);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_UPLOAD_ON_OVERRIDE_FOR_EXTENDED_WINDOW)).thenReturn(true);
        ;
        when(pricingConfigurationLTBDEService.isDateInExtendedWindowForOverrides(toJavaLocalDate(cpDecisionBAROutput.getArrivalDate()), dateService.getCaughtUpJavaLocalDate())).thenReturn(true);

        assertFalse(productManagementService.isDateInOptimizationWindow(cpDecisionBAROutput, dateService.getCaughtUpJavaLocalDate()));

    }

    private void createAllProducts() {
        Product idp1 = ProductBuilder.createIndependentProductProduct("Idp");
        Product sg1 = ProductBuilder.createSmallGroupProduct("SG1");
        Product sg2 = ProductBuilder.createSmallGroupProduct("SG2");
        Product AgRate1 = ProductBuilder.createAgileRateProduct("AgRate1");
        Product AgRate2 = ProductBuilder.createAgileRateProduct("AgRate2");
        tenantCrudService.save(of(idp1, sg1, sg2, AgRate1, AgRate2));
    }

    @Test
    void shouldReturnProductDTO() {
        when(crudService.findByNamedQuery(Product.GET_ALL)).thenReturn(List.of(getProduct()));
        List<ProductDTO> productDetails = productManagementService.getProductDetails();
        assertEquals(1, productDetails.size(), "product size should be 1");

    }


    @Test
    void shouldUpdateProductConfigurationUsingProductId() {
        inject(productManagementService,"crudService", tenantCrudService);
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID_ACTIVE_PRODUCT, QueryParameter.with("productId", 1).parameters());
        assertFalse(product.isUpload());
        productManagementService.updateProductConfiguration(1,null, "upload", "true");
        productManagementService.updateProductConfiguration(1, null, "name", "LV0");
        productManagementService.updateProductConfiguration(1, null, "floor", "30.00");
        productManagementService.updateProductConfiguration(1, null, "minRooms","100");


        product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID_ACTIVE_PRODUCT, QueryParameter.with("productId", 1).parameters());
        assertTrue(product.isUpload());
        assertEquals("LV0",product.getName());
        assertEquals(new BigDecimal("30.00"),product.getFloor());
        assertEquals(Integer.parseInt("100"),product.getMinRooms());
    }


    @Test
    void shouldUpdateProductConfigurationUsingProductName() {
        inject(productManagementService,"crudService", tenantCrudService);
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_NAME, QueryParameter.with("name", "BAR").parameters());
        assertFalse(product.isUpload());
        productManagementService.updateProductConfiguration(null,"BAR", "upload", "true");
        productManagementService.updateProductConfiguration(null, "BAR", "name", "LV0");
        productManagementService.updateProductConfiguration(null, "LV0", "floor", "30.00");
        productManagementService.updateProductConfiguration(null, "LV0", "minRooms","100");

        product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_NAME, QueryParameter.with("name", "LV0").parameters());
        assertTrue(product.isUpload());
        assertEquals("LV0",product.getName());
        assertEquals(new BigDecimal("30.00"),product.getFloor());
        assertEquals(Integer.parseInt("100"),product.getMinRooms());
    }

    @Test
    void shouldNotUpdateProductConfigurationWhenProductIdIsInvalid() {
        inject(productManagementService, "crudService", tenantCrudService);
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID_ACTIVE_PRODUCT, QueryParameter.with("productId", 1).parameters());
        assertFalse(product.isUpload());
        Response response = productManagementService.updateProductConfiguration(null, null, "upload", "true");
        assertEquals("ProductId or Product Name is invalid.", response.getEntity().toString());
    }


    private Product getProduct() {
        Product product = new Product();
        ProductCode productCode = new ProductCode();
        productCode.setId(1);
        productCode.setProductCodeName("ABC_Code");
        product.setId(1);
        product.setStatus(TenantStatusEnum.ACTIVE);
        product.setName("ABC");
        product.setSystemDefault(true);
        product.setProductCode(productCode);
        return product;
    }

    @Test
    void getProductDetailsTest(){

        ProductDetailsDTO dto1 = new ProductDetailsDTO();
        dto1.setId(1);
        dto1.setName("Product 1");

        ProductDetailsDTO dto2 = new ProductDetailsDTO();
        dto2.setId(2);
        dto2.setName("Product 2");

        List<Product> mockProducts = getMockProductsList();
        Map<Integer, List<String>> mockRateCodes = Map.of(1, List.of("RateCode11", "RateCode12"), 2, List.of("RateCode21", "RateCode22"));
        Map<Integer, List<Integer>> mockRoomTypes = Map.of(1, List.of(11, 12), 2, List.of(21, 22));
        Map<Integer, List<WebrateOverrideChannel>> webrateChannelMap =  new HashMap<>();

        when(spyService.findAllBaseProducts()).thenReturn(mockProducts);
        when(spyService.getProductToRateCodesMap(anyList())).thenReturn(mockRateCodes);
        when(spyService.getProductsToAccomTypeMap(anyList())).thenReturn(mockRoomTypes);
        doReturn(dto1).when(spyService).buildProductDetailsDTO(mockProducts.get(0), mockRateCodes, mockRoomTypes, webrateChannelMap);
        doReturn(dto2).when(spyService).buildProductDetailsDTO(mockProducts.get(1), mockRateCodes, mockRoomTypes, webrateChannelMap);

        List<ProductDetailsDTO> result = spyService.getBaseProductDetails();

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals(2, result.get(1).getId());
        assertEquals("Product 1", result.get(0).getName());
        assertEquals("Product 2", result.get(1).getName());

        verify(spyService, times(2)).buildProductDetailsDTO(any(Product.class), anyMap(), anyMap(), anyMap());
    }

    @Test
    void getProductDetailsWithNoBaseProductsTest(){
        doReturn(Collections.emptyList()).when(spyService).findAllBaseProducts();
        List<ProductDetailsDTO> result = spyService.getBaseProductDetails();
        assertEquals(0, result.size());
        verify(spyService, times(0)).getProductToRateCodesMap(anyList());
        verify(spyService, times(0)).getProductsToAccomTypeMap(anyList());
        verify(spyService, times(0)).buildProductDetailsDTO(any(Product.class), anyMap(), anyMap(), anyMap());
    }

    @Test
    void buildProductDetailsDTOTest(){
        List<Product> mockProducts = getMockProductsList();
        Map<Integer, List<String>> mockRateCodes = Map.of(1, List.of("RateCode11", "RateCode12"));
        Map<Integer, List<Integer>> mockRoomTypes = Map.of(1, List.of(11, 12) );
        Map<Integer, List<WebrateOverrideChannel>> webrateChannelMap =  new HashMap<>();
        doNothing().when(spyService).populateProductDetails(any(), any(), any(), any(), any());
        ProductDetailsDTO dto = spyService.buildProductDetailsDTO(mockProducts.get(0), mockRateCodes, mockRoomTypes,webrateChannelMap);
        assertNotNull(dto);
        verify(spyService, times(1)).populateProductDetails(eq(mockProducts.get(0)), any(ProductDetailsDTO.class), eq(mockRateCodes), eq(mockRoomTypes), anyMap());
    }

    @Test
    void populateProductDetailsTest(){
        Product product = new Product();
        product.setId(1);
        product.setName("Test Product");
        product.setUpload(true);
        product.setDescription("Product Description");
        product.setCode("P123");
        product.setType("Product Type");
        product.setDisplayOrder(10);
        product.setStatus(TenantStatusEnum.ACTIVE);
        product.setIsOverridable(OverridableProductEnum.ALLOW_OVERRIDES);
        product.setMinLOS(1);
        product.setMaxLOS(10);
        product.setRateShoppingLOSMin(-1);
        product.setRateShoppingLOSMax(-1);
        product.setSystemDefault(true);

        boolean isRDLEnabled = false;

        Map<Integer, List<String>> mockRateCodes = Map.of(1, List.of("RateCode11", "RateCode12"));
        Map<Integer, List<Integer>> mockRoomTypes = Map.of(1, List.of(11, 12) );
        Map<Integer, List<WebrateOverrideChannel>> webrateChannelMap =  new HashMap<>();


        doReturn(roundingRulesDTO).when(spyService).getRoundingRulesDTO(product);
        doReturn(List.of(hierarchyDTO)).when(spyService).getProductHierarchyList(product);
        doReturn(Collections.singletonList(floorCeilDetailsDTO)).when(spyService).getFloorCeiling(product);
        doReturn(Collections.singletonList(offsetDetailsDTO)).when(spyService).getProductOffsets(product);
        doReturn(Collections.singletonList(supplementDTO)).when(spyService).getProductSupplements(product);
        doReturn(Collections.singletonList(priceChangeRangeDTO)).when(spyService).getPriceChangeRange(product);
        doReturn(Collections.singletonList(linkProductsDTO)).when(spyService).getLinkedProducts(product, isRDLEnabled);
        doReturn(new RmsAuditInfo()).when(spyService).getAuditInfo(product);

        ProductDetailsDTO dto = new ProductDetailsDTO();
        spyService.populateProductDetails(product, dto, mockRateCodes, mockRoomTypes, webrateChannelMap);

        assertEquals(1, dto.getId());
        assertEquals("Test Product", dto.getName());
        assertEquals("Product Description", dto.getDescription());
        assertEquals("P123", dto.getCode());
        assertEquals("Product Type", dto.getType());
        assertEquals(10, dto.getDisplayOrder());
        assertEquals(TenantStatusEnum.ACTIVE, dto.getStatus());
        assertEquals(true, dto.getDecisionUploadEnabled());
        assertEquals(OverridableProductEnum.ALLOW_OVERRIDES, dto.getOverridableType());
        assertEquals(1, dto.getMinimumLOS());
        assertEquals(10, dto.getMaximumLOS());
        assertEquals(List.of("RateCode11", "RateCode12"), dto.getRateCodes());
        assertEquals(List.of(11, 12), dto.getRoomtypeIds());
        assertEquals(roundingRulesDTO, dto.getRoundingRules());
        assertEquals(List.of(hierarchyDTO), dto.getHierarchy());
        assertEquals(List.of(floorCeilDetailsDTO), dto.getFloorCeilDetails());
        assertEquals(List.of(offsetDetailsDTO), dto.getOffsetDetails());
        assertEquals(List.of(supplementDTO), dto.getSupplement());
        assertEquals(List.of(priceChangeRangeDTO), dto.getPriceChangeRange());
        assertEquals(List.of(linkProductsDTO), dto.getLinkedProducts());
        assertEquals(-1, dto.getRateShoppingMinimumLOS());
        assertEquals(-1, dto.getRateShoppingMaximumLOS());
        assertTrue(dto.getRateShoppingAllLOS());
        assertTrue(dto.getPrimaryPriced());
    }



    @Test
    public void testPopulateProductDetails_EmptyMaps() {
        Product product = new Product();
        product.setId(1);
        product.setName("Test Product");

        Map<Integer, List<String>> rateCodesMap = new HashMap<>();
        Map<Integer, List<Integer>> accomTypesMap = new HashMap<>();
        Map<Integer, List<WebrateOverrideChannel>> webrateChannelMap =  new HashMap<>();

        doReturn(roundingRulesDTO).when(spyService).getRoundingRulesDTO(product);
        doReturn(List.of(hierarchyDTO)).when(spyService).getProductHierarchyList(product);
        doReturn(Collections.singletonList(floorCeilDetailsDTO)).when(spyService).getFloorCeiling(product);
        doReturn(Collections.singletonList(offsetDetailsDTO)).when(spyService).getProductOffsets(product);
        doReturn(Collections.singletonList(supplementDTO)).when(spyService).getProductSupplements(product);
        doReturn(Collections.singletonList(priceChangeRangeDTO)).when(spyService).getPriceChangeRange(product);
        doReturn(Collections.singletonList(linkProductsDTO)).when(spyService).getLinkedProducts(product, false);
        doReturn(new RmsAuditInfo()).when(spyService).getAuditInfo(product);

        ProductDetailsDTO dto = new ProductDetailsDTO();
        spyService.populateProductDetails(product, dto, rateCodesMap, accomTypesMap, webrateChannelMap);

        assertEquals(dto.getRateShoppingAllLOS(), false);
        assertEquals(Collections.emptyList(), dto.getRateCodes());
        assertEquals(Collections.emptyList(), dto.getRoomtypeIds());
    }

    @Test
    void getLinkedProductsTest(){
        Product product = mockProduct(1, "ABC");
        boolean isRDLEnabled = false;
        List<Product> childLinkedProducts = List.of(mockProduct(2, "Child1"), mockProduct(3, "Child2"));
        doReturn(childLinkedProducts).when(spyService).getAllChildLinkedProducts(product.getId());
        doReturn(Map.of(2, List.of("RateCode"))).when(spyService).getProductToRateCodesMap(childLinkedProducts);
        doReturn(Map.of(2, List.of(7))).when(spyService).getProductsToAccomTypeMap(childLinkedProducts);
        doReturn(new LinkProductsDTO()).when(spyService).buildLinkProductDTO(any(), any(), any(), any(), eq(isRDLEnabled));
        List<LinkProductsDTO> result = spyService.getLinkedProducts(product, isRDLEnabled);
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(spyService, times(1)).getProductToRateCodesMap(anyList());
        verify(spyService, times(1)).getProductsToAccomTypeMap(anyList());
        verify(spyService, times(2)).buildLinkProductDTO(any(), anyMap(), anyMap(), any(), eq(isRDLEnabled));
    }

    @Test
    void getLinkedProducts_EmptyLinkedProducts_Test(){
        Product product = mockProduct(1, "ABC");
        boolean isRDLEnabled = false;
        doReturn(Collections.emptyList()).when(spyService).getAllChildLinkedProducts(product.getId());
        List<LinkProductsDTO> result = spyService.getLinkedProducts(product, isRDLEnabled);
        assertEquals(0, result.size());
        verify(spyService, times(0)).getProductToRateCodesMap(anyList());
        verify(spyService, times(0)).getProductsToAccomTypeMap(anyList());
        verify(spyService, times(0)).buildLinkProductDTO(any(), anyMap(), anyMap(), any(), eq(isRDLEnabled));
    }

    @Test
    void buildLinkProductDTO_Test(){

        Product product = new Product();
        product.setId(1);
        product.setName("Test Product");
        product.setDescription("Test Description");
        product.setCode("TP001");
        product.setType("TYPE_A");
        product.setDisplayOrder(5);
        product.setStatus(TenantStatusEnum.ACTIVE);
        product.setUpload(false);
        product.setIsOverridable(OverridableProductEnum.ALLOW_OVERRIDES);
        product.setMinLOS(1);
        product.setMaxLOS(10);
        product.setDependentProductId(99);
        product.setMinDTA(2);
        product.setMaxDTA(8);
        product.setMinimumPriceChange(BigDecimal.valueOf(15.0));
        product.setFloorType(FloorType.FIXED_RATE);
        product.setFloor(BigDecimal.valueOf(100.0));
        product.setFloorPercentage(BigDecimal.valueOf(10.0));
        product.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        product.setOffsetForExtraAdult(true);
        product.setOffsetForExtraChild(false);
        product.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        product.setOptimized(true);
        product.setDefaultInactive(false);

        Map<Integer, List<String>> mockRateCodes = Map.of(1, List.of("RateCode11", "RateCode12"));
        Map<Integer, List<Integer>> mockRoomTypes = Map.of(1, List.of(11, 12));

        doReturn(new RmsAuditInfo()).when(spyService).getAuditInfo(product);

        LinkProductsDTO dto = spyService.buildLinkProductDTO(product, mockRateCodes, mockRoomTypes, 1, false);

        assertEquals(1, dto.getId());
        assertEquals("Test Product", dto.getName());
        assertEquals("Test Description", dto.getDescription());
        assertEquals("TP001", dto.getCode());
        assertEquals("TYPE_A", dto.getType());
        assertEquals(5, dto.getDisplayOrder());
        assertEquals(TenantStatusEnum.ACTIVE, dto.getStatus());
        assertEquals(false, dto.getDecisionUploadEnabled());
        assertEquals(OverridableProductEnum.ALLOW_OVERRIDES, dto.getOverridableType());
        assertEquals(1, dto.getMinimumLOS());
        assertEquals(10, dto.getMaximumLOS());
        assertEquals(99, dto.getDependentProductId());
        assertEquals(2, dto.getMinimumDaysToArrival());
        assertEquals(8, dto.getMaximumDaysToArrival());
        assertEquals(BigDecimal.valueOf(15.0), dto.getMinimumPriceChange());
        assertEquals(FloorType.FIXED_RATE, dto.getFloorType());
        assertEquals(BigDecimal.valueOf(100.00).setScale(2), dto.getFloorRate());
        assertEquals(BigDecimal.valueOf(10.0), dto.getFloorPercentage());
        assertEquals(AgileRatesOffsetMethod.PERCENTAGE, dto.getOffsetMethod());
        assertTrue(dto.getOffsetForExtraAdult());
        assertFalse(dto.getOffsetForExtraChild());
        assertEquals(AgileRatesDecisionsSentBy.PRICE, dto.getDecisionsSentBy());
        assertTrue(dto.getOptimized());
        assertFalse(dto.getSeasonalProductOnly());
        assertEquals( List.of("RateCode11", "RateCode12"), dto.getRateCodes());
        assertEquals(List.of(11, 12) , dto.getRoomtypeIds());
    }

    @Test
    void testBuildLinkProductDTO_withEmptyRateCodesAndRoomTypesMap(){
        Product product = mockProduct(4, "ABC");
        product.setStatus(TenantStatusEnum.ACTIVE);
        product.setIsOverridable(OverridableProductEnum.ALLOW_OVERRIDES);
        product.setFloorType(FloorType.FIXED_RATE);
        product.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        product.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        doReturn(new RmsAuditInfo()).when(spyService).getAuditInfo(product);
        LinkProductsDTO dto = spyService.buildLinkProductDTO(product, Collections.emptyMap(), Collections.emptyMap(), 1, false);
        assertEquals(4, dto.getId());
        assertEquals("ABC", dto.getName());
        assertTrue(dto.getRateCodes().isEmpty());
        assertTrue(dto.getRoomtypeIds().isEmpty());

    }

    @Test
    void getPriceChangeRange_WhenNoPriceChangeRange_Test(){
        Product product = mockProduct(3, "ABC");
        when(crudService.findByNamedQuery(eq(ProductFlatRateSeason.GET_ALL_SEASONS_BY_PRODUCT_ID), any()))
                .thenReturn(Collections.emptyList());

        List<PriceChangeRangeDTO> result = productManagementService.getPriceChangeRange(product);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getPriceChangeRange_Test(){
        Product product = mockProduct(3, "ABC");
        ProductFlatRateSeason season1 = new ProductFlatRateSeason();
        ProductFlatRateSeason season2 = new ProductFlatRateSeason();
        when(crudService.findByNamedQuery(eq(ProductFlatRateSeason.GET_ALL_SEASONS_BY_PRODUCT_ID), any())).thenReturn(Arrays.asList(season1, season2));
        doReturn(new PriceChangeRangeDTO()).when(spyService).createPriceChangeRangeDTO(season1);
        doReturn(new PriceChangeRangeDTO()).when(spyService).createPriceChangeRangeDTO(season2);

        List<PriceChangeRangeDTO> result = spyService.getPriceChangeRange(product);

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(spyService, times(2)).createPriceChangeRangeDTO(any());
        verify(crudService, times(1)).findByNamedQuery(eq(ProductFlatRateSeason.GET_ALL_SEASONS_BY_PRODUCT_ID), any());

    }

    @Test
    void getPriceChangeRange_WhenFrequencyIsNull_Test(){
        Product product = mockProduct(3, "ABC");
        ProductFlatRateSeason season1 = new ProductFlatRateSeason(1, 3,  null, null, null);
        when(crudService.findByNamedQuery(eq(ProductFlatRateSeason.GET_ALL_SEASONS_BY_PRODUCT_ID), any())).thenReturn(Arrays.asList(season1));
        List<PriceChangeRangeDTO> result = spyService.getPriceChangeRange(product);
        assertEquals(1, result.size());
        verify(spyService, times(1)).createPriceChangeRangeDTO(any());
        verify(crudService, times(1)).findByNamedQuery(eq(ProductFlatRateSeason.GET_ALL_SEASONS_BY_PRODUCT_ID), any());
        assertEquals(null, result.get(0).getStartDate());
        assertEquals(null, result.get(0).getEndDate());
        assertEquals(null, result.get(0).getFrequency());
    }

    @Test
    void getProductSupplements_withNoSupplement_Test(){
        Product product = mockProduct(2, "ABC");
        doReturn(Collections.emptyList()).when(spyService).fetchSupplements(product);
        List<SupplementDTO> result = spyService.getProductSupplements(product);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(spyService, times(0)).createSupplementDTO(any(), anyList());
    }

    @Test
    void getProductSupplements_withSupplement_Test(){
        Product product = mockProduct(2, "ABC");

        AccomTypeSupplement supplement1 = new AccomTypeSupplement();
        supplement1.setName("Summer");
        supplement1.setStartDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 01)));
        supplement1.setStartDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 31)));

        AccomTypeSupplement supplement2 = new AccomTypeSupplement();
        supplement2.setName(null);

        List<AccomTypeSupplement> supplements = Arrays.asList(supplement1, supplement2);

        doReturn(supplements).when(spyService).fetchSupplements(product);
        doReturn(new SupplementDTO("Summer", java.time.LocalDate.of(2025,05,01), java.time.LocalDate.of(2025,05,10)  , new ArrayList<>())).when(spyService).createSupplementDTO(eq("Summer"), any());
        doReturn(new SupplementDTO(null, null, null, new ArrayList<>())).when(spyService).createSupplementDTO(eq(DEFAULT), any());

       List<SupplementDTO> result = spyService.getProductSupplements(product);

        assertEquals(2, result.size());
        assertEquals(null, result.get(0).getSeasonName());
        assertEquals(null, result.get(0).getStartDate());
        assertEquals(null, result.get(0).getEndDate());
        assertEquals("Summer", result.get(1).getSeasonName());
        assertEquals(java.time.LocalDate.of(2025,05,01), result.get(1).getStartDate());
        assertEquals(java.time.LocalDate.of(2025,05,10), result.get(1).getEndDate());
        verify(spyService, times(2)).createSupplementDTO(any(), anyList());
    }

    @Test
    void fetchSupplements_WhenSupplementNotExists_Test(){
        Product product = mockProduct(2, "ABC");
        when(crudService.findByNamedQuery(eq(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT), any())).thenReturn(Collections.emptyList());

        List<AccomTypeSupplement> result = productManagementService.fetchSupplements(product);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(crudService, times(1)).findByNamedQuery(eq(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT), any());
    }

    @Test
    void fetchSupplements_WhenSupplementExists_Test(){
        Product product = mockProduct(2, "ABC");
        AccomTypeSupplement supplement1 = new AccomTypeSupplement();
        AccomTypeSupplement supplement2 = new AccomTypeSupplement();
        when(crudService.findByNamedQuery(eq(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT), any())).thenReturn(Arrays.asList(supplement1, supplement2));
        List<AccomTypeSupplement> result = productManagementService.fetchSupplements(product);
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(crudService, times(1)).findByNamedQuery(eq(AccomTypeSupplement.FIND_SUPPLEMENTS_BY_PRODUCT), any());
    }

    @Test
    void createSupplementDTO_withValidSupplementsSeason_Test(){

        AccomType at1 = new AccomType();
        at1.setId(1);
        final String SEASON_NAME = "Season";
        AccomTypeSupplement supplement1 =  new AccomTypeSupplement();
        supplement1.setName(SEASON_NAME);
        supplement1.setAccomType(at1);
        supplement1.setStartDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 01)));
        supplement1.setEndDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 15)));
        doReturn(new SupplementByRoomTypeDTO()).when(spyService).createSupplementByRoomTypeDTO(any()) ;
        SupplementDTO result = spyService.createSupplementDTO(SEASON_NAME, List.of(supplement1));
        assertEquals("Season", result.getSeasonName());
        assertEquals(java.time.LocalDate.of(2025,05,01), result.getStartDate());
        assertEquals(java.time.LocalDate.of(2025,05,15), result.getEndDate());
        assertEquals(1, result.getSupplementByRoomTypes().size());
    }

    @Test
    void createSupplementDTO_withDefaultSupplements_Test(){
        AccomType at1 = new AccomType();
        at1.setId(1);
        final String SEASON_NAME = DEFAULT;
        AccomTypeSupplement supplement1 =  new AccomTypeSupplement();
        supplement1.setName(SEASON_NAME);
        supplement1.setAccomType(at1);
        supplement1.setStartDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 01)));
        supplement1.setEndDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 15)));
        doReturn(new SupplementByRoomTypeDTO()).when(spyService).createSupplementByRoomTypeDTO(any()) ;
        SupplementDTO result = spyService.createSupplementDTO(SEASON_NAME, List.of(supplement1));
        assertEquals(null, result.getSeasonName());
        assertEquals(java.time.LocalDate.of(2025,05,01), result.getStartDate());
        assertEquals(java.time.LocalDate.of(2025,05,15), result.getEndDate());
        assertEquals(1, result.getSupplementByRoomTypes().size());
    }

    @Test
    void createSupplementByRoomTypeDTO_Test(){
        int accomTypeId = 1;
        AccomTypeSupplement supplement1 = new AccomTypeSupplement();
        AccomTypeSupplement supplement2 = new AccomTypeSupplement();

        Map.Entry<Integer, List<AccomTypeSupplement>> roomEntry = Map.entry(accomTypeId, List.of(supplement1, supplement2));
        doReturn(new SupplementValueDTO()).when(spyService).createSupplementValueDTO(any(AccomTypeSupplement.class));
        SupplementByRoomTypeDTO result = spyService.createSupplementByRoomTypeDTO(roomEntry);
        assertNotNull(result);
        assertEquals(accomTypeId, result.roomTypeId);
        assertEquals(2, result.supplementValues.size());
    }

    @Test
    void createSupplementValueDTO_Test(){
        AccomTypeSupplement supplement = new AccomTypeSupplement();
        supplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        supplement.setOccupancyType(OccupancyType.SINGLE);
        supplement.setSundaySupplementValue(BigDecimal.valueOf(10.0));
        supplement.setMondaySupplementValue(BigDecimal.valueOf(20.0));
        supplement.setTuesdaySupplementValue(BigDecimal.valueOf(30.0));
        supplement.setWednesdaySupplementValue(BigDecimal.valueOf(40.0));
        supplement.setThursdaySupplementValue(null);
        supplement.setFridaySupplementValue(BigDecimal.valueOf(60.0));
        supplement.setSaturdaySupplementValue(BigDecimal.valueOf(0));

        SupplementValueDTO valueDTO = productManagementService.createSupplementValueDTO(supplement);
        assertNotNull(valueDTO);
        assertEquals(OffsetMethod.FIXED_OFFSET, valueDTO.getOffsetMethod());
        assertEquals(OccupancyType.SINGLE, valueDTO.getOccupancyType());
        assertEquals(BigDecimal.valueOf(10.0), valueDTO.getSundaySupplementValue());
        assertEquals(BigDecimal.valueOf(20.0), valueDTO.getMondaySupplementValue());
        assertEquals(BigDecimal.valueOf(30.0), valueDTO.getTuesdaySupplementValue());
        assertEquals(BigDecimal.valueOf(40.0), valueDTO.getWednesdaySupplementValue());
        assertEquals(null, valueDTO.getThursdaySupplementValue());
        assertEquals(BigDecimal.valueOf(60.0), valueDTO.getFridaySupplementValue());
        assertEquals(BigDecimal.valueOf(0), valueDTO.getSaturdaySupplementValue());
    }

    public List<Product> getMockProductsList(){
        final Integer PRODUCT_1_ID = 1;
        final Integer PRODUCT_2_ID = 2;
        final String PRODUCT_1 = "Product 1";
        final String PRODUCT_2 = "Product 2";
        return List.of(mockProduct(PRODUCT_1_ID, PRODUCT_1), mockProduct(PRODUCT_2_ID, PRODUCT_2));
    }

    private Product mockProduct(int id, String name){
        Product product = new Product();
        product.setId(id);
        product.setName(name);
        return product;
    }

    private WebrateChannel createMockChannel(Integer id){
        WebrateChannel webrateChannel = new WebrateChannel();
        webrateChannel.setId(id);
        return webrateChannel;
    }

    private WebrateDefaultChannel createMockDefaultChannel() {
        WebrateDefaultChannel channel = mock(WebrateDefaultChannel.class);
        when(channel.getWebrateChannelMon()).thenReturn(createMockChannel(1));
        when(channel.getWebrateChannelTues()).thenReturn(createMockChannel(2));
        when(channel.getWebrateChannelWed()).thenReturn(createMockChannel(3));
        when(channel.getWebrateChannelThurs()).thenReturn(createMockChannel(4));
        when(channel.getWebrateChannelFri()).thenReturn(createMockChannel(5));
        when(channel.getWebrateChannelSat()).thenReturn(createMockChannel(6));
        when(channel.getWebrateChannelSun()).thenReturn(createMockChannel(7));
        return channel;
    }

    private WebrateOverrideChannel createMockOverrideChannel(String name) {
        WebrateOverrideChannel channel = mock(WebrateOverrideChannel.class);
        when(channel.getWebrateOverrideName()).thenReturn(name);
        java.time.LocalDate date = java.time.LocalDate.of(2025,01, 01);
        when(channel.getChannelOverrideStartDT()).thenReturn(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        when(channel.getChannelOverrideEndDT()).thenReturn(Date.from(date.plusDays(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));
        when(channel.getWebrateChannelMon()).thenReturn(createMockChannel(11));
        when(channel.getWebrateChannelTues()).thenReturn(createMockChannel(12));
        when(channel.getWebrateChannelWed()).thenReturn(createMockChannel(13));
        when(channel.getWebrateChannelThurs()).thenReturn(createMockChannel(14));
        when(channel.getWebrateChannelFri()).thenReturn(createMockChannel(15));
        when(channel.getWebrateChannelSat()).thenReturn(createMockChannel(16));
        when(channel.getWebrateChannelSun()).thenReturn(createMockChannel(17));
        return channel;
    }

    private TransientPricingBaseAccomType moockTransientPricingBaseAccomType(int id, String seasonName, java.time.LocalDate startDate, java.time.LocalDate endDate){
        TransientPricingBaseAccomType dto = new TransientPricingBaseAccomType();
        dto.setId(id);
        dto.setAccomType(new AccomType());
        dto.setSeasonName(seasonName);
        dto.setStartDate(startDate == null ?  null : convertJavaToJodaLocalDate(startDate));
        dto.setEndDate(endDate == null ? null : convertJavaToJodaLocalDate(endDate));
        return dto;
    }

    private ProductAccomType mockProductAccomType(AccomType accomType, Product product){
        ProductAccomType dto = new ProductAccomType();
        dto.setProduct(product);
        dto.setAccomType(accomType);
        return dto;
    }

    private ProductRateCode  mockProductRateCode(String rateCode, Product product){
        ProductRateCode  dto = new ProductRateCode ();
        dto.setProduct(product);
        dto.setRateCode(rateCode);
        return dto;
    }

    @Test
    void getProductOffsets_WhenNoOffsetsConfigured_Test(){
        Product product = mockProduct(1, "ABC");
        when(crudService.findByNamedQuery(eq(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY), any())).thenReturn(Collections.emptyList());
        List<OffsetDetailsDTO> result = spyService.getProductOffsets(product);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(spyService, times(0)).createOffsetDetails(any());
    }

    @Test
    void getProductOffsets_WhenOffsetsConfigured_Test(){
        Product product = mockProduct(1, "ABC");
        CPConfigOffsetAccomType offset1 = new CPConfigOffsetAccomType();
        offset1.setName("Summer");
        CPConfigOffsetAccomType offset2 = new CPConfigOffsetAccomType();
        offset2.setName(null);
        when(crudService.findByNamedQuery(eq(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY), any())).thenReturn(List.of(offset1, offset2));
        doReturn(new OffsetDetailsDTO()).when(spyService).createOffsetDetails(any());
        Map.Entry<String, List<CPConfigOffsetAccomType>> winterEntry = Map.entry("Summer", List.of(offset1));
        Map.Entry<String, List<CPConfigOffsetAccomType>> summerEntry = Map.entry(DEFAULT, List.of(offset2));
        doReturn(new OffsetDetailsDTO("Summer",java.time.LocalDate.of(2025,05,01), java.time.LocalDate.of(2025,05,10), new ArrayList<>())).when(spyService).createOffsetDetails(eq(winterEntry));
        doReturn(new OffsetDetailsDTO(null,null, null, new ArrayList<>())).when(spyService).createOffsetDetails(eq(summerEntry));
        List<OffsetDetailsDTO> result = spyService.getProductOffsets(product);
        assertEquals(2, result.size());
        assertEquals(null, result.get(0).getSeasonName());
        assertEquals("Summer", result.get(1).getSeasonName());
        verify(spyService, times(2)).createOffsetDetails(any());
    }

    @Test
    void createOffsetDetails_withSeasonalOffsets_Test(){
        final String SEASON_NAME = "Season";
        AccomType at1 = new AccomType();
        at1.setId(1);
        AccomType at2 = new AccomType();
        at2.setId(2);
        CPConfigOffsetAccomType offset1 = new CPConfigOffsetAccomType();
        offset1.setAccomType(at1);
        offset1.setName(SEASON_NAME);
        offset1.setStartDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 01)));
        offset1.setEndDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 10)));

        CPConfigOffsetAccomType offset2 = new CPConfigOffsetAccomType();
        offset2.setAccomType(at2);
        offset2.setName(SEASON_NAME);
        offset2.setStartDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 01)));
        offset2.setEndDate(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 10)));

        List<CPConfigOffsetAccomType> seasonData = Arrays.asList(offset1, offset2);
        Map.Entry<String, List<CPConfigOffsetAccomType>> entry = Map.entry(SEASON_NAME, seasonData);
        doReturn( new OffsetByRoomTypeDTO()).when(spyService).createOffsetByRoomTypeDTO(any());

        OffsetDetailsDTO dto = spyService.createOffsetDetails(entry);

        assertNotNull(dto);
        assertEquals(SEASON_NAME, dto.getSeasonName());
        assertEquals(java.time.LocalDate.of(2025,05,01), dto.getStartDate());
        assertEquals(java.time.LocalDate.of(2025,05,10), dto.getEndDate());
        assertEquals(2, dto.getOffsetByRoomType().size());
    }

    @Test
    void createOffsetDetails_withDefaultOffsets_Test(){
        final String SEASON_NAME = DEFAULT;
        AccomType at1 = new AccomType();
        at1.setId(1);
        AccomType at2 = new AccomType();
        at2.setId(2);
        CPConfigOffsetAccomType offset1 = new CPConfigOffsetAccomType();
        offset1.setAccomType(at1);
        offset1.setName(SEASON_NAME);
        offset1.setStartDate(null);
        offset1.setEndDate(null);
        CPConfigOffsetAccomType offset2 = new CPConfigOffsetAccomType();
        offset2.setAccomType(at2);
        offset2.setName(SEASON_NAME);
        offset2.setStartDate(null);
        offset2.setEndDate(null);
        List<CPConfigOffsetAccomType> seasonData = Arrays.asList(offset1, offset2);
        Map.Entry<String, List<CPConfigOffsetAccomType>> entry = Map.entry(SEASON_NAME, seasonData);
        doReturn( new OffsetByRoomTypeDTO()).when(spyService).createOffsetByRoomTypeDTO(any());
        OffsetDetailsDTO dto = spyService.createOffsetDetails(entry);
        assertNotNull(dto);
        assertEquals(null, dto.getSeasonName());
        assertEquals(null, dto.getStartDate());
        assertEquals(null, dto.getEndDate());
        assertEquals(2, dto.getOffsetByRoomType().size());
    }

    @Test
    void createOffsetByRoomTypeDTO_Test(){
        Integer roomTypeId = 1;
        CPConfigOffsetAccomType offset1 = new CPConfigOffsetAccomType();
        CPConfigOffsetAccomType offset2 = new CPConfigOffsetAccomType();
        List<CPConfigOffsetAccomType> offsets = Arrays.asList(offset1, offset2);
        Map.Entry<Integer, List<CPConfigOffsetAccomType>> entry = Map.entry(roomTypeId, offsets);
        doReturn(new OffsetValueDTO()).when(spyService).createOffsetValueDTO(any());
        OffsetByRoomTypeDTO roomDTO = spyService.createOffsetByRoomTypeDTO(entry);
        assertNotNull(roomDTO);
        assertEquals(roomTypeId, roomDTO.getRoomTypeId());
        assertEquals(2, roomDTO.getOffsetValues().size());
    }

    @Test
    void CreateOffsetValueDTO_Test() {
        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();

        offset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        offset.setOccupancyType(OccupancyType.SINGLE);
        offset.setSundayOffsetValueWithTax(new BigDecimal("11.1"));
        offset.setMondayOffsetValueWithTax(new BigDecimal("12.2"));
        offset.setTuesdayOffsetValueWithTax(new BigDecimal("13.3"));
        offset.setWednesdayOffsetValueWithTax(new BigDecimal("14.4"));
        offset.setThursdayOffsetValueWithTax(new BigDecimal("15.5"));
        offset.setFridayOffsetValueWithTax(new BigDecimal("16.6"));
        offset.setSaturdayOffsetValueWithTax(new BigDecimal("17.7"));

        OffsetValueDTO dto = productManagementService.createOffsetValueDTO(offset);

        assertEquals(OffsetMethod.PERCENTAGE, dto.getOffsetMethod());
        assertEquals(OccupancyType.SINGLE, dto.getOccupancyType());

        assertEquals(new BigDecimal("11.1"), dto.getSundayValueWithTax());
        assertEquals(new BigDecimal("12.2"), dto.getMondayValueWithTax());
        assertEquals(new BigDecimal("13.3"), dto.getTuesdayValueWithTax());
        assertEquals(new BigDecimal("14.4"), dto.getWednesdayValueWithTax());
        assertEquals(new BigDecimal("15.5"), dto.getThursdayValueWithTax());
        assertEquals(new BigDecimal("16.6"), dto.getFridayValueWithTax());
        assertEquals(new BigDecimal("17.7"), dto.getSaturdayValueWithTax());
    }


    @Test
    void getFloorCeiling_WhenFloorAndCeilingsNotConfigured_Test(){
        Product product = mockProduct(1, "ABC");
        when(crudService.findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_ALL_BY_PRODUCT_ID), any())).thenReturn(Collections.emptyList());
        List<FloorCeilDetailsDTO> result = spyService.getFloorCeiling(product);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(spyService, times(0)).groupFloorCeilingBySeason(any());
        verify(spyService, times(0)).buildFloorCeilDetailsDTO(any(), anyList());
    }

    @Test
    void getFloorCeiling_WhenFloorAndCeilingsConfigured_Test(){
        AccomType at1 = new AccomType();
        at1.setId(1);
        Product product = mockProduct(1, "ABC");
        TransientPricingBaseAccomType pricing1 = new TransientPricingBaseAccomType();
        pricing1.setSeasonName(null);
        pricing1.setAccomType(at1);
        TransientPricingBaseAccomType pricing2 = new TransientPricingBaseAccomType();
        pricing2.setSeasonName("Summer");
        pricing2.setAccomType(at1);
        when(crudService.findByNamedQuery(eq(TransientPricingBaseAccomType.FIND_ALL_BY_PRODUCT_ID), any())).thenReturn(List.of(pricing1, pricing2));
        doReturn(new FloorCeilDetailsDTO(null, null, null, new ArrayList<>())).
                when(spyService).buildFloorCeilDetailsDTO(eq(DEFAULT), anyList());
        doReturn(new FloorCeilDetailsDTO("Summer", java.time.LocalDate.of(2025,05,01), java.time.LocalDate.of(2025,05,10), new ArrayList<>())).
                when(spyService).buildFloorCeilDetailsDTO(eq("Summer"), anyList());
        List<FloorCeilDetailsDTO> result = spyService.getFloorCeiling(product);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(null, result.get(0).getSeasonName());
        assertEquals("Summer", result.get(1).getSeasonName());
    }

    @Test
    void groupFloorCeilingBySeason_Test(){
        TransientPricingBaseAccomType dto1 = moockTransientPricingBaseAccomType(1, null, null, null);
        TransientPricingBaseAccomType dto2 = moockTransientPricingBaseAccomType(1, "Season",
                (java.time.LocalDate.of(2025,05, 01)), (java.time.LocalDate.of(2025,05, 10)));
        Map<String, List<TransientPricingBaseAccomType>> result = productManagementService.groupFloorCeilingBySeason(List.of(dto1, dto2));
        assertEquals(2, result.size());
        assertTrue(result.containsKey(DEFAULT));
        assertTrue(result.containsKey("Season"));
        assertEquals(null, result.get(DEFAULT).get(0).getStartDate());
        assertEquals(null, result.get(DEFAULT).get(0).getEndDate());
        assertEquals(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 01)), result.get("Season").get(0).getStartDate());
        assertEquals(convertJavaToJodaLocalDate(java.time.LocalDate.of(2025,05, 10)), result.get("Season").get(0).getEndDate());
    }
    @Test
    void buildFloorCeilDetailsDTO_WhenDefaultValues_Test(){
        TransientPricingBaseAccomType dto1 = moockTransientPricingBaseAccomType(1, null, null, null);
        TransientPricingBaseAccomType dto2 = moockTransientPricingBaseAccomType(1, null, null, null);
        FloorCeilDetailsDTO dto = productManagementService.buildFloorCeilDetailsDTO(DEFAULT, List.of(dto1, dto2));
        assertNull(dto.getSeasonName());
        assertNull(dto.getStartDate());
        assertNull(dto.getEndDate());
        assertEquals(2, dto.getFloorCeilValues().size());
    }

    @Test
    void buildFloorCeilDetailsDTO_WhenSeasonalValues_Test(){
        TransientPricingBaseAccomType dto1 = moockTransientPricingBaseAccomType(1, "Season",
                (java.time.LocalDate.of(2025,05, 01)), (java.time.LocalDate.of(2025,05, 10)));
        TransientPricingBaseAccomType dto2 = moockTransientPricingBaseAccomType(1, "Season",
                (java.time.LocalDate.of(2025,05, 01)), (java.time.LocalDate.of(2025,05, 10)));

        FloorCeilDetailsDTO dto = productManagementService.buildFloorCeilDetailsDTO("Season", List.of(dto1, dto2));
        assertEquals("Season", dto.getSeasonName());
        assertEquals(java.time.LocalDate.of(2025,05,01), dto.getStartDate());
        assertEquals(java.time.LocalDate.of(2025,05,10), dto.getEndDate());
        assertEquals(2, dto.getFloorCeilValues().size());
    }

    @Test
    void buildFloorCeilValuesList_Test(){
        TransientPricingBaseAccomType dto1 = moockTransientPricingBaseAccomType(1, "Season",
                (java.time.LocalDate.of(2025,05, 01)), (java.time.LocalDate.of(2025,05, 10)));
        TransientPricingBaseAccomType dto2 = moockTransientPricingBaseAccomType(1, "Season",
                (java.time.LocalDate.of(2025,05, 01)), (java.time.LocalDate.of(2025,05, 10)));
        List<FloorCeilValuesDTO> result = productManagementService.buildFloorCeilValuesList(List.of(dto1, dto2));
        assertEquals(2, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(1));
    }

    @Test
    void testGetProductHierarchyList_whenHierarchyExists_Test(){
        Product product = mockProduct(1, "ABC");
        ProductHierarchy hierarchy = new ProductHierarchy();
        AccomClass masterAccomClass = new AccomClass();
        masterAccomClass.setId(100);
        List<HierarchyDTO> expectedDTOs = List.of(new HierarchyDTO());
        when(crudService.findByNamedQuery(eq(ProductHierarchy.BY_PRODUCT_ID), anyMap())).thenReturn(List.of(hierarchy));
        try (MockedStatic<PacmanWorkContextHelper> mockedStatic = mockStatic(PacmanWorkContextHelper.class)) {
            mockedStatic.when(PacmanWorkContextHelper::getPropertyId).thenReturn(100);
            when(accommodationService.findMasterClass(100)).thenReturn(masterAccomClass);
            doReturn(expectedDTOs).when(spyService).buildHierarchyDTOList(List.of(hierarchy), masterAccomClass);

            List<HierarchyDTO> result = spyService.getProductHierarchyList(product);

            assertEquals(expectedDTOs.size(), result.size());
            assertEquals(expectedDTOs, result);
        }
    }


    @Test
    void buildFloorCeilValuesDTO_Test(){
        AccomType accomType = new AccomType();
        accomType.setId(99);

        TransientPricingBaseAccomType floorCeiling = new TransientPricingBaseAccomType();
        floorCeiling.setAccomType(accomType);

        floorCeiling.setSundayCeilingRateWithTax(new BigDecimal("20.00"));
        floorCeiling.setSundayFloorRateWithTax(new BigDecimal("15.00"));
        floorCeiling.setMondayCeilingRateWithTax(new BigDecimal("21.00"));
        floorCeiling.setMondayFloorRateWithTax(new BigDecimal("16.00"));
        floorCeiling.setTuesdayCeilingRateWithTax(new BigDecimal("22.00"));
        floorCeiling.setTuesdayFloorRateWithTax(new BigDecimal("17.00"));
        floorCeiling.setWednesdayCeilingRateWithTax(new BigDecimal("23.00"));
        floorCeiling.setWednesdayFloorRateWithTax(new BigDecimal("18.00"));
        floorCeiling.setThursdayCeilingRateWithTax(new BigDecimal("24.00"));
        floorCeiling.setThursdayFloorRateWithTax(new BigDecimal("19.00"));
        floorCeiling.setFridayCeilingRateWithTax(new BigDecimal("25.00"));
        floorCeiling.setFridayFloorRateWithTax(new BigDecimal("20.00"));
        floorCeiling.setSaturdayCeilingRateWithTax(new BigDecimal("26.00"));
        floorCeiling.setSaturdayFloorRateWithTax(new BigDecimal("21.00"));

        FloorCeilValuesDTO dto = productManagementService.buildFloorCeilValuesDTO(floorCeiling);

        assertEquals(99, dto.getRoomTypeId());
        assertEquals(new BigDecimal("20.00"), dto.getSundayCeilingRateWithTax());
        assertEquals(new BigDecimal("15.00"), dto.getSundayFloorRateWithTax());
        assertEquals(new BigDecimal("21.00"), dto.getMondayCeilingRateWithTax());
        assertEquals(new BigDecimal("16.00"), dto.getMondayFloorRateWithTax());
        assertEquals(new BigDecimal("22.00"), dto.getTuesdayCeilingRateWithTax());
        assertEquals(new BigDecimal("17.00"), dto.getTuesdayFloorRateWithTax());
        assertEquals(new BigDecimal("23.00"), dto.getWednesdayCeilingRateWithTax());
        assertEquals(new BigDecimal("18.00"), dto.getWednesdayFloorRateWithTax());
        assertEquals(new BigDecimal("24.00"), dto.getThursdayCeilingRateWithTax());
        assertEquals(new BigDecimal("19.00"), dto.getThursdayFloorRateWithTax());
        assertEquals(new BigDecimal("25.00"), dto.getFridayCeilingRateWithTax());
        assertEquals(new BigDecimal("20.00"), dto.getFridayFloorRateWithTax());
        assertEquals(new BigDecimal("26.00"), dto.getSaturdayCeilingRateWithTax());
        assertEquals(new BigDecimal("21.00"), dto.getSaturdayFloorRateWithTax());
    }

    @Test
    void getProductHierarchy_WithNoHierarchy_Test(){
        Product product = mockProduct(1, "ABC");
        when(crudService.findByNamedQuerySingleResult(eq(ProductHierarchy.BY_PRODUCT_ID), any())).thenReturn(null);
        List<HierarchyDTO> result = productManagementService.getProductHierarchyList(product);
        assertTrue(result.isEmpty());
    }

    @Test
    void buildHierarchyDTOList_WithValidHierarchy_Test(){
        Product fromProduct = mockProduct(1, "ABC");
        Product toProduct = mockProduct(2, "CDE");
        ProductHierarchy hierarchy = new ProductHierarchy();
        hierarchy.setFromProduct(fromProduct);
        hierarchy.setToProduct(toProduct);
        List<ProductHierarchy> hierarchies = List.of(hierarchy);
        AccomClass mockAccomClass = new AccomClass();
        BigDecimal expectedDiff = new BigDecimal("25.50");
        doReturn(expectedDiff).when(spyService).getSundayMinPriceDiff(hierarchy, mockAccomClass);
        List<HierarchyDTO> result = spyService.buildHierarchyDTOList(hierarchies, mockAccomClass);
        assertTrue(result.size() == 1);
        assertEquals(1, result.size());
        HierarchyDTO dto = result.get(0);
        assertEquals(fromProduct.getId(), dto.getFromProductId());
        assertEquals(toProduct.getId(), dto.getToProductId());
        assertEquals(expectedDiff, dto.getMinimumPriceDifference());
    }

    @Test
    void getSundayMinPriceDiff(){
        AccomClass masterClass = new AccomClass();
        masterClass.setId(10);
        AccomClass diffClass = new AccomClass();
        diffClass.setId(15);
        ProductMinPriceDiff diff1 = new ProductMinPriceDiff();
        diff1.setAccomClass(diffClass);
        diff1.setSundayDiffWithTax(new BigDecimal("45.67"));
        ProductMinPriceDiff diff2 = new ProductMinPriceDiff();
        diff2.setAccomClass(masterClass);
        diff2.setSundayDiffWithTax(new BigDecimal("99.99"));
        ProductHierarchy hierarchy = new ProductHierarchy();
        hierarchy.setProductMinPriceDiffList(List.of(diff1, diff2));
        BigDecimal result = productManagementService.getSundayMinPriceDiff(hierarchy, masterClass);
        assertEquals(new BigDecimal("99.99"), result, "Should return correct Sunday diff with tax");
    }


    @Test
    void getProductRateOffsets_WhenProductRateOffsetsAreNotConfigured(){
        Product product = mockProduct(1, "ABC");
        when(agileRatesConfigurationService.findProductRateOffsetsByProduct(product)).thenReturn(Collections.emptyList());
        List<ProductRateOffsetsDTO> result = productManagementService.getProductRateOffsets(product);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    @Test
    void getProductRateOffsets_WhenProductRateOffsetsAreConfigured(){
        Product product = mockProduct(1, "ABC");

        ProductRateOffset offset = new ProductRateOffset();
        offset.setId(1);
        offset.setSeasonName(null);
        offset.setAccomClass(mock(AccomClass.class));
        offset.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        offset.setStartDate(null);
        offset.setEndDate(null);
        when(agileRatesConfigurationService.findProductRateOffsetsByProduct(product)).thenReturn(List.of(offset));
        List<ProductRateOffsetsDTO> result = productManagementService.getProductRateOffsets(product);
        assertEquals(1, result.size());
        assertEquals(null, result.get(0).getSeasonName());
        assertEquals(null, result.get(0).getStartDate());
        assertEquals(null, result.get(0).getEndDate());
        assertEquals(AgileRatesOffsetMethod.FIXED, result.get(0).getOffsetMethod());
    }

    @Test
    void getProductPackage_whenPackagesAreNotConfigured_Test(){
        Product product = mockProduct(1, "ABC");
        when(crudService.findByNamedQuery(eq(AgileRatesPackage.GET_AGILE_RATES_PACKAGES_BY_PRODUCT_ID), anyMap())).thenReturn(Collections.emptyList());
        List<ProductPackageDTO> result = productManagementService.getProductPackage(product);
        assertEquals(0, result.size());
    }

    @Test
    void getProductPackage_whenPackagesAreConfigured_Test(){
        Product product = mockProduct(1, "ABC");
        AgileRatesPackage package1 =  new AgileRatesPackage();
        package1.setName("Adult Package");
        package1.setDescription("Adult Package");
        package1.setChargeType(AgileRatesChargeType.PER_ADULT);
        package1.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        package1.setOffsetValue(BigDecimal.valueOf(12.34));
        when(crudService.findByNamedQuery(eq(AgileRatesPackage.GET_AGILE_RATES_PACKAGES_BY_PRODUCT_ID), anyMap())).thenReturn(List.of(package1));
        List<ProductPackageDTO> result = productManagementService.getProductPackage(product);
        assertEquals(1, result.size());
        ProductPackageDTO dto = result.get(0);
        assertEquals("Adult Package", dto.getPackageName());
        assertEquals("Adult Package", dto.getDescription());
        assertEquals(AgileRatesChargeType.PER_ADULT, dto.getChargeType());
        assertEquals(AgileRatesOffsetMethod.FIXED, dto.getOffsetMethod());
        assertEquals(BigDecimal.valueOf(12.34), dto.getOffsetValue());
    }

    @Test
    void getRoundingRulesDTO_Test(){
        Product product = mockProduct(1, "ABC");
        product.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        PrettyPricingRulesDTO rule1 = new PrettyPricingRulesDTO(PricingDigit.TEN_THOUSANDS, "4,9");
        PrettyPricingRulesDTO rule2 = new PrettyPricingRulesDTO(PricingDigit.TENTHS, "0");
        doReturn(List.of(rule1, rule2)).when(spyService).getRoundingRules(product.getId());
        RoundingRulesDTO result = spyService.getRoundingRulesDTO(product);
        assertNotNull(result);
        assertEquals(RoundingRule.PRICE_ROUNDING, result.getRoundingRule());
        assertEquals(2, result.getPrettyPricingRules().size());
        assertEquals(PricingDigit.TEN_THOUSANDS, result.getPrettyPricingRules().get(0).getDigit());
        assertEquals("4,9", result.getPrettyPricingRules().get(0).getRuleNumbers());
        assertEquals(PricingDigit.TENTHS, result.getPrettyPricingRules().get(1).getDigit());
        assertEquals("0", result.getPrettyPricingRules().get(1).getRuleNumbers());
    }

    @Test
    void getRoundingRules_WhenPricingRuleIsNull_Test(){
        Integer productId = 1;
        when(prettyPricingService.getPricingRule(productId)).thenReturn(null);
        List<PrettyPricingRulesDTO> result = spyService.getRoundingRules(productId);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getRoundingRules_WhenPricingRuleIsConfigured_Test(){
        Integer productId = 1;
        PricingRule pricingRule = new PricingRule();
        PrettyPricingRuleRow pricingRuleRow1 = new PrettyPricingRuleRow();
        pricingRuleRow1.setDigit(PricingDigit.THOUSANDS);
        pricingRuleRow1.setRuleNumbers("4,9");
        PrettyPricingRuleRow pricingRuleRow2 = new PrettyPricingRuleRow();
        pricingRuleRow2.setDigit(PricingDigit.HUNDREDS);
        pricingRuleRow2.setRuleNumbers("9");
        pricingRule.addRule(pricingRuleRow1.getDigit(), pricingRuleRow1);
        pricingRule.addRule(pricingRuleRow2.getDigit(), pricingRuleRow2);
        when(prettyPricingService.getPricingRule(productId)).thenReturn(pricingRule);
        List<PrettyPricingRulesDTO> result = spyService.getRoundingRules(productId);
        result.sort(Comparator.comparing(PrettyPricingRulesDTO::getDigit));
        assertNotNull(result);
        assertEquals(2, result.size(), "The result should contain 2 rules");
        PrettyPricingRulesDTO dto1 = result.get(0);
        assertEquals(PricingDigit.THOUSANDS, dto1.getDigit());
        assertEquals("4,9", dto1.getRuleNumbers());
        PrettyPricingRulesDTO dto2 = result.get(1);
        assertEquals(PricingDigit.HUNDREDS, dto2.getDigit());
        assertEquals("9", dto2.getRuleNumbers());
    }

    @Test
    void getProductsToAccomTypeMap_Test(){
        Product product1 = mockProduct(1, "ABC");
        Product product2 = mockProduct(2, "DEF");
        List<Product> products = Arrays.asList(product1, product2);
        AccomType rt1 =  new AccomType();
        rt1.setId(2);
        AccomType rt2 =  new AccomType();
        rt2.setId(5);
        ProductAccomType pat1 = mockProductAccomType(rt1, product1);
        ProductAccomType pat2 = mockProductAccomType(rt2, product1);
        ProductAccomType pat3 = mockProductAccomType(rt1, product2);
        when(crudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", products).parameters())).thenReturn(List.of(pat1, pat2, pat3));
        Map<Integer, List<Integer>> result = productManagementService.getProductsToAccomTypeMap(products);
        Map<Integer, List<Integer>> expected = new HashMap<>();
        expected.put(1, List.of(2,5));
        expected.put(2, List.of(2));
        assertEquals(expected, result);
    }

    @Test
    void getProductToRateCodesMap_Test(){
        Product product1 = mockProduct(1, "ABC");
        Product product2 = mockProduct(2, "DEF");
        List<Product> products = Arrays.asList(product1, product2);
        ProductRateCode rateCode1 = mockProductRateCode("TEST", product1);
        ProductRateCode rateCode2 = mockProductRateCode("Rate", product1);
        ProductRateCode rateCode3 = mockProductRateCode("New", product2);
        when(crudService.findByNamedQuery(ProductRateCode.BY_PRODUCTS, QueryParameter.with("products", products).parameters())).thenReturn(List.of(rateCode1, rateCode2, rateCode3));
        Map<Integer, List<String>> result = productManagementService.getProductToRateCodesMap(products);
        Map<Integer, List<String>> expected = new HashMap<>();
        expected.put(1, List.of("TEST", "Rate"));
        expected.put(2, List.of("New"));
        assertEquals(expected, result);
    }

    @Test
    void shouldReturnDefaultAndOverrideChannelsTest(){
        Product product = mockProduct(1, "ABC");
        WebrateDefaultChannel defaultChannel = createMockDefaultChannel();
        List<WebrateOverrideChannel> overrideChannels = List.of(createMockOverrideChannel("Override1"));
        Map<Integer, List<WebrateOverrideChannel>> map = Map.of(1, overrideChannels);
        doReturn(defaultChannel).when(spyService).getWebrateDefaultChannel(any());
        List<DefaultRateShoppingChannelDTO> result = spyService.getDefaultRateShoppingChannels(product, map);
        assertEquals(2, result.size());
        assertNull(result.get(0).getSeasonName());
        assertEquals("Override1", result.get(1).getSeasonName());
    }

    @Test
    void shouldReturnEmptyListWhenNoDefaultAndOverrideChannelsTest(){
        Product product = mockProduct(1, "ABC");
        WebrateDefaultChannel defaultChannel = null;
        List<WebrateOverrideChannel> overrideChannels = Collections.emptyList();
        Map<Integer, List<WebrateOverrideChannel>> map = Map.of(1, overrideChannels);
        doReturn(defaultChannel).when(spyService).getWebrateDefaultChannel(any());
        List<DefaultRateShoppingChannelDTO> result = spyService.getDefaultRateShoppingChannels(product, map);
        assertEquals(0, result.size());
        assertNotNull(result);
    }

    @Test
    void shouldReturnOnlyDefaultChannelWhenNoOverrides() {
        Product product = mockProduct(1, "ABC");
        WebrateDefaultChannel defaultChannel = createMockDefaultChannel();
        Map<Integer, List<WebrateOverrideChannel>> map = Collections.emptyMap();
        doReturn(defaultChannel).when(spyService).getWebrateDefaultChannel(any());
        List<DefaultRateShoppingChannelDTO> result = spyService.getDefaultRateShoppingChannels(product, map);
        assertEquals(1, result.size());
        assertNull(result.get(0).getSeasonName());
    }

    @Test
    void shouldMapChannelIdsCorrectly() {
        WebrateDefaultChannel defaultChannel = createMockDefaultChannel();
        DefaultRateShoppingChannelDTO dto = productManagementService.createDefaultChannelDTO(defaultChannel);
        assertNull(dto.getSeasonName());
        assertNull(dto.getStartDate());
        assertNull(dto.getEndDate());
        assertEquals(1, dto.getMondayDefaultChannelId());
        assertEquals(2, dto.getTuesdayDefaultChannelId());
        assertEquals(3, dto.getWednesdayDefaultChannelId());
        assertEquals(4, dto.getThursdayDefaultChannelId());
        assertEquals(5, dto.getFridayDefaultChannelId());
        assertEquals(6, dto.getSaturdayDefaultChannelId());
        assertEquals(7, dto.getSundayDefaultChannelId());
    }


    @Test
    void shouldMapOverrideChannelsToDTOs() {
        WebrateOverrideChannel channel1 = createMockOverrideChannel("C1");
        WebrateOverrideChannel channel2 = createMockOverrideChannel("C2");

        List<WebrateOverrideChannel> input = List.of(channel1, channel2);

        List<DefaultRateShoppingChannelDTO> result = productManagementService.createOverrideChannelDTOs(input);

        assertEquals(2, result.size());
        DefaultRateShoppingChannelDTO dto1 = result.get(0);
        assertEquals("C1", dto1.getSeasonName());
        assertEquals(channel1.getChannelOverrideStartDT(), dto1.getStartDate());
        assertEquals(channel1.getChannelOverrideEndDT(), dto1.getEndDate());
        assertEquals(11, dto1.getMondayDefaultChannelId());
        assertEquals(12, dto1.getTuesdayDefaultChannelId());
        assertEquals(13, dto1.getWednesdayDefaultChannelId());
        assertEquals(14, dto1.getThursdayDefaultChannelId());
        assertEquals(15, dto1.getFridayDefaultChannelId());
        assertEquals(16, dto1.getSaturdayDefaultChannelId());
        assertEquals(17, dto1.getSundayDefaultChannelId());

        dto1 = result.get(1);
        assertEquals("C2", dto1.getSeasonName());
    }

    @Test
    public void testCreateOverrideChannelDTO(){
        WebrateOverrideChannel channel = createMockOverrideChannel("C1");
        DefaultRateShoppingChannelDTO dto = productManagementService.createOverrideChannelDTO(channel);
        assertEquals("C1", dto.getSeasonName());
        assertEquals(channel.getChannelOverrideStartDT(), dto.getStartDate());
        assertEquals(channel.getChannelOverrideEndDT(), dto.getEndDate());
        assertEquals(11, dto.getMondayDefaultChannelId());
        assertEquals(12, dto.getTuesdayDefaultChannelId());
        assertEquals(13, dto.getWednesdayDefaultChannelId());
        assertEquals(14, dto.getThursdayDefaultChannelId());
        assertEquals(15, dto.getFridayDefaultChannelId());
        assertEquals(16, dto.getSaturdayDefaultChannelId());
        assertEquals(17, dto.getSundayDefaultChannelId());
    }


    @Test
    void testCreateAuditInfoDto_AllFieldsPresent(){
        GlobalUser createdByUser = new GlobalUser();
        createdByUser.setCognitoUserId("user-123");
        GlobalUser updatedByUser = new GlobalUser();
        updatedByUser.setCognitoUserId("user-456");
        LocalDateTime createdTime = LocalDateTime.of(2023, 7, 20, 10, 30);
        LocalDateTime updatedTime = LocalDateTime.of(2023, 7, 21, 15, 45);
        RmsAuditInfo result = productManagementService.createAuditInfoDto(createdByUser, createdTime, updatedByUser, updatedTime);
        assertNotNull(result);
        assertEquals("user-123", result.getCreatedBy());
        assertEquals(createdTime, result.getCreatedDateTime());
        assertEquals("user-456", result.getLastUpdatedBy());
        assertEquals(updatedTime, result.getLastUpdatedDateTime());
    }

    @Test
    void testCreateAuditInfoDto_WithNullValues(){
        RmsAuditInfo result = productManagementService.createAuditInfoDto(null, null, null, null);
        assertNotNull(result);
        assertNull(result.getCreatedBy());
        assertNull(result.getCreatedDateTime());
        assertNull(result.getLastUpdatedBy());
        assertNull(result.getLastUpdatedDateTime());
    }

    @Test
    void getProductFlatRateSeasonsWithinForecastWindow_returnSeasons_withinForecastWindow() {
        Product product = mockProduct(1, "Test Product");
        List<ProductFlatRateSeason> expectedSeasons = new ArrayList<>();
        expectedSeasons.add(getProductFlatRateSeason(1, java.time.LocalDate.of(2025,6,1), java.time.LocalDate.of(2025,6,30)));
        expectedSeasons.add(getProductFlatRateSeason(1, java.time.LocalDate.of(2025,7,1), java.time.LocalDate.of(2025,7,30)));
        expectedSeasons.add(getProductFlatRateSeason(1,  java.time.LocalDate.of(2025,8,1), java.time.LocalDate.of(2025,8,30)));
        expectedSeasons.add(getProductFlatRateSeason(1,  java.time.LocalDate.of(2027,8,1), java.time.LocalDate.of(2027,8,30)));

       doReturn(expectedSeasons).when(crudService).findByNamedQuery(ProductFlatRateSeason.GET_ALL_SEASONS_BY_PRODUCT_ID,
                QueryParameter.with("productId", product.getId()).parameters());
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2025,7,10));
        when(dateService.getForecastWindowEndDateBDE())
                .thenReturn(Date.from(java.time.LocalDate.of(2026, 7, 9).atStartOfDay(ZoneId.systemDefault()).toInstant()));

        List<ProductFlatRateSeason> result = productManagementService.getFlatRateSeasonsWithinForecastWindow(product);

        assertEquals(2, result.size());
        assertEquals(expectedSeasons.get(1), result.get(0));
        assertEquals(expectedSeasons.get(2), result.get(1));
    }
    @Test
    void getProductFlatRateSeasonsWithinDateRange_returnsEmptyList_WhenNoValidSeasonsExist() {
        Product product = mockProduct(1, "Test Product");
        List<ProductFlatRateSeason> expectedSeasons = new ArrayList<>();
        expectedSeasons.add(getProductFlatRateSeason(1, java.time.LocalDate.of(2025,6,1), java.time.LocalDate.of(2025,6,30)));
        expectedSeasons.add(getProductFlatRateSeason(1, java.time.LocalDate.of(2025,7,1), java.time.LocalDate.of(2025,7,30)));
        expectedSeasons.add(getProductFlatRateSeason(1,  java.time.LocalDate.of(2026,12,1), java.time.LocalDate.of(2026,12,30)));

        doReturn(expectedSeasons).when(crudService).findByNamedQuery(ProductFlatRateSeason.GET_ALL_SEASONS_BY_PRODUCT_ID,
                QueryParameter.with("productId", product.getId()).parameters());
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2025, 9, 1));
        when(dateService.getForecastWindowEndDateBDE())
                .thenReturn(Date.from(java.time.LocalDate.of(2026, Calendar.SEPTEMBER, 30).atStartOfDay(ZoneId.systemDefault()).toInstant()));

        List<ProductFlatRateSeason> result = productManagementService.getFlatRateSeasonsWithinForecastWindow(product);

        assertTrue(result.isEmpty());
    }

    private static ProductFlatRateSeason getProductFlatRateSeason(int id, java.time.LocalDate startDate, java.time.LocalDate endDate) {
        ProductFlatRateSeason season =  new ProductFlatRateSeason();
        season.setProductId(id);
        season.setStartDate(startDate);
        season.setEndDate(endDate);
        return season;
    }


    @Test
    void testGetRestrictionRateQualifiedIds_whenReturnValidResult(){
        Product product =  mockProduct(1, "AA");

        RateQualified rate1 = new RateQualified();
        rate1.setId(101);
        RateQualified rate2 = new RateQualified();
        rate2.setId(102);

        AgileProductRestrictionAssociation assoc1 = new AgileProductRestrictionAssociation();
        assoc1.setRateQualified(rate1);
        AgileProductRestrictionAssociation assoc2 = new AgileProductRestrictionAssociation();
        assoc2.setRateQualified(rate2);

        List<AgileProductRestrictionAssociation> mockAssociations = Arrays.asList(assoc1, assoc2);
        when(agileRatesConfigurationService.getAgileProductRestrictionAssociations(1)).thenReturn(mockAssociations);

        List<Integer> result = productManagementService.getRestrictionRateQualifiedIds(product);

        assertEquals(Arrays.asList(101, 102), result);
        verify(agileRatesConfigurationService, times(1)).getAgileProductRestrictionAssociations(1);
    }

    @Test
    void testGetRestrictionRateQualifiedIds_whenReturnEmptyList(){
        Product product =  mockProduct(1, "AA");
        when(agileRatesConfigurationService.getAgileProductRestrictionAssociations(1)).thenReturn(Collections.emptyList());
        List<Integer> result = productManagementService.getRestrictionRateQualifiedIds(product);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void getAgeBasePricingOffsets_whenAgeBasePricingDisabled(){
        Product product =  mockProduct(5, "AA");
        Integer baseProductId = 1;
        when(spyService.isAgeBasePricingEnabled()).thenReturn(false);
        List<OffsetDetailsDTO> result = spyService.getAgeBasePricingOffsets(product, baseProductId);
        assertTrue(result.isEmpty());
    }


    @Test
    void getAgeBasePricingOffsets__whenEmptyOccupantBucketIds(){
        Product product =  mockProduct(5, "AA");
        Integer baseProductId = 1;
        when(spyService.isAgeBasePricingEnabled()).thenReturn(true);
        when(spyService.getOccupantBucketIds()).thenReturn(Collections.emptyList());
        List<OffsetDetailsDTO> result = spyService.getAgeBasePricingOffsets(product, baseProductId);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAgeBasePricingOffsets__whenEmptyOffsets(){
        Product product =  mockProduct(5, "AA");
        Integer baseProductId = 1;

        OccupantBucketEntity occupantBucketEntity = new OccupantBucketEntity();
        occupantBucketEntity.setId(1);
        occupantBucketEntity.setOccupancyType(OccupancyType.CHILD_BUCKET_1);

        when(spyService.isAgeBasePricingEnabled()).thenReturn(true);
        when(agileRatesConfigurationService.getAllOccupantBuckets()).thenReturn(List.of(occupantBucketEntity));
        when(spyService.fetchOffsets(product, baseProductId)).thenReturn(Collections.emptyList());
        List<OffsetDetailsDTO> result = spyService.getAgeBasePricingOffsets(product, baseProductId);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAgeBasePricingOffsets__whenEmptyFilterChildBucketOffsets(){
        Product product =  mockProduct(5, "AA");
        Integer baseProductId = 1;

        OccupantBucketEntity occupantBucketEntity = new OccupantBucketEntity();
        occupantBucketEntity.setId(1);
        occupantBucketEntity.setOccupancyType(OccupancyType.CHILD_BUCKET_1);

        CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
        offset.setId(1);
        offset.setOccupancyType(OccupancyType.SINGLE);

        when(spyService.isAgeBasePricingEnabled()).thenReturn(true);
        when(agileRatesConfigurationService.getAllOccupantBuckets()).thenReturn(List.of(occupantBucketEntity));
        when(spyService.fetchOffsets(product, baseProductId)).thenReturn(List.of(offset));
        when(spyService.filterChildBucketOffsets(List.of(offset), List.of(13))).thenReturn(Collections.emptyList());

        List<OffsetDetailsDTO> result = spyService.getAgeBasePricingOffsets(product, baseProductId);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAgeBasePricingOffsets__whenReturnValidChildAgeOffsets(){
        Product mockProduct =  mockProduct(5, "AA");
        Integer baseProductId = 1001;

        List<Integer> bucketIds = Arrays.asList(1, 2);
        List<CPConfigOffsetAccomType> offsets = Arrays.asList(new CPConfigOffsetAccomType(), new CPConfigOffsetAccomType());
        List<CPConfigOffsetAccomType> filteredOffsets = Collections.singletonList(new CPConfigOffsetAccomType());
        Map<String, List<CPConfigOffsetAccomType>> groupedOffsets = new HashMap<>();
        groupedOffsets.put("Summer", filteredOffsets);

        OffsetDetailsDTO offsetDTO = new OffsetDetailsDTO();
        offsetDTO.setSeasonName("Summer");

        doReturn(true).when(spyService).isAgeBasePricingEnabled();
        doReturn(bucketIds).when(spyService).getOccupantBucketIds();
        doReturn(offsets).when(spyService).fetchOffsets(mockProduct, baseProductId);
        doReturn(filteredOffsets).when(spyService).filterChildBucketOffsets(offsets, bucketIds);
        doReturn(groupedOffsets).when(spyService).groupOffsetsBySeason(filteredOffsets);
        doReturn(offsetDTO).when(spyService).createOffsetDetails(any());

        List<OffsetDetailsDTO> result = spyService.getAgeBasePricingOffsets(mockProduct, baseProductId);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Summer", result.get(0).getSeasonName());

        verify(spyService).getOccupantBucketIds();
        verify(spyService).fetchOffsets(mockProduct, baseProductId);
        verify(spyService).filterChildBucketOffsets(offsets, bucketIds);
        verify(spyService).groupOffsetsBySeason(filteredOffsets);
        verify(spyService).createOffsetDetails(any());
    }

    @Test
    void testGOccupantBucketIds_whenReturnEmptyList(){
        when(agileRatesConfigurationService.getAllOccupantBuckets()).thenReturn(Collections.emptyList());
        List<Integer> result = productManagementService.getOccupantBucketIds();
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void testGOccupantBucketIds_whenReturnValidOccupantList(){
        OccupantBucketEntity occupantBucketEntity1 = new OccupantBucketEntity();
        occupantBucketEntity1.setId(1);
        occupantBucketEntity1.setOccupancyType(OccupancyType.CHILD_BUCKET_1);

        OccupantBucketEntity occupantBucketEntity2 = new OccupantBucketEntity();
        occupantBucketEntity2.setId(2);
        occupantBucketEntity2.setOccupancyType(OccupancyType.CHILD_BUCKET_2);

        when(agileRatesConfigurationService.getAllOccupantBuckets()).thenReturn(List.of(occupantBucketEntity1, occupantBucketEntity2));
        List<Integer> result = productManagementService.getOccupantBucketIds();
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(13, result.get(0));
        assertEquals(14, result.get(1));
    }

    @Test
    void testFetchOffsets_whenChileAgePricingIsSetToDoNotUpload(){
        Product mockProduct =  mockProduct(5, "AA");
        mockProduct.setChildPricingType(0);
        Integer baseProductId = 1001;

        List<CPConfigOffsetAccomType> result = productManagementService.fetchOffsets(mockProduct, baseProductId);
        assertNotNull(result);
        assertEquals(0, result.size());

        verify(crudService, times(0)).findByNamedQuery(any(), any());
    }

    @Test
    void testFetchOffsets_whenChileAgePricingIsSetToCustomForProduct(){
        Product mockProduct =  mockProduct(5, "AA");
        mockProduct.setChildPricingType(2);
        Integer baseProductId = 1001;

        CPConfigOffsetAccomType expectedOffsets = new CPConfigOffsetAccomType();
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY, QueryParameter.with("propertyId",
                PacmanWorkContextHelper.getPropertyId()).and("productID", mockProduct.getId()).parameters())).thenReturn(List.of(expectedOffsets));

        List<CPConfigOffsetAccomType> result = productManagementService.fetchOffsets(mockProduct, baseProductId);
        assertNotNull(result);
        assertEquals(1, result.size());

        ArgumentCaptor<Map<String, Object>> captor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNamedQuery(eq(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY), captor.capture());

        assertEquals(mockProduct.getId(), captor.getValue().get("productID"));
    }

    @Test
    void testFetchOffsets_whenChileAgePricingIsSetToUseSystemDefault(){
        Product mockProduct =  mockProduct(5, "AA");
        mockProduct.setChildPricingType(1);
        Integer baseProductId = 1001;

        CPConfigOffsetAccomType expectedOffsets = new CPConfigOffsetAccomType();
        when(crudService.findByNamedQuery(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY, QueryParameter.with("propertyId",
                PacmanWorkContextHelper.getPropertyId()).and("productID", baseProductId).parameters())).thenReturn(List.of(expectedOffsets));

        List<CPConfigOffsetAccomType> result = productManagementService.fetchOffsets(mockProduct, baseProductId);
        assertNotNull(result);
        assertEquals(1, result.size());

        ArgumentCaptor<Map<String, Object>> captor = ArgumentCaptor.forClass(Map.class);
        verify(crudService).findByNamedQuery(eq(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY), captor.capture());

        assertEquals(baseProductId, captor.getValue().get("productID"));
    }

    @Test
    void testFilterChildBucketOffsets_shouldReturnMatchingOffsets() {
        OccupancyType type1 = mock(OccupancyType.class);
        when(type1.getId()).thenReturn(1);

        OccupancyType type2 = mock(OccupancyType.class);
        when(type2.getId()).thenReturn(2);

        CPConfigOffsetAccomType offset1 = mock(CPConfigOffsetAccomType.class);
        CPConfigOffsetAccomType offset2 = mock(CPConfigOffsetAccomType.class);
        CPConfigOffsetAccomType offset3 = mock(CPConfigOffsetAccomType.class);

        when(offset1.getOccupancyType()).thenReturn(type1);
        when(offset2.getOccupancyType()).thenReturn(type2);
        when(offset3.getOccupancyType()).thenReturn(type1);

        List<CPConfigOffsetAccomType> offsets = Arrays.asList(offset1, offset2, offset3);
        List<Integer> occupantBucketIds = Collections.singletonList(1); // Only ID 1 allowed

        List<CPConfigOffsetAccomType> result = productManagementService.filterChildBucketOffsets(offsets, occupantBucketIds);

        assertEquals(2, result.size());
        assertTrue(result.contains(offset1));
        assertTrue(result.contains(offset3));
        assertFalse(result.contains(offset2));
    }


    @Test
    void filterChildBucketOffsets_shouldReturnEmptyList_whenNoMatches() {
        OccupancyType type = mock(OccupancyType.class);
        when(type.getId()).thenReturn(5);

        CPConfigOffsetAccomType offset = mock(CPConfigOffsetAccomType.class);
        when(offset.getOccupancyType()).thenReturn(type);

        List<CPConfigOffsetAccomType> offsets = Collections.singletonList(offset);
        List<Integer> occupantBucketIds = Arrays.asList(1, 2, 3);

        List<CPConfigOffsetAccomType> result = productManagementService.filterChildBucketOffsets(offsets, occupantBucketIds);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void groupOffsetsBySeason_shouldGroupUnderDefault_whenNameIsNull() {
        CPConfigOffsetAccomType offset1 = mock(CPConfigOffsetAccomType.class);
        CPConfigOffsetAccomType offset2 = mock(CPConfigOffsetAccomType.class);

        when(offset1.getName()).thenReturn(null);
        when(offset2.getName()).thenReturn(null);

        List<CPConfigOffsetAccomType> offsets = Arrays.asList(offset1, offset2);

        Map<String, List<CPConfigOffsetAccomType>> result = productManagementService.groupOffsetsBySeason(offsets);

        assertEquals(1, result.size());
        assertTrue(result.containsKey(DEFAULT));
        assertEquals(2, result.get(DEFAULT).size());
    }

    @Test
    void groupOffsetsBySeason_shouldGroupMixedNamesAndNulls() {
        CPConfigOffsetAccomType offset1 = mock(CPConfigOffsetAccomType.class);
        CPConfigOffsetAccomType offset2 = mock(CPConfigOffsetAccomType.class);
        CPConfigOffsetAccomType offset3 = mock(CPConfigOffsetAccomType.class);

        when(offset1.getName()).thenReturn("Spring");
        when(offset2.getName()).thenReturn(null);
        when(offset3.getName()).thenReturn("Spring");

        List<CPConfigOffsetAccomType> offsets = Arrays.asList(offset1, offset2, offset3);

        Map<String, List<CPConfigOffsetAccomType>> result = productManagementService.groupOffsetsBySeason(offsets);

        assertEquals(2, result.size());
        assertTrue(result.containsKey("Spring"));
        assertTrue(result.containsKey(DEFAULT));
        assertEquals(2, result.get("Spring").size());
        assertEquals(1, result.get(DEFAULT).size());
    }

    @Test
    void isRateShoppingAllLOSApplicable_whenRDLEnabled_andValidDTO_shouldReturnTrue() {
        Product product = new Product();
        WebrateTypeProductDTO dto = new WebrateTypeProductDTO();
        dto.setMinLOS(1);
        dto.setMaxLOS(365);

        boolean isRDLEnabled = true;

        boolean result = productManagementService.isRateShoppingAllLOSApplicable(isRDLEnabled, product, dto);
        assertTrue(result);
    }

    @Test
    void isRateShoppingAllLOSApplicable_whenRDLEnabled_andInvalidLOS_shouldReturnFalse() {
        Product product = new Product();
        WebrateTypeProductDTO dto = new WebrateTypeProductDTO();
        dto.setMinLOS(2);
        dto.setMaxLOS(15);

        boolean isRDLEnabled = true;

        boolean result = productManagementService.isRateShoppingAllLOSApplicable(isRDLEnabled, product, dto);
        assertFalse(result);
    }

    @Test
    void isRateShoppingAllLOSApplicable_whenRDLEnabled_andNullDTO_shouldReturnFalse() {
        Product product = new Product();
        boolean isRDLEnabled = true;

        boolean result = productManagementService.isRateShoppingAllLOSApplicable(isRDLEnabled, product, null);
        assertFalse(result);
    }

    @Test
    void isRateShoppingAllLOSApplicable_whenNotRDLEnabled_andLOSMinMaxAreMinus1_shouldReturnTrue() {
        Product product = new Product();
        product.setRateShoppingLOSMin(-1);
        product.setRateShoppingLOSMax(-1);
        boolean isRDLEnabled = false;

        boolean result = productManagementService.isRateShoppingAllLOSApplicable(isRDLEnabled, product, null);
        assertTrue(result);
    }

    @Test
    void isRateShoppingAllLOSApplicable_whenNotRDLEnabled_andLOSNotMinus1_shouldReturnFalse() {
        Product product = new Product();
        product.setRateShoppingLOSMin(1);
        product.setRateShoppingLOSMax(10);
        boolean isRDLEnabled = false;

        boolean result = productManagementService.isRateShoppingAllLOSApplicable(isRDLEnabled, product, null);
        assertFalse(result);
    }

    @Test
    void isRateShoppingAllLOSApplicable_whenNotRDLEnabled_andLOSNull_shouldReturnFalse() {
        Product product = new Product();
        boolean isRDLEnabled = false;
        boolean result = productManagementService.isRateShoppingAllLOSApplicable(isRDLEnabled, product, null);
        assertFalse(result);
    }


}