package com.ideas.tetris.pacman.services.groupmeetingsandevents.configurations;

import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.StalenessFlag;
import com.ideas.tetris.pacman.services.functionspace.configuration.dto.PackageDTO;
import com.ideas.tetris.pacman.services.functionspace.configuration.dto.PackageElementMapDTO;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpacePackageType;
import com.ideas.tetris.pacman.services.functionspace.configuration.packaging.dto.PackageElementDto;
import com.ideas.tetris.pacman.services.functionspace.configuration.packaging.dto.RevenueGroupDto;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.configurations.dto.Package;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.configurations.dto.*;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.input.GuestRoom;
import com.ideas.tetris.pacman.services.grouppricing.configuration.dto.ConferenceAndBanquetDto;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigAccomType;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryAssignment;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryAssignmentSeason;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationAncillaryStream;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationAncillaryService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.packaging.PackageService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.AlternateDateDto;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationCostGroup;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationDayOfStay;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationRoomType;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationAlternateDateService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.syncflags.service.SyncFlagService;
import com.ideas.tetris.pacman.services.userpreferences.DateFormatPreference;
import com.ideas.tetris.pacman.services.userpreferences.UserPreferenceService;
import com.ideas.tetris.pacman.services.userpreferences.UserPreferences;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.opends.sdk.DN;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.groupmeetingsandevents.configurations.EvaluationPropertyStateReason.PROCESSING_IN_PROGRESS;
import static com.ideas.tetris.pacman.services.groupmeetingsandevents.configurations.EvaluationPropertyStateReason.PROPERTY_IS_NOT_ONE_WAY;
import static com.ideas.tetris.platform.common.time.LocalDateUtils.toJavaLocalDate;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.toDate;
import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class GroupMeetingAndEventsConfigurationServiceTest {

    private final int PROPERTY_ID = 5;

    @Spy
    @InjectMocks
    private GroupMeetingAndEventsConfigurationService groupMeetingAndEventsConfigurationService;

    @Mock
    private GroupEvaluationService groupEvaluationService;

    @Mock
    private GroupPricingConfigurationService groupPricingConfigurationService;

    @Mock
    private GroupPricingConfigurationAncillaryService groupPricingConfigurationAncillaryService;

    @Mock
    private ConferenceAndBanquetService conferenceAndBanquetService;

    @Mock
    private PackageService packageService;

    @Mock
    private DateService dateService;

    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    private GroupEvaluationAlternateDateService groupEvaluationAlternateDateService;

    @Mock
    private PricingConfigurationService pricingConfigurationService;

    @Mock
    private RegulatorService regulatorService;

    @Mock
    private UserPreferenceService userPreferenceService;

    @Mock
    private PropertyService propertyService;

    @Mock
    private SyncFlagService syncService;

    @Mock
    AuthorizationService authorizationService;

    @Mock
    GlobalCrudServiceBean globalCrudService;

    @BeforeEach
    void setup() {
        RegulatorService testRegulatorService = new RegulatorService();
        RegulatorService.PropertyState regulatorPropertyState = testRegulatorService.new PropertyState();
        regulatorPropertyState.setReadOnly(false);
        when(regulatorService.getPropertyState()).thenReturn(regulatorPropertyState);
        when(groupEvaluationService.isPropertyOneWayOrTwoWay()).thenReturn(true);
        when(configParamsService.getBooleanParameterValue("pacman.feature.GroupWashByGroupEnabled"))
                .thenReturn(true);
        when(configParamsService.getBooleanParameterValue("pacman.feature.isGroupFloorOverrideEnabled"))
                .thenReturn(true);
    }

    @Test
    public void shouldGetMarketSegments_WhenToggle_IS_MARKET_SEGMENTS_SORTED_IN_DESCENDING_ORDER_FOR_GME_IsEnabled() {
        //GIVEN
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        List<MarketSegmentSummary> marketSegmentSummaries = new ArrayList<>();
        marketSegmentSummaries.add(createMarketSegmentSummaryWith(1, "CORP"));
        doReturn(marketSegmentSummaries).when(groupEvaluationService).getGroupMarketSegmentsInDescOrderByVolume(PROPERTY_ID);
        //WHEN
        List<MarketSegment> marketSegments = groupMeetingAndEventsConfigurationService.getMarketSegments();
        //THEN
        assertEquals(1, marketSegments.get(0).getId());
        assertEquals("CORP", marketSegments.get(0).getName());
    }

    @Test
    void shouldGetRoomTypes() {
        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(createAccomType(1, "DELUXE", "DELUXE", "DLX", "DLX"));
        accomTypes.add(createAccomType(2, "STANDARD", "STANDARD", "STD", "STD"));
        when(groupPricingConfigurationService.getAccomTypesConfiguredForRoomClassEvaluations()).thenReturn(accomTypes);

        List<RoomType> roomTypes = groupMeetingAndEventsConfigurationService.getRoomTypes();

        assertEquals(2, roomTypes.size());
        assertEquals(1, roomTypes.get(0).getId());
        assertEquals("DLX", roomTypes.get(0).getRoomTypeName());
        assertEquals("DELUXE", roomTypes.get(0).getRoomClassName());
        assertEquals(2, roomTypes.get(1).getId());
        assertEquals("STD", roomTypes.get(1).getRoomTypeName());
        assertEquals("STANDARD", roomTypes.get(1).getRoomClassName());
        verify(groupPricingConfigurationService).getAccomTypesConfiguredForRoomClassEvaluations();
    }

    @Test
    void shouldGetRoomTypesWhenAccomTypesAreEmpty() {
        List<AccomType> accomTypes = new ArrayList<>();
        when(groupPricingConfigurationService.getAccomTypesConfiguredForRoomClassEvaluations()).thenReturn(accomTypes);

        List<RoomType> roomTypes = groupMeetingAndEventsConfigurationService.getRoomTypes();

        assertEquals(0, roomTypes.size());
        verify(groupPricingConfigurationService).getAccomTypesConfiguredForRoomClassEvaluations();
    }

    @Test
    void shouldGetAncillaryRevenueStreamsWhenTypeIsAncillary() {
        List<GroupPricingConfigurationAncillaryStream> ancillaryRevenueStreams = List.of(createAncillaryRevenueStreams(1, "A1", BigDecimal.TEN));
        List<RevenueStream> revenueStreams = convertToRevenueStreamDto(ancillaryRevenueStreams);
        when(groupPricingConfigurationAncillaryService.getGroupPricingConfigurationAncillaries()).thenReturn(ancillaryRevenueStreams);
        when(groupPricingConfigurationAncillaryService.getSeasons(true)).thenReturn(emptyList());

        List<RevenueStream> result = groupMeetingAndEventsConfigurationService.getRevenueStreamsBy(RevenueStreamType.ANCILLARY.name());

        assertEquals(revenueStreams.size(), result.size());
        assertEquals(revenueStreams.get(0).getId(), result.get(0).getId());
        assertEquals(revenueStreams.get(0).getName(), result.get(0).getName());
        assertEquals(RevenueStreamType.ANCILLARY.name(), result.get(0).getType());
        verify(groupPricingConfigurationAncillaryService).getGroupPricingConfigurationAncillaries();
    }

    @Test
    void shouldGetAncillaryRevenueStreamsWithSeasonsWhenTypeIsAncillary() {
        List<GroupPricingConfigurationAncillaryStream> ancillaryRevenueStreams = List.of(createAncillaryRevenueStreams(1, "A1", new BigDecimal("10.00000000")));
        List<GroupPricingConfigurationAncillaryAssignmentSeason> ancillarySeasons = createAncillarySeasonsFor(ancillaryRevenueStreams);
        when(groupPricingConfigurationAncillaryService.getGroupPricingConfigurationAncillaries()).thenReturn(ancillaryRevenueStreams);
        when(groupPricingConfigurationAncillaryService.getSeasons(true)).thenReturn(ancillarySeasons);

        List<RevenueStream> result = groupMeetingAndEventsConfigurationService.getRevenueStreamsBy(RevenueStreamType.ANCILLARY.name());

        assertEquals(ancillaryRevenueStreams.size(), result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals("A1", result.get(0).getName());
        assertEquals("ANCILLARY", result.get(0).getType());
        assertEquals(2, result.get(0).getSeasonAssignments().size());
        assertTrue(result.get(0).getSeasonAssignments().get(0).getIsYearRound());
        assertNull(result.get(0).getSeasonAssignments().get(0).getStartDate());
        assertNull(result.get(0).getSeasonAssignments().get(0).getEndDate());
        assertEquals("CORP", result.get(0).getSeasonAssignments().get(0).getMarketSegment());
        assertEquals(new BigDecimal("10.00000"), result.get(0).getSeasonAssignments().get(0).getProfitPercentage());
        assertEquals(new BigDecimal("150.00000"), result.get(0).getSeasonAssignments().get(0).getRevenuePerRoomNight());
        assertFalse(result.get(0).getSeasonAssignments().get(1).getIsYearRound());
        assertEquals("2024-08-21", result.get(0).getSeasonAssignments().get(1).getStartDate());
        assertEquals("2024-08-28", result.get(0).getSeasonAssignments().get(1).getEndDate());
        assertEquals("CORP", result.get(0).getSeasonAssignments().get(1).getMarketSegment());
        assertEquals(new BigDecimal("10.00000"), result.get(0).getSeasonAssignments().get(1).getProfitPercentage());
        assertEquals(new BigDecimal("150.00000"), result.get(0).getSeasonAssignments().get(1).getRevenuePerRoomNight());
    }

    @Test
    void shouldGetAncillaryRevenueStreamsWhenNoSeasonsArePresent() {
        List<GroupPricingConfigurationAncillaryStream> ancillaryRevenueStreams = List.of(createAncillaryRevenueStreams(1, "A1", new BigDecimal("10.00000000")));
        when(groupPricingConfigurationAncillaryService.getGroupPricingConfigurationAncillaries()).thenReturn(ancillaryRevenueStreams);
        when(groupPricingConfigurationAncillaryService.getSeasons(true)).thenReturn(emptyList());

        List<RevenueStream> result = groupMeetingAndEventsConfigurationService.getRevenueStreamsBy(RevenueStreamType.ANCILLARY.name());

        assertEquals(ancillaryRevenueStreams.size(), result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals("A1", result.get(0).getName());
        assertEquals("ANCILLARY", result.get(0).getType());
        assertEquals(new BigDecimal("10.00000"), result.get(0).getProfitPercentage());
        assertTrue(result.get(0).getSeasonAssignments().isEmpty());
    }

    @Test
    void shouldGetConferenceAndBanquetsWhenTypeIsConferenceAndBanquet() {
        List<ConferenceAndBanquetDto> conferenceAndBanquets = List.of(createConferenceAndBanquets(1, "Food", new BigDecimal("10.000000000")));
        when(conferenceAndBanquetService.getConferenceAndBanquets()).thenReturn(conferenceAndBanquets);

        List<RevenueStream> result = groupMeetingAndEventsConfigurationService.getRevenueStreamsBy(RevenueStreamType.CONFERENCE_AND_BANQUET.name());

        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals("Food", result.get(0).getName());
        assertEquals(new BigDecimal("10.00000"), result.get(0).getProfitPercentage());
        assertEquals(RevenueStreamType.CONFERENCE_AND_BANQUET.name(), result.get(0).getType());
        verify(conferenceAndBanquetService).getConferenceAndBanquets();
    }

    @Test
    void shouldGetAncillaryAndConferenceAndBanquetRevenueStreamsWhenTypeNotProvided() {
        List<GroupPricingConfigurationAncillaryStream> ancillaryRevenueStreams = List.of(createAncillaryRevenueStreams(1, "A1", BigDecimal.TEN));
        List<ConferenceAndBanquetDto> conferenceAndBanquets = List.of(createConferenceAndBanquets(2, "Food", new BigDecimal("10.000000000")));
        List<RevenueStream> revenueStreams = convertToRevenueStreamDto(ancillaryRevenueStreams);
        revenueStreams.addAll(convertConfAndBanqToRevenueStreamDto(conferenceAndBanquets));
        when(conferenceAndBanquetService.getConferenceAndBanquets()).thenReturn(conferenceAndBanquets);
        when(groupPricingConfigurationAncillaryService.getGroupPricingConfigurationAncillaries()).thenReturn(ancillaryRevenueStreams);

        List<RevenueStream> result = groupMeetingAndEventsConfigurationService.getRevenueStreamsBy(null);

        assertEquals(1, result.get(0).getId());
        assertEquals("A1", result.get(0).getName());
        assertEquals(RevenueStreamType.ANCILLARY.name(), result.get(0).getType());
        assertEquals(2, result.get(1).getId());
        assertEquals("Food", result.get(1).getName());
        assertEquals(RevenueStreamType.CONFERENCE_AND_BANQUET.name(), result.get(1).getType());
        verify(conferenceAndBanquetService).getConferenceAndBanquets();
        verify(groupPricingConfigurationAncillaryService).getGroupPricingConfigurationAncillaries();
    }

    @Test
    void shouldGetCosts() {
        List<Cost> costs = groupMeetingAndEventsConfigurationService.getCosts();

        assertNotNull(costs);
        assertEquals(5, costs.size());
        assertEquals(GroupEvaluationCostGroup.COMPLIMENTARY, costs.get(0).getCost());
        assertTrue(costs.get(0).getIsCostTypeApplicable());
        assertEquals(GroupEvaluationCostGroup.DISCOUNTED, costs.get(1).getCost());
        assertTrue(costs.get(1).getIsCostTypeApplicable());
        assertEquals(GroupEvaluationCostGroup.REBATES, costs.get(2).getCost());
        assertFalse(costs.get(2).getIsCostTypeApplicable());
        assertEquals(GroupEvaluationCostGroup.COMMISSION, costs.get(3).getCost());
        assertFalse(costs.get(3).getIsCostTypeApplicable());
        assertEquals(GroupEvaluationCostGroup.OTHER_CONCESSIONS, costs.get(4).getCost());
        assertFalse(costs.get(4).getIsCostTypeApplicable());
    }

    @Test
    void shouldGetAllActivePackagesWithCommissionTypeFull() {
        List<PackageDTO> packageDtos = List.of(
                createPackage(1, "Weekend", true, TenantStatusEnum.ACTIVE,
                        createPackageType(1, "Family"), List.of(
                                createPackageElementMap(1, "Lunch", true),
                                createPackageElementMap(2, "Dinner", true))),
                createPackage(2, "Holiday", false, TenantStatusEnum.INACTIVE,
                        createPackageType(8, "Seasonal"), List.of(createPackageElementMap(1, "Lunch", true))));
        when(packageService.getAllPackagesSortedByName()).thenReturn(packageDtos);

        List<Package> packages = groupMeetingAndEventsConfigurationService.getActivePackages();

        assertEquals(1, packages.size());
        Package package1 = packages.get(0);
        assertPackageDetails(package1);
        assertEquals(PackageCommissionType.FULL, package1.getCommissionable());
        verify(packageService).getAllPackagesSortedByName();
    }

    @Test
    void shouldGetAllActivePackagesWithCommissionTypePartial() {
        List<PackageDTO> packageDtos = List.of(
                createPackage(1, "Weekend", true, TenantStatusEnum.ACTIVE,
                        createPackageType(1, "Family"), List.of(
                                createPackageElementMap(1, "Lunch", true),
                                createPackageElementMap(2, "Dinner", false))),
                createPackage(2, "Holiday", false, TenantStatusEnum.INACTIVE,
                        createPackageType(8, "Seasonal"), List.of(createPackageElementMap(1, "Lunch", true))));
        when(packageService.getAllPackagesSortedByName()).thenReturn(packageDtos);

        List<Package> packages = groupMeetingAndEventsConfigurationService.getActivePackages();

        assertEquals(1, packages.size());
        Package package1 = packages.get(0);
        assertPackageDetails(package1);
        assertEquals(PackageCommissionType.PARTIAL, package1.getCommissionable());
        verify(packageService).getAllPackagesSortedByName();
    }

    @Test
    void shouldGetAllActivePackagesWithCommissionTypeNone() {
        List<PackageDTO> packageDtos = List.of(
                createPackage(1, "Weekend", true, TenantStatusEnum.ACTIVE,
                        createPackageType(1, "Family"), List.of(
                                createPackageElementMap(1, "Lunch", false),
                                createPackageElementMap(2, "Dinner", false))),
                createPackage(2, "Holiday", false, TenantStatusEnum.INACTIVE,
                        createPackageType(8, "Seasonal"), List.of(createPackageElementMap(1, "Lunch", true))));
        when(packageService.getAllPackagesSortedByName()).thenReturn(packageDtos);

        List<Package> packages = groupMeetingAndEventsConfigurationService.getActivePackages();

        assertEquals(1, packages.size());
        Package package1 = packages.get(0);
        assertPackageDetails(package1);
        assertEquals(PackageCommissionType.NONE, package1.getCommissionable());
        verify(packageService).getAllPackagesSortedByName();
    }

    @Test
    void shouldGetValidDateRangeInGmeConfigurationsForStandardWindow() {
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(false).when(configParamsService).getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
        verify(configParamsService, never()).getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value());
    }

    @Test
    void shouldGetValidDateRangeInGmeConfigurationsForExtendedWindow(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(true).when(configParamsService).getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND);
        doReturn(1094).when(configParamsService).getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value());
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2014-12-16"), gmeConfiguration.getValidDateRange().getEndDate());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService, never()).getEvaluationWindowEndDate();
        verify(configParamsService).getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value());
    }

    @Test
    void shouldGetMaxNumberOfNightsForGroupPricingInGmeConfigurations(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(false).when(configParamsService).getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertEquals(90, gmeConfiguration.getMaxNumberOfNights());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldGetMaxNumberOfNightsWhenGroupEvaluationUptoForecastWindowEnabled(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(false).when(configParamsService).getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND);
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_GROUP_EVALUATION_UPTO_FORECAST_WINDOW);
        doReturn(364).when(dateService).getForecastWindowOffsetBDE();
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertEquals(364, gmeConfiguration.getMaxNumberOfNights());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
        verify(dateService).getForecastWindowOffsetBDE();
    }

    @Test
    void shouldGetPackagePricingEnabledInGmeConfigurationsWhenBothFunctionSpaceAndGroupPricingPackageEnabled(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(false).when(configParamsService).getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND);
        doReturn(true).when(configParamsService).getBooleanParameterValue(GUIConfigParamName.IS_FUNCTION_SPACE_PACKAGE_ENABLED);
        doReturn(true).when(configParamsService).getBooleanParameterValue(PreProductionConfigParamName.IS_GROUP_PRICING_PACKAGE_ENABLED);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertEquals(90, gmeConfiguration.getMaxNumberOfNights());
        assertTrue(gmeConfiguration.isPackagePricingEnabled());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldGetPackagePricingAsDisabledInGmeConfigurationsWhenAnyOfFunctionSpaceAndGroupPricingPackageIsNotEnabled(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(false).when(configParamsService).getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND);
        doReturn(false).when(configParamsService).getBooleanParameterValue(GUIConfigParamName.IS_FUNCTION_SPACE_PACKAGE_ENABLED);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertEquals(90, gmeConfiguration.getMaxNumberOfNights());
        assertFalse(gmeConfiguration.isPackagePricingEnabled());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldGetShowRoomClassCapacityForRCEvaluationInGmeConfigurations(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(true).when(configParamsService).getBooleanParameterValue(GUIConfigParamName.IS_SHOW_ACCOM_CLASS_CAPACITY_FOR_RC_EVAL);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertTrue(gmeConfiguration.isShowRoomClassCapacityForRCEvaluation());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldGetRestrictROHtoRCEvaluationMethodInGmeConfigurations(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.RESTRICT_ROH_TO_RC_EVALUATION_METHOD);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertTrue(gmeConfiguration.isRestrictROHtoRCEvaluationMethod());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldEnableVATForEvaluationResultInGmeConfigurations(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(true).when(configParamsService).getBooleanParameterValue(PreProductionConfigParamName.SHOW_INCLUDE_VAT_CHECKBOX_FUNCTION_SPACE_GROUP_PRICING_EVAL);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertTrue(gmeConfiguration.isEnableVATForEvaluationResult());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldEnableEvaluationResultsByOccupancyDateInGmeConfigurations(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_RESULTS_BY_OCCUPANCY_DATE);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertTrue(gmeConfiguration.isEnableEvaluationResultsByOccupancyDate());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldEnableSalesAndCateringUrlRedirectInGmeConfigurations(){
        //GIVEN
        doReturn(toDate("2012-01-01")).when(dateService).getCaughtUpDate();
        doReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31))).when(groupEvaluationService).getEvaluationWindowEndDate();
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_URL_REDIRECT_ENABLED);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertTrue(gmeConfiguration.isEnableSalesAndCateringUrlRedirect());
        verify(dateService).getCaughtUpDate();
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldGetYieldCurrencyCode() {
        // GIVEN
        String expectedCurrencyCode = "USD";
        when(dateService.getCaughtUpDate()).thenReturn(toDate("2012-01-01"));
        when(groupEvaluationService.getEvaluationWindowEndDate()).thenReturn(LocalDateUtils.toJodaLocalDate(LocalDate.of(2012, 12, 31)));
        when(configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE)).thenReturn(expectedCurrencyCode);
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertEquals(toDate("2012-01-01"), gmeConfiguration.getValidDateRange().getStartDate());
        assertEquals(toDate("2012-12-31"), gmeConfiguration.getValidDateRange().getEndDate());
        assertEquals(expectedCurrencyCode, gmeConfiguration.getCurrencyCode());
        verify(configParamsService).getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
        verify(groupEvaluationService).getEvaluationWindowEndDate();
    }

    @Test
    void shouldGetCaughtUpDate() {
        // GIVEN
        LocalDateTime expectedDateTime = LocalDateTime.of(2023, 12, 31, 10, 15, 30);
        when(dateService.getCaughtUpLocalDateTime()).thenReturn(expectedDateTime);
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertEquals(expectedDateTime, gmeConfiguration.getCaughtUpDate());
        verify(dateService).getCaughtUpLocalDateTime();

    }

    @Test
    void shouldGetPropertySyncStateAsTrueWhenSyncFlagsAvailable() {
        // GIVEN
        List<StalenessFlag> syncFlags = new ArrayList<>();
        syncFlags.add(new StalenessFlag("1", "Optimization in progress", true));
        when(syncService.getStalenessFlags()).thenReturn(syncFlags);
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertTrue(gmeConfiguration.isSyncState());
    }

    @Test
    void shouldGetPropertySyncStateAsFalseWhenSyncFlagsNotAvailable() {
        // GIVEN
        when(syncService.getStalenessFlags()).thenReturn(null);
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertFalse(gmeConfiguration.isSyncState());
    }

    @Test
    void shouldGetUserDisplayPreferenceToggleValue() {
        // GIVEN
        when(groupEvaluationService.isUserDisplayPreferenceForGMEToggleEnabled()).thenReturn(true);
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertTrue(gmeConfiguration.isUserDisplayPreferenceForGMEEnabled());
    }

    @Test
    void shouldGetUserPreferredDateFormat() {
        // GIVEN
        String expectedPreferredDateFormat = "dd-mmm-yyyy";
        UserPreferences mockUserPreferences = mock(UserPreferences.class);
        DateFormatPreference mockDateFormat = mock(DateFormatPreference.class);
        doReturn(mockUserPreferences).when(userPreferenceService).getUserPreferences();
        doReturn(mockDateFormat).when(mockUserPreferences).getDateFormat();
        doReturn(expectedPreferredDateFormat).when(mockDateFormat).getFormatString();
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertEquals(expectedPreferredDateFormat, gmeConfiguration.getUserPreferredDateFormat());
        verify(userPreferenceService).getUserPreferences();
    }

    @Test
    void shouldReturnDefaultForNullDateFormatInUserPreferences() {
        // GIVEN
        String expectedPreferredDateFormat = "dd-mmm-yyyy";
        UserPreferences mockUserPreferences = mock(UserPreferences.class);
        DateFormatPreference mockDateFormat = mock(DateFormatPreference.class);
        doReturn(mockUserPreferences).when(userPreferenceService).getUserPreferences();
        doReturn(mockDateFormat).when(mockUserPreferences).getDateFormat();
        doReturn(null).when(mockDateFormat).getFormatString();
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertEquals(expectedPreferredDateFormat, gmeConfiguration.getUserPreferredDateFormat());
        verify(userPreferenceService).getUserPreferences();
    }

    @Test
    void shouldReturnDefaultForMissingDateFormatInUserPreferences() {
        // GIVEN
        String expectedPreferredDateFormat = "dd-mmm-yyyy";
        UserPreferences mockUserPreferences = mock(UserPreferences.class);
        doReturn(mockUserPreferences).when(userPreferenceService).getUserPreferences();
        doReturn(null).when(mockUserPreferences).getDateFormat();
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertEquals(expectedPreferredDateFormat, gmeConfiguration.getUserPreferredDateFormat());
        verify(userPreferenceService).getUserPreferences();
    }

    @Test
    void shouldReturnDefaultForMissingUserPreferences() {
        // GIVEN
        String expectedPreferredDateFormat = "dd-mmm-yyyy";
        doReturn(null).when(userPreferenceService).getUserPreferences();
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertEquals(expectedPreferredDateFormat, gmeConfiguration.getUserPreferredDateFormat());
        verify(userPreferenceService).getUserPreferences();
    }

    @Test
    void shouldReturnNullWhenPropertyTimeZoneIsNullForTimeZoneDisplayName() {
        // GIVEN
        doReturn(null).when(propertyService).getPropertyTimeZone();
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertNull(gmeConfiguration.getPropertyTimeZone());
        verify(propertyService).getPropertyTimeZone();
    }

    @Test
    void shouldGetTimeZoneDisplayName() {
        // GIVEN
        TimeZone mockTimeZone = mock(TimeZone.class);
        String expectedTimeZoneDisplayName = "CDT";
        Date mockDate = mock(Date.class);
        doReturn(mockTimeZone).when(propertyService).getPropertyTimeZone();
        doReturn(mockDate).when(dateService).getCaughtUpDate();
        doReturn(expectedTimeZoneDisplayName).when(dateService).getPropertyTimeZoneDisplayName(mockDate, mockTimeZone, TimeZone.SHORT);
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertEquals(expectedTimeZoneDisplayName, gmeConfiguration.getPropertyTimeZone());
        verify(propertyService).getPropertyTimeZone();
        verify(dateService).getPropertyTimeZoneDisplayName(mockDate, mockTimeZone, TimeZone.SHORT);
    }

    @Test
    void shouldReturnNullWhenCaughtUpDateIsNullForTimeZoneDisplayName() {
        // GIVEN
        TimeZone mockTimeZone = mock(TimeZone.class);
        doReturn(mockTimeZone).when(propertyService).getPropertyTimeZone();
        doReturn(null).when(dateService).getCaughtUpDate();
        // WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        // THEN
        assertNull(gmeConfiguration.getPropertyTimeZone());
        verify(dateService).getPropertyTimeZoneDisplayName(null, mockTimeZone, TimeZone.SHORT);
    }
    @Test
    void shouldGetAllowedPropertyStateInGMEConfigurations() {
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();

        PropertyStateForGME propertyState = gmeConfiguration.getPropertyState();
        assertTrue(propertyState.isEvaluationAllowed());
        assertNull(propertyState.getReason());
    }

    @Test
    void shouldGetLessThanOneWayPropertyStateInGMEConfigurations() {
        when(groupEvaluationService.isPropertyOneWayOrTwoWay()).thenReturn(false);

        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();

        PropertyStateForGME propertyState = gmeConfiguration.getPropertyState();
        assertFalse(propertyState.isEvaluationAllowed());
        assertEquals(PROPERTY_IS_NOT_ONE_WAY, propertyState.getReason());
    }

    @Test
    void shouldGetReadOnlyPropertyStateInGMEConfigurations() {
        RegulatorService testRegulatorService = new RegulatorService();
        RegulatorService.PropertyState regulatorPropertyState = testRegulatorService.new PropertyState();
        regulatorPropertyState.setReadOnly(true);
        when(regulatorService.getPropertyState()).thenReturn(regulatorPropertyState);

        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();

        PropertyStateForGME propertyState = gmeConfiguration.getPropertyState();
        assertFalse(propertyState.isEvaluationAllowed());
        assertEquals(PROCESSING_IN_PROGRESS, propertyState.getReason());
    }

    @Test
    void shouldBeAbleCalculateEstimatedDisplacementsForRCEvaluation() {

        List<Date> dates = Arrays.asList(toDate("2024-10-20"), toDate("2024-10-21"),
                toDate("2024-10-22"), toDate("2024-10-23"), toDate("2024-10-24"), toDate("2024-10-25"));
        List<GuestRoom> guestRooms = new ArrayList<>();
        guestRooms.add(new GuestRoom(toJavaLocalDate(toDate("2024-10-20")), 3, 11, "QNES", "SUPERIOR"));
        guestRooms.add(new GuestRoom(toJavaLocalDate(toDate("2024-10-20")), 4, 12, "QNEX", "EXECUTIVE"));
        guestRooms.add(new GuestRoom(toJavaLocalDate(toDate("2024-10-21")), 5, 11, "QNES", "SUPERIOR"));
        guestRooms.add(new GuestRoom(toJavaLocalDate(toDate("2024-10-21")), 6, 12, "QNEX", "EXECUTIVE"));
        DisplacementRequest displacementRequest = new DisplacementRequest(dates, guestRooms);

        List<AccomType> accomTypes = new ArrayList<>();
        accomTypes.add(createAccomTypeWith(11, 21));
        accomTypes.add(createAccomTypeWith(12, 22));
        when(groupPricingConfigurationService.getAccomTypesConfiguredForRoomClassEvaluations()).thenReturn(accomTypes);

        ArgumentCaptor<Map> roomClassRoomTypeArgumentCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<List> datesArgumentCaptor = ArgumentCaptor.forClass(List.class);

        List<AlternateDateDto> alternateDates = new ArrayList();
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-20")), new BigDecimal("1.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-21")), new BigDecimal("0.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-22")), new BigDecimal("2.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-23")), new BigDecimal("3.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-24")), new BigDecimal("1.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-25")), new BigDecimal("2.00")));
        when(groupEvaluationAlternateDateService.getRCAlternateDates(any(List.class), any(Map.class))).thenReturn(alternateDates);
        //WHEN
        List<EstimatedDisplacement> estimatedDisplacements = groupMeetingAndEventsConfigurationService.calculateEstimatedDisplacements(displacementRequest);
        //THEN
        verify(groupEvaluationAlternateDateService).getRCAlternateDates(datesArgumentCaptor.capture(), roomClassRoomTypeArgumentCaptor.capture());
        List<org.joda.time.LocalDate> alternateDatesActual = datesArgumentCaptor.getValue();
        Map<Integer, List<GroupEvaluationRoomType>> roomClassRoomTypeActual = roomClassRoomTypeArgumentCaptor.getValue();

        assertEquals(toDate("2024-10-20"), alternateDatesActual.get(0).toDate());
        assertEquals(toDate("2024-10-21"), alternateDatesActual.get(1).toDate());
        assertEquals(toDate("2024-10-22"), alternateDatesActual.get(2).toDate());
        assertEquals(toDate("2024-10-23"), alternateDatesActual.get(3).toDate());
        assertEquals(toDate("2024-10-24"), alternateDatesActual.get(4).toDate());
        assertEquals(toDate("2024-10-25"), alternateDatesActual.get(5).toDate());

        assertEquals(11, roomClassRoomTypeActual.get(21).get(0).getRoomType().getId());
        assertEquals(1, roomClassRoomTypeActual.get(21).get(0).getGroupEvaluationRoomTypeDayOfStays().get(0).getDayOfStay());
        assertEquals(3, roomClassRoomTypeActual.get(21).get(0).getGroupEvaluationRoomTypeDayOfStays().get(0).getNumberOfRooms());

        assertEquals(12, roomClassRoomTypeActual.get(22).get(0).getRoomType().getId());
        assertEquals(1, roomClassRoomTypeActual.get(22).get(0).getGroupEvaluationRoomTypeDayOfStays().get(0).getDayOfStay());
        assertEquals(4, roomClassRoomTypeActual.get(22).get(0).getGroupEvaluationRoomTypeDayOfStays().get(0).getNumberOfRooms());

        assertEquals(11, roomClassRoomTypeActual.get(21).get(1).getRoomType().getId());
        assertEquals(2, roomClassRoomTypeActual.get(21).get(1).getGroupEvaluationRoomTypeDayOfStays().get(0).getDayOfStay());
        assertEquals(5, roomClassRoomTypeActual.get(21).get(1).getGroupEvaluationRoomTypeDayOfStays().get(0).getNumberOfRooms());

        assertEquals(12, roomClassRoomTypeActual.get(22).get(1).getRoomType().getId());
        assertEquals(2, roomClassRoomTypeActual.get(22).get(1).getGroupEvaluationRoomTypeDayOfStays().get(0).getDayOfStay());
        assertEquals(6, roomClassRoomTypeActual.get(22).get(1).getGroupEvaluationRoomTypeDayOfStays().get(0).getNumberOfRooms());

        assertEquals(toDate("2024-10-20"), estimatedDisplacements.get(0).getDate());
        assertEquals(new BigDecimal("1.00"), estimatedDisplacements.get(0).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-21"), estimatedDisplacements.get(1).getDate());
        assertEquals(new BigDecimal("0.00"), estimatedDisplacements.get(1).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-22"), estimatedDisplacements.get(2).getDate());
        assertEquals(new BigDecimal("2.00"), estimatedDisplacements.get(2).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-23"), estimatedDisplacements.get(3).getDate());
        assertEquals(new BigDecimal("3.00"), estimatedDisplacements.get(3).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-24"), estimatedDisplacements.get(4).getDate());
        assertEquals(new BigDecimal("1.00"), estimatedDisplacements.get(4).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-25"), estimatedDisplacements.get(5).getDate());
        assertEquals(new BigDecimal("2.00"), estimatedDisplacements.get(5).getEstimatedDisplacement());
    }

    @Test
    void shouldBeAbleCalculateEstimatedDisplacementsForROHEvaluation() {

        List<Date> dates = Arrays.asList(toDate("2024-10-20"), toDate("2024-10-21"),
                toDate("2024-10-22"), toDate("2024-10-23"), toDate("2024-10-24"), toDate("2024-10-25"));
        List<GuestRoom> guestRooms = new ArrayList<>();
        guestRooms.add(new GuestRoom(toJavaLocalDate(toDate("2024-10-20")), 3, null, null, null));
        guestRooms.add(new GuestRoom(toJavaLocalDate(toDate("2024-10-21")), 5, null, null, null));
        DisplacementRequest displacementRequest = new DisplacementRequest(dates, guestRooms);

        ArgumentCaptor<List> dayOfStayArgumentCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<List> datesArgumentCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<List> propertyIdArgumentCaptor = ArgumentCaptor.forClass(List.class);

        List<AlternateDateDto> alternateDates = new ArrayList();
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-20")), new BigDecimal("1.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-21")), new BigDecimal("0.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-22")), new BigDecimal("2.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-23")), new BigDecimal("3.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-24")), new BigDecimal("1.00")));
        alternateDates.add(new AlternateDateDto(LocalDateUtils.fromDate(toDate("2024-10-25")), new BigDecimal("2.00")));
        when(groupEvaluationAlternateDateService.getROHAlternateDates(any(List.class), any(List.class), any(List.class))).thenReturn(alternateDates);
        //WHEN
        List<EstimatedDisplacement> estimatedDisplacements = groupMeetingAndEventsConfigurationService.calculateEstimatedDisplacements(displacementRequest);
        //THEN
        verify(groupEvaluationAlternateDateService).getROHAlternateDates(datesArgumentCaptor.capture(), propertyIdArgumentCaptor.capture(), dayOfStayArgumentCaptor.capture());
        List<org.joda.time.LocalDate> alternateDatesActual = datesArgumentCaptor.getValue();

        assertEquals(toDate("2024-10-20"), alternateDatesActual.get(0).toDate());
        assertEquals(toDate("2024-10-21"), alternateDatesActual.get(1).toDate());
        assertEquals(toDate("2024-10-22"), alternateDatesActual.get(2).toDate());
        assertEquals(toDate("2024-10-23"), alternateDatesActual.get(3).toDate());
        assertEquals(toDate("2024-10-24"), alternateDatesActual.get(4).toDate());
        assertEquals(toDate("2024-10-25"), alternateDatesActual.get(5).toDate());

        List<GroupEvaluationDayOfStay> dayOfStays = dayOfStayArgumentCaptor.getValue();
        assertEquals(1, dayOfStays.get(0).getDayOfStay());
        assertEquals(3, dayOfStays.get(0).getNumberOfRooms());

        assertEquals(2, dayOfStays.get(1).getDayOfStay());
        assertEquals(5, dayOfStays.get(1).getNumberOfRooms());

        assertEquals(toDate("2024-10-20"), estimatedDisplacements.get(0).getDate());
        assertEquals(new BigDecimal("1.00"), estimatedDisplacements.get(0).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-21"), estimatedDisplacements.get(1).getDate());
        assertEquals(new BigDecimal("0.00"), estimatedDisplacements.get(1).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-22"), estimatedDisplacements.get(2).getDate());
        assertEquals(new BigDecimal("2.00"), estimatedDisplacements.get(2).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-23"), estimatedDisplacements.get(3).getDate());
        assertEquals(new BigDecimal("3.00"), estimatedDisplacements.get(3).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-24"), estimatedDisplacements.get(4).getDate());
        assertEquals(new BigDecimal("1.00"), estimatedDisplacements.get(4).getEstimatedDisplacement());

        assertEquals(toDate("2024-10-25"), estimatedDisplacements.get(5).getDate());
        assertEquals(new BigDecimal("2.00"), estimatedDisplacements.get(5).getEstimatedDisplacement());
    }

    @Test
    void shouldReturnConfigurationsRequiredForEvaluation(){
        //GIVEN
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        doReturn(false).when(pricingConfigurationService).isBaseAccomTypeSetupComplete();
        doReturn(true).when(pricingConfigurationService).isPerRoomServicingCostAvailableForAllRoomClass();
        doReturn(true).when(pricingConfigurationService).isCeilingFloorGroupComplete(PROPERTY_ID);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertNotNull(gmeConfiguration.getConfigurationStatusRequiredForEvaluation());
        assertConfigurationsRequiredForEvaluation(gmeConfiguration.getConfigurationStatusRequiredForEvaluation());
        verify(pricingConfigurationService).isBaseAccomTypeSetupComplete();
        verify(pricingConfigurationService).isPerRoomServicingCostAvailableForAllRoomClass();
        verify(pricingConfigurationService).isCeilingFloorGroupComplete(PROPERTY_ID);
    }

    @Test
    void shouldReturnRulesConfigurationStatusAsNoAccessWhenFeatureToggleIsNotEnabled(){
        //GIVEN
        doReturn(false).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        //WHEN
        String rulesConfigurationPermission = groupMeetingAndEventsConfigurationService.getRulesConfigurationPermission();
        //THEN
        assertEquals("NO_ACCESS", rulesConfigurationPermission);
    }

    @Test
    void shouldReturnRulesConfigurationStatusAsReadWriteWhenUserHasAccessToPropertyAndGroupPricingAndRulesConfiguration(){
        //GIVEN
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        setUpTetrisPrincipleForTest();
        doReturn(5).when(groupMeetingAndEventsConfigurationService).getPropertyIdFromWorkContext();
        doReturn(true).when(authorizationService).userHasAccessToProperty(5);
        doReturn("readWrite").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.GROUP_PRICING_EVALUATION);
        doReturn("readWrite").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.RULES_CONFIGURATION);
        groupMeetingAndEventsConfigurationService.globalCrudService = this.globalCrudService;
        //WHEN
        String rulesConfigurationPermission = groupMeetingAndEventsConfigurationService.getRulesConfigurationPermission();
        //THEN
        assertEquals("READ_WRITE", rulesConfigurationPermission);
    }

    @Test
    void shouldReturnRulesConfigurationStatusAsReadWriteWhenUserHasAccessToPropertyAndReadOnlyAccessToGroupPricingAndWriteAccessToRulesConfiguration(){
        //GIVEN
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        doNothing().when(groupMeetingAndEventsConfigurationService).setUpTetrisPrincipleWithUser();
        doReturn(5).when(groupMeetingAndEventsConfigurationService).getPropertyIdFromWorkContext();
        doReturn(true).when(authorizationService).userHasAccessToProperty(5);
        doReturn("readOnly").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.GROUP_PRICING_EVALUATION);
        doReturn("readWrite").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.RULES_CONFIGURATION);
        groupMeetingAndEventsConfigurationService.globalCrudService = this.globalCrudService;
        //WHEN
        String rulesConfigurationPermission = groupMeetingAndEventsConfigurationService.getRulesConfigurationPermission();
        //THEN
        assertEquals("READ_WRITE", rulesConfigurationPermission);
    }

    @Test
    void shouldReturnRulesConfigurationStatusAsReadWhenUserHasAccessToPropertyAndWriteAccessToGroupPricingAndReadOnlyAccessToRulesConfiguration(){
        //GIVEN
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        doNothing().when(groupMeetingAndEventsConfigurationService).setUpTetrisPrincipleWithUser();
        doReturn(5).when(groupMeetingAndEventsConfigurationService).getPropertyIdFromWorkContext();
        doReturn(true).when(authorizationService).userHasAccessToProperty(5);
        doReturn("readWrite").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.GROUP_PRICING_EVALUATION);
        doReturn("readOnly").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.RULES_CONFIGURATION);
        groupMeetingAndEventsConfigurationService.globalCrudService = this.globalCrudService;
        //WHEN
        String rulesConfigurationPermission = groupMeetingAndEventsConfigurationService.getRulesConfigurationPermission();
        //THEN
        assertEquals("READ", rulesConfigurationPermission);
    }

    @Test
    void shouldReturnRulesConfigurationStatusAsReadWhenUserHasAccessToPropertyAndReadOnlyAccessToGroupPricingAndReadOnlyAccessToRulesConfiguration(){
        //GIVEN
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        doNothing().when(groupMeetingAndEventsConfigurationService).setUpTetrisPrincipleWithUser();
        doReturn(5).when(groupMeetingAndEventsConfigurationService).getPropertyIdFromWorkContext();
        doReturn(true).when(authorizationService).userHasAccessToProperty(5);
        doReturn("readOnly").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.GROUP_PRICING_EVALUATION);
        doReturn("readOnly").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.RULES_CONFIGURATION);
        groupMeetingAndEventsConfigurationService.globalCrudService = this.globalCrudService;
        //WHEN
        String rulesConfigurationPermission = groupMeetingAndEventsConfigurationService.getRulesConfigurationPermission();
        //THEN
        assertEquals("READ", rulesConfigurationPermission);
    }

    @Test
    void shouldReturnRulesConfigurationStatusAsNoAccessWhenUserHasAccessToPropertyAndNoAccessToGroupPricingAndWriteAccessToRulesConfiguration(){
        //GIVEN
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        doNothing().when(groupMeetingAndEventsConfigurationService).setUpTetrisPrincipleWithUser();
        doReturn(5).when(groupMeetingAndEventsConfigurationService).getPropertyIdFromWorkContext();
        doReturn(true).when(authorizationService).userHasAccessToProperty(5);
        doReturn(null).when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.GROUP_PRICING_EVALUATION);
        doReturn("readWrite").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.RULES_CONFIGURATION);
        groupMeetingAndEventsConfigurationService.globalCrudService = this.globalCrudService;
        //WHEN
        String rulesConfigurationPermission = groupMeetingAndEventsConfigurationService.getRulesConfigurationPermission();
        //THEN
        assertEquals("NO_ACCESS", rulesConfigurationPermission);
    }

    @Test
    void shouldReturnRulesConfigurationStatusAsNoAccessWhenUserHasAccessToPropertyAndNoAccessToGroupPricingAndReadAccessToRulesConfiguration(){
        //GIVEN
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        doNothing().when(groupMeetingAndEventsConfigurationService).setUpTetrisPrincipleWithUser();
        doReturn(5).when(groupMeetingAndEventsConfigurationService).getPropertyIdFromWorkContext();
        doReturn(true).when(authorizationService).userHasAccessToProperty(5);
        doReturn(null).when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.GROUP_PRICING_EVALUATION);
        doReturn("readOnly").when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.RULES_CONFIGURATION);
        groupMeetingAndEventsConfigurationService.globalCrudService = this.globalCrudService;
        //WHEN
        String rulesConfigurationPermission = groupMeetingAndEventsConfigurationService.getRulesConfigurationPermission();
        //THEN
        assertEquals("NO_ACCESS", rulesConfigurationPermission);
    }

    @Test
    void shouldReturnRulesConfigurationStatusAsNoAccessWhenUserHasAccessToPropertyAndNoAccessToGroupPricingAndNoAccessToRulesConfiguration(){
        //GIVEN
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        doNothing().when(groupMeetingAndEventsConfigurationService).setUpTetrisPrincipleWithUser();
        doReturn(5).when(groupMeetingAndEventsConfigurationService).getPropertyIdFromWorkContext();
        doReturn(true).when(authorizationService).userHasAccessToProperty(5);
        doReturn(null).when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.GROUP_PRICING_EVALUATION);
        doReturn(null).when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.RULES_CONFIGURATION);
        groupMeetingAndEventsConfigurationService.globalCrudService = this.globalCrudService;
        //WHEN
        String rulesConfigurationPermission = groupMeetingAndEventsConfigurationService.getRulesConfigurationPermission();
        //THEN
        assertEquals("NO_ACCESS", rulesConfigurationPermission);
    }

    @Test
    void shouldReturnRulesConfigurationsFeatureEnableStatus(){
        //GIVEN
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        doReturn(false).when(pricingConfigurationService).isBaseAccomTypeSetupComplete();
        doReturn(true).when(pricingConfigurationService).isPerRoomServicingCostAvailableForAllRoomClass();
        doReturn(true).when(pricingConfigurationService).isCeilingFloorGroupComplete(PROPERTY_ID);
        doReturn(true).when(configParamsService).getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_RULES_CONFIGURATION_FOR_GROUP_PRICING);
        doNothing().when(groupMeetingAndEventsConfigurationService).setUpTetrisPrincipleWithUser();
        doReturn(5).when(groupMeetingAndEventsConfigurationService).getPropertyIdFromWorkContext();
        doReturn(true).when(authorizationService).userHasAccessToProperty(5);
        doReturn(null).when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.GROUP_PRICING_EVALUATION);
        doReturn(null).when(authorizationService).getPermsForRequestedPage(TetrisPermissionKey.RULES_CONFIGURATION);
        //WHEN
        GmeConfiguration gmeConfiguration = groupMeetingAndEventsConfigurationService.getGmeConfigurations();
        //THEN
        assertNotNull(gmeConfiguration.getConfigurationStatusRequiredForEvaluation());
        assertTrue(gmeConfiguration.isRulesConfigurationFeatureEnabled());
    }


    private void setUpTetrisPrincipleForTest() {
        GlobalUser user = new GlobalUser();
        user.setId(11403);
        user.setClientCode("BSTN");
        doReturn(11403).when(groupMeetingAndEventsConfigurationService).getUserIdFromWorkContext();
        doReturn(new TetrisPrincipal()).when(groupMeetingAndEventsConfigurationService).getTetrisPrincipal();
        DN dnMock = mock(DN.class);
        doReturn(dnMock).when(groupMeetingAndEventsConfigurationService).getUserDN(user);
        doReturn(user).when(globalCrudService).find(GlobalUser.class, 11403);
    }

    private void assertConfigurationsRequiredForEvaluation(ConfigurationStatusRequiredForEvaluation requiredConfigurations) {
        assertFalse(requiredConfigurations.getPricingConfigurationComplete());
        assertTrue(requiredConfigurations.getPerRoomServicingCostComplete());
        assertTrue(requiredConfigurations.getCeilingFloorDefaultComplete());
    }


    private void assertPackageDetails(Package package1) {
        assertEquals(1, package1.getId());
        assertEquals("Weekend", package1.getName());
        assertEquals(true, package1.getGuestRoomIncluded());
        assertEquals(1, package1.getPackageTypeId());
        assertEquals("Family", package1.getPackageTypeName());
    }


    private AccomType createAccomTypeWith(int accomTypeId, int accomClassId) {
        AccomType accomType = new AccomType();
        accomType.setId(accomTypeId);
        AccomClass accomClass = new AccomClass();
        accomClass.setId(accomClassId);
        accomType.setAccomClass(accomClass);
        return accomType;
    }

    private PackageDTO createPackage(int id, String name, boolean guestRoomIncluded, TenantStatusEnum status,
                                     FunctionSpacePackageType packageType, List<PackageElementMapDTO> packageElementMapDTO) {
        PackageDTO newPackage = new PackageDTO();
        newPackage.setPackageId(id);
        newPackage.setPackageName(name);
        newPackage.setGuestRoomIncluded(guestRoomIncluded);
        newPackage.setStatus(status);
        newPackage.setPackageType(packageType);
        newPackage.setFunctionSpacePackageElementMapDTOList(packageElementMapDTO);
        return newPackage;
    }

    private FunctionSpacePackageType createPackageType(int id, String name) {
        FunctionSpacePackageType packageType = new FunctionSpacePackageType();
        packageType.setId(id);
        packageType.setName(name);
        return packageType;
    }

    private PackageElementMapDTO createPackageElementMap(int id, String name, boolean isCommissionable) {
        RevenueGroupDto revenueGroupDto = new RevenueGroupDto(1, "FoodAndBeverages", "FoodAndBeverages", BigDecimal.TEN);
        PackageElementDto packageElementDto = new PackageElementDto(id, name, revenueGroupDto, BigDecimal.TEN, isCommissionable, false, true, TenantStatusEnum.ACTIVE, 1);
        PackageElementMapDTO packageElementMapDTO = new PackageElementMapDTO();
        packageElementMapDTO.setPackageElementMapId(id);
        packageElementMapDTO.setPackageElementDto(packageElementDto);
        return packageElementMapDTO;
    }

    private List<RevenueStream> convertConfAndBanqToRevenueStreamDto(List<ConferenceAndBanquetDto> conferenceAndBanquets) {
        return conferenceAndBanquets.stream()
                .map(revStream ->
                        new RevenueStream(revStream.getId(),
                                revStream.getRevenueStream(),
                                revStream.getProfitPercentage(),
                                RevenueStreamType.CONFERENCE_AND_BANQUET.name()))
                .collect(Collectors.toList());
    }

    private ConferenceAndBanquetDto createConferenceAndBanquets(int id, String name, BigDecimal profitPercentage) {
        ConferenceAndBanquetDto confAndBanq = new ConferenceAndBanquetDto();
        confAndBanq.setId(id);
        confAndBanq.setRevenueStream(name);
        confAndBanq.setProfitPercentage(profitPercentage);
        return confAndBanq;
    }

    private List<RevenueStream> convertToRevenueStreamDto(List<GroupPricingConfigurationAncillaryStream> ancillaryRevenueStreams) {
        return ancillaryRevenueStreams.stream()
                .map(revStream ->
                        new RevenueStream(revStream.getId(),
                                revStream.getRevenueStream(),
                                revStream.getProfitPercentage(),
                                RevenueStreamType.ANCILLARY.name()))
                .collect(Collectors.toList());
    }

    private GroupPricingConfigurationAncillaryStream createAncillaryRevenueStreams(Integer id, String name, BigDecimal profitPercentage) {
        GroupPricingConfigurationAncillaryStream revenueStream = new GroupPricingConfigurationAncillaryStream();
        revenueStream.setId(id);
        revenueStream.setRevenueStream(name);
        revenueStream.setProfitPercentage(profitPercentage);
        return revenueStream;
    }

    private List<GroupPricingConfigurationAncillaryAssignmentSeason> createAncillarySeasonsFor(List<GroupPricingConfigurationAncillaryStream> ancillaries) {
        List<GroupPricingConfigurationAncillaryAssignmentSeason> seasons = new ArrayList<>();
        ancillaries.forEach(ancillary -> {
            GroupPricingConfigurationAncillaryAssignmentSeason yearRoundSeason = createAncillarySeason(true);
            yearRoundSeason.setAncillaryAssignments(createAncillarySeasonAssignments(ancillary, yearRoundSeason));
            seasons.add(yearRoundSeason);
            GroupPricingConfigurationAncillaryAssignmentSeason datedSeason = createAncillarySeason(false);
            datedSeason.setAncillaryAssignments(createAncillarySeasonAssignments(ancillary, datedSeason));
            seasons.add(datedSeason);
        });
        return seasons;
    }

    private GroupPricingConfigurationAncillaryAssignmentSeason createAncillarySeason(boolean isYearRound) {
        GroupPricingConfigurationAncillaryAssignmentSeason season = new GroupPricingConfigurationAncillaryAssignmentSeason();
        season.setDefault(isYearRound);
        if (!isYearRound) {
            season.setStartDate(JavaLocalDateUtils.toJodaLocalDate(LocalDate.of(2024, 8, 21)));
            season.setEndDate(JavaLocalDateUtils.toJodaLocalDate(LocalDate.of(2024, 8, 28)));
        }
        return season;
    }

    private Set<GroupPricingConfigurationAncillaryAssignment> createAncillarySeasonAssignments(
            GroupPricingConfigurationAncillaryStream ancillary, GroupPricingConfigurationAncillaryAssignmentSeason season) {
        GroupPricingConfigurationAncillaryAssignment ancillaryAssignment = new GroupPricingConfigurationAncillaryAssignment();
        ancillaryAssignment.setMarketSegment(createMarketSegmentSummaryWith(1, "CORP"));
        ancillaryAssignment.setRevenue(BigDecimal.valueOf(150.0000000000));
        ancillaryAssignment.setGroupPricingConfigurationAncillaryStream(ancillary);
        ancillaryAssignment.setGroupPricingConfigurationAncillaryAssignmentSeason(season);
        return Set.of(ancillaryAssignment);
    }


    private AccomType createAccomType(int id, String accomClassName, String accomClassCode, String acocmTypeName, String accomTypeCode) {
        AccomClass accomClass = new AccomClass();
        accomClass.setName(accomClassName);
        accomClass.setCode(accomClassCode);
        AccomType accomType = new AccomType();
        accomType.setId(id);
        accomType.setName(acocmTypeName);
        accomType.setAccomTypeCode(accomTypeCode);
        accomType.setAccomClass(accomClass);

        GroupPricingConfigAccomType grpPrcAccomType = new GroupPricingConfigAccomType();
        grpPrcAccomType.setAccomType(accomType);
        grpPrcAccomType.setActive(true);
        return grpPrcAccomType.getAccomType();
    }

    private static MarketSegmentSummary createMarketSegmentSummaryWith(int id, String code) {
        MarketSegmentSummary marketSegmentSummary = new MarketSegmentSummary();
        marketSegmentSummary.setId(id);
        marketSegmentSummary.setCode(code);
        return marketSegmentSummary;
    }

}