package com.ideas.tetris.pacman.services.limiteddatabuild;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyCriteria;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ConsolidatedPropertyView;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantPropertyAudit;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.hiltonconfiguration.MarketSegmentMasterService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.LDBPropertyRolloutData;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.*;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.*;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.pacman.services.mktSegAttribution.ExplodedMCATAttributesService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.reservationnight.AMSResolutionTest;
import com.ideas.tetris.pacman.services.reservationnight.ReservationNightService;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.roa.forecastparams.service.ROARuntimeParamService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncDisplayNameService;
import com.ideas.tetris.pacman.services.webrate.entity.Webrate;
import com.ideas.tetris.pacman.util.CustomizedDisplayName;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.*;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum.MAX_FCST_HISTORY_LENGTH;
import static com.ideas.tetris.pacman.services.roa.forecastparams.service.ROARuntimeParamService.*;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toJodaLocalDate;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DATE_FORMAT_DD_MMM_YYYY;
import static java.util.stream.Collectors.toMap;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class LDBServiceTest {

    public static final AnalyticalMarketSegment IT = getAnalyticalMarketSegment("IT", "A1", "IT_QY", 1);
    public static final AnalyticalMarketSegment MKT = getAnalyticalMarketSegment("MKT", "A2", "MKT_QYL", 2);
    public static final AnalyticalMarketSegment CNR = getAnalyticalMarketSegment("CNR", "A3", "CNR_QN", 3);
    public static final AnalyticalMarketSegment DISC = getAnalyticalMarketSegment("DISC", "A4", "DISC_U", 4);
    public static final AnalyticalMarketSegment LNR = getAnalyticalMarketSegment("LNR", "A5", "LNR_UF", 5);
    public static final List<String> SPLIT_MARKET_SEGMENTS = List.of("IT", "LNR", "MKT", "CNR", "DISC");

    private static final int PROPERTY_ID = 913;

    private static final int CLIENT_ID = 526;

    private static final Integer USER_ID = 1211;

    private static final String USER_NAME = "dummy user";

    private static final String PROPERTY_CODE = "BOSCO";

    private static final String CLIENT_CODE = "Hilton";

    private static final String CLIENT_NAME = "Hilton Hotels";

    private static final String PROPERTY_NAME = "Boston Hotel";

    private static final Stage STAGE = Stage.ONE_WAY;

    private static final String LDB_FOLDER = "src/test/resources/limitedDataBuild".replace("/", File.separator); //resolvePath("src/test/resources/limitedDataBuild");

    private static final String DB_SERVER_NAME = "databaseServer";

    private static final String DATABASE_NAME = "myDataBase";

    private static final Integer DB_PORT_NUMBER = 1211;

    private static final LocalDate FUTURE_SOFT_OPENING_END_DATE = new LocalDate("2116-09-13");
    private static final java.time.LocalDate JAVA_FUTURE_SOFT_OPENING_END_DATE = java.time.LocalDate.of(2024, 1, 7);
    private static final java.time.LocalDate JAVA_SOFT_OPENING_END_DATE = java.time.LocalDate.of(2024, 1, 1);

    private static final LocalDate PROPERTY_ADDED_DATE = new LocalDate("2115-09-13");

    private static final int MARKET_SEGMENT_ID = 913;

    private static final String MARKET_SEGMENT_CODE = "CONS";

    private static final String ROOM_TYPE_CODE = "STD";

    private static final LocalDate SOFT_OPENING_END_DATE = LocalDate.now().plusDays(100);
    private static final java.time.LocalDate OPENING_DATE = java.time.LocalDate.now().plusDays(30);
    private static final LocalDate PROJECTIONS_END_DATE = LocalDate.now().plusDays(365);

    private static final LocalDateTime LAST_PROJECTION_UPDATE_DATE = LocalDateTime.now().plusDays(365);

    private static final LocalDateTime LAST_CLONE_DATA_UPDATE_DATE = LocalDateTime.now().minusDays(10);

    private static final LocalDateTime LAST_GENERIC_DATA_UPDATE_DATE = LocalDateTime.now().minusDays(20);

    private static final LocalDateTime LDB_CONFIG_UPDATE_DATE = LocalDateTime.now().minusDays(30);

    @Mock
    @TenantCrudServiceBean.Qualifier
    CrudService tenantCrudService;

    @Mock
    @GlobalCrudServiceBean.Qualifier
    CrudService globalCrudService;

    @Mock
    PacmanConfigParamsService configParamsService;

    @Mock
    IJobServicesBridgeExtension jobServicesBridgeExtension;

    @Mock
    private ExplodedMCATAttributesService explodedMCATAttributesService;

    @Mock
    private MarketSegmentMasterService marketSegmentMasterService;
    @Mock
    private MarketSegmentComponent marketSegmentComponent;
    @Mock
    private AnalyticalMarketSegmentService analyticalMarketSegmentService;
    @Mock
    private MarketSegmentService marketSegmentService;
    @Mock
    private ReservationNightService reservationNightService;

    @Mock
    IJobServicesBridge jobServicesBridge;

    @Mock
    ProjectionDataService projectionDataService;

    @Mock
    LDBPatternService ldbPatternService;

    @Mock
    IJobExecutionService jobExecutionService;

    @Mock
    DataSourceCacheBean dataSourceCache;

    @Mock
    JobServiceLocal jobService;

    @Mock
    UserService userService;

    @Mock
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Mock
    DateService dateService;

    @Mock
    LDBPropertyRolloutDataCache ldbPropertyRolloutDataCache;

    @Mock
    PropertyService propertyService;

    @Mock
    ClientService clientService;

    @Mock
    ROARuntimeParamService roaRuntimeParamService;

    @Mock
    ROAPropertyAttributeService propertyAttributeService;

    @Mock
    protected SyncEventAggregatorService syncEventAggregatorService;

    @Mock
    private AlertService alertService;

    @InjectMocks
    LDBService ldbService;

    @Captor
    ArgumentCaptor<List<AMSCompositionChange>> compositionChangeCaptor;
    @Captor
    ArgumentCaptor<List<AnalyticalMarketSegment>> amsCaptor;
    @Mock
    private SyncDisplayNameService syncDisplayNameService;

    @Captor
    private ArgumentCaptor<LDBConfig> ldbConfigArgumentCaptor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        WorkContextType workContext = new WorkContextType();
        workContext.setPropertyId(PROPERTY_ID);
        workContext.setPropertyCode(PROPERTY_CODE);
        workContext.setClientId(CLIENT_ID);
        workContext.setClientCode(CLIENT_CODE);
        workContext.setUserId(String.valueOf(USER_ID));
        PacmanWorkContextHelper.setWorkContext(workContext);
    }

    @Test
    void test_setConfigLocked() {
        LDBConfig origEntity = new LDBConfig();
        origEntity.setLocked(false);
        List<LDBConfig> entities = List.of(origEntity);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBConfig.GET_ALL, new HashMap<>())).thenReturn(entities);
        ldbService.setConfigLocked(PROPERTY_ID, true);
        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(multiPropertyCrudService).save(Mockito.eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig entity = saveCaptor.getValue();
        assertTrue(entity.isLocked());
    }

    @Test
    void testEstimatedNormalPropertyDateWithSoftOpeningEndDate() {
        ConsolidatedPropertyView view = new ConsolidatedPropertyView();
        List<LDBConfig> ldbConfigList = new ArrayList<>();
        LDBConfig ldbConfig = new LDBConfig();
        ldbConfig.setSoftOpeningEndDate(new LocalDate("2017-02-05"));
        ldbConfigList.add(ldbConfig);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(0, LDBConfig.GET_ALL, new HashMap<>())).thenReturn(ldbConfigList);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(0, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(new LocalDate("2017-01-01").toDate());
        LDBPropertyRolloutData ldbPropertyRolloutData = ldbService.mapToDto(view);
        assertEquals("05-Feb-2018", ldbPropertyRolloutData.getEstimatedNormalPropertyDate());
    }

    @Test
    void testEstimatedNormalPropertyDateWithHotelOpeningDateAndNoSoftOpeningDate() {
        ConsolidatedPropertyView view = new ConsolidatedPropertyView();
        List<LDBConfig> ldbConfigList = new ArrayList<>();
        LDBConfig ldbConfig = new LDBConfig();
        ldbConfigList.add(ldbConfig);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(0, LDBConfig.GET_ALL, new HashMap<>())).thenReturn(ldbConfigList);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(0, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(new LocalDate("2017-01-01").toDate());
        LDBPropertyRolloutData ldbPropertyRolloutData = ldbService.mapToDto(view);
        assertEquals("01-Jan-2018", ldbPropertyRolloutData.getEstimatedNormalPropertyDate());
    }

    @Test
    void testEstimatedNormalPropertyDateWithHotelOpeningDateAndNoLdbConfig() {
        ConsolidatedPropertyView view = new ConsolidatedPropertyView();
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(0, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(new LocalDate("2017-01-01").toDate());
        LDBPropertyRolloutData ldbPropertyRolloutData = ldbService.mapToDto(view);
        assertEquals("01-Jan-2018", ldbPropertyRolloutData.getEstimatedNormalPropertyDate());
    }

    @Test
    void testEstimatedNormalPropertyDateWithNoAvailableData() {
        ConsolidatedPropertyView view = new ConsolidatedPropertyView();
        LDBPropertyRolloutData ldbPropertyRolloutData = ldbService.mapToDto(view);
        assertNull(ldbPropertyRolloutData.getEstimatedNormalPropertyDate());
    }

    @Test
    void test_getPropertyRolloutData_noLdbConfig() {
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID + 1);
        setupConsolidatedPropertyViews();
        List<Date> dates = new ArrayList<>();
        dates.add(PROPERTY_ADDED_DATE.toDate());
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, TenantPropertyAudit.GET_DATE_ADDED, new HashMap<>())).thenReturn(dates);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(new ArrayList<LDBConfig>());
        when(projectionDataService.getLastProjectionDate()).thenReturn(PROJECTIONS_END_DATE);
        when(projectionDataService.getLastUpdatedProjectionDate()).thenReturn(LAST_PROJECTION_UPDATE_DATE.toString("dd-MMM-yyyy HH:mm"));
        when(projectionDataService.getUserNameForLastUpdatedProjections()).thenReturn(USER_NAME);
        setupMarketSegments();
        LDBBuildHistory history = new LDBBuildHistory();
        List<LDBBuildHistory> buildHistory = List.of(history);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBBuildHistory.GET_ALL, new HashMap<>())).thenReturn(buildHistory);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(new ArrayList<AccomType>());
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBConfigAudit.GET_ALL, new HashMap<>())).thenReturn(new ArrayList<LDBConfigAudit>());
        List<LDBPropertyRolloutData> results = ldbService.getPropertyRolloutData(null);
        assertPropertyRolloutDataResults_noLdbConfig(results, buildHistory);
        assertEquals(PROPERTY_ID + 1, PacmanWorkContextHelper.getPropertyId().intValue());
    }

    @Test
    void test_getPropertyRolloutData() {
        PacmanWorkContextHelper.setPropertyId(PROPERTY_ID + 1);
        setupConsolidatedPropertyViews();
        List<Date> dates = new ArrayList<>();
        dates.add(PROPERTY_ADDED_DATE.toDate());
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, TenantPropertyAudit.GET_DATE_ADDED, new HashMap<>())).thenReturn(dates);
        setupLdbConfig();
        when(projectionDataService.getLastProjectionDate()).thenReturn(PROJECTIONS_END_DATE);
        when(projectionDataService.getLastUpdatedProjectionDate()).thenReturn(LAST_PROJECTION_UPDATE_DATE.toString("dd-MMM-yyyy HH:mm"));
        when(projectionDataService.getUserNameForLastUpdatedProjections()).thenReturn(USER_NAME);
        setupMarketSegments();
        LDBBuildHistory history = new LDBBuildHistory();
        List<LDBBuildHistory> buildHistory = List.of(history);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBBuildHistory.GET_ALL, new HashMap<>())).thenReturn(buildHistory);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(new ArrayList<AccomType>());
        setupLDBConfigAuditHistory();
        List<LDBPropertyRolloutData> results = ldbService.getPropertyRolloutData(null);
        assertPropertyRolloutDataResults(results, buildHistory);
        assertEquals(PROPERTY_ID + 1, PacmanWorkContextHelper.getPropertyId().intValue());
    }

    private void assertPropertyRolloutDataResults(List<LDBPropertyRolloutData> results, List<LDBBuildHistory> buildHistory) {
        assertEquals(1, results.size());
        LDBPropertyRolloutData data = results.get(0);
        assertEquals(CLIENT_ID, data.getClientId().intValue());
        assertEquals(PROPERTY_ID, data.getPropertyId().intValue());
        assertEquals(CLIENT_NAME, data.getClientName());
        assertEquals(PROPERTY_ADDED_DATE.toString("dd-MMM-yyyy"), data.getDateAdded());
        assertEquals(PROJECTIONS_END_DATE.toString("dd-MMM-yyyy"), data.getProjectionsEndDate());
        assertEquals(PROPERTY_CODE, data.getPropertyCode());
        assertEquals(PROPERTY_NAME, data.getPropertyName());
        assertEquals(SOFT_OPENING_END_DATE.toString("dd-MMM-yyyy"), data.getSoftOpeningEndDate());
        assertEquals(OPENING_DATE.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy")), data.getOpeningDate());
        assertEquals(LAST_PROJECTION_UPDATE_DATE.toString("dd-MMM-yyyy HH:mm") + " " + USER_NAME, data.getProjectionsLastUpdatedInfo());
        assertEquals(LAST_CLONE_DATA_UPDATE_DATE.toString("dd-MMM-yyyy HH:mm") + " " + USER_NAME, data.getCloneDataLastUpdatedInfo());
        assertEquals(LAST_GENERIC_DATA_UPDATE_DATE.toString("dd-MMM-yyyy HH:mm") + " " + USER_NAME, data.getGenericDataLastUpdatedInfo());
        assertEquals(LDB_CONFIG_UPDATE_DATE.toString("dd-MMM-yyyy HH:mm") + " " + USER_NAME, data.getSoftOpeningEndDateLastUpdatedInfo());
        assertEquals(LDB_CONFIG_UPDATE_DATE.toString("dd-MMM-yyyy HH:mm") + " " + USER_NAME, data.getPatternSourceLastUpdatedInfo());
        assertEquals(JavaLocalDateUtils.jodaToJavaLocalDateTime(LDB_CONFIG_UPDATE_DATE).format(DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm"))+ " " + USER_NAME, data.getOpeningDateLastUpdatedInfo());
        assertEquals(PatternSource.GENERIC.toString(), data.getPatternSource());
        assertEquals(STAGE.getCode(), data.getStage());
        assertTrue(data.isConfigLocked());
        assertTrue(data.getUnattributedMarketSegments().isEmpty());
        assertTrue(data.getUnmappedRoomTypes().isEmpty());
    }

    private void assertPropertyRolloutDataResults_noLdbConfig(List<LDBPropertyRolloutData> results, List<LDBBuildHistory> buildHistory) {
        assertEquals(1, results.size());
        LDBPropertyRolloutData data = results.get(0);
        assertEquals(CLIENT_ID, data.getClientId().intValue());
        assertEquals(PROPERTY_ID, data.getPropertyId().intValue());
        assertEquals(CLIENT_NAME, data.getClientName());
        assertEquals(PROPERTY_ADDED_DATE.toString("dd-MMM-yyyy"), data.getDateAdded());
        assertEquals(PROJECTIONS_END_DATE.toString("dd-MMM-yyyy"), data.getProjectionsEndDate());
        assertEquals(PROPERTY_CODE, data.getPropertyCode());
        assertEquals(PROPERTY_NAME, data.getPropertyName());
        assertEquals(LAST_PROJECTION_UPDATE_DATE.toString("dd-MMM-yyyy HH:mm") + " " + USER_NAME, data.getProjectionsLastUpdatedInfo());
        assertNull(data.getCloneDataLastUpdatedInfo());
        assertNull(data.getGenericDataLastUpdatedInfo());
        assertNull(data.getPatternSource());
        assertEquals(STAGE.getCode(), data.getStage());
        assertFalse(data.isConfigLocked());
        assertTrue(data.getUnattributedMarketSegments().isEmpty());
        assertTrue(data.getUnmappedRoomTypes().isEmpty());
        assertNull(data.getOpeningDate());
        assertNull(data.getSoftOpeningEndDate());
        assertEquals(EMPTY_STRING,data.getOpeningDateLastUpdatedInfo());
        assertEquals(EMPTY_STRING,data.getSoftOpeningEndDateLastUpdatedInfo());
    }

    private void setupLDBConfigAuditHistory() {
        LDBClonePatternSource cloneEntity = new LDBClonePatternSource();
        cloneEntity.setLastUpdatedDate(LAST_CLONE_DATA_UPDATE_DATE.toDate());
        cloneEntity.setLastUpdatedByUserId(USER_ID);
        when(ldbPatternService.getClonePatternsLastUpdate()).thenReturn(cloneEntity);
        LDBGenericPatternData genericEntity = new LDBGenericPatternData();
        genericEntity.setLastUpdatedDate(LAST_GENERIC_DATA_UPDATE_DATE.toDate());
        genericEntity.setLastUpdatedByUserId(USER_ID);
        when(ldbPatternService.getGenericPatternsLastUpdate()).thenReturn(genericEntity);
        GlobalUser user = new GlobalUser();
        user.setFullName(USER_NAME);
        when(userService.getGlobalUser(USER_ID, false)).thenReturn(user);
        LDBConfigAudit addAudit = new LDBConfigAudit();
        addAudit.setRevType(LDBConfigAudit.ADDED_ACTION);
        LDBConfigAudit updateAudit = new LDBConfigAudit();
        updateAudit.setModifiedDate(LDB_CONFIG_UPDATE_DATE.toDate());
        updateAudit.setRevType(LDBConfigAudit.UPDATED_ACTION);
        updateAudit.setPatternSource(PatternSource.GENERIC);
        updateAudit.setHotelOpeningDate(java.time.LocalDate.now());
        updateAudit.setSoftOpeningEndDate(LocalDate.now());
        updateAudit.setModifiedByUserId(new Long(USER_ID));
        List<LDBConfigAudit> auditHistory = Arrays.asList(updateAudit, addAudit);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBConfigAudit.GET_ALL, new HashMap<>())).thenReturn(auditHistory);
    }

    private void setupConsolidatedPropertyViews() {
        List<ConsolidatedPropertyView> views = new ArrayList<>();
        ConsolidatedPropertyView view = new ConsolidatedPropertyView();
        view.setStage(STAGE);
        view.setPropertyId(PROPERTY_ID);
        view.setClientCode(CLIENT_CODE);
        view.setClientId(CLIENT_ID);
        view.setClientName(CLIENT_NAME);
        view.setPropertyName(PROPERTY_NAME);
        view.setPropertyCode(PROPERTY_CODE);
        views.add(view);
        when(globalCrudService.findByCriteria(Mockito.any(ConsolidatedPropertyCriteria.class))).thenReturn(views);
    }

    private void setupLdbConfig() {
        List<LDBConfig> entities = new ArrayList<>();
        LDBConfig entity = new LDBConfig();
        entity.setHotelOpeningDate(OPENING_DATE);
        entity.setSoftOpeningEndDate(SOFT_OPENING_END_DATE);
        entity.setLocked(true);
        entity.setLastUpdatedByUserId(1);
        entity.setPatternSource(PatternSource.GENERIC);
        entity.setLocked(true);
        entities.add(entity);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entities);
    }

    private void setupMarketSegments() {
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID);
        List<MktSeg> marketSegments = List.of(marketSegment);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        MktSegDetails detail = new MktSegDetails();
        detail.setStatusId(Constants.ACTIVE_STATUS_ID);
        detail.setMktSeg(marketSegment);
        List<MktSegDetails> details = List.of(detail);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, MktSegDetails.GET_ALL, new HashMap<>())).thenReturn(details);
    }

    @Test
    void test_getUnattributedMarketSegments() {
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID);
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        List<MktSeg> marketSegments = List.of(marketSegment);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_ALL, new HashMap<>())).thenReturn(new ArrayList<MktSegDetails>());
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetailsProposed.ALL, new HashMap<>())).thenReturn(new ArrayList<MktSegDetailsProposed>());
        List<MktSeg> unattributedMarketSegments = ldbService.getUnattributedMarketSegments();
        assertEquals(1, unattributedMarketSegments.size());
        assertEquals(marketSegment, unattributedMarketSegments.get(0));
    }

    @Test
    void test_getUnattributedMarketSegmentCodes() {
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID);
        marketSegment.setCode(MARKET_SEGMENT_CODE);
        List<MktSeg> marketSegments = List.of(marketSegment);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_ALL, new HashMap<>())).thenReturn(new ArrayList<MktSegDetails>());
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetailsProposed.ALL, new HashMap<>())).thenReturn(new ArrayList<MktSegDetailsProposed>());
        List<String> unattributedMarketSegments = ldbService.getUnattributedMarketSegmentCodes();
        assertEquals(1, unattributedMarketSegments.size());
        assertEquals(MARKET_SEGMENT_CODE, unattributedMarketSegments.get(0));
    }

    @Test
    void test_getUnmappedRoomTypes() {
        AccomType roomType = new AccomType();
        roomType.setAccomTypeCode(ROOM_TYPE_CODE);
        List<AccomType> roomTypes = List.of(roomType);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(roomTypes);
        List<AccomType> unmappedRoomTypes = ldbService.getUnmappedRoomTypes();
        assertEquals(1, unmappedRoomTypes.size());
        assertEquals(roomType, unmappedRoomTypes.get(0));
    }

    @Test
    void test_getUnmappedRoomTypeCodes() {
        AccomType roomType = new AccomType();
        roomType.setAccomTypeCode(ROOM_TYPE_CODE);
        List<AccomType> roomTypes = List.of(roomType);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(roomTypes);
        List<String> unmappedRoomTypes = ldbService.getUnmappedRoomTypeCodes();
        assertEquals(1, unmappedRoomTypes.size());
        assertEquals(ROOM_TYPE_CODE, unmappedRoomTypes.get(0));
    }

    @Test
    void test_isMarketSegmentAttributionComplete_inDetails() {
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID);
        List<MktSeg> marketSegments = List.of(marketSegment);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        MktSegDetails detail = new MktSegDetails();
        detail.setStatusId(Constants.ACTIVE_STATUS_ID);
        detail.setMktSeg(marketSegment);
        List<MktSegDetails> details = List.of(detail);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_ALL, new HashMap<>())).thenReturn(details);
        assertTrue(ldbService.isMarketSegmentAttributionComplete());
    }

    @Test
    void test_isMarketSegmentAttributionComplete_inProposedDetails() {
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID);
        List<MktSeg> marketSegments = List.of(marketSegment);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        MktSegDetailsProposed detail = new MktSegDetailsProposed();
        detail.setStatusId(Constants.ACTIVE_STATUS_ID);
        detail.setMktSeg(marketSegment);
        List<MktSegDetailsProposed> details = List.of(detail);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_ALL, new HashMap<>())).thenReturn(new ArrayList<MktSegDetails>());
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetailsProposed.ALL, new HashMap<>())).thenReturn(details);
        assertTrue(ldbService.isMarketSegmentAttributionComplete());
    }

    @Test
    void test_isMarketSegmentAttributionComplete_false() {
        MktSeg marketSegment = new MktSeg();
        marketSegment.setId(MARKET_SEGMENT_ID);
        List<MktSeg> marketSegments = List.of(marketSegment);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, MktSeg.BY_PROPERTY_ID, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(marketSegments);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetails.GET_ALL, new HashMap<>())).thenReturn(new ArrayList<MktSegDetails>());
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), MktSegDetailsProposed.ALL, new HashMap<>())).thenReturn(new ArrayList<MktSegDetailsProposed>());
        assertFalse(ldbService.isMarketSegmentAttributionComplete());
    }

    @Test
    void test_computeRealDataStartDate() {
        LocalDate today = LocalDate.now();
        LocalDate SOFT_OPENING_END_DATE = today.plusDays(100);
        List<LDBConfig> entities = new ArrayList<>();
        LDBConfig entity = new LDBConfig();
        entity.setSoftOpeningEndDate(SOFT_OPENING_END_DATE);
        entities.add(entity);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entities);
        LocalDate results = ldbService.computeRealDataStartDate();
        assertEquals(SOFT_OPENING_END_DATE, results);
    }

    @Test
    void test_computeRealDataStartDate_notEntered() {
        List<LDBConfig> entities = new ArrayList<>();
        LDBConfig entity = new LDBConfig();
        entities.add(entity);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entities);
        LocalDate results = ldbService.computeRealDataStartDate();
        assertNull(results);
    }

    @Test
    void computeSimulationEndDate_shouldThrowExceptionWhenMinOccupancyDateNotFound() {
        assertThrows(TetrisException.class, () -> {
            when(dateService.getEarliestOccupancyDate()).thenReturn(null);
            setupTestLdbConfig(false);
            ldbService.computeSimulationEndDate(null);
        });
    }

    private void setupTestLdbConfig(boolean isHybrid) {
        LDBConfig ldbConfig = new LDBConfig();
        ldbConfig.setHybrid(isHybrid);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_ALL)).thenReturn(ldbConfig);
    }

    @Test
    void test_computeSimulationEndDate_whenRealDataStartDateIsSameAsBusinessDate_hybridLdb() {
        LocalDate businessDate = LocalDate.now();
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        LocalDate realDataStartDate = businessDate;
        setupTestLdbConfig(true);
        LocalDate results = ldbService.computeSimulationEndDate(realDataStartDate);
        assertEquals(realDataStartDate.minusDays(2), results);
    }

    @Test
    void test_computeSimulationEndDate_whenRealDataStartDateIsInPast_hybridLdb() {
        LocalDate businessDate = new LocalDate(2019, 01, 05);
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        LocalDate realDataStartDate = businessDate.minusDays(100);
        setupTestLdbConfig(true);
        LocalDate results = ldbService.computeSimulationEndDate(realDataStartDate);
        assertEquals(realDataStartDate.minusDays(2), results);
    }

    @Test
    void test_computeSimulationEndDate_whenRealDataStartDateIsInFuture_hybridLdb() {
        LocalDate businessDate = new LocalDate(2019, 01, 05);
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        LocalDate realDataStartDate = businessDate.plusDays(100);
        setupTestLdbConfig(true);
        LocalDate results = ldbService.computeSimulationEndDate(realDataStartDate);
        assertEquals(businessDate.minusDays(2), results);
    }

    @Test
    void test_computeSimulationEndDate_whenMinOccupancyDateIsInPast() {
        LocalDate businessDate = LocalDate.now();
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.LDB_OVERRIDE_SAS_HISTORY_WITH_SIMULATED_HISTORY)).thenReturn(false);
        LocalDate MIN_OCCUPANCY_DATE = businessDate.minusDays(100);
        when(dateService.getEarliestOccupancyDate()).thenReturn(MIN_OCCUPANCY_DATE.toDate());
        setupTestLdbConfig(false);
        LocalDate results = ldbService.computeSimulationEndDate(null);
        assertEquals(MIN_OCCUPANCY_DATE.minusDays(2), results);
    }

    @Test
    void test_computeSimulationEndDate_whenMinOccupancyDateIsInFuture() {
        LocalDate businessDate = LocalDate.now();
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.LDB_OVERRIDE_SAS_HISTORY_WITH_SIMULATED_HISTORY)).thenReturn(false);
        LocalDate MIN_OCCUPANCY_DATE = businessDate.plusDays(100);
        when(dateService.getEarliestOccupancyDate()).thenReturn(MIN_OCCUPANCY_DATE.toDate());
        setupTestLdbConfig(false);
        LocalDate results = ldbService.computeSimulationEndDate(null);
        assertEquals(businessDate.minusDays(2), results);
    }

    @Test
    void test_computeSimulationEndDate_whenMinOccupancyDateIsToday() {
        LocalDate businessDate = LocalDate.now();
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.LDB_OVERRIDE_SAS_HISTORY_WITH_SIMULATED_HISTORY)).thenReturn(false);
        LocalDate MIN_OCCUPANCY_DATE = businessDate;
        when(dateService.getEarliestOccupancyDate()).thenReturn(MIN_OCCUPANCY_DATE.toDate());
        setupTestLdbConfig(false);
        LocalDate results = ldbService.computeSimulationEndDate(null);
        assertEquals(MIN_OCCUPANCY_DATE.minusDays(2), results);
    }

    @Test
    void test_computeSimulationEndDate_whenRealDataStartDateIsInPast_withLdbOverrideSasHistory() {
        LocalDate businessDate = LocalDate.now();
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.LDB_OVERRIDE_SAS_HISTORY_WITH_SIMULATED_HISTORY)).thenReturn(true);
        LocalDate realDataStartDate = businessDate.minusDays(100);
        setupTestLdbConfig(false);
        LocalDate results = ldbService.computeSimulationEndDate(realDataStartDate);
        assertEquals(realDataStartDate.minusDays(2), results);
    }

    @Test
    void test_computeSimulationEndDate_whenRealDataStartDateIsInFuture_withLdbOverrideSasHistory() {
        LocalDate businessDate = LocalDate.now();
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.LDB_OVERRIDE_SAS_HISTORY_WITH_SIMULATED_HISTORY)).thenReturn(true);
        LocalDate realDataStartDate = businessDate.plusDays(100);
        setupTestLdbConfig(false);
        LocalDate results = ldbService.computeSimulationEndDate(realDataStartDate);
        assertEquals(businessDate.minusDays(2), results);
    }

    @Test
    void test_computeSimulationEndDate_whenRealDataStartDateIsToday_withLdbOverrideSasHistory() {
        LocalDate businessDate = LocalDate.now();
        when(dateService.getBusinessDate()).thenReturn(businessDate.toDate());
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.LDB_OVERRIDE_SAS_HISTORY_WITH_SIMULATED_HISTORY)).thenReturn(true);
        LocalDate realDataStartDate = businessDate;
        setupTestLdbConfig(false);
        LocalDate results = ldbService.computeSimulationEndDate(realDataStartDate);
        assertEquals(businessDate.minusDays(2), results);
    }

    @Test
    void test_isSufficientPaceAvailablePostSoftOpening_softOpeningDateNotEntered() {
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        boolean results = ldbService.isSufficientPaceAvailablePostSoftOpening();
        assertFalse(results);
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_isSufficientPaceAvailablePostSoftOpening_insufficientPace() {
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(FUTURE_SOFT_OPENING_END_DATE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn(LDBService.PACE_DAYS_REQUIRED_TO_DISABLE_PACE_POPULATION - 1);
        boolean results = ldbService.isSufficientPaceAvailablePostSoftOpening();
        assertFalse(results);
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_isSufficientPaceAvailablePostSoftOpening_sufficientPace() {
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(FUTURE_SOFT_OPENING_END_DATE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn(LDBService.PACE_DAYS_REQUIRED_TO_DISABLE_PACE_POPULATION);
        boolean results = ldbService.isSufficientPaceAvailablePostSoftOpening();
        assertTrue(results);
    }

    @Test
    void isSufficientHistoricalDataAvailablePostSoftOpening_softOpeningDateNotEntered() {
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        boolean results = ldbService.isSufficientHistoricalDataAvailablePostSoftOpening();
        assertFalse(results);
    }

    @Test
    void isSufficientHistoricalDataAvailablePostSoftOpening_sufficientHistoryAvailable() {
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(FUTURE_SOFT_OPENING_END_DATE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn(LDBService.NUMBER_OF_DAYS_HISTORY_REQUIRED_AFTER_SNAPSHOT_DATE);
        boolean results = ldbService.isSufficientHistoricalDataAvailablePostSoftOpening();
        assertTrue(results);
    }

    @Test
    void isSufficientHistoricalDataAvailablePostSoftOpening_inSufficientHistoryAvailable() {
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(FUTURE_SOFT_OPENING_END_DATE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn(LDBService.NUMBER_OF_DAYS_HISTORY_REQUIRED_AFTER_SNAPSHOT_DATE - 3);
        boolean results = ldbService.isSufficientHistoricalDataAvailablePostSoftOpening();
        assertFalse(results);
    }

    @Test
    void test_getLDBBuildHistory() {
        LDBBuildHistory entity = new LDBBuildHistory();
        entity.setUserId(USER_ID.toString());
        List<LDBBuildHistory> entities = List.of(entity);
        GlobalUser user = new GlobalUser();
        user.setFullName(USER_NAME);
        when(userService.getGlobalUser(USER_ID, false)).thenReturn(user);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBBuildHistory.GET_ALL, new HashMap<>())).thenReturn(entities);
        List<LDBBuildHistory> results = ldbService.getLDBBuildHistory();
        assertEquals(entities, results);
        assertEquals(USER_NAME, entity.getUserId());
        verify(userService).getGlobalUser(USER_ID, false);
        verify(multiPropertyCrudService).findByNamedQueryForSingleProperty(PROPERTY_ID, LDBBuildHistory.GET_ALL, new HashMap<>());
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Test
    void test_startLDBBuildJob() {
        List<ConsolidatedPropertyView> properties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        properties.add(property);
        when(configParamsService.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_INPUT_LOCATION)).thenReturn(LDB_FOLDER);
        ldbService.startLDBBuildJob(properties);
        ArgumentCaptor<Map> parameterCaptor = ArgumentCaptor.forClass(Map.class);
        verify(jobService).startJob(Mockito.eq(JobName.LimitedDataBuildJob), parameterCaptor.capture());
        Map<String, Object> parameters = (Map<String, Object>) parameterCaptor.getValue();
        assertNotNull(parameters.get(JobParameterKey.DATE));
        assertEquals(PROPERTY_ID, parameters.get(JobParameterKey.PROPERTY_ID));
        assertEquals("will generate", parameters.get(JobParameterKey.PARAM_FILE));
        assertEquals(false, parameters.get(JobParameterKey.USE_EXISTING_CSV_FILES));
        assertEquals(LDB_FOLDER, parameters.get(JobParameterKey.LDB_INPUT_FOLDER));
        assertNull(parameters.get(JobParameterKey.USER_ID));
    }

    @Test
    void test_getPropertiesDueForRebuild_noBookingActivity() {
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(null);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @Test
    void test_getPropertiesDueForRebuild_notOpen() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.plusDays(180).toDate());
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @Test
    void test_getPropertiesDueForRebuild_openLessThan30Days() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.plusDays(29).toDate());
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @Test
    void test_getPropertiesDueForRebuild_noLdbConfig() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @Test
    void test_getPropertiesDueForRebuild_propertyUnlocked() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(false);
        config.setPatternSource(PatternSource.CLONE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @Test
    void test_getPropertiesDueForRebuild_noSoftOpeningEndDate() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(true);
        config.setSoftOpeningEndDate(null);
        config.setPatternSource(PatternSource.CLONE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @Test
    void test_getPropertiesDueForRebuild_noCloneSources() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(true);
        config.setSoftOpeningEndDate(today.minusDays(60));
        config.setPatternSource(PatternSource.CLONE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(ldbPatternService.isClonePatternSourceConfigurationComplete()).thenReturn(false);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @Test
    void test_getPropertiesDueForRebuild_afterSoftOpening() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(true);
        config.setSoftOpeningEndDate(today.minusDays(10));
        config.setPatternSource(PatternSource.CLONE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(ldbPatternService.isClonePatternSourceConfigurationComplete()).thenReturn(true);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(355);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @Test
    void test_getPropertiesDueForRebuild_insufficientProjections() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(true);
        config.setSoftOpeningEndDate(today.plusDays(60));
        config.setPatternSource(PatternSource.CLONE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(300);
        when(ldbPatternService.isClonePatternSourceConfigurationComplete()).thenReturn(true);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
        verify(projectionDataService).getNumberOfFutureProjectionDays();
    }

    @Test
    void test_getPropertiesDueForRebuild_recentlyBuilt() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(true);
        config.setSoftOpeningEndDate(today.plusDays(60));
        config.setPatternSource(PatternSource.CLONE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(365);
        List<LDBBuildHistory> buildHistory = new ArrayList<>();
        LDBBuildHistory history = new LDBBuildHistory();
        history.setBuildDate(today.minusDays(7).toLocalDateTime(LocalTime.MIDNIGHT));
        buildHistory.add(history);
        when(ldbPatternService.isClonePatternSourceConfigurationComplete()).thenReturn(true);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBBuildHistory.GET_ALL), Mockito.anyMap())).thenReturn(buildHistory);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_getPropertiesDueForRebuild_buildInProgress() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(true);
        config.setSoftOpeningEndDate(today.plusDays(60));
        config.setPatternSource(PatternSource.CLONE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(365);
        List<LDBBuildHistory> buildHistory = new ArrayList<>();
        LDBBuildHistory history = new LDBBuildHistory();
        history.setBuildDate(today.minusDays(31).toLocalDateTime(LocalTime.MIDNIGHT));
        buildHistory.add(history);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBBuildHistory.GET_ALL), Mockito.anyMap())).thenReturn(buildHistory);
        when(jobServicesBridgeExtension.getJobServicesBridge()).thenReturn(jobServicesBridge);
        when(jobServicesBridge.getJobExecutionService()).thenReturn(jobExecutionService);
        when(jobExecutionService.isJobActive(Mockito.eq(JobName.LimitedDataBuildJob), Mockito.anyMap())).thenReturn(true);
        when(ldbPatternService.isClonePatternSourceConfigurationComplete()).thenReturn(true);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(0, results.size());
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_getPropertiesDueForRebuild_selfClone() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(true);
        config.setSoftOpeningEndDate(today.plusDays(60));
        config.setPatternSource(PatternSource.SELF);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(365);
        List<LDBBuildHistory> buildHistory = new ArrayList<>();
        LDBBuildHistory history = new LDBBuildHistory();
        history.setBuildDate(today.minusDays(31).toLocalDateTime(LocalTime.MIDNIGHT));
        buildHistory.add(history);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBBuildHistory.GET_ALL), Mockito.anyMap())).thenReturn(buildHistory);
        when(jobServicesBridgeExtension.getJobServicesBridge()).thenReturn(jobServicesBridge);
        when(jobServicesBridge.getJobExecutionService()).thenReturn(jobExecutionService);
        when(jobExecutionService.isJobActive(Mockito.eq(JobName.LimitedDataBuildJob), Mockito.anyMap())).thenReturn(false);
        when(ldbPatternService.isClonePatternSourceConfigurationComplete()).thenReturn(false);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(1, results.size());
    }

    @SuppressWarnings("unchecked")
    @Test
    void test_getPropertiesDueForRebuild_happyPath() {
        LocalDate today = LocalDate.now();
        List<Stage> stages = Arrays.asList(Stage.ONE_WAY, Stage.TWO_WAY);
        List<ConsolidatedPropertyView> ldbProperties = new ArrayList<>();
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        ldbProperties.add(property);
        when(globalCrudService.<ConsolidatedPropertyView>findByNamedQuery(ConsolidatedPropertyView.LDB_ENABLED_BY_STAGE, QueryParameter.with("stages", stages).parameters())).thenReturn(ldbProperties);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(today.minusDays(180).toDate());
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setLocked(true);
        config.setSoftOpeningEndDate(today.plusDays(60));
        config.setPatternSource(PatternSource.CLONE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(365);
        List<LDBBuildHistory> buildHistory = new ArrayList<>();
        LDBBuildHistory history = new LDBBuildHistory();
        history.setBuildDate(today.minusDays(31).toLocalDateTime(LocalTime.MIDNIGHT));
        buildHistory.add(history);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBBuildHistory.GET_ALL), Mockito.anyMap())).thenReturn(buildHistory);
        when(jobServicesBridgeExtension.getJobServicesBridge()).thenReturn(jobServicesBridge);
        when(jobServicesBridge.getJobExecutionService()).thenReturn(jobExecutionService);
        when(jobExecutionService.isJobActive(Mockito.eq(JobName.LimitedDataBuildJob), Mockito.anyMap())).thenReturn(false);
        when(ldbPatternService.isClonePatternSourceConfigurationComplete()).thenReturn(true);
        List<ConsolidatedPropertyView> results = ldbService.getPropertiesDueForRebuild();
        assertEquals(1, results.size());
    }

    @Test
    void test_areSufficientProjectionsAvailable_tooFewAndNoRealData() {
        LocalDate today = LocalDate.now();
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(today.plusDays(63));
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(300);
        assertFalse(ldbService.areSufficientProjectionsAvailable());
        verify(projectionDataService).getNumberOfFutureProjectionDays();
    }

    @Test
    void test_areSufficientProjectionsAvailable_tooFewAndNotEnoughRealData() {
        LocalDate today = LocalDate.now();
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(today.minusDays(100));
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(100);
        assertFalse(ldbService.areSufficientProjectionsAvailable());
        verify(projectionDataService).getNumberOfFutureProjectionDays();
    }

    @Test
    void test_areSufficientProjectionsAvailable_tooFewButEnoughRealData() {
        LocalDate today = LocalDate.now();
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(today.minusDays(64));
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(300);
        assertTrue(ldbService.areSufficientProjectionsAvailable());
        verify(projectionDataService).getNumberOfFutureProjectionDays();
    }

    @Test
    void test_areSufficientProjectionsAvailable_enoughProjections() {
        when(projectionDataService.getNumberOfFutureProjectionDays()).thenReturn(365);
        assertTrue(ldbService.areSufficientProjectionsAvailable());
        verify(projectionDataService).getNumberOfFutureProjectionDays();
    }

    @Test
    void test_createConfigCsvFile_duringSoftOpening() throws IOException {
        List<LDBConfig> entityList = new ArrayList<>();
        LocalDate hotelOpeningDate = new LocalDate("2016-01-01");
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(FUTURE_SOFT_OPENING_END_DATE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        DBLoc dbLoc = new DBLoc();
        dbLoc.setServerName(DB_SERVER_NAME);
        dbLoc.setDbName(DATABASE_NAME);
        dbLoc.setPortNumber(DB_PORT_NUMBER);
        when(dataSourceCache.getDBLoc(PROPERTY_ID)).thenReturn(dbLoc);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(hotelOpeningDate.toDate());
        LocalDate realDataStartDate = LocalDate.now();
        LocalDate simulationEndDate = hotelOpeningDate.minusDays(1);
        File file = ldbService.createConfigCsvFile(LDB_FOLDER, realDataStartDate, simulationEndDate);
        assertEquals(LDBService.CONFIG_FILE_NAME, file.getName());
        String contents = FileUtils.readFileToString(file);
        assertTrue(contents.contains("MAX_DTA,365,"));
        assertTrue(contents.contains("DB_SERVER," + DB_SERVER_NAME + ","));
        assertTrue(contents.contains("DB_USER," + SystemConfig.getSasDbUserId() + ","));
        assertTrue(contents.contains("DB_PASSWORD," + SystemConfig.getSasDbPassword() + ","));
        assertTrue(contents.contains("DB_NAME," + DATABASE_NAME + ","));
        assertTrue(contents.contains("DB_PORT," + DB_PORT_NUMBER + ","));
        assertTrue(contents.contains("REAL_DATA_START_DT," + realDataStartDate.toString("yyyy-MM-dd") + ","));
        assertTrue(contents.contains("SIM_END_DT," + simulationEndDate.toString("yyyy-MM-dd") + ","));
        file.delete();
    }

    @Test
    void test_createConfigCsvFile_hotelNotOpen() throws IOException {
        List<LDBConfig> entityList = new ArrayList<>();
        LocalDate hotelOpeningDate = new LocalDate("2100-01-01");
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(FUTURE_SOFT_OPENING_END_DATE);
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        DBLoc dbLoc = new DBLoc();
        dbLoc.setServerName(DB_SERVER_NAME);
        dbLoc.setDbName(DATABASE_NAME);
        dbLoc.setPortNumber(DB_PORT_NUMBER);
        when(dataSourceCache.getDBLoc(PROPERTY_ID)).thenReturn(dbLoc);
        when(multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PROPERTY_ID, TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(hotelOpeningDate.toDate());
        LocalDate realDataStartDate = hotelOpeningDate;
        LocalDate simulationEndDate = LocalDate.now().minusDays(1);
        File file = ldbService.createConfigCsvFile(LDB_FOLDER, realDataStartDate, simulationEndDate);
        assertEquals(LDBService.CONFIG_FILE_NAME, file.getName());
        String contents = FileUtils.readFileToString(file);
        assertTrue(contents.contains("MAX_DTA,365,"));
        assertTrue(contents.contains("DB_SERVER," + DB_SERVER_NAME + ","));
        assertTrue(contents.contains("DB_USER," + SystemConfig.getSasDbUserId() + ","));
        assertTrue(contents.contains("DB_PASSWORD," + SystemConfig.getSasDbPassword() + ","));
        assertTrue(contents.contains("DB_NAME," + DATABASE_NAME + ","));
        assertTrue(contents.contains("DB_PORT," + DB_PORT_NUMBER + ","));
        assertTrue(contents.contains("REAL_DATA_START_DT," + realDataStartDate.toString("yyyy-MM-dd") + ","));
        assertTrue(contents.contains("SIM_END_DT," + simulationEndDate.toString("yyyy-MM-dd") + ","));
        file.delete();
    }

    @Test
    void test_getLDBConfig() {
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        LDBConfig result = ldbService.getLDBConfig();
        assertEquals(config, result);
    }

    @Test
    void test_updateLDBConfig_WithSyncEventTriggered_WhenPropertyStageAboveOneWay() {
        PatternSource PATTERN_SOURCE = PatternSource.CLONE;
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        entityList.add(config);
        when(tenantCrudService.findAll(LDBConfig.class)).thenReturn(entityList);
        when(tenantCrudService.save(config)).thenReturn(config);
        when(propertyService.isStageAtLeast(Stage.ONE_WAY)).thenReturn(true);
        when(syncEventAggregatorService.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        when(alertService.getAlertType(anyString())).thenReturn(getAlertType());
        when(alertService.findExistingAlert(any())).thenReturn(null);
        LDBConfig result = ldbService.updateLDBConfig(FUTURE_SOFT_OPENING_END_DATE, PATTERN_SOURCE, true);
        assertEquals(config, result);
        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameService).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
        LDBConfig value = saveCaptor.getValue();
        assertEquals(FUTURE_SOFT_OPENING_END_DATE, value.getSoftOpeningEndDate());
        assertEquals(PATTERN_SOURCE, value.getPatternSource());
    }

    @Test
    void test_updateLDBConfig_WithSyncEventNotTriggered_WhenPropertyStageBelowOneWay() {
        PatternSource PATTERN_SOURCE = PatternSource.CLONE;
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        entityList.add(config);
        when(tenantCrudService.findAll(LDBConfig.class)).thenReturn(entityList);
        when(tenantCrudService.save(config)).thenReturn(config);
        when(propertyService.isStageAtLeast(Stage.ONE_WAY)).thenReturn(false);
        when(alertService.getAlertType(anyString())).thenReturn(getAlertType());
        when(alertService.findExistingAlert(any())).thenReturn(null);
        LDBConfig result = ldbService.updateLDBConfig(FUTURE_SOFT_OPENING_END_DATE, PATTERN_SOURCE, true);
        assertEquals(config, result);
        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        verify(syncEventAggregatorService, times(0)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        LDBConfig value = saveCaptor.getValue();
        assertEquals(FUTURE_SOFT_OPENING_END_DATE, value.getSoftOpeningEndDate());
        assertEquals(PATTERN_SOURCE, value.getPatternSource());
    }

    @Test
    void test_updateLDBConfig_triggerSyncEventFlagTrue_DateAreSameShouldNotTriggerSync() {
        PatternSource PATTERN_SOURCE = PatternSource.CLONE;
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(FUTURE_SOFT_OPENING_END_DATE);
        entityList.add(config);
        when(tenantCrudService.findAll(LDBConfig.class)).thenReturn(entityList);
        when(tenantCrudService.save(config)).thenReturn(config);
        when(syncEventAggregatorService.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED)).thenReturn(true);
        when(alertService.getAlertType(anyString())).thenReturn(getAlertType());
        when(alertService.findExistingAlert(any())).thenReturn(null);
        LDBConfig result = ldbService.updateLDBConfig(FUTURE_SOFT_OPENING_END_DATE, PATTERN_SOURCE, true);
        assertEquals(config, result);
        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        verify(syncEventAggregatorService, times(0)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameService, times(0)).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
        LDBConfig value = saveCaptor.getValue();
        assertEquals(FUTURE_SOFT_OPENING_END_DATE, value.getSoftOpeningEndDate());
        assertEquals(PATTERN_SOURCE, value.getPatternSource());
    }

    @Test
    void test_updateLDBConfig_shouldTriggerSyncEventFalse() {
        PatternSource PATTERN_SOURCE = PatternSource.CLONE;
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        entityList.add(config);
        when(tenantCrudService.findAll(LDBConfig.class)).thenReturn(entityList);
        when(tenantCrudService.save(config)).thenReturn(config);
        when(alertService.getAlertType(anyString())).thenReturn(getAlertType());
        when(alertService.findExistingAlert(any())).thenReturn(null);
        LDBConfig result = ldbService.updateLDBConfig(FUTURE_SOFT_OPENING_END_DATE, PATTERN_SOURCE, false);
        assertEquals(config, result);
        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        verify(syncEventAggregatorService, times(0)).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameService, times(0)).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED,
                CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
        LDBConfig value = saveCaptor.getValue();
        assertEquals(FUTURE_SOFT_OPENING_END_DATE, value.getSoftOpeningEndDate());
        assertEquals(PATTERN_SOURCE, value.getPatternSource());
    }

    @Test
    void test_updatePatternSource() {
        PatternSource PATTERN_SOURCE = PatternSource.SELF;
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        entityList.add(config);
        when(tenantCrudService.findAll(LDBConfig.class)).thenReturn(entityList);
        when(tenantCrudService.save(config)).thenReturn(config);
        LDBConfig result = ldbService.updatePatternSource(PATTERN_SOURCE);
        assertEquals(config, result);
        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(PATTERN_SOURCE, value.getPatternSource());
    }

    @Test
    void test_createMarketSegmentCsvFile() throws IOException {
        Integer MARKET_SEGMENT_ID1 = 1211;
        Integer MARKET_SEGMENT_ID2 = 1212;
        Integer MARKET_SEGMENT_ID3 = 1213;
        Integer MARKET_SEGMENT_ID4 = 1214;
        List<MktSeg> existingEntities = new ArrayList<>();
        MktSeg entity1 = new MktSeg();
        entity1.setId(MARKET_SEGMENT_ID1);
        entity1.setCode("foo");
        existingEntities.add(entity1);
        MktSeg entity2 = new MktSeg();
        entity2.setId(MARKET_SEGMENT_ID2);
        entity2.setCode("foo");
        existingEntities.add(entity2);
        MktSeg entity3 = new MktSeg();
        entity3.setId(MARKET_SEGMENT_ID3);
        entity3.setCode("foo");
        existingEntities.add(entity3);
        MktSeg entity4 = new MktSeg();
        entity4.setId(MARKET_SEGMENT_ID4);
        entity4.setCode(LDBService.DUMMY_MARKET_SEGMENT_CODE);
        existingEntities.add(entity4);
        when(tenantCrudService.findAll(MktSeg.class)).thenReturn(existingEntities);
        String response = ldbService.createMarketSegmentCsvFile(LDB_FOLDER);
        assertEquals("created file " + LDB_FOLDER + File.separator + LDBService.MARKET_SEGMENT_FILE_NAME + " with " + (existingEntities.size() - 1) + " market segments", response);
        File file = new File(LDB_FOLDER, LDBService.MARKET_SEGMENT_FILE_NAME);
        assertTrue(file.exists());
        BufferedReader reader = new BufferedReader(new FileReader(file));
        String header = reader.readLine();
        assertEquals("MKT_SEG_ID,MKT_SEG_GRP_ID,WEIGHT", header.trim());
        String line1 = reader.readLine();
        assertEquals(MARKET_SEGMENT_ID1 + "," + MARKET_SEGMENT_ID1 + ",1", line1.trim());
        String line2 = reader.readLine();
        assertEquals(MARKET_SEGMENT_ID2 + "," + MARKET_SEGMENT_ID2 + ",1", line2.trim());
        String line3 = reader.readLine();
        assertEquals(MARKET_SEGMENT_ID3 + "," + MARKET_SEGMENT_ID3 + ",1", line3.trim());
        assertNull(reader.readLine());
        reader.close();
        file.delete();
        verify(tenantCrudService).findAll(MktSeg.class);
    }

    @Test
    void test_createRoomTypeCapacityCsvFile() throws IOException {
        Integer ACCOM_TYPE_ID1 = 1211;
        Integer ACCOM_TYPE_ID2 = 1212;
        Integer ACCOM_TYPE_ID3 = 1213;
        Integer CAPACITY1 = 913;
        Integer CAPACITY2 = 914;
        Integer CAPACITY3 = 915;
        List<AccomType> existingEntities = new ArrayList<>();
        AccomType entity1 = new AccomType();
        entity1.setId(ACCOM_TYPE_ID1);
        entity1.setAccomTypeCapacity(CAPACITY1);
        existingEntities.add(entity1);
        AccomType entity2 = new AccomType();
        entity2.setId(ACCOM_TYPE_ID2);
        entity2.setAccomTypeCapacity(CAPACITY2);
        existingEntities.add(entity2);
        AccomType entity3 = new AccomType();
        entity3.setId(ACCOM_TYPE_ID3);
        entity3.setAccomTypeCapacity(CAPACITY3);
        existingEntities.add(entity3);
        when(tenantCrudService.<AccomType>findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(existingEntities);
        String response = ldbService.createRoomTypeCapacityCsvFile(LDB_FOLDER);
        assertEquals("created file " + LDB_FOLDER + File.separator + LDBService.ROOM_TYPE_CAPACITY_FILE_NAME + " with " + existingEntities.size() + " room types", response);
        File file = new File(LDB_FOLDER, LDBService.ROOM_TYPE_CAPACITY_FILE_NAME);
        assertTrue(file.exists());
        BufferedReader reader = new BufferedReader(new FileReader(file));
        String header = reader.readLine();
        assertEquals("ROOM_TYPE_ID,CAPACITY", header.trim());
        String line1 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID1 + "," + CAPACITY1, line1.trim());
        String line2 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID2 + "," + CAPACITY2, line2.trim());
        String line3 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID3 + "," + CAPACITY3, line3.trim());
        assertNull(reader.readLine());
        reader.close();
        file.delete();
        verify(tenantCrudService).findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with("propertyId", PROPERTY_ID).parameters());
    }

    @Test
    void test_createRoomTypeCapacityCsvFile_hybridLdb() throws IOException {
        Integer ACCOM_TYPE_ID1 = 1211;
        Integer ACCOM_TYPE_ID2 = 1212;
        Integer CAPACITY1 = 913;
        Integer CAPACITY2 = 914;
        List<AccomType> hybridRoomTypes = new ArrayList<>();
        AccomType entity1 = new AccomType();
        entity1.setId(ACCOM_TYPE_ID1);
        entity1.setAccomTypeCapacity(CAPACITY1);
        hybridRoomTypes.add(entity1);
        AccomType entity2 = new AccomType();
        entity2.setId(ACCOM_TYPE_ID2);
        entity2.setAccomTypeCapacity(CAPACITY2);
        hybridRoomTypes.add(entity2);
        List<Object> changedAccomTypeIds = Arrays.asList(ACCOM_TYPE_ID1, ACCOM_TYPE_ID2);
        LDBConfig ldbConfig = new LDBConfig();
        ldbConfig.setHybrid(true);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_ALL)).thenReturn(ldbConfig);
        when(tenantCrudService.findByNamedQuery(LDBConfigAccomType.GET_ALL_ACCOMTYPE_IDS)).thenReturn(changedAccomTypeIds);
        when(tenantCrudService.<AccomType>findByNamedQuery(AccomType.ALL_BY_IDS, QueryParameter.with("accomTypeIds", changedAccomTypeIds).parameters())).thenReturn(hybridRoomTypes);
        String response = ldbService.createRoomTypeCapacityCsvFile(LDB_FOLDER);
        assertEquals("created file " + LDB_FOLDER + File.separator + LDBService.ROOM_TYPE_CAPACITY_FILE_NAME + " with " + hybridRoomTypes.size() + " room types", response);
        File file = new File(LDB_FOLDER, LDBService.ROOM_TYPE_CAPACITY_FILE_NAME);
        assertTrue(file.exists());
        BufferedReader reader = new BufferedReader(new FileReader(file));
        String header = reader.readLine();
        assertEquals("ROOM_TYPE_ID,CAPACITY", header.trim());
        String line1 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID1 + "," + CAPACITY1, line1.trim());
        String line2 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID2 + "," + CAPACITY2, line2.trim());
        reader.close();
        file.delete();
        verify(tenantCrudService).findByNamedQuery(AccomType.ALL_BY_IDS, QueryParameter.with("accomTypeIds", changedAccomTypeIds).parameters());
        verify(tenantCrudService, never()).findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with("propertyId", PROPERTY_ID).parameters());
    }

    @Test
    void test_createRoomClassCsvFile() throws IOException {
        Integer ACCOM_TYPE_ID1 = 1211;
        Integer ACCOM_TYPE_ID2 = 1212;
        Integer ACCOM_TYPE_ID3 = 1213;
        Integer ACCOM_CLASS_ID1 = 913;
        Integer ACCOM_CLASS_ID2 = 914;
        Integer ACCOM_CLASS_ID3 = 915;
        List<AccomType> existingEntities = new ArrayList<>();
        AccomType entity1 = new AccomType();
        entity1.setId(ACCOM_TYPE_ID1);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(ACCOM_CLASS_ID1);
        entity1.setAccomClass(accomClass1);
        existingEntities.add(entity1);
        AccomType entity2 = new AccomType();
        entity2.setId(ACCOM_TYPE_ID2);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(ACCOM_CLASS_ID2);
        entity2.setAccomClass(accomClass2);
        existingEntities.add(entity2);
        AccomType entity3 = new AccomType();
        entity3.setId(ACCOM_TYPE_ID3);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(ACCOM_CLASS_ID3);
        entity3.setAccomClass(accomClass3);
        existingEntities.add(entity3);
        when(tenantCrudService.<AccomType>findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with("propertyId", PROPERTY_ID).parameters())).thenReturn(existingEntities);
        String response = ldbService.createRoomClassCsvFile(LDB_FOLDER);
        assertEquals("created file " + LDB_FOLDER + File.separator + LDBService.ROOM_CLASS_FILE_NAME + " with " + existingEntities.size() + " room types", response);
        File file = new File(LDB_FOLDER, LDBService.ROOM_CLASS_FILE_NAME);
        assertTrue(file.exists());
        BufferedReader reader = new BufferedReader(new FileReader(file));
        String header = reader.readLine();
        assertEquals("ROOM_TYPE_ID,ROOM_CLASS_ID", header.trim());
        String line1 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID1 + "," + ACCOM_CLASS_ID1, line1.trim());
        String line2 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID2 + "," + ACCOM_CLASS_ID2, line2.trim());
        String line3 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID3 + "," + ACCOM_CLASS_ID3, line3.trim());
        assertNull(reader.readLine());
        reader.close();
        file.delete();
        verify(tenantCrudService).findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with("propertyId", PROPERTY_ID).parameters());
    }

    @Test
    void test_createRoomClassCsvFile_hybridLdb() throws IOException {
        Integer ACCOM_TYPE_ID1 = 1211;
        Integer ACCOM_TYPE_ID2 = 1212;
        Integer ACCOM_CLASS_ID1 = 913;
        Integer ACCOM_CLASS_ID2 = 914;
        List<AccomType> hybridAccomTypes = new ArrayList<>();
        AccomType entity1 = new AccomType();
        entity1.setId(ACCOM_TYPE_ID1);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(ACCOM_CLASS_ID1);
        entity1.setAccomClass(accomClass1);
        hybridAccomTypes.add(entity1);
        AccomType entity2 = new AccomType();
        entity2.setId(ACCOM_TYPE_ID2);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(ACCOM_CLASS_ID2);
        entity2.setAccomClass(accomClass2);
        hybridAccomTypes.add(entity2);
        List<Object> changedAccomTypeIds = Arrays.asList(ACCOM_TYPE_ID1, ACCOM_TYPE_ID2);
        LDBConfig ldbConfig = new LDBConfig();
        ldbConfig.setHybrid(true);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_ALL)).thenReturn(ldbConfig);
        when(tenantCrudService.findByNamedQuery(LDBConfigAccomType.GET_ALL_ACCOMTYPE_IDS)).thenReturn(changedAccomTypeIds);
        when(tenantCrudService.<AccomType>findByNamedQuery(AccomType.ALL_BY_IDS, QueryParameter.with("accomTypeIds", changedAccomTypeIds).parameters())).thenReturn(hybridAccomTypes);
        String response = ldbService.createRoomClassCsvFile(LDB_FOLDER);
        assertEquals("created file " + LDB_FOLDER + File.separator + LDBService.ROOM_CLASS_FILE_NAME + " with " + hybridAccomTypes.size() + " room types", response);
        File file = new File(LDB_FOLDER, LDBService.ROOM_CLASS_FILE_NAME);
        assertTrue(file.exists());
        BufferedReader reader = new BufferedReader(new FileReader(file));
        String header = reader.readLine();
        assertEquals("ROOM_TYPE_ID,ROOM_CLASS_ID", header.trim());
        String line1 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID1 + "," + ACCOM_CLASS_ID1, line1.trim());
        String line2 = reader.readLine();
        assertEquals(ACCOM_TYPE_ID2 + "," + ACCOM_CLASS_ID2, line2.trim());
        assertNull(reader.readLine());
        reader.close();
        file.delete();
        verify(tenantCrudService).findByNamedQuery(AccomType.ALL_BY_IDS, QueryParameter.with("accomTypeIds", changedAccomTypeIds).parameters());
        verify(tenantCrudService, never()).findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with("propertyId", PROPERTY_ID).parameters());
    }

    @Test
    void getZipInputFilesTest() {
        when(configParamsService.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_INPUT_LOCATION)).thenReturn(LDB_FOLDER + File.separator + "zipFile");
        Map<String, String> inputZipFile = ldbService.getInputZipFile();
        assertEquals(1, inputZipFile.size());
        assertEquals("GGGGI.zip", inputZipFile.get("zip"));
    }

    @Test
    void getNoZipInputFilesTest() {
        when(configParamsService.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_INPUT_LOCATION)).thenReturn(LDB_FOLDER);
        Map<String, String> inputZipFile = ldbService.getInputZipFile();
        assertEquals(1, inputZipFile.size());
        assertEquals("not needed", inputZipFile.get("zip"));
    }

    @Test
    void verifyUpdateIpMaskingDateRange() {
        List dateResult = new ArrayList<>();
        LocalDate lastNonSoftExtractDate = new LocalDate("2008-06-30");
        dateResult.add(lastNonSoftExtractDate.toDate());
        when(tenantCrudService.findByNativeQuery(LDBConfig.FIND_LAST_SOFT_OPENING_END_DATE_FROM_LDB_CONFIG)).thenReturn(dateResult);
        LocalDate newNonSoftExtractDate = new LocalDate("2008-12-31");
        ldbService.updateIpMaskingDateRange(newNonSoftExtractDate);
        verify(tenantCrudService).executeUpdateByNamedQuery(LDBConfig.UPDATE_IP_CFG_MARK_PROPERTY_DATE_TABLE, QueryParameter.with("maskingEndDate", newNonSoftExtractDate).and("earlierMaskingDate", lastNonSoftExtractDate).parameters());
    }

    @Test
    void verifyUpdateIpMaskingDateRangeWhenNullLastNonSoftExtractDate() {
        List dateResult = new ArrayList<>();
        dateResult.add(null);
        when(tenantCrudService.findByNativeQuery(LDBConfig.FIND_LAST_SOFT_OPENING_END_DATE_FROM_LDB_CONFIG)).thenReturn(dateResult);
        LocalDate newNonSoftExtractDate = new LocalDate("2008-12-31");
        ldbService.updateIpMaskingDateRange(newNonSoftExtractDate);
        verify(tenantCrudService).executeUpdateByNamedQuery(LDBConfig.UPDATE_IP_CFG_MARK_PROPERTY_DATE_TABLE_BASED_ON_LATEST_END_DATE, QueryParameter.with("maskingEndDate", newNonSoftExtractDate).parameters());
    }

    @Test
    void verifyUpdateIpMaskingDateRangeShouldUpdateBasedOnLatestEndDateWhenLDBConfigIsEmpty() {
        List dateResult = new ArrayList<>();
        when(tenantCrudService.findByNativeQuery(LDBConfig.FIND_LAST_SOFT_OPENING_END_DATE_FROM_LDB_CONFIG)).thenReturn(dateResult);
        LocalDate newNonSoftExtractDate = new LocalDate("2008-12-31");
        ldbService.updateIpMaskingDateRange(newNonSoftExtractDate);
        verify(tenantCrudService).executeUpdateByNamedQuery(LDBConfig.UPDATE_IP_CFG_MARK_PROPERTY_DATE_TABLE_BASED_ON_LATEST_END_DATE, QueryParameter.with("maskingEndDate", newNonSoftExtractDate).parameters());
    }

    @Test
    void verifyShouldSkipIpMaskingDateRangeWhenFirstTimeInsertingNonSoftOpeningDate() {
        List dateResult = new ArrayList<>();
        when(tenantCrudService.findByNativeQuery(LDBConfig.FIND_LAST_SOFT_OPENING_END_DATE_FROM_LDB_CONFIG)).thenReturn(dateResult);
        LocalDate newNonSoftExtractDate = new LocalDate("2008-12-31");
        ldbService.updateIpMaskingDateRange(newNonSoftExtractDate);
        verify(tenantCrudService, times(0)).executeUpdateByNamedQuery(LDBConfig.UPDATE_IP_CFG_MARK_PROPERTY_DATE_TABLE, QueryParameter.with("maskingEndDate", newNonSoftExtractDate).and("earlierMaskingDate", newNonSoftExtractDate).parameters());
    }

    @Test
    void testIsHiltonTrue() {
        WorkContextType wc = new WorkContextType();
        wc.setClientCode("Hilton");
        PacmanWorkContextHelper.setWorkContext(wc);
        assertTrue(ldbService.isHilton());
    }

    @Test
    void testIsHiltonFalse() {
        WorkContextType wc = new WorkContextType();
        wc.setClientCode("NotHilton");
        PacmanWorkContextHelper.setWorkContext(wc);
        assertFalse(ldbService.isHilton());
    }

    @Test
    void testHasLdbRunFalseNullScenario() {
        when(tenantCrudService.findByNamedQuery(LDBBuildHistory.GET_ALL)).thenReturn(null);
        assertFalse(ldbService.hasLdbRun());
    }

    @Test
    void isLdbEnabled() {
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(true);
        assertTrue(ldbService.isLdbEnabled());
    }

    @Test
    void testHasLdbRunFalseEmptyScenario() {
        List<LDBBuildHistory> ldbBuildHistoryList = new ArrayList<>();
        when(tenantCrudService.<LDBBuildHistory>findByNamedQuery(LDBBuildHistory.GET_ALL)).thenReturn(ldbBuildHistoryList);
        assertFalse(ldbService.hasLdbRun());
    }

    @Test
    void testHasLdbRunTrue() {
        List<LDBBuildHistory> ldbBuildHistoryList = new ArrayList<>();
        LDBBuildHistory ldbBuildHistory = new LDBBuildHistory();
        ldbBuildHistoryList.add(ldbBuildHistory);
        when(tenantCrudService.<LDBBuildHistory>findByNamedQuery(LDBBuildHistory.GET_ALL)).thenReturn(ldbBuildHistoryList);
        assertTrue(ldbService.hasLdbRun());
    }

    @Test
    void deleteLdbInputFolderLocation() {
        String context = Constants.CONFIG_PARAMS_NODE_PREFIX + "." + CLIENT_CODE + "." + PROPERTY_CODE;
        when(configParamsService.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_INPUT_LOCATION)).thenReturn("");
        ldbService.deleteLdbInputFolderLocation();
        configParamsService.deleteParameterValue(context, IPConfigParamName.CORE_LIMITED_DATA_BUILD_INPUT_LOCATION.value(), Boolean.TRUE);
        Mockito.verify(configParamsService).deleteParameterValue(context, IPConfigParamName.CORE_LIMITED_DATA_BUILD_INPUT_LOCATION.value(), Boolean.TRUE);
    }

    @Test
    void testIsTLUK() {
        ldbService.isTLUK();
        verify(clientService).isTLUK();
    }

    @Test
    void testHasExtractsBeenProcessedFalse() {
        when(tenantCrudService.findByNamedQuerySingleResult(FileMetadata.FOR_NUMBER_OF_FULL_PUSH_EXTRACTS, QueryParameter.with("bde", 1).and("processStatusId", Constants.PROCESS_STATUS_SUCCESSFUL).and("tenantPropertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Long.valueOf(0));
        assertFalse(ldbService.hasExtractsBeenProcessed());
    }

    @Test
    void testHasExtractsBeenProcessedTrue() {
        when(tenantCrudService.findByNamedQuerySingleResult(FileMetadata.FOR_NUMBER_OF_FULL_PUSH_EXTRACTS, QueryParameter.with("bde", 1).and("processStatusId", Constants.PROCESS_STATUS_SUCCESSFUL).and("tenantPropertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(Long.valueOf(5));
        assertTrue(ldbService.hasExtractsBeenProcessed());
    }

    @Test
    void testGetFirstBDEExtract() {
        final Date expectedDate = LocalDate.now().plusDays(-30).toDate();
        when(tenantCrudService.findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB)).thenReturn(expectedDate);
        assertEquals(expectedDate, ldbService.getFirstBdeExtractDate());
        verify(tenantCrudService).findByNamedQuerySingleResult(FileMetadata.FIRST_BDE_EXTRACT_DATE_LDB);
    }

    @Test
    @Tag("limitedDataBuild-flaky")
    void shouldCreateLdbInputFolderLocation() {
        String clientCode = "TestClient";
        String propertyCode = "TestProperty";
        String expectedFolderLocation = SystemConfig.getG3DataFolder().replace("\\", "/") + "LDB/" + clientCode + "/" + propertyCode;
        String folderCreatedAtLocation = ldbService.createLdbInputFolder(clientCode, propertyCode);
        assertEquals(expectedFolderLocation, folderCreatedAtLocation);
        verify(configParamsService).addParameterValue("pacman.TestClient.TestProperty", IPConfigParamName.CORE_LIMITED_DATA_BUILD_INPUT_LOCATION.value(), expectedFolderLocation);
        File folder = new File(folderCreatedAtLocation);
        assertTrue(folder.exists());
        folder.delete();
    }

    @Test
    void setLDBParameter() {
        ldbService.setLDBParameter(true);
        Mockito.verify(configParamsService).addParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value(), "true");
    }

    private String getContext() {
        return Constants.getPropertyConfigContext(CLIENT_CODE, PROPERTY_CODE);
    }

    @Test
    void shouldRevertHybridLDBPropertyToStandard() {
        when(configParamsService.propertyNode(any(WorkContextType.class))).thenReturn(getContext());
        ldbService.revertLdbByRTPropertyToStandard();
        Mockito.verify(configParamsService).addParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value(), "false");
        Mockito.verify(projectionDataService).deleteAllProjections();
        Mockito.verify(configParamsService).deleteParameterValue(eq("pacman.Hilton.BOSCO"), Mockito.eq(IPConfigParamName.CORE_LIMITED_DATA_BUILD_INPUT_LOCATION.value()), Mockito.eq(Boolean.TRUE));
        Mockito.verify(tenantCrudService).findAll(LDBConfig.class);
        Mockito.verify(tenantCrudService).delete(any(Collection.class));
    }

    @Test
    void testRemoveOverriddenIpParametersForNonTLUKAndNonScandicOrSgroup() {
        PacmanWorkContextHelper.setClientCode("BSTN");
        ldbService.clientService = new ClientService();
        ldbService.removeOverriddenIpParameters();
        ArgumentCaptor<List<String>> runtimeParamsCaptor = ArgumentCaptor.forClass(List.class);
        verify(roaRuntimeParamService).removeRuntimeOverridesForSpecifiedParameters(runtimeParamsCaptor.capture());
        List<String> runtimeOvrParams = runtimeParamsCaptor.getValue();
        List<String> runtimeOverideParametersList = List.of(MIN_ELAS, MAX_ELAS, DEFAULT_ELAS, OUTLIER_DETECT, TS_CLASS_ID, GROUP_BC_HISTORY, TRANSIENT_BC_HISTORY, FCST_HISTORY_LENGTH_VALUE);
        assertTrue(CollectionUtils.isEqualCollection(runtimeOverideParametersList, runtimeOvrParams));
        ArgumentCaptor<List<String>> propertyAttributeParamsCaptor = ArgumentCaptor.forClass(List.class);
        verify(propertyAttributeService).removePropertyAttributeOverrideForSpecifiedParameters(propertyAttributeParamsCaptor.capture());
        List<String> propertyAttributeOvrParams = propertyAttributeParamsCaptor.getValue();
        List<String> propertyAttributeOverideParametersList = List.of(MAX_FCST_HISTORY_LENGTH.getAttributeName());
        assertTrue(CollectionUtils.isEqualCollection(propertyAttributeOverideParametersList, propertyAttributeOvrParams));
    }

    @Test
    void testRemoveOverriddenIpParametersForScandic() {
        PacmanWorkContextHelper.setClientCode(SCANDIC);
        ldbService.clientService = new ClientService();
        ldbService.removeOverriddenIpParameters();
        verify(roaRuntimeParamService, times(0)).removeRuntimeOverridesForSpecifiedParameters(any());
        verify(propertyAttributeService, times(0)).removePropertyAttributeOverrideForSpecifiedParameters(any());
    }

    @Test
    void testRemoveOverriddenIpParametersForSGroup() {
        PacmanWorkContextHelper.setClientCode(SGROUP);
        ldbService.clientService = new ClientService();
        ldbService.removeOverriddenIpParameters();
        verify(roaRuntimeParamService, times(0)).removeRuntimeOverridesForSpecifiedParameters(any());
        verify(propertyAttributeService, times(0)).removePropertyAttributeOverrideForSpecifiedParameters(any());
    }

    @Test
    void testRemoveOverriddenIpParametersForTLUK() {
        PacmanWorkContextHelper.setClientCode(TRAVELODGE);
        ldbService.clientService = new ClientService();
        ldbService.removeOverriddenIpParameters();
        verify(roaRuntimeParamService, times(0)).removeRuntimeOverridesForSpecifiedParameters(any());
        verify(propertyAttributeService, times(0)).removePropertyAttributeOverrideForSpecifiedParameters(any());
    }

    @Test
    void updateLDBCOnfig_IsHybrid_SoftOpeningDate() {
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        entityList.add(config);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(Mockito.eq(PROPERTY_ID), Mockito.eq(LDBConfig.GET_ALL), Mockito.anyMap())).thenReturn(entityList);
        java.time.LocalDate softOpeningDate = java.time.LocalDate.of(2021, 12, 25);
        when(alertService.getAlertType(anyString())).thenReturn(getAlertType());
        when(alertService.findExistingAlert(any())).thenReturn(null);
        ldbService.updateLDBConfig(true, softOpeningDate);
        verify(tenantCrudService).save(ldbConfigArgumentCaptor.capture());
        LDBConfig value = ldbConfigArgumentCaptor.getValue();
        assertTrue(value.isHybrid());
        assertEquals(toJodaLocalDate(softOpeningDate), value.getSoftOpeningEndDate());
    }

    @Test
    void testUpdateLDBConfig() {
        java.time.LocalDate hotelOpeningDate = java.time.LocalDate.now().plusDays(30);
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.now()));
        config.setHotelOpeningDate(hotelOpeningDate);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);

        when(multiPropertyCrudService.save(eq(PROPERTY_ID), eq(config))).thenReturn(config);
        LDBConfig result = ldbService.updateLDBConfig(config);
        assertEquals(config, result);
        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.now()), value.getSoftOpeningEndDate());
        assertEquals(hotelOpeningDate, value.getHotelOpeningDate());
    }

    @Test
    void testUpdateLDBConfigWithHotelOpeningDate() {
        java.time.LocalDate hotelOpeningDate = java.time.LocalDate.now().plusDays(10);
        java.time.LocalDate softOpeningEndDate = java.time.LocalDate.now().plusDays(50);
        PatternSource PATTERN_SOURCE = PatternSource.GENERIC;
        List<LDBConfig> entityList = new ArrayList<>();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(softOpeningEndDate));
        config.setHotelOpeningDate(hotelOpeningDate);
        config.setPatternSource(PATTERN_SOURCE);
        config.setHybrid(false);
        entityList.add(config);

        when(tenantCrudService.findAll(LDBConfig.class)).thenReturn(entityList);
        when(tenantCrudService.save(config)).thenReturn(config);
        when(alertService.getAlertType(anyString())).thenReturn(getAlertType());
        when(alertService.findExistingAlert(any())).thenReturn(null);
        LDBConfig result = ldbService.updateLDBConfig(softOpeningEndDate, PATTERN_SOURCE, false, hotelOpeningDate);

        assertEquals(config, result);
        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(tenantCrudService).save(saveCaptor.capture());
        verify(syncEventAggregatorService, never()).registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(syncDisplayNameService, never()).addDisplayNameForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED, CustomizedDisplayName.SPECIAL_EVENT_CHANGED);
        LDBConfig value = saveCaptor.getValue();
        assertEquals(JavaLocalDateUtils.toJodaLocalDate(softOpeningEndDate), value.getSoftOpeningEndDate());
        assertEquals(hotelOpeningDate, value.getHotelOpeningDate());
        assertEquals(PATTERN_SOURCE, value.getPatternSource());
    }


    @Test
    void testNormalizationDateInPastWithSystemDate_ForPastSoftOpeningEndDate() {
        LocalDate today = LocalDate.now();
        LocalDate softOpeningEndDate = today.minusDays(30);

        when(dateService.getCaughtUpDate()).thenReturn(today.toDate());
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(softOpeningEndDate);

        final boolean normalizationDateInPast = ldbService.isNormalizationDateInPast();

        verify(dateService).getCaughtUpDate();
        verify(tenantCrudService).findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES);

        assertTrue(normalizationDateInPast);
    }

    @Test
    void testNormalizationDateInPastWithSystemDate_ForFutureSoftOpeningEndDate() {
        LocalDate today = LocalDate.now();
        LocalDate softOpeningEndDate = today.plusDays(30);

        when(dateService.getCaughtUpDate()).thenReturn(today.toDate());
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(softOpeningEndDate);

        final boolean normalizationDateInPast = ldbService.isNormalizationDateInPast();

        verify(dateService).getCaughtUpDate();
        verify(tenantCrudService).findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES);

        assertFalse(normalizationDateInPast);
    }

    @Test
    void testNormalizationDateInPastWithSystemDate_WhenSoftOpeningDateIsNull() {
        LocalDate today = LocalDate.now();

        when(dateService.getCaughtUpDate()).thenReturn(today.toDate());
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(null);

        final boolean normalizationDateInPast = ldbService.isNormalizationDateInPast();

        verify(dateService).getCaughtUpDate();
        verify(tenantCrudService).findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES);

        assertFalse(normalizationDateInPast);
    }


    @Test
    void testGetLastLimitedDataBuildDate_WhenLDBRan() {
        java.time.LocalDateTime ldbBuildDateTime = java.time.LocalDateTime.of(2024, 1, 1, 10, 10, 15);
        LDBBuildHistory ldbBuildHistory = new LDBBuildHistory();
        ldbBuildHistory.setBuildDate(JavaLocalDateUtils.toJodaLocalDateTime(ldbBuildDateTime));

        when(tenantCrudService.<LDBBuildHistory>findByNamedQuerySingleResult(LDBBuildHistory.GET_LAST_BUILD_DATE)).thenReturn(ldbBuildHistory);

        java.time.LocalDateTime lastLimitedDataBuildDate = ldbService.getLastLimitedDataBuildDate();
        assertEquals(ldbBuildDateTime, lastLimitedDataBuildDate);
    }

    @Test
    void testGetLastLimitedDataBuildDate_WhenLDBNotRan() {
        when(tenantCrudService.findByNamedQuerySingleResult(LDBBuildHistory.GET_LAST_BUILD_DATE)).thenReturn(null);
        java.time.LocalDateTime lastLimitedDataBuildDate = ldbService.getLastLimitedDataBuildDate();
        assertNull(lastLimitedDataBuildDate);
    }

    @Test
    void testGetLDBConfigUnlockedDate_WhenFirstTimeLDBLRanConfigGetLocked_andStillConfigUnlocked() {
        when(tenantCrudService.findByNativeQuery(LDBConfigAudit.GET_LATEST_LDB_CONFIG_UNLOCKED_DATE)).thenReturn(null);
        java.time.LocalDate ldbConfigUnlockDate = ldbService.getLDBConfigUnlockDate();
        assertNull(ldbConfigUnlockDate);
    }

    @Test
    void testGetLastLimitedDataBuildDate_WhenFirstTimeLDBLRanConfigGetLocked_thenConfigUnlocked() {
        java.time.LocalDate currentDate = java.time.LocalDate.now();
        List<Object> dateResult = List.of(LocalDateUtils.toDate(currentDate));
        when(tenantCrudService.findByNativeQuery(LDBConfigAudit.GET_LATEST_LDB_CONFIG_UNLOCKED_DATE)).thenReturn(dateResult);
        java.time.LocalDate ldbConfigUnlockDate = ldbService.getLDBConfigUnlockDate();
        assertEquals(currentDate, ldbConfigUnlockDate);
    }

    @Test
    void testUpdateOpeningDateIfNull_WhenNormalizationDateInPast() {
        java.time.LocalDate today = java.time.LocalDate.now();
        java.time.LocalDate hotelOpeningDate = today.minusDays(30);
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today.minusDays(30)));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        ldbService.updateOpeningDateIfNull(config);

        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(dateService).getCaughtUpJavaLocalDate();
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(hotelOpeningDate, value.getHotelOpeningDate());
    }

    @Test
    void testUpdateOpeningDateIfNull_WhenNormalizationDateInFuture_NoProjectionsDataAvailable() {
        java.time.LocalDate today = java.time.LocalDate.now();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today.plusDays(30)));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(projectionDataService.getFirstNonZeroProjectionDate()).thenReturn(null);
        ldbService.updateOpeningDateIfNull(config);

        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(dateService).getCaughtUpJavaLocalDate();
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(today, value.getHotelOpeningDate());
    }

    @Test
    void testUpdateOpeningDateIfNull_WhenNormalizationDateInFuture_FirstProjectionDateInFutureWithCaughtupDate() {
        java.time.LocalDate today = java.time.LocalDate.now();
        java.time.LocalDate firstProjectionDate = today.plusDays(15);
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today.plusDays(30)));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(projectionDataService.getFirstNonZeroProjectionDate()).thenReturn(firstProjectionDate);
        ldbService.updateOpeningDateIfNull(config);

        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(dateService).getCaughtUpJavaLocalDate();
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(firstProjectionDate, value.getHotelOpeningDate());
    }

    @Test
    void testUpdateOpeningDateIfNull_WhenNormalizationDateInFuture_FirstProjectionDateInPastWithCaughtupDate() {
        java.time.LocalDate today = java.time.LocalDate.now();
        java.time.LocalDate firstProjectionDate = today.minusDays(15);
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today.plusDays(30)));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(projectionDataService.getFirstNonZeroProjectionDate()).thenReturn(firstProjectionDate);
        ldbService.updateOpeningDateIfNull(config);

        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(dateService).getCaughtUpJavaLocalDate();
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(today, value.getHotelOpeningDate());
    }

    @Test
    void testUpdateOpeningDateIfNull_WhenNormalizationDateCaughtupDateSame() {
        java.time.LocalDate today = java.time.LocalDate.now();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(projectionDataService.getFirstNonZeroProjectionDate()).thenReturn(null);
        ldbService.updateOpeningDateIfNull(config);

        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(dateService).getCaughtUpJavaLocalDate();
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(today, value.getHotelOpeningDate());
    }

    @Test
    void testUpdateOpeningDateIfNull_WhenNormalizationDateCaughtupDateSame_FirstProjectionDateInFutureWithCaughtupDate() {
        java.time.LocalDate today = java.time.LocalDate.now();
        java.time.LocalDate firstProjectionDate = today.plusDays(15);
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(projectionDataService.getFirstNonZeroProjectionDate()).thenReturn(firstProjectionDate);
        ldbService.updateOpeningDateIfNull(config);

        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(dateService).getCaughtUpJavaLocalDate();
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(firstProjectionDate, value.getHotelOpeningDate());
    }

    @Test
    void testUpdateOpeningDateIfNull_WhenNormalizationDateCaughtupDateSame_FirstProjectionDateInPastWithCaughtupDate() {
        java.time.LocalDate today = java.time.LocalDate.now();
        java.time.LocalDate firstProjectionDate = today.minusDays(15);
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(projectionDataService.getFirstNonZeroProjectionDate()).thenReturn(firstProjectionDate);
        ldbService.updateOpeningDateIfNull(config);

        ArgumentCaptor<LDBConfig> saveCaptor = ArgumentCaptor.forClass(LDBConfig.class);
        verify(dateService).getCaughtUpJavaLocalDate();
        verify(multiPropertyCrudService).save(eq(PROPERTY_ID), saveCaptor.capture());
        LDBConfig value = saveCaptor.getValue();
        assertEquals(today, value.getHotelOpeningDate());
    }


    @Test
    void testIsDueForRebuild_AutoBuildShouldNotTrigger_WhenOpeningDateIsNull() {
        java.time.LocalDate today = java.time.LocalDate.now();
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);

        List<LDBConfig> entities = List.of(config);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_OPENING_DATE_FOR_LDB)).thenReturn(true);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBConfig.GET_ALL, new HashMap<>())).thenReturn(entities);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        config.setHotelOpeningDate(today);
        when(multiPropertyCrudService.save(PROPERTY_ID, config)).thenReturn(config);
        when(projectionDataService.getFirstProjectionDateByOpeningDate()).thenReturn(null);
        ConsolidatedPropertyView property = createProperty();
        boolean dueForRebuild = ldbService.isDueForRebuild(property);
        assertFalse(dueForRebuild);
    }

    @Test
    void testIsDueForRebuild_AutoBuildShouldNotTrigger_WhenOpeningDateIsInFuture() {
        java.time.LocalDate today = java.time.LocalDate.now();
        java.time.LocalDate firstProjectionDate = today.plusDays(15);
        LDBConfig config = new LDBConfig();
        config.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(today.plusDays(30)));
        config.setHotelOpeningDate(null);
        config.setPatternSource(PatternSource.GENERIC);
        config.setHybrid(false);

        List<LDBConfig> entities = List.of(config);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_OPENING_DATE_FOR_LDB)).thenReturn(true);
        when(multiPropertyCrudService.findByNamedQueryForSingleProperty(PROPERTY_ID, LDBConfig.GET_ALL, new HashMap<>())).thenReturn(entities);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        config.setHotelOpeningDate(firstProjectionDate);
        when(multiPropertyCrudService.save(PROPERTY_ID, config)).thenReturn(config);
        when(projectionDataService.getFirstProjectionDateByOpeningDate()).thenReturn(firstProjectionDate);
        ConsolidatedPropertyView property = createProperty();
        boolean dueForRebuild = ldbService.isDueForRebuild(property);
        assertFalse(dueForRebuild);
    }
   @Test
    void testIsEligibleBasedOnShoppedData_NoShoppedData() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(tenantCrudService.findByNamedQuery(Webrate.GET_FUTURE_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(Collections.emptyList());
        when(tenantCrudService.findByNamedQuery(Webrate.GET_PAST_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(Collections.emptyList());
        assertFalse(ldbService.isEligibleBasedOnShoppedData(today));
    }
    @Test
    void testIsEligibleBasedOnShoppedData_FutureShoppedDataAvailable() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        List<Object[]> occupancyDates = getOccupancyDates(today, 90, true);
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_FUTURE_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(occupancyDates);
        assertTrue(ldbService.isEligibleBasedOnShoppedData(today));
    }
    @Test
    void testIsEligibleBasedOnShoppedData_PastShoppedDataAvailable() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        List<Object[]> occupancyDates = getOccupancyDates(today, 120, false);
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_FUTURE_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(Collections.emptyList());
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_PAST_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(occupancyDates);
        assertTrue(ldbService.isEligibleBasedOnShoppedData(today));
    }
    @Test
    void testIsEligibleBasedOnShoppedData_FutureAndPastShoppedDataAvailable() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        List<Object[]> futureOccupancyDates = getOccupancyDates(today, 90, true);
        List<Object[]> pastOccupancyDates = getOccupancyDates(today, 120, false);
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_FUTURE_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(futureOccupancyDates);
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_PAST_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(pastOccupancyDates);
        assertTrue(ldbService.isEligibleBasedOnShoppedData(today));
    }

    @Test
    void testIsEligibleBasedOnShoppedData_SufficientShoppedDataNotAvailable() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        List<Object[]> futureOccupancyDates = getOccupancyDates(today, 40, true);
        List<Object[]> pastOccupancyDates = getOccupancyDates(today, 100, false);
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_FUTURE_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(futureOccupancyDates);
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_PAST_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(pastOccupancyDates);
        assertFalse(ldbService.isEligibleBasedOnShoppedData(today));
    }
    @Test
    void testIsEligibleBasedOnNormalizationDate_NoNormalization() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(null);
        assertFalse(ldbService.isEligibleBasedOnNormalizationDate(today));
    }

    @Test
    void testIsEligibleBasedOnNormalizationDate_NormalizationDateIsNotCrossed120DaysMark(){
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(LocalDateUtils.toJodaLocalDate(today.minusDays(110)));
        assertFalse(ldbService.isEligibleBasedOnNormalizationDate(today));
    }


    @Test
    void testIsEligibleBasedOnNormalizationDate_NoOfBDEsIsLessWithoutMaskingPeriod() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(LocalDateUtils.toJodaLocalDate(today.minusDays(120)));
        when(tenantCrudService.findByNativeQuery("EXEC GET_PROPERTY_MASKED_DAY_PROC :startDate,:endDate,:maskDaysThreshold",
                QueryParameter.with("startDate", today.minusDays(120))
                        .and("endDate", today).and("maskDaysThreshold",1).parameters())).thenReturn(Collections.emptyList());
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn("119");
        assertFalse(ldbService.isEligibleBasedOnNormalizationDate(today));
    }
    @Test
    void testIsEligibleBasedOnNormalizationDate_NoOfBDEsIsLessWithMaskingPeriod() {
        List<Object[]> results = new ArrayList<>();
        Object[] serverObj1 = new Object[]{5, 30};
        results.add(0, serverObj1);
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(LocalDateUtils.toJodaLocalDate(today.minusDays(150)));
        when(tenantCrudService.<Object[]>findByNativeQuery("EXEC GET_PROPERTY_MASKED_DAY_PROC :startDate,:endDate,:maskDaysThreshold",
                QueryParameter.with("startDate", today.minusDays(150))
                        .and("endDate", today).and("maskDaysThreshold",1).parameters())).thenReturn(results);
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn("119");
        assertFalse(ldbService.isEligibleBasedOnNormalizationDate(today));
    }
    @Test
    void testIsEligibleBasedOnNormalizationDate_HappyPath_WithoutMaskingDays() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(LocalDateUtils.toJodaLocalDate(today.minusDays(120)));
        when(tenantCrudService.findByNativeQuery("EXEC GET_PROPERTY_MASKED_DAY_PROC :startDate,:endDate,:maskDaysThreshold",
                QueryParameter.with("startDate", today.minusDays(120))
                        .and("endDate", today).and("maskDaysThreshold",1).parameters())).thenReturn(Collections.emptyList());
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn("120");
        assertTrue(ldbService.isEligibleBasedOnNormalizationDate(today));
    }
    @Test
    void testIsEligibleBasedOnNormalizationDate_HappyPath_WithmaskingDays() {
        List<Object[]> results = new ArrayList<>();
        Object[] serverObj1 = new Object[]{5, 30};
        results.add(0, serverObj1);
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(LocalDateUtils.toJodaLocalDate(today.minusDays(150)));
        when(tenantCrudService.<Object[]>findByNativeQuery("EXEC GET_PROPERTY_MASKED_DAY_PROC :startDate,:endDate,:maskDaysThreshold",
                QueryParameter.with("startDate", today.minusDays(150))
                        .and("endDate", today).and("maskDaysThreshold",1).parameters())).thenReturn(results);
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn("120");
        assertTrue(ldbService.isEligibleBasedOnNormalizationDate(today));
    }
    @Test
    void testIsLdbEligibleForSuggestFeature_NoCriteriaMet() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        when(tenantCrudService.findByNamedQuery(Webrate.GET_FUTURE_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(Collections.emptyList());
        when(tenantCrudService.findByNamedQuery(Webrate.GET_PAST_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(Collections.emptyList());
        assertFalse(ldbService.isLdbEligibleForSuggestFeature());
    }

    @Test
    void testIsLdbEligibleForSuggestFeature_ShoppingDataOnly() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        List<Object[]> occupancyDates = getOccupancyDates(today, 90, true);
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_FUTURE_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(occupancyDates);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(null);
        assertTrue(ldbService.isLdbEligibleForSuggestFeature());
    }

    @Test
    void testIsLdbEligibleForSuggestFeature_NormalizationDataOnly() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);

        // shopping data fails
        when(tenantCrudService.findByNamedQuery(
                Webrate.GET_FUTURE_OCCUPANCY_DTS,
                QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE)
                        .and("caughtUpDate", today.toString())
                        .parameters()))
                .thenReturn(java.util.Collections.emptyList());
        when(tenantCrudService.findByNamedQuery(
                Webrate.GET_PAST_OCCUPANCY_DTS,
                QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE)
                        .and("caughtUpDate", today.toString())
                        .parameters()))
                .thenReturn(java.util.Collections.emptyList());

        // normalization passes
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES))
                .thenReturn(LocalDateUtils.toJodaLocalDate(today.minusDays(130)));
        when(tenantCrudService.findByNativeQuery(
                "EXEC GET_PROPERTY_MASKED_DAY_PROC :startDate,:endDate,:maskDaysThreshold",
                QueryParameter.with("startDate", today.minusDays(130))
                        .and("endDate", today)
                        .and("maskDaysThreshold", 1)
                        .parameters()))
                .thenReturn(java.util.Collections.emptyList());
        when(tenantCrudService.findByNamedQuerySingleResult(
                org.mockito.Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE),
                org.mockito.Mockito.anyMap()))
                .thenReturn("130");

        // OR logic ⇒ true
        org.junit.jupiter.api.Assertions.assertTrue(ldbService.isLdbEligibleForSuggestFeature());
    }

    @Test
    void testIsLdbEligibleForSuggestFeature_BothCriteriaMet() {
        java.time.LocalDate today = java.time.LocalDate.now();
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(today);
        List<Object[]> occupancyDates = getOccupancyDates(today, 90, true);
        when(tenantCrudService.<Object[]>findByNamedQuery(Webrate.GET_FUTURE_OCCUPANCY_DTS,QueryParameter.with("webrateStatus", WEBRATE_ACTIVE_STATUS_CODE).and("caughtUpDate", today.toString()).parameters())).thenReturn(occupancyDates);
        when(tenantCrudService.findByNamedQuerySingleResult(LDBConfig.GET_NORMALIZATION_DATES)).thenReturn(LocalDateUtils.toJodaLocalDate(today.minusDays(130)));
        when(tenantCrudService.findByNativeQuery("EXEC GET_PROPERTY_MASKED_DAY_PROC :startDate,:endDate,:maskDaysThreshold",
                QueryParameter.with("startDate", today.minusDays(130))
                        .and("endDate", today).and("maskDaysThreshold",1).parameters())).thenReturn(Collections.emptyList());
        when(tenantCrudService.findByNamedQuerySingleResult(Mockito.eq(FileMetadata.COUNT_BDE_SINCE_DATE), Mockito.anyMap())).thenReturn("130");
        assertTrue(ldbService.isLdbEligibleForSuggestFeature());
    }
    private static List<Object[]> getOccupancyDates(java.time.LocalDate today, int max, boolean isfutureDatesRequired) {
        List<Object[]> occupancyDates = new ArrayList<>();
        if(isfutureDatesRequired) {
            for (int i = 0; i < max; i++) {
                Object[] dataRow = { today.plusDays(i)};
                occupancyDates.add(dataRow);
            }
        } else {
            for (int i = 0; i < max; i++) {
                Object[] dataRow = { today.minusDays(i)};
                occupancyDates.add(dataRow);
            }
        }
        return occupancyDates;
    }

    private ConsolidatedPropertyView createProperty() {
        ConsolidatedPropertyView property = new ConsolidatedPropertyView();
        property.setPropertyId(PROPERTY_ID);
        property.setClientCode(CLIENT_CODE);
        property.setPropertyCode(PROPERTY_CODE);
        property.setStage(Stage.TWO_WAY);
        property.setPropertyTimeZone("America/Chicago");
        property.setLdbEnabled(Boolean.TRUE);
        return property;
    }

    @Test
    void updateAMSCompositionConfigurations() {
        final List<AnalyticalMarketSegment> splitAMS = List.of(IT, CNR, MKT, DISC, LNR);
        when(explodedMCATAttributesService.getSplitMarketSegments()).thenReturn(SPLIT_MARKET_SEGMENTS);
        when(marketSegmentComponent.getProcessStatusByName("Updated")).thenReturn(new ProcessStatus());
        when(analyticalMarketSegmentService.getAMSRulesWithMarketCodes(SPLIT_MARKET_SEGMENTS)).thenReturn(splitAMS);
        when(marketSegmentService.getAllActiveMktSegments()).thenReturn(List.of(getMktSeg("BAR", 10),
                getMktSeg("IT", 1), getMktSeg("MKT", 2), getMktSeg("CNR", 3), getMktSeg("DISC", 4),
                getMktSeg("LNR", 5)));
        var mktSegMaster1 = getMarketSegmentMaster("BAR", 1, 0, 1, 0);
        var mktSegMaster2 = getMarketSegmentMaster("IT", 1, 1, 0, 0);
        var mktSegMaster3 = getMarketSegmentMaster("MKT", 2, 1, 0, 1);
        var mktSegMaster4 = getMarketSegmentMaster("CNR", 3, 1, 0, 0);
        var mktSegMaster5 = getMarketSegmentMaster("DISC", 3, 1, 0, 0);
        var mktSegMaster6 = getMarketSegmentMaster("LNR", 3, 1, 0, 0);
        when(marketSegmentMasterService.findAllMarketSegmentMasters()).thenReturn(List.of(mktSegMaster1, mktSegMaster2, mktSegMaster3, mktSegMaster4, mktSegMaster5, mktSegMaster6));
        when(reservationNightService.getStandardHonorsRateCodes()).thenReturn(List.of("HHNSRR"));
        when(tenantCrudService.save(anyList())).thenAnswer(AdditionalAnswers.returnsFirstArg());

        try {
            ldbService.createAMSCompositionChangeRecords();
        } catch (Exception e) {
            fail("Should not fail");
        }
        verify(tenantCrudService, times(2)).save(compositionChangeCaptor.capture());
        final List<AMSCompositionChange> savedRecords = compositionChangeCaptor.getAllValues().get(0);
        savedRecords.sort(Comparator.comparing(AMSCompositionChange::getAMSId));
        assertEquals(5, savedRecords.size());
        for (int i = 0; i < savedRecords.size(); i++) {
            AMSCompositionChange change = savedRecords.get(i);
            assertEquals(i + 1, change.getAMSId());
            assertEquals(i + 1, change.getMarketSegmentId());
        }
    }

    private MarketSegmentMaster getMarketSegmentMaster(String code, int yieldTypeId, int qualified, int fenced, int link) {
        MarketSegmentMaster mktSegMaster = new MarketSegmentMaster();
        mktSegMaster.setCode(code);
        mktSegMaster.setBusinessTypeId(2);
        mktSegMaster.setYieldTypeId(yieldTypeId);
        mktSegMaster.setForecastActivityTypeId(1);
        mktSegMaster.setQualified(qualified);
        mktSegMaster.setBookingBlockPc(0);
        mktSegMaster.setFenced(fenced);
        mktSegMaster.setPackageValue(0);
        mktSegMaster.setLink(link);
        mktSegMaster.setPriceByBar(0);
        return mktSegMaster;
    }

    @Test
    void createDefaultAMSForMissingMS() {
        final Map<String, Integer> segmentMap = Stream.of(IT, CNR, MKT).collect(toMap(AnalyticalMarketSegment::getMarketCode, AnalyticalMarketSegment::getId));
        when(tenantCrudService.save(anyList())).thenReturn(List.of(DISC, LNR));
        ldbService.createDefaultAMSForMissingMS(SPLIT_MARKET_SEGMENTS, segmentMap, f -> getTestMktSegMaster());
        verify(tenantCrudService, times(2)).save(amsCaptor.capture());
        final List<AnalyticalMarketSegment> amsCreated = amsCaptor.getAllValues().get(0);
        assertEquals(2, amsCreated.size());
        assertEquals(5, segmentMap.size());
    }

    @Test
    void doNotCreateDefaultAMS_whenAllSplitMSPresent() {
        final Map<String, Integer> segmentMap =
                Stream.of(IT, CNR, MKT, DISC, LNR).collect(toMap(AnalyticalMarketSegment::getMarketCode,
                        AnalyticalMarketSegment::getId));
        final var mktSegMaster = getTestMktSegMaster();
        Function<String, IMktSegAttributes> attributeSup = f -> mktSegMaster;
        ldbService.createDefaultAMSForMissingMS(SPLIT_MARKET_SEGMENTS, segmentMap, attributeSup);
        verifyNoInteractions(tenantCrudService);
    }

    private static MarketSegmentMaster getTestMktSegMaster() {
        final var mktSegMaster = new MarketSegmentMaster();
        mktSegMaster.setBusinessTypeId(1);
        mktSegMaster.setYieldTypeId(1);
        mktSegMaster.setQualified(0);
        mktSegMaster.setBookingBlockPc(0);
        mktSegMaster.setPriceByBar(0);
        mktSegMaster.setFenced(1);
        mktSegMaster.setPackageValue(0);
        mktSegMaster.setLink(0);
        mktSegMaster.setCode("TEST");
        mktSegMaster.setForecastActivityTypeId(1);
        return mktSegMaster;
    }

    @Test
    void testUpdateLDBConfigResolveAlertWhenSoftOpeningDateUpdated() {

        LDBConfig ldbConfig = new LDBConfig();
        ldbConfig.setSoftOpeningEndDate(JavaLocalDateUtils.toJodaLocalDate(JAVA_SOFT_OPENING_END_DATE));
        ldbConfig.setPatternSource(PatternSource.GENERIC);

        when(tenantCrudService.findAll(LDBConfig.class)).thenReturn(List.of(ldbConfig));
        when(tenantCrudService.save(ldbConfig)).thenReturn(ldbConfig);

        ldbService.updateLDBConfig(JAVA_FUTURE_SOFT_OPENING_END_DATE, PatternSource.GENERIC, false, JAVA_FUTURE_SOFT_OPENING_END_DATE.minusDays(10));
        verify(alertService, atLeastOnce()).resolveAllAlerts(eq(AlertType.ApproachingSoftOpeningDate), eq(PacmanWorkContextHelper.getPropertyId()));
        verify(alertService, atLeastOnce()).resolveAllAlerts(eq(AlertType.ApproachingLDBEndDate), eq(PacmanWorkContextHelper.getPropertyId()));

    }

    @Test
    void shouldReturnTrueWhenSufficientRoomSoldDataAvailablePostHotelOpening(){
        java.time.LocalDate hotelOpeningDate = java.time.LocalDate.of(2024,8,31);

        when(tenantCrudService.findByNamedQuerySingleResult(TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(JavaLocalDateUtils.toDate(hotelOpeningDate));
        when(tenantCrudService.findByNamedQuerySingleResult(FileMetadata.COUNT_BDE_SINCE_DATE,
                QueryParameter.with("snapshotDate", JavaLocalDateUtils.toDate(hotelOpeningDate)).parameters())).thenReturn(30);
        assertTrue(ldbService.isSufficientRoomSoldDataAvailablePostHotelOpening());
    }

    @Test
    void shouldReturnFalseWhenSufficientRoomSoldDataNotAvailablePostHotelOpening(){
        java.time.LocalDate hotelOpeningDate = java.time.LocalDate.of(2024,8,31);

        when(tenantCrudService.findByNamedQuerySingleResult(TotalActivity.GET_HOTEL_OPENING_DATE)).thenReturn(JavaLocalDateUtils.toDate(hotelOpeningDate));
        when(tenantCrudService.findByNamedQuerySingleResult(FileMetadata.COUNT_BDE_SINCE_DATE,
                QueryParameter.with("snapshotDate", JavaLocalDateUtils.toDate(hotelOpeningDate)).parameters())).thenReturn(29);
        assertFalse(ldbService.isSufficientRoomSoldDataAvailablePostHotelOpening());
    }

    @Test
    void testUpdateCachedLDBConfig(){
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT_DD_MMM_YYYY);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm");
        GlobalUser user = new GlobalUser();
        user.setFullName(USER_NAME);
        ConsolidatedPropertyView view = new ConsolidatedPropertyView();
        LDBPropertyRolloutData ldbPropertyRolloutData = ldbService.mapToDto(view);
        when(ldbPropertyRolloutDataCache.get(PacmanWorkContextHelper.getPropertyId())).thenReturn(ldbPropertyRolloutData);
        when(userService.getGlobalUser(USER_ID, false)).thenReturn(user);
        java.time.LocalDate localDate = java.time.LocalDate.now();
        java.time.LocalDate normalizationDate = localDate.plusDays(60);
        java.time.LocalDate openingDate = localDate.plusDays(20);
        ldbService.updateCachedLDBConfig(normalizationDate,PatternSource.GENERIC,normalizationDate.minusDays(30),PatternSource.GENERIC,openingDate.minusDays(10), openingDate);
        assertEquals(openingDate.format(dateFormatter),ldbPropertyRolloutData.getOpeningDate());
        assertEquals(normalizationDate.format(dateFormatter),ldbPropertyRolloutData.getSoftOpeningEndDate());
        assertEquals(normalizationDate.plusDays(365).format(dateFormatter),ldbPropertyRolloutData.getEstimatedNormalPropertyDate());
        assertEquals(java.time.LocalDateTime.now().format(dateTimeFormatter)+ " " + USER_NAME,ldbPropertyRolloutData.getOpeningDateLastUpdatedInfo());
        assertEquals(java.time.LocalDateTime.now().format(dateTimeFormatter)+ " " + USER_NAME,ldbPropertyRolloutData.getSoftOpeningEndDateLastUpdatedInfo());
    }

    private static AnalyticalMarketSegment getAnalyticalMarketSegment(String mkt2, String a2, String mkt_qyl, int id) {
        final AnalyticalMarketSegment amsRule = AMSResolutionTest.createAMSRule(mkt2, a2, mkt_qyl, 10,
                RateCodeTypeEnum.EQUALS);
        amsRule.setId(id);
        return amsRule;
    }

    public static MktSeg getMktSeg(String name, int id) {
        MktSeg mktSeg = new MktSeg();
        mktSeg.setId(id);
        mktSeg.setCode(name);
        mktSeg.setDescription(name);
        mktSeg.setStatusId(1);
        mktSeg.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        return mktSeg;
    }

    private InfoMgrTypeEntity getAlertType() {
        InfoMgrTypeEntity alertTypeEntity = new InfoMgrTypeEntity();
        alertTypeEntity.setName(AlertType.ApproachingSoftOpeningDate.name());
        alertTypeEntity.setEnabled(true);
        return alertTypeEntity;
    }

}
