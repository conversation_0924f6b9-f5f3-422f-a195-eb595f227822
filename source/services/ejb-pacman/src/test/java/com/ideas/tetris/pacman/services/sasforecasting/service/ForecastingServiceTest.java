package com.ideas.tetris.pacman.services.sasforecasting.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.xml.schema.forecasting.request.v1.ForecastRequestType;
import com.ideas.tetris.pacman.common.xml.schema.forecasting.request.v1.SASRequest;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.contextholder.BusinessContext;
import com.ideas.tetris.pacman.services.contextholder.BusinessContextService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.eventaggregator.SystemComponent;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateShoppingConfig;
import com.ideas.tetris.pacman.services.webrate.service.WebrateDataSchedulingService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.util.xml.XmlHelper;
import com.ideas.tetris.platform.common.util.xml.XmlHelperImpl;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.EXECUTE_FORECAST_STEP_CDP_FOR_UNPROCESSED_RSS_EXTRACT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class ForecastingServiceTest extends AbstractG3JupiterTest {
    @Mock
    DateService dateService;

    @Mock
    DecisionService decisionService;

    @Mock
    PacmanConfigParamsService configParamsService;

    @Mock
    BusinessContextService businessContextService;

    @Mock
    SyncEventAggregatorService syncEventAggregatorService;

    @InjectMocks
    ForecastingService forecastingService;

    @Mock
    WebrateDataSchedulingService webrateDataSchedulingService;

    @Mock
    FileMetadataService fileMetadataService;

    private CrudService crudService;

    private XmlHelper xmlHelper;

    private String QUERY_GET_IND_TASK_LIST = "select Prop_Task_id from ip_cfg_property_task where Task_Name = 'ELSD_IDP_REFPRICE'";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        Mockito.when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS.value())).thenReturn("12345");
        Mockito.when(configParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value())).thenReturn("100");
        xmlHelper = new XmlHelperImpl();
    }

    @Test
    public void testGetOperationName() {
        assertEquals("forecast", forecastingService.getOperationName());
    }

    @Test
    public void testGetDecisionBDE() {
        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.BDE);
        Decision decision = new Decision();
        Mockito.when(decisionService.createBdeDecision()).thenReturn(decision);
        assertEquals(decision, forecastingService.getDecision(forecastRequest));
    }

    @Test
    public void testGetDecisionLTBDE() {
        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.LTBDE);
        Decision decision = new Decision();
        Mockito.when(decisionService.createLtBdeDecision()).thenReturn(decision);
        assertEquals(decision, forecastingService.getDecision(forecastRequest));
    }

    @Test
    public void testGetDecisionBDEUserInvokedSync() {
        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.BDE);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        assertEquals(decision, forecastingService.getDecision(forecastRequest));
    }

    @Test
    public void testGetDecisionCDP() {
        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.CDP);
        Decision decision = new Decision();
        Mockito.when(decisionService.createCdpDecision()).thenReturn(decision);
        assertEquals(decision, forecastingService.getDecision(forecastRequest));
    }

    @Test
    public void testGetDecisionCDPUserInvokedSync() {
        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.CDP);
        Mockito.when(syncEventAggregatorService.isSystemComponentDirty(SystemComponent.USER_INVOKED_SYNC)).thenReturn(true);
        Decision decision = new Decision();
        Mockito.when(decisionService.createCdpDecision()).thenReturn(decision);
        assertEquals(decision, forecastingService.getDecision(forecastRequest));
    }

    @Test
    public void testGetDecisionOnDemand() {
        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.ON_DEMAND);
        Decision decision = new Decision();
        Mockito.when(decisionService.createOndemandDecision()).thenReturn(decision);
        assertEquals(decision, forecastingService.getDecision(forecastRequest));
    }

    @Test
    public void testGetDecisionUnknownOperationType() {
        assertThrows(TetrisException.class, () -> {
            ForecastRequestType forecastRequest = new ForecastRequestType();
            forecastRequest.setOperationType("unknown");
            forecastingService.getDecision(forecastRequest);
        });
    }

    @Test
    public void shouldCallPopulateForecastingWebRateStalenessWhenUseForecastingWebRateStalenessEnabled() {
        ForecastRequestType forecastRequest = new ForecastRequestType();
        Decision decision = new Decision();
        forecastRequest.setOperationType(Constants.BDE);
        decision.setId(new Integer(111));

        mockDataToTestForecastingStalenessCall();

        ForecastingService forecastingServiceMock = Mockito.spy(forecastingService);

        forecastingServiceMock.getSASRequest(forecastRequest, decision);

        Mockito.verify(forecastingServiceMock, times(1)).initStalenessEntries(any());
        Mockito.verify(forecastingServiceMock, times(1)).populateForecastingWebRateStaleness(any());
    }

    private void mockDataToTestForecastingStalenessCall() {
        Date caughtUpDate = new Date();
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Date forecastWindowStartDate = DateUtils.addDays(caughtUpDate, 1);
        Mockito.when(dateService.getForecastWindowStartDate()).thenReturn(forecastWindowStartDate);
        Date forecastWindowEndDate = DateUtils.addDays(caughtUpDate, 2);
        Mockito.when(dateService.getForecastWindowEndDateBDE()).thenReturn(forecastWindowEndDate);

        BusinessContext bc = new BusinessContext();
        bc.setMasterRoomClassId(99);
        bc.setSingleBARDecisionEnabled(true);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(bc);
    }

    @Test
    public void testPopulateForecastingWebRateStaleness() {
        //when
        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.BDE);

        //create webrateShoppingConfigList
        List<WebrateShoppingConfig> webrateShoppingConfigList = new ArrayList<>();
        WebrateShoppingConfig webrateShoppingConfig1 = getWebrateShoppingConfig(2, 7, 3);
        WebrateShoppingConfig webrateShoppingConfig2 = getWebrateShoppingConfig(5, 5, 1);
        webrateShoppingConfigList.add(webrateShoppingConfig1);
        webrateShoppingConfigList.add(webrateShoppingConfig2);
        Mockito.when(webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty()).thenReturn(webrateShoppingConfigList);

        forecastingService.populateForecastingWebRateStaleness(forecastRequest);

        //Verify
        assertEquals(0, forecastRequest.getStalenessEntries().getStalenessEntry().get(0).getStartDta().intValue());
        assertEquals(webrateShoppingConfig1.getRollingDaysToShop().intValue(), forecastRequest.getStalenessEntries().getStalenessEntry().get(0).getEndDta().intValue());
        assertEquals(webrateShoppingConfig1.getWebrateShoppingFrequency() + webrateShoppingConfig1.getWebrateShoppingThreshold(), forecastRequest.getStalenessEntries().getStalenessEntry().get(0).getValue());

        assertEquals(webrateShoppingConfig1.getRollingDaysToShop() + 1, forecastRequest.getStalenessEntries().getStalenessEntry().get(1).getStartDta().intValue());
        assertEquals(webrateShoppingConfig2.getRollingDaysToShop().intValue(), forecastRequest.getStalenessEntries().getStalenessEntry().get(1).getEndDta().intValue());
        assertEquals(webrateShoppingConfig2.getWebrateShoppingFrequency() + webrateShoppingConfig2.getWebrateShoppingThreshold(), forecastRequest.getStalenessEntries().getStalenessEntry().get(1).getValue());
    }

    private WebrateShoppingConfig getWebrateShoppingConfig(int rollingDaysToShop, int webrateShoppingFrequency, int webrateShoppingThreshold) {
        WebrateShoppingConfig webrateShoppingConfig = new WebrateShoppingConfig();
        webrateShoppingConfig.setRollingDaysToShop(rollingDaysToShop);
        webrateShoppingConfig.setWebrateShoppingFrequency(webrateShoppingFrequency);
        webrateShoppingConfig.setWebrateShoppingThreshold(webrateShoppingThreshold);
        return webrateShoppingConfig;
    }

    @Test
    public void testGetSasRequestLTBDE() {
        Date caughtUpDate = new Date();
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Date forecastWindowStartDate = DateUtils.addDays(caughtUpDate, 1);
        Mockito.when(dateService.getForecastWindowStartDate()).thenReturn(forecastWindowStartDate);
        Date forecastWindowEndDateLtBde = DateUtils.addDays(caughtUpDate, 2);
        Mockito.when(dateService.getForecastWindowEndDateLTBDE()).thenReturn(forecastWindowEndDateLtBde);

        BusinessContext bc = new BusinessContext();
        bc.setMasterRoomClassId(99);
        bc.setSingleBARDecisionEnabled(true);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(bc);

        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.LTBDE);
        Decision decision = new Decision();
        decision.setId(111);

        SASRequest sasRequest = (SASRequest) forecastingService.getSASRequest(forecastRequest, decision);

        assertEquals(111, sasRequest.getForecastRequest().getDecisionId());
        assertEquals(xmlHelper.convertDateToXMLGregorian(caughtUpDate), sasRequest.getForecastRequest().getCaughtUpDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowStartDate), sasRequest.getForecastRequest().getFcstStartDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowEndDateLtBde), sasRequest.getForecastRequest().getFcstEndDate());
        assertEquals("12345", sasRequest.getForecastRequest().getWebRateAlias());
        assertEquals(new Integer(100), sasRequest.getForecastRequest().getStaleness());
        assertEquals(bc.getMasterRoomClassId(), sasRequest.getForecastRequest().getMasterAccomClassId());
        assertFalse(sasRequest.getForecastRequest().isIsDemand360Enabled());
    }

    @Test
    public void testGetSasRequestBDE() {
        Date caughtUpDate = new Date();
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Date forecastWindowStartDate = DateUtils.addDays(caughtUpDate, 1);
        Mockito.when(dateService.getForecastWindowStartDate()).thenReturn(forecastWindowStartDate);
        Date forecastWindowEndDate = DateUtils.addDays(caughtUpDate, 2);
        Mockito.when(dateService.getForecastWindowEndDateBDE()).thenReturn(forecastWindowEndDate);

        BusinessContext bc = new BusinessContext();
        bc.setMasterRoomClassId(99);
        bc.setSingleBARDecisionEnabled(true);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(bc);

        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.BDE);
        Decision decision = new Decision();
        decision.setId(new Integer(111));

        SASRequest sasRequest = (SASRequest) forecastingService.getSASRequest(forecastRequest, decision);

        assertEquals(111, sasRequest.getForecastRequest().getDecisionId());
        assertEquals(xmlHelper.convertDateToXMLGregorian(caughtUpDate), sasRequest.getForecastRequest().getCaughtUpDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowStartDate), sasRequest.getForecastRequest().getFcstStartDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowEndDate), sasRequest.getForecastRequest().getFcstEndDate());
        assertEquals("12345", sasRequest.getForecastRequest().getWebRateAlias());
        assertEquals(new Integer(100), sasRequest.getForecastRequest().getStaleness());
        assertEquals(bc.getMasterRoomClassId(), sasRequest.getForecastRequest().getMasterAccomClassId());
        assertFalse(sasRequest.getForecastRequest().isIsDemand360Enabled());
    }

    @Test
    public void testGetSasRequestCDP() {
        Date caughtUpDate = new Date();
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Date forecastWindowStartDate = DateUtils.addDays(caughtUpDate, 1);
        Mockito.when(dateService.getForecastWindowStartDate()).thenReturn(forecastWindowStartDate);
        // note that even though its CDP, it still calls getForecastWindowEndDateBDE
        Date forecastWindowEndDate = DateUtils.addDays(caughtUpDate, 2);
        Mockito.when(dateService.getForecastWindowEndDateBDE()).thenReturn(forecastWindowEndDate);
        Mockito.when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.DEMAND360SUBSCRIBER_PROPERTY_ID.value())).thenReturn("123");

        BusinessContext bc = new BusinessContext();
        bc.setMasterRoomClassId(99);
        bc.setSingleBARDecisionEnabled(true);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(bc);

        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.CDP);
        Decision decision = new Decision();
        decision.setId(new Integer(111));

        SASRequest sasRequest = (SASRequest) forecastingService.getSASRequest(forecastRequest, decision);

        assertEquals(111, sasRequest.getForecastRequest().getDecisionId());
        assertEquals(xmlHelper.convertDateToXMLGregorian(caughtUpDate), sasRequest.getForecastRequest().getCaughtUpDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowStartDate), sasRequest.getForecastRequest().getFcstStartDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowEndDate), sasRequest.getForecastRequest().getFcstEndDate());
        assertEquals("12345", sasRequest.getForecastRequest().getWebRateAlias());
        assertEquals(new Integer(100), sasRequest.getForecastRequest().getStaleness());
        assertEquals(bc.getMasterRoomClassId(), sasRequest.getForecastRequest().getMasterAccomClassId());
        assertTrue(sasRequest.getForecastRequest().isIsDemand360Enabled());
    }
    @Test
    public void testGetSasRequestOnDemandForSmartSync() {
        Date caughtUpDate = new Date();
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Date forecastWindowStartDate = DateUtils.addDays(caughtUpDate, 1);
        Mockito.when(dateService.getForecastWindowStartDate()).thenReturn(forecastWindowStartDate);
        Date forecastWindowEndDate = DateUtils.addDays(caughtUpDate, 365);
        Mockito.when(dateService.getForecastWindowEndDateBDE()).thenReturn(forecastWindowEndDate);
        Date smartSyncEndDate = DateUtils.addDays(caughtUpDate, 15);

        BusinessContext bc = new BusinessContext();
        bc.setMasterRoomClassId(99);
        bc.setSingleBARDecisionEnabled(true);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(bc);

        ForecastRequestType forecastRequest = new ForecastRequestType();
        XmlHelperImpl xmlHelper = new XmlHelperImpl();
        forecastRequest.setFcstEndDate(xmlHelper.convertDateToXMLGregorian(smartSyncEndDate));
        forecastRequest.setOperationType(Constants.ON_DEMAND);
        Decision decision = new Decision();
        decision.setId(111);

        SASRequest sasRequest = (SASRequest) forecastingService.getSASRequest(forecastRequest, decision);

        assertEquals(111, sasRequest.getForecastRequest().getDecisionId());
        assertEquals(xmlHelper.convertDateToXMLGregorian(caughtUpDate), sasRequest.getForecastRequest().getCaughtUpDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowStartDate), sasRequest.getForecastRequest().getFcstStartDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowEndDate), sasRequest.getForecastRequest().getFcstEndDate());
        assertEquals(Constants.ON_DEMAND, sasRequest.getForecastRequest().getOperationType());
        assertEquals("12345", sasRequest.getForecastRequest().getWebRateAlias());
        assertEquals(Integer.valueOf(100), sasRequest.getForecastRequest().getStaleness());
        assertEquals(bc.getMasterRoomClassId(), sasRequest.getForecastRequest().getMasterAccomClassId());
        assertFalse(sasRequest.getForecastRequest().isIsDemand360Enabled());
    }

    @Test
    public void testGetSasRequestOnDemand() {
        Date caughtUpDate = new Date();
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Date forecastWindowStartDate = DateUtils.addDays(caughtUpDate, 1);
        Mockito.when(dateService.getForecastWindowStartDate()).thenReturn(forecastWindowStartDate);
        Date forecastWindowEndDate = DateUtils.addDays(caughtUpDate, 2);
        Mockito.when(dateService.getForecastWindowEndDateBDE()).thenReturn(forecastWindowEndDate);

        BusinessContext bc = new BusinessContext();
        bc.setMasterRoomClassId(99);
        bc.setSingleBARDecisionEnabled(true);
        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(bc);

        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.ON_DEMAND);
        Decision decision = new Decision();
        decision.setId(new Integer(111));

        SASRequest sasRequest = (SASRequest) forecastingService.getSASRequest(forecastRequest, decision);

        assertEquals(111, sasRequest.getForecastRequest().getDecisionId());
        assertEquals(xmlHelper.convertDateToXMLGregorian(caughtUpDate), sasRequest.getForecastRequest().getCaughtUpDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowStartDate), sasRequest.getForecastRequest().getFcstStartDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowEndDate), sasRequest.getForecastRequest().getFcstEndDate());
        assertEquals("12345", sasRequest.getForecastRequest().getWebRateAlias());
        assertEquals(new Integer(100), sasRequest.getForecastRequest().getStaleness());
        assertEquals(bc.getMasterRoomClassId(), sasRequest.getForecastRequest().getMasterAccomClassId());
        assertFalse(sasRequest.getForecastRequest().isIsDemand360Enabled());
    }

    @Test
    public void testRequestHeaderWithPerformanceDebugEnabled() {
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.SAS_LOG_SASPERFORMANCE_DEBUG_ENABLED.value())).thenReturn(true);
        Date caughtUpDate = new Date();
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Date forecastWindowStartDate = DateUtils.addDays(caughtUpDate, 1);
        Mockito.when(dateService.getForecastWindowStartDate()).thenReturn(forecastWindowStartDate);
        Date forecastWindowEndDate = DateUtils.addDays(caughtUpDate, 2);
        Mockito.when(dateService.getForecastWindowEndDateBDE()).thenReturn(forecastWindowEndDate);

        BusinessContext bc = new BusinessContext();
        bc.setSingleBARDecisionEnabled(true);
        bc.setMasterRoomClassId(99);

        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(bc);

        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.BDE);
        Decision decision = new Decision();
        decision.setId(111);

        SASRequest sasRequest = (SASRequest) forecastingService.getSASRequest(forecastRequest, decision);
        assertEquals(sasRequest.getRequestHeader().getPerfLogLevel(), Integer.valueOf(2));

        assertEquals(111, sasRequest.getForecastRequest().getDecisionId());
        assertEquals(xmlHelper.convertDateToXMLGregorian(caughtUpDate), sasRequest.getForecastRequest().getCaughtUpDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowStartDate), sasRequest.getForecastRequest().getFcstStartDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowEndDate), sasRequest.getForecastRequest().getFcstEndDate());
        assertEquals("12345", sasRequest.getForecastRequest().getWebRateAlias());
        assertEquals(new Integer(100), sasRequest.getForecastRequest().getStaleness());
        assertEquals(bc.getMasterRoomClassId(), sasRequest.getForecastRequest().getMasterAccomClassId());
        assertFalse(sasRequest.getForecastRequest().isIsDemand360Enabled());
    }

    @Test
    public void testRequestHeaderWithPerformanceDebugDisabled() {
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.SAS_LOG_SASPERFORMANCE_DEBUG_ENABLED.value())).thenReturn(false);
        Date caughtUpDate = new Date();
        Mockito.when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Date forecastWindowStartDate = DateUtils.addDays(caughtUpDate, 1);
        Mockito.when(dateService.getForecastWindowStartDate()).thenReturn(forecastWindowStartDate);
        Date forecastWindowEndDate = DateUtils.addDays(caughtUpDate, 2);
        Mockito.when(dateService.getForecastWindowEndDateBDE()).thenReturn(forecastWindowEndDate);

        BusinessContext bc = new BusinessContext();
        bc.setSingleBARDecisionEnabled(true);
        bc.setMasterRoomClassId(99);

        Mockito.when(businessContextService.getCurrentBusinessContext()).thenReturn(bc);

        ForecastRequestType forecastRequest = new ForecastRequestType();
        forecastRequest.setOperationType(Constants.BDE);
        Decision decision = new Decision();
        decision.setId(new Integer(111));

        SASRequest sasRequest = (SASRequest) forecastingService.getSASRequest(forecastRequest, decision);
        assertNull(sasRequest.getRequestHeader().getPerfLogLevel());

        assertEquals(111, sasRequest.getForecastRequest().getDecisionId());
        assertEquals(xmlHelper.convertDateToXMLGregorian(caughtUpDate), sasRequest.getForecastRequest().getCaughtUpDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowStartDate), sasRequest.getForecastRequest().getFcstStartDate());
        assertEquals(xmlHelper.convertDateToXMLGregorian(forecastWindowEndDate), sasRequest.getForecastRequest().getFcstEndDate());
        assertEquals("12345", sasRequest.getForecastRequest().getWebRateAlias());
        assertEquals(new Integer(100), sasRequest.getForecastRequest().getStaleness());
        assertEquals(bc.getMasterRoomClassId(), sasRequest.getForecastRequest().getMasterAccomClassId());
        assertEquals(false, sasRequest.getForecastRequest().isIsDemand360Enabled());
    }

    @Test
    public void shouldAddIndTaskListTagWhenSmartSyncAndUnprocessedWebrateExtract() {
        crudService = mock(CrudService.class);
        forecastingService.setCrudService(crudService);
        when(crudService.findByNativeQuerySingleResult(QUERY_GET_IND_TASK_LIST, Collections.emptyMap())).thenReturn("1234");
        when(configParamsService.getBooleanParameterValue(EXECUTE_FORECAST_STEP_CDP_FOR_UNPROCESSED_RSS_EXTRACT)).thenReturn(true);
        when(fileMetadataService.isAnyUnprocessedRSSExtract()).thenReturn(true);
        when(syncEventAggregatorService.isAnyFlagDirty()).thenReturn(false);
        Date date = DateUtil.getDate(10, 10, 2025);
        ForecastRequestType forecastRequestType = forecastingService.createForecastRequestType(Constants.ON_DEMAND, date);

        assertEquals("1234", forecastRequestType.getIndTaskList());
        assertEquals(Constants.ON_DEMAND,forecastRequestType.getOperationType());
    }
    @Test
    public void shouldAddIndTaskListTagWhenCDPAndUnprocessedWebrateExtract() {
        crudService = mock(CrudService.class);
        forecastingService.setCrudService(crudService);
        when(crudService.findByNativeQuerySingleResult(QUERY_GET_IND_TASK_LIST, Collections.emptyMap())).thenReturn("1234");
        when(configParamsService.getBooleanParameterValue(EXECUTE_FORECAST_STEP_CDP_FOR_UNPROCESSED_RSS_EXTRACT)).thenReturn(true);
        when(fileMetadataService.isAnyUnprocessedRSSExtract()).thenReturn(true);
        when(syncEventAggregatorService.isAnyFlagDirty()).thenReturn(false);

        ForecastRequestType forecastRequestType = forecastingService.createForecastRequestType(Constants.CDP, null);

        assertEquals("1234", forecastRequestType.getIndTaskList());
    }

    @Test
    public void shouldNotAddIndTaskListTagWhenNotCDPAndUnprocessedWebrateExtract() {
        ForecastRequestType forecastRequestType = forecastingService.createForecastRequestType(Constants.BDE, null);
        assertNull(forecastRequestType.getIndTaskList());
    }

}
