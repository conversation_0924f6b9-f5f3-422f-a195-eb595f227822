package com.ideas.tetris.pacman.services.meetingpackagepricing.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.OverrideService;
import com.ideas.tetris.pacman.services.bestavailablerate.PrettyPricingService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingRule;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.inventorylimit.InventoryLimitDecisionService;
import com.ideas.tetris.pacman.services.meetingpackagepricing.dto.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.dto.MeetingPackageDecisionOverrideHistory;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.dto.MeetingPackagePricingDecisionKey;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.criteria.DatesWithFilter;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.criteria.MeetingPackagePaceSearchCriteria;
import com.ideas.tetris.pacman.services.meetingpackagepricing.repository.criteria.MeetingPackageSearchCriteria;
import com.ideas.tetris.pacman.services.meetingpackagepricing.service.mapper.MeetingPackagePricingMapper;
import com.ideas.tetris.pacman.services.meetingpackagepricing.transformer.MeetingPackagePricingOverrideTransformer;
import com.ideas.tetris.pacman.services.ngi.decision.decisiondelivery.DecisionType;
import com.ideas.tetris.pacman.services.ngi.dto.meetingroompricing.MeetingPackagePricing;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.sasoptimization.dto.OptimizationWindowDto;
import com.ideas.tetris.pacman.services.sasoptimization.service.OptimizationWindowService;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncFlagService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.entity.DecisionUploadType;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.CONFIG_PARAM_CONTEXT;
import static com.ideas.tetris.pacman.services.meetingpackagepricing.entity.MeetingPackageProduct.PRODUCT_TYPE_LINKED;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.fromDate;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.isDateBetween;
import static java.util.Comparator.comparing;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.function.BinaryOperator.maxBy;
import static java.util.stream.Collectors.*;
import static org.springframework.util.CollectionUtils.isEmpty;

@Component
@Transactional
public class MeetingPackageDecisionService {

    private static final String PENDING = "PENDING";
    private static final String USER = "USER";
    private static final String FLOOR = "FLOOR";
    private static final String CEIL = "CEIL";
    private static final String FLOORANDCEIL = "FLOORANDCEIL";
    private static final int DEFAULT_PRECISION = 5;
    private static final Logger logger = Logger.getLogger(InventoryLimitDecisionService.class);

    @Autowired
    private MeetingProductBarDecisionRepository meetingProductBarDecisionRepository;

    @Autowired
    private MeetingPackagePaceBarDecisionRepository meetingPackagePaceBarDecisionRepository;

    @Autowired
    private MeetingPackageBarDecisionOverrideRepository meetingPackageBarDecisionOverrideRepository;
    @Autowired
    private MeetingPackageProductRateOffsetOverrideRepository meetingPackageProductRateOffsetOverrideRepository;

    @Autowired
    private MeetingPackageProductRepository meetingPackageProductRepository;

    @Autowired
    private MeetingPackagePricingOverrideTransformer meetingPackagePricingOverrideTransformer;

    @Autowired
    private PacmanConfigParamsService configParamsService;

    @Autowired
    private DateService dateService;

    @Autowired
    private SpecialEventService specialEventService;

    @Autowired
    private DecisionService decisionService;

    @Autowired
    private MeetingPackageProductService meetingPackageProductService;

    @Autowired
    private OverrideService overrideService;

    @Autowired
    private SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
    private SyncFlagService syncFlagService;

    @Autowired
    private UserService userService;

    @Autowired
    protected OptimizationWindowService optimizationWindowService;

    @Autowired
    private PrettyPricingService prettyPricingService;

    @Autowired
    private MeetingPackageOptimalPriceBARService meetingPackageOptimalPriceBARService;

    @Autowired
    private MeetingPackageProductRateOffsetRepository meetingPackageProductRateOffsetRepository;

    public List<MeetingPackagePricing> getMeetingPackagePricings(Date startDate, Date endDate, Date lastUploadDate, String correlationId) {
        if (endDate.before(startDate)) {
            return Collections.emptyList();
        }

        List<MeetingPackageBarDecision> meetingPackageBarDecisions = fetchMeetingPackageBarDecisions(startDate, endDate, lastUploadDate);
        return prepareMeetingPackagePricings(meetingPackageBarDecisions, correlationId);
    }

    private List<MeetingPackageBarDecision> fetchMeetingPackageBarDecisions(Date startDate, Date endDate, Date lastUploadDate) {
        if (nonNull(lastUploadDate)) {
            return meetingProductBarDecisionRepository.getMeetingProductBarDifferentialDecisionsForUploadByOccupancyDateRange(startDate, endDate, lastUploadDate);
        }

        return meetingProductBarDecisionRepository.getMeetingProductBarDecisionsByOccupancyDateRange(startDate, endDate);
    }

    private List<MeetingPackagePricing> prepareMeetingPackagePricings(List<MeetingPackageBarDecision> meetingPackageBarDecisions, String correlationId) {
        String clientCode = PacmanWorkContextHelper.getClientCode();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();
        boolean useMeetingPackageProductPackageId = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_MEETING_PACKAGE_PRODUCT_PACKAGE_ID);
        return meetingPackageBarDecisions.stream()
                .map(meetingPackagePaceBarDecision -> createMeetingPackagePricing(clientCode, propertyCode, correlationId, useMeetingPackageProductPackageId, meetingPackagePaceBarDecision))
                .collect(Collectors.toList());
    }

    private String getYieldCurrencyCode() {
        return configParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE);
    }

    public List<MeetingPackagePricingPerDay> getMeetingPackageBarDecisions(Date startDate, Date endDate, List<Integer> meetingRoomIds, List<Integer> productIds) {
        MeetingPackageSearchCriteria searchCriteria = buildSearchCriteria(startDate, endDate, meetingRoomIds, productIds);
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = meetingProductBarDecisionRepository.searchByFilterCriteria(searchCriteria);
        if (CollectionUtils.isEmpty(meetingPackageBarDecisions)) {
            return Collections.emptyList();
        }
        return buildMeetingPackagePricingPerDay(startDate, endDate, meetingRoomIds, productIds, meetingPackageBarDecisions);
    }

    public List<MeetingPackagePricingPerDay> getMeetingPackageBarDecisionsChangesSince(Date startDate,
                                                                                       Date endDate,
                                                                                       List<Integer> meetingRoomIds,
                                                                                       List<Integer> productIds,
                                                                                       Integer decisionId) {
        MeetingPackageSearchCriteria searchCriteria = buildSearchCriteria(startDate, endDate, meetingRoomIds, productIds);
        searchCriteria.setDecisionId(Long.valueOf(decisionId));
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = meetingProductBarDecisionRepository.searchByFilterCriteria(searchCriteria);
        return buildMeetingPackagePricingPerDay(startDate, endDate, meetingRoomIds, productIds, meetingPackageBarDecisions);
    }

    private List<MeetingPackagePricingPerDay> buildMeetingPackagePricingPerDay(Date startDate,
                                                                               Date endDate,
                                                                               List<Integer> meetingRoomIds,
                                                                               List<Integer> productIds,
                                                                               List<MeetingPackageBarDecision> meetingPackageBarDecisions) {
        List<Integer> parentProductIds = getIPProductIdsForLinkedProducts(meetingPackageBarDecisions);
        List<MeetingPackageBarDecision> parentProductDecisions =
                getParentDecisionsForLinkedProducts(startDate, endDate, meetingRoomIds, parentProductIds, meetingPackageBarDecisions);

        Map<String, List<PropertySpecialEvent>> specialEventsMap = specialEventService.getAllSpecialEventsByDateRange(startDate, endDate);
        BaseMeetingRoomDTO baseMeetingRoom = meetingPackageProductService.getBaseMeetingRoom();
        Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap = getActiveOverridesForLinkedProducts(meetingPackageBarDecisions);
        Map<ProductOccupancyKey, BigDecimal> adjustmentMap = getProductAdjustmentsFromProductRateOffsets(meetingPackageBarDecisions, productIds);
        MeetingPackagePaceSearchCriteria searchCriteriaForPaceBarDecision = buildPaceSearchCriteria(startDate, endDate, meetingRoomIds, productIds, null);
        List<MeetingPackagePaceBarDecision> meetingPackagePaceBarDecision = meetingPackagePaceBarDecisionRepository.searchByFilterCriteria(searchCriteriaForPaceBarDecision);
        Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> paceBarDecisionsMap = getMeetingPackagePaceBarDecisionMap(meetingPackagePaceBarDecision);
        return MeetingPackagePricingMapper.toPricingPerDay(meetingPackageBarDecisions,
                baseMeetingRoom,
                specialEventsMap,
                productRateOffsetOverrideMap,
                adjustmentMap,
                paceBarDecisionsMap,
                parentProductDecisions);
    }

    public List<MeetingPackagePricingPerDay> getFilteredMeetingPackageBarDecisions(Date startDate,
                                                                                   Date endDate,
                                                                                   List<Integer> meetingRoomIds,
                                                                                   List<Integer> productIds,
                                                                                   String datesWithFilter,
                                                                                   Date changesSinceDateTime) {
        Optional<Integer> decisionId = getChangesSinceDecisionId(datesWithFilter, changesSinceDateTime);
        if (decisionId.isEmpty()) {
            return new ArrayList<>();
        }
        List<MeetingPackagePricingPerDay> pricingDetails = getMeetingPackageBarDecisionsChangesSince(startDate, endDate, meetingRoomIds, productIds, decisionId.get());
        List<MeetingPackagePaceBarDecision> paceDecisions = getMeetingPackagePaceDecisions(startDate, endDate, meetingRoomIds,
                productIds, decisionId.get());
        filterUnchangedPrices(pricingDetails, paceDecisions);
        return pricingDetails;
    }

    protected Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> getMeetingPackagePaceBarDecisionMap(
            List<MeetingPackagePaceBarDecision> meetingPackagePaceBarDecisionList) {

        return meetingPackagePaceBarDecisionList.stream()
                .collect(Collectors.toMap(
                        decision -> new PricingDifferentialGroupKey(
                                decision.getMeetingPackageProduct().getId(),
                                decision.getFunctionSpaceFunctionRoom().getId(),
                                decision.getOccupancyDate()
                        ),
                        Function.identity(),
                        (existing, replacement) ->
                                replacement.getDecisionId() > existing.getDecisionId() ? replacement : existing
                ));
    }

    protected Map<ProductOccupancyKey, BigDecimal> getProductAdjustmentsFromProductRateOffsets(List<MeetingPackageBarDecision> decisions, List<Integer> productIds) {
        Map<Integer, List<MeetingPackageProductRateOffset>> seasonOffsetsByProductId = new HashMap<>();
        Map<Integer, MeetingPackageProductRateOffset> defaultOffsetsByProductId = new HashMap<>();
        List<MeetingPackageProductRateOffset> productRateAdjustments = meetingPackageProductRateOffsetRepository.getMeetingPackageProductRateAdjustments(productIds);
        productRateAdjustments.forEach(offset -> {
            Integer productId = offset.getMeetingPackageProduct().getId();
            LocalDate offsetStart = offset.getStartDate();
            LocalDate offsetEnd = offset.getEndDate();
            if (isNull(offsetStart) && isNull(offsetEnd)) {
                defaultOffsetsByProductId.put(productId, offset);
            } else {
                seasonOffsetsByProductId
                        .computeIfAbsent(productId, k -> new ArrayList<>())
                        .add(offset);
            }
        });

        return decisions.stream()
                .filter(decision -> PRODUCT_TYPE_LINKED.equals(decision.getMeetingPackageProduct().getType()))
                .map(decision -> {
                    Integer productId = decision.getMeetingPackageProduct().getId();
                    Date occupancyDate = decision.getOccupancyDate();
                    LocalDate occupancyLocalDate = LocalDateUtils.toLocalDate(occupancyDate);

                    List<MeetingPackageProductRateOffset> seasonOffsets = seasonOffsetsByProductId.getOrDefault(productId, Collections.emptyList());
                    MeetingPackageProductRateOffset defaultOffset = defaultOffsetsByProductId.get(productId);

                    MeetingPackageProductRateOffset selectedOffset = seasonOffsets.stream()
                            .filter(offset -> isDateBetween(occupancyLocalDate, offset.getStartDate(), offset.getEndDate()))
                            .findFirst()
                            .orElse(defaultOffset);

                    return nonNull(selectedOffset)
                            ? Map.entry(new ProductOccupancyKey(productId, occupancyDate), selectedOffset.getOffsetValueForDate(occupancyLocalDate))
                            : null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing
                ));
    }

    protected Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> getActiveOverridesForLinkedProducts(
            List<MeetingPackageBarDecision> decisions) {

        Set<Integer> linkedProductIds = decisions.stream()
                .filter(decicion -> PRODUCT_TYPE_LINKED.equalsIgnoreCase(decicion.getMeetingPackageProduct().getType()))
                .map(decision -> decision.getMeetingPackageProduct().getId())
                .collect(toSet());
        Set<Date> occupancyDates = decisions.stream()
                .map(decision -> fromDate(decision.getOccupancyDate()))
                .distinct()
                .map(JavaLocalDateUtils::toDate)
                .collect(toSet());
        if (isEmpty(linkedProductIds) || isEmpty(occupancyDates)) {
            return new HashMap<>();
        }
        return getActiveOverridesForLinkedProductsFor(linkedProductIds, occupancyDates);
    }

    private Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> getActiveOverridesForLinkedProductsFor(
            Set<Integer> linkedProductIds, Set<Date> occupancyDates) {
        List<MeetingPackageProductRateOffsetOverride> activeOverrides =
                meetingPackageProductRateOffsetOverrideRepository.findActiveOverridesForProductOccupancyPairs(
                        linkedProductIds, occupancyDates, TenantStatusEnum.ACTIVE);

        return activeOverrides.stream()
                .collect(Collectors.toMap(
                        override -> new ProductOccupancyKey(
                                override.getProduct().getId(),
                                override.getOccupancyDate()
                        ),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    protected MeetingPackageSearchCriteria buildSearchCriteria(Date startDate, Date endDate, List<Integer> meetingRoomIds, List<Integer> productIds) {
        MeetingPackageSearchCriteria meetingPackageSearchCriteria = new MeetingPackageSearchCriteria();
        meetingPackageSearchCriteria.setStartDate(startDate);
        meetingPackageSearchCriteria.setEndDate(endDate);
        meetingPackageSearchCriteria.setMeetingRoomIds(meetingRoomIds);
        meetingPackageSearchCriteria.setProductIds(productIds);
        return meetingPackageSearchCriteria;
    }

    public int populatePaceMeetingPackageUploadDifferentialData(String operationType) {
        OptimizationWindowDto windowForType = optimizationWindowService.getWindowForType(operationType);
        Date startDate = windowForType.getStartDate();
        Date endDate = windowForType.getEndDate();
        return meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate);
    }

    public int populatePaceMeetingPackageUploadDifferentialData(Date startDate, Date endDate) {
        return meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate);
    }

    public int populatePaceMeetingPackageUploadDifferentialDetailsForManualUpload() {
        Date startDate = dateService.getOptimizationWindowStartDate();
        Date endDate = dateService.getDecisionUploadWindowEndDate();
        return meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(startDate, endDate);
    }

    public boolean isMeetingPackagePricingDecisionTypeConfigured(String externalSystem) {
        String parameterName = DecisionType.MEETING_PACKAGE_PRICING.getIntegrationConfigParamName().value(externalSystem);
        String uploadType = getMeetingPackagePricingDecisionUploadType(parameterName);
        return nonNull(uploadType) &&
                (uploadType.equals(DecisionUploadType.DIFFERENTIAL.getConfigParamValue()) || uploadType.equals(DecisionUploadType.FULL.getConfigParamValue()));
    }

    private String getMeetingPackagePricingDecisionUploadType(String parameterName) {
        return configParamsService.getValue(
                String.format(CONFIG_PARAM_CONTEXT, PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()),
                parameterName);
    }

    private MeetingPackagePricing createMeetingPackagePricing(String clientCode, String propertyCode, String correlationId,
                                                              boolean useMeetingPackageProductPackageId, MeetingPackageBarDecision meetingPackageBarDecision) {
        MeetingPackagePricing meetingPackagePricing = new MeetingPackagePricing();
        meetingPackagePricing.setClientCode(clientCode);
        meetingPackagePricing.setPropertyCode(propertyCode);
        meetingPackagePricing.setPackageId(getPackageId(useMeetingPackageProductPackageId, meetingPackageBarDecision));
        meetingPackagePricing.setFunctionRoomId(meetingPackageBarDecision.getFunctionSpaceFunctionRoom().getSalesCateringIdentifier());
        meetingPackagePricing.setOccupancyDate(meetingPackageBarDecision.getOccupancyDate());
        meetingPackagePricing.setAmount(meetingPackageBarDecision.getFinalBar());
        meetingPackagePricing.setCurrencyCode(getYieldCurrencyCode());
        meetingPackagePricing.setCorrelationId(correlationId);
        return meetingPackagePricing;
    }

    private String getPackageId(boolean useMeetingPackageProductPackageId, MeetingPackageBarDecision meetingPackageBarDecision) {
        if (useMeetingPackageProductPackageId) {
            if (isNull(meetingPackageBarDecision.getMeetingPackageProduct().getPackageId())) {
                throw new TetrisException("Package ID is missing for the meeting package product: " + meetingPackageBarDecision.getMeetingPackageProduct().getName());
            }
            return meetingPackageBarDecision.getMeetingPackageProduct().getPackageId();
        }
        return meetingPackageBarDecision.getMeetingPackageProduct().getName();
    }

    public Optional<PricingOverrideDetail> getPricingOverrideDetails(Long evaluationOverrideId) {
        return Optional.ofNullable(meetingPackageBarDecisionOverrideRepository.getMeetingPackageBarDecisionOverrideBy(evaluationOverrideId))
                .map(meetingPackagePricingOverrideTransformer::convert);
    }

    public void savePricingOverrideDetails(List<PricingOverride> pricingOverrides) {
        if (shouldRecalculateMeetingRoomPricesOnOverride() && syncFlagIsOff()) {
            saveRecalculatedPricingOverrides(pricingOverrides);
        } else {
            savePricingOverrides(pricingOverrides);
        }
    }

    protected void saveRecalculatedPricingOverrides(List<PricingOverride> pricingOverrides) {
        List<PricingOverride> allOverrides = new ArrayList<>();
        List<PricingOverride> ipBaseRoomSpecificOverrides = new ArrayList<>();
        Set<Integer> productIdsToUpdate = pricingOverrides.stream().map(PricingOverride::getProductId).collect(toSet());
        BaseMeetingRoom baseMeetingRoom = meetingPackageProductRepository.getBaseMeetingRoom();
        List<MeetingPackageProductOffset> offsets = meetingPackageProductRepository.getMPProductOffsetsForProducts(productIdsToUpdate);
        Map<Integer, PricingRule> pricingRules = prettyPricingService.getPricingRules();
        Product primaryPricingProduct = meetingPackageProductRepository.getPrimaryPricingProduct(); //we are using bar product rounding rules

        for (PricingOverride override : pricingOverrides) {
            boolean hasSpecificOverride = nonNull(override.getSpecificOverride());
            boolean hasAdjustment = nonNull(override.getAdjustment());
            boolean isBaseMeetingRoom = override.getMeetingRoomId().equals(baseMeetingRoom.getFunctionSpaceFunctionRoom().getId());

            if (hasSpecificOverride && !isBaseMeetingRoom) { // both base & non-base meeting room overrides are sent
                MeetingPackageProductOffset offset = getProductOffsetForMeetingRoom(offsets, override.getProductId(), override.getMeetingRoomId(), override.getStartDate());
                updateOffsetForNonBaseOverrides(override, offset, pricingRules, primaryPricingProduct);
            }
            if (hasSpecificOverride && isBaseMeetingRoom) {
                ipBaseRoomSpecificOverrides.add(override);
            }
            allOverrides.add(override);
        }
        savePricingOverrides(allOverrides);

        recalculateChildProductPricesOfOverriddenBaseProduct(ipBaseRoomSpecificOverrides, pricingRules, primaryPricingProduct);
    }

    protected void recalculateChildProductPricesOfOverriddenBaseProduct(List<PricingOverride> ipBaseRoomSpecificOverrides, Map<Integer, PricingRule> pricingRules, Product primaryPricingProduct) {
        if (CollectionUtils.isEmpty(ipBaseRoomSpecificOverrides) || !syncFlagIsOff()) {
            return;
        }
        List<MeetingPackageProduct> activeMPProducts = meetingPackageProductService.getActiveMeetingPackageProducts();
        List<Integer> activeProductIds = activeMPProducts.stream()
                .map(MeetingPackageProduct::getId)
                .collect(toList());
        Map<Integer, BigDecimal> totalPackageElementOffsetByProductId = meetingPackageOptimalPriceBARService.getProductIdToTotalOffsetMap(activeProductIds);
        Map<Integer, List<MeetingPackageProduct>> childProductsByParentId = activeMPProducts.stream()
                .filter(mpProduct -> nonNull(mpProduct.getDependentProductId()))
                .collect(groupingBy(MeetingPackageProduct::getDependentProductId));
        List<MeetingPackageBarDecision> mppDecisionsToSave = new ArrayList<>();
        ipBaseRoomSpecificOverrides.forEach(ipBaseRoomSpecificOverride -> {
            recalculateChildLinkedProductPrices(pricingRules, primaryPricingProduct, totalPackageElementOffsetByProductId, childProductsByParentId, mppDecisionsToSave, ipBaseRoomSpecificOverride);
        });
        meetingProductBarDecisionRepository.saveAll(mppDecisionsToSave);
    }

    private void recalculateChildLinkedProductPrices(Map<Integer, PricingRule> pricingRules, Product primaryPricingProduct, Map<Integer, BigDecimal> totalPackageElementOffsetByProductId, Map<Integer, List<MeetingPackageProduct>> childProductsByParentId, List<MeetingPackageBarDecision> mppDecisionsToSave, PricingOverride ipBaseRoomSpecificOverride) {
        Integer baseProductId = ipBaseRoomSpecificOverride.getProductId();
        Date occupancyDate = DateUtil.convertLocalDateToJavaUtilDate(ipBaseRoomSpecificOverride.getStartDate());
        List<MeetingPackageProduct> childProducts = childProductsByParentId.get(baseProductId);
        if (CollectionUtils.isNotEmpty(childProducts)) {
            List<Integer> childProductIds = childProducts.stream()
                    .map(MeetingPackageProduct::getId)
                    .collect(Collectors.toList());
            List<Integer> productIds = new ArrayList<>(childProductIds);
            productIds.add(baseProductId);
            List<MeetingPackageBarDecision> meetingPackageBarDecisions = getMeetingPackageBarDecisions(occupancyDate, productIds);
            Map<Integer, List<MeetingPackageBarDecision>> decisionsByProductId = meetingPackageBarDecisions.stream()
                    .collect(groupingBy(mpDecision -> mpDecision.getMeetingPackageProduct().getId()));
            Map<Integer, MeetingPackageBarDecision> baseProductDecisionByRoomId = decisionsByProductId
                    .getOrDefault(baseProductId, List.of())
                    .stream()
                    .collect(toMap(meetingPackageBarDecision -> meetingPackageBarDecision.getFunctionSpaceFunctionRoom().getId(), Function.identity()));
            List<MeetingPackageProductRateOffsetOverride> productRateAdjustmentOverrides = meetingPackageProductRateOffsetOverrideRepository.findActiveOverridesForProductOccupancyPairs(
                    new HashSet<>(childProductIds),
                    Set.of(occupancyDate),
                    TenantStatusEnum.ACTIVE);
            List<MeetingPackageProductRateOffset> productRateAdjustments = meetingPackageProductRateOffsetRepository.getMeetingPackageProductRateAdjustments(childProductIds);

            childProducts.forEach(childMPProduct -> {
                BigDecimal offsetAmount = getOffsetAmountForChild(
                        ipBaseRoomSpecificOverride,
                        productRateAdjustmentOverrides,
                        productRateAdjustments,
                        childMPProduct);
                List<MeetingPackageBarDecision> childProductDecisions = decisionsByProductId.get(childMPProduct.getId());

                childProductDecisions.forEach(linkedProductDecision -> {
                    calculateLinkedProductDecision(
                            pricingRules,
                            primaryPricingProduct,
                            totalPackageElementOffsetByProductId,
                            baseProductDecisionByRoomId,
                            childMPProduct,
                            offsetAmount,
                            linkedProductDecision);
                    mppDecisionsToSave.add(linkedProductDecision);
                });
            });
        }
    }

    private void calculateLinkedProductDecision(Map<Integer, PricingRule> pricingRules, Product primaryPricingProduct, Map<Integer, BigDecimal> totalPackageElementOffsetByProductId, Map<Integer, MeetingPackageBarDecision> baseProductDecisionByRoomId, MeetingPackageProduct childMPProduct, BigDecimal offsetAmount, MeetingPackageBarDecision linkedProductDecision) {
        MeetingPackageBarDecision baseProductDecision = baseProductDecisionByRoomId.get(linkedProductDecision.getFunctionSpaceFunctionRoom().getId());
        BigDecimal updatedPrice = BigDecimalUtil.addIfNotNull(baseProductDecision.getFinalBar(), offsetAmount);
        BigDecimal prettyPrice = meetingPackageOptimalPriceBARService.applyProductRoundingRule(
                primaryPricingProduct.getId(),
                primaryPricingProduct.getRoundingRule(),
                updatedPrice,
                pricingRules);
        BigDecimal finalPrice = meetingPackageOptimalPriceBARService.checkPrettyPriceAgainstFloorRate(prettyPrice, childMPProduct);
        BigDecimal totalPackagePrice = meetingPackageOptimalPriceBARService.getTotalPackagePrice(
                finalPrice,
                childMPProduct.getId(),
                totalPackageElementOffsetByProductId);
        linkedProductDecision.setPrettyBar(prettyPrice);
        linkedProductDecision.setFinalBar(finalPrice);
        linkedProductDecision.setDecisionId(baseProductDecision.getDecisionId());
        linkedProductDecision.setTotalPackagePrice(totalPackagePrice);
    }

    private List<MeetingPackageBarDecision> getMeetingPackageBarDecisions(Date occupancyDate, List<Integer> productIds) {
        MeetingPackageSearchCriteria mpBarDecisionsSearchCriteria = buildSearchCriteria(occupancyDate, occupancyDate, Collections.emptyList(), productIds);
        List<MeetingPackageBarDecision> meetingPackageBarDecisions = meetingProductBarDecisionRepository.searchByFilterCriteria(mpBarDecisionsSearchCriteria);
        return meetingPackageBarDecisions;
    }

    private BigDecimal getOffsetAmountForChild(PricingOverride override, List<MeetingPackageProductRateOffsetOverride> productRateAdjustmentOverrides, List<MeetingPackageProductRateOffset> productRateAdjustments, MeetingPackageProduct childMPProduct) {
        MeetingPackageProductRateOffsetOverride productRateOffsetOverride = productRateAdjustmentOverrides.stream()
                .filter(meetingPackageProductRateOffsetOverride -> meetingPackageProductRateOffsetOverride.getProduct().getId().equals(childMPProduct.getId()))
                .findFirst()
                .orElse(null);
        BigDecimal offsetAmount;
        if (nonNull(productRateOffsetOverride)) {
            offsetAmount = productRateOffsetOverride.getOffsetMethod().getOffsetValueForMethod(productRateOffsetOverride.getOffsetValue(), override.getSpecificOverride());
        } else {
            MeetingPackageProductRateOffset productRateOffsetForMeetingRoom = getProductRateOffsetForMeetingRoom(productRateAdjustments, childMPProduct.getId(), override.getStartDate());
            offsetAmount = productRateOffsetForMeetingRoom.getOffsetMethod().getOffsetValueForMethod(productRateOffsetForMeetingRoom.getOffsetValueForDate(override.getStartDate()), override.getSpecificOverride());
        }
        return offsetAmount;
    }

    private MeetingPackageProductRateOffset getProductRateOffsetForMeetingRoom(List<MeetingPackageProductRateOffset> productRateAdjustments,
                                                                               Integer productId,
                                                                               LocalDate startDate) {
        Optional<MeetingPackageProductRateOffset> seasonOffset = productRateAdjustments.stream()
                .filter(offset -> productId.equals(offset.getMeetingPackageProduct().getId()))
                .filter(offset -> nonNull(offset.getStartDate()) && nonNull(offset.getEndDate()))
                .filter(offset -> isDateBetween(startDate, offset.getStartDate(), offset.getEndDate()))
                .findFirst();
        return seasonOffset.orElseGet(() -> productRateAdjustments.stream()
                .filter(offset -> productId.equals(offset.getMeetingPackageProduct().getId()))
                .filter(offset -> isNull(offset.getStartDate()) && isNull(offset.getEndDate()))
                .findFirst().orElse(null));
    }

    private void updateOffsetForNonBaseOverrides(PricingOverride override,
                                                 MeetingPackageProductOffset offset,
                                                 Map<Integer, PricingRule> pricingRules,
                                                 Product barProduct) {
        BigDecimal offsetAmount = offset.getOffsetMethod().getOffsetValueForMethod(
                offset.getOffsetValueForDate(override.getStartDate()),
                override.getSpecificOverride());
        // non-base MR overrides are sent with the base MR override values
        BigDecimal nonBaseSpecificOverride = BigDecimalUtil.addIfNotNull(override.getSpecificOverride(), offsetAmount);
        BigDecimal roundedOverride = meetingPackageOptimalPriceBARService.applyProductRoundingRule(barProduct.getId(),
                barProduct.getRoundingRule(), nonBaseSpecificOverride, pricingRules);

        override.setMeetingRoomPrice(roundedOverride);
        override.setSpecificOverride(roundedOverride);
    }

    private MeetingPackageProductOffset getProductOffsetForMeetingRoom(List<MeetingPackageProductOffset> mpProductOffsets,
                                                                       Integer productId,
                                                                       Integer meetingRoomId,
                                                                       LocalDate startDate) {
        Optional<MeetingPackageProductOffset> seasonOffset = mpProductOffsets.stream()
                .filter(offset -> meetingRoomId.equals(offset.getProductMeetingRoom().getFunctionSpaceFunctionRoom().getId())
                        && productId.equals(offset.getMeetingPackageProduct().getId()))
                .filter(offset -> nonNull(offset.getStartDate()) && nonNull(offset.getEndDate()))
                .filter(offset -> isDateBetween(startDate, offset.getStartDate(), offset.getEndDate()))
                .findFirst();
        return seasonOffset.orElseGet(() -> mpProductOffsets.stream()
                .filter(offset -> meetingRoomId.equals(offset.getProductMeetingRoom().getFunctionSpaceFunctionRoom().getId())
                        && productId.equals(offset.getMeetingPackageProduct().getId()))
                .filter(offset -> isNull(offset.getStartDate()) && isNull(offset.getEndDate()))
                .findFirst().orElse(null));
    }

    public void savePricingOverrides(List<PricingOverride> pricingOverrides) {
        boolean isRecalculateMeetingRoomPriceOnOverrideEnabled = isRecalculateMeetingRoomPriceOnOverrideEnabled();

        Map<PricingOverrideGroupKey, List<PricingOverride>> pricingOverrideByProductAndMeetingRoom = pricingOverrides.stream()
                .collect(Collectors.groupingBy(
                        pricingOverride -> new PricingOverrideGroupKey(pricingOverride.getProductId(), pricingOverride.getMeetingRoomId()),
                        Collectors.toList()));

        pricingOverrideByProductAndMeetingRoom.keySet().forEach(pricingOverrideGroupKey -> {
            List<PricingOverride> overrides = pricingOverrideByProductAndMeetingRoom.get(pricingOverrideGroupKey).stream()
                    .filter(pricingOverride -> pricingOverride.getStartDate().equals(pricingOverride.getEndDate()))
                    .collect(Collectors.toList());

            List<Date> occupancyDates = getOccupancyDates(overrides);
            Map<Date, MeetingPackageBarDecision> meetingPackageBarDecisionByOccupancyDate = getMeetingPackageBarDecisionByOccupancyDate(pricingOverrideGroupKey, occupancyDates);
            Map<Date, MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrideByOccupancyDate = getMeetingPackageBarDecisionOverrideByOccupancyDate(pricingOverrideGroupKey, occupancyDates);
            List<PricingOverride> overridesToSave = filterMeetingPackageBarDecisionOverridesToSave(overrides, meetingPackageBarDecisionOverrideByOccupancyDate);
            List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrideToSave = prepareMeetingPackageBarDecisionOverridesToSave(overridesToSave, meetingPackageBarDecisionByOccupancyDate);
            updateMeetingPackageBarDecision(meetingPackageBarDecisionByOccupancyDate, overridesToSave);
            updateDecisionIdForNewOverrides(meetingPackageBarDecisionByOccupancyDate, meetingPackageBarDecisionOverrideToSave, isRecalculateMeetingRoomPriceOnOverrideEnabled);
            updateDecisionIdForDeletedOverrides(meetingPackageBarDecisionByOccupancyDate, meetingPackageBarDecisionOverrideToSave, isRecalculateMeetingRoomPriceOnOverrideEnabled);

            handleAdjustmentOverrides(isRecalculateMeetingRoomPriceOnOverrideEnabled, overrides, meetingPackageBarDecisionByOccupancyDate);

            meetingProductBarDecisionRepository.saveAll(meetingPackageBarDecisionByOccupancyDate.values());
            meetingPackageBarDecisionOverrideRepository.saveAll(meetingPackageBarDecisionOverrideToSave);
        });

    }

    private void handleAdjustmentOverrides(boolean isRecalculateMeetingRoomPriceOnOverrideEnabled, List<PricingOverride> overrides, Map<Date, MeetingPackageBarDecision> meetingPackageBarDecisionByOccupancyDate) {
        List<PricingOverride> adjustmentOverrides = overrides.stream()
                .filter(override -> override.getAdjustment() != null)
                .collect(Collectors.toList());

        List<PricingOverride> adjustmentOverridesToBeRemoved = overrides.stream()
                .filter(override -> checkIfOverrideToBeRemoved(override, meetingPackageBarDecisionByOccupancyDate))
                .collect(Collectors.toList());

        List<MeetingPackageProductRateOffsetOverride> rateOffsetOverridesToSave = adjustmentOverrides.stream()
                .map(pricingOverride -> convertToRateOffsetOverride(pricingOverride, meetingPackageBarDecisionByOccupancyDate))
                .collect(Collectors.toList());

        List<MeetingPackageProductRateOffsetOverride> rateOffsetOverridesToRemove = adjustmentOverridesToBeRemoved.stream()
                .map(pricingOverride -> convertToRateOffsetOverride(pricingOverride, meetingPackageBarDecisionByOccupancyDate))
                .collect(Collectors.toList());

        AtomicBoolean shouldTriggerSync = new AtomicBoolean(false);

        if (CollectionUtils.isNotEmpty(rateOffsetOverridesToSave)) {
            setInactiveStatusForRateOffsetOverrides(rateOffsetOverridesToSave, shouldTriggerSync, isRecalculateMeetingRoomPriceOnOverrideEnabled);
            meetingPackageProductRateOffsetOverrideRepository.saveAll(rateOffsetOverridesToSave);
            setSyncStateForRateOffsetOverride(isRecalculateMeetingRoomPriceOnOverrideEnabled, shouldTriggerSync);
        }

        if (CollectionUtils.isNotEmpty(rateOffsetOverridesToRemove)) {
            setInactiveStatusForRateOffsetOverrides(rateOffsetOverridesToRemove, shouldTriggerSync, isRecalculateMeetingRoomPriceOnOverrideEnabled);
        }

        if (shouldTriggerSync.get()) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_LINKED_PRODUCT_CHANGED);
        }
    }

    private static void setSyncStateForRateOffsetOverride(boolean isRecalculateMeetingRoomPriceOnOverrideEnabled, AtomicBoolean shouldTriggerSync) {
        if(isRecalculateMeetingRoomPriceOnOverrideEnabled) {
            shouldTriggerSync.set(false);
        }else{
            shouldTriggerSync.set(true);
        }
    }

    private boolean checkIfOverrideToBeRemoved(PricingOverride pricingOverride, Map<Date, MeetingPackageBarDecision> meetingPackageBarDecisionByOccupancyDate) {
        if (pricingOverride.getAdjustment() != null) {
            return false;
        }

        return Optional.ofNullable(meetingPackageBarDecisionByOccupancyDate.get(LocalDateUtils.toDate(pricingOverride.getStartDate())))
                .map(MeetingPackageBarDecision::getMeetingPackageProduct)
                .map(MeetingPackageProduct::getType)
                .map(PRODUCT_TYPE_LINKED::equals)
                .orElse(false);
    }

    private void setInactiveStatusForRateOffsetOverrides(List<MeetingPackageProductRateOffsetOverride> rateOffsetOverridesToSave, AtomicBoolean shouldTriggerSync, boolean isRecalculateMeetingRoomPriceOnOverrideEnabled) {

        for (MeetingPackageProductRateOffsetOverride newOverride : rateOffsetOverridesToSave) {
            List<MeetingPackageProductRateOffsetOverride> existingOverrides =
                    meetingPackageProductRateOffsetOverrideRepository
                            .findActiveByProductAndOccupancyDate(
                                    newOverride.getProduct(),
                                    newOverride.getOccupancyDate());

            for (MeetingPackageProductRateOffsetOverride existingOverride : existingOverrides) {
                existingOverride.setStatus(TenantStatusEnum.DELETED);
            }

            if (!existingOverrides.isEmpty()) {
                meetingPackageProductRateOffsetOverrideRepository.saveAll(existingOverrides);
                setSyncStateForRateOffsetOverride(isRecalculateMeetingRoomPriceOnOverrideEnabled, shouldTriggerSync);
            }
        }
    }


    private MeetingPackageProductRateOffsetOverride convertToRateOffsetOverride(PricingOverride pricingOverride,
                                                                                Map<Date, MeetingPackageBarDecision> decisionByDateMap) {
        MeetingPackageProductRateOffsetOverride override = new MeetingPackageProductRateOffsetOverride();

        MeetingPackageBarDecision decision = decisionByDateMap.get(LocalDateUtils.toDate(pricingOverride.getStartDate()));

        override.setProduct(decision.getMeetingPackageProduct());
        override.setOccupancyDate(decision.getOccupancyDate());
        override.setStatus(TenantStatusEnum.ACTIVE);
        override.setOffsetMethod(decision.getMeetingPackageProduct().getOffsetMethod());
        override.setOffsetValue(pricingOverride.getAdjustment());
        override.setOffsetValueFloor(pricingOverride.getAdjustment());
        override.setOffsetValueCeil(pricingOverride.getAdjustment());
        return override;
    }


    private void updateDecisionIdForNewOverrides(Map<Date, MeetingPackageBarDecision> meetingPackageBarDecisionByOccupancyDate,
                                                 List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrides,
                                                 boolean isRecalculateMeetingRoomPriceOnOverrideEnabled) {
        AtomicBoolean floorCeilingOverrideChanged = new AtomicBoolean(false);
        AtomicBoolean specificOverrideChanged = new AtomicBoolean(false);
        AtomicBoolean specificOverrideRemoved = new AtomicBoolean(false);
        List<MeetingPackageBarDecisionOverride> newMeetingPackageBarDecisionOverrides = filterNewMeetingPackageBarDecisionOverrides(meetingPackageBarDecisionOverrides);
        newMeetingPackageBarDecisionOverrides.forEach(meetingPackageBarDecisionOverride -> {
            Decision decision = decisionService.createMeetingPackagePricingOverrideDecision();
            Long decisionId = Long.valueOf(decision.getId());
            MeetingPackageBarDecision meetingPackageBarDecision = meetingPackageBarDecisionByOccupancyDate.get(meetingPackageBarDecisionOverride.getOccupancyDate());
            meetingPackageBarDecision.setDecisionId(decisionId);
            meetingPackageBarDecisionOverride.setDecisionId(decisionId);

            if(isRecalculateMeetingRoomPriceOnOverrideEnabled) {
                shouldSetSyncStateWhenRecalculateOverrideIsEnabled(floorCeilingOverrideChanged, specificOverrideRemoved, meetingPackageBarDecisionOverride);
            }else {
                shouldSetSyncStateWhenRecalculateOverrideIsDisabled(floorCeilingOverrideChanged, specificOverrideChanged, meetingPackageBarDecisionOverride);
            }
        });
    }

    private void shouldSetSyncStateWhenRecalculateOverrideIsDisabled(AtomicBoolean floorCeilingOverrideChanged, AtomicBoolean specificOverrideChanged, MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride) {
        if (specificOverrideChanged(meetingPackageBarDecisionOverride)) {
            specificOverrideChanged.set(true);
        }
        if (floorCeilingOverrideChanged(meetingPackageBarDecisionOverride)) {
            floorCeilingOverrideChanged.set(true);
        }

        handleFloorCeilingOverridesSyncEvent(floorCeilingOverrideChanged);
        handleSpecificOverridesSyncEvent(specificOverrideChanged);
    }

    private void shouldSetSyncStateWhenRecalculateOverrideIsEnabled(AtomicBoolean floorCeilingOverrideChanged, AtomicBoolean specificOverrideRemoved, MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride) {
        if (floorCeilingOverrideChanged(meetingPackageBarDecisionOverride)) {
            floorCeilingOverrideChanged.set(true);
        }
        if (specificOverrideRemovedAndFloorCeilingApplied(meetingPackageBarDecisionOverride)) {
            specificOverrideRemoved.set(true);
        }
        if (floorOrCeilingOverrideOrBothRemovedAndSpecificApplied(meetingPackageBarDecisionOverride)) {
            floorCeilingOverrideChanged.set(true);
        }

        handleOverridesSyncEventsWithRecalculationEnabled(floorCeilingOverrideChanged, specificOverrideRemoved);
    }

    public boolean isRecalculateMeetingRoomPriceOnOverrideEnabled() {
        return configParamsService.getBooleanParameterValue(
                PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE);
    }

    private void handleSpecificOverridesSyncEvent(AtomicBoolean specificOverrideChanged) {
        if (specificOverrideChanged.get()) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        }
    }

    private static boolean specificOverrideChanged(MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride) {
        return nonNull(meetingPackageBarDecisionOverride.getNewBar()) && !BigDecimalUtil.equals(meetingPackageBarDecisionOverride.getOldBar(), meetingPackageBarDecisionOverride.getNewBar());
    }

    private static boolean floorCeilingOverrideChanged(MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride) {
        return nonNull(meetingPackageBarDecisionOverride.getNewFloorRate()) || nonNull(meetingPackageBarDecisionOverride.getNewCeilRate());
    }

    private void updateDecisionIdForDeletedOverrides(Map<Date, MeetingPackageBarDecision> meetingPackageBarDecisionByOccupancyDate,
                                                     List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrides,
                                                     boolean isRecalculateMeetingRoomPriceOnOverrideEnabled) {
        List<MeetingPackageBarDecisionOverride> deletedMeetingPackageBarDecisionOverrides = filterDeletedMeetingPackageBarDecisionOverrides(meetingPackageBarDecisionOverrides);
        if (CollectionUtils.isEmpty(deletedMeetingPackageBarDecisionOverrides)) {
            return;
        }
        AtomicBoolean floorCeilingOverrideChanged = new AtomicBoolean(false);
        AtomicBoolean specificOverrideRemoved = new AtomicBoolean(false);
        Decision decision = decisionService.createMeetingPackagePricingOverrideDecision();
        deletedMeetingPackageBarDecisionOverrides.forEach(meetingPackageBarDecisionOverride -> {
            Long decisionId = Long.valueOf(decision.getId());
            MeetingPackageBarDecision meetingPackageBarDecision = meetingPackageBarDecisionByOccupancyDate.get(meetingPackageBarDecisionOverride.getOccupancyDate());
            meetingPackageBarDecision.setDecisionId(decisionId);
            meetingPackageBarDecisionOverride.setDecisionId(decisionId);

            if (specificOverrideRemoved(meetingPackageBarDecisionOverride)) {
                specificOverrideRemoved.set(true);
            }

            if (floorOrCeilingOverrideOrBothRemoved(meetingPackageBarDecisionOverride)) {
                floorCeilingOverrideChanged.set(true);
            }
        });

        if (isRecalculateMeetingRoomPriceOnOverrideEnabled) {
            handleOverridesSyncEventsWithRecalculationEnabled(floorCeilingOverrideChanged, specificOverrideRemoved);
        } else {
            handleOverridesSyncEventsWithRecalculationDisabled(floorCeilingOverrideChanged, specificOverrideRemoved);
        }
    }

    private void handleOverridesSyncEventsWithRecalculationEnabled(AtomicBoolean floorCeilingOverrideChanged, AtomicBoolean specificOverrideRemoved) {
        if (specificOverrideRemoved.get()) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        }
        handleFloorCeilingOverridesSyncEvent(floorCeilingOverrideChanged);
    }

    private void handleOverridesSyncEventsWithRecalculationDisabled(AtomicBoolean floorCeilingOverrideChanged, AtomicBoolean specificOverrideRemoved) {
        if (specificOverrideRemoved.get()) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_SPECIFIC_OVERRIDE_CHANGED);
        }
        handleFloorCeilingOverridesSyncEvent(floorCeilingOverrideChanged);
    }

    private void handleFloorCeilingOverridesSyncEvent(AtomicBoolean floorCeilingOverrideChanged) {
        if (floorCeilingOverrideChanged.get()) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.MEETING_PACKAGE_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        }
    }

    private static boolean floorOrCeilingOverrideOrBothRemoved(MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride) {
        return DecisionOverrideType.PENDING.equals(meetingPackageBarDecisionOverride.getNewOverrideType()) &&
                (DecisionOverrideType.FLOOR.equals(meetingPackageBarDecisionOverride.getOldOverrideType()) ||
                        DecisionOverrideType.CEIL.equals(meetingPackageBarDecisionOverride.getOldOverrideType()) ||
                        DecisionOverrideType.FLOORANDCEIL.equals(meetingPackageBarDecisionOverride.getOldOverrideType()));
    }

    private static boolean floorOrCeilingOverrideOrBothRemovedAndSpecificApplied(MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride) {
        return DecisionOverrideType.USER.equals(meetingPackageBarDecisionOverride.getNewOverrideType()) &&
                (DecisionOverrideType.FLOOR.equals(meetingPackageBarDecisionOverride.getOldOverrideType()) ||
                        DecisionOverrideType.CEIL.equals(meetingPackageBarDecisionOverride.getOldOverrideType()) ||
                        DecisionOverrideType.FLOORANDCEIL.equals(meetingPackageBarDecisionOverride.getOldOverrideType()));
    }

    private static boolean specificOverrideRemoved(MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride) {
        return DecisionOverrideType.PENDING.equals(meetingPackageBarDecisionOverride.getNewOverrideType()) &&
                DecisionOverrideType.USER.equals(meetingPackageBarDecisionOverride.getOldOverrideType());
    }

    private static boolean specificOverrideRemovedAndFloorCeilingApplied(MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride) {
        return (DecisionOverrideType.FLOOR.equals(meetingPackageBarDecisionOverride.getNewOverrideType()) ||
                DecisionOverrideType.CEIL.equals(meetingPackageBarDecisionOverride.getNewOverrideType()) ||
                DecisionOverrideType.FLOORANDCEIL.equals(meetingPackageBarDecisionOverride.getNewOverrideType())) &&
                DecisionOverrideType.USER.equals(meetingPackageBarDecisionOverride.getOldOverrideType());
    }

    private static List<MeetingPackageBarDecisionOverride> filterDeletedMeetingPackageBarDecisionOverrides(List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrideToSave) {
        return meetingPackageBarDecisionOverrideToSave.stream()
                .filter(meetingPackageBarDecisionOverride -> meetingPackageBarDecisionOverride.getNewOverrideType().equals(DecisionOverrideType.PENDING))
                .collect(Collectors.toList());
    }

    private static List<MeetingPackageBarDecisionOverride> filterNewMeetingPackageBarDecisionOverrides(List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrideToSave) {
        return meetingPackageBarDecisionOverrideToSave.stream()
                .filter(meetingPackageBarDecisionOverride -> !meetingPackageBarDecisionOverride.getNewOverrideType().equals(DecisionOverrideType.PENDING))
                .collect(Collectors.toList());
    }

    private void updateMeetingPackageBarDecision(Map<Date, MeetingPackageBarDecision> meetingPackageBarDecisionByOccupancyDate,
                                                 List<PricingOverride> overridesToSave) {
        overridesToSave.forEach(override -> {
            Date occupancyDate = LocalDateUtils.toDate(override.getStartDate());
            MeetingPackageBarDecision meetingPackageBarDecision = meetingPackageBarDecisionByOccupancyDate.get(occupancyDate);
            meetingPackageBarDecision.setFinalBar(override.getMeetingRoomPrice());
            meetingPackageBarDecision.setUserSpecifiedRate(override.getSpecificOverride());
            meetingPackageBarDecision.setFloorRate(override.getFloorOverride());
            meetingPackageBarDecision.setCeilRate(override.getCeilingOverride());
            meetingPackageBarDecision.setTotalPackagePrice(meetingPackageBarDecision.getFinalBar());
            meetingPackageBarDecision.setDecisionOverrideType(getOverrideTypeForMeetingPackageBarDecision(override));
        });
    }

    public List<PricingOverrideHistory> getMeetingPackageDecisionOverrideHistory(Integer productId, Integer functionRoomId, Date occupancyDate) {
        List<MeetingPackageDecisionOverrideHistory> meetingPackageDecisionOverrideHistory = meetingPackageBarDecisionOverrideRepository.getMeetingPackageDecisionOverrideHistory(productId, functionRoomId, occupancyDate);
        List<MeetingPackageDecisionOverrideHistory> overrideHistoryWithReplaceOverride = processOverrideHistoryWithReplaceOverride(meetingPackageDecisionOverrideHistory);
        List<PricingOverrideHistory> pricingOverrideHistoryList = new ArrayList<>();
        overrideHistoryWithReplaceOverride.forEach(overrideHistory -> pricingOverrideHistoryList.add(preparePricingOverrideHistory(overrideHistory)));
        return pricingOverrideHistoryList;
    }

    private PricingOverrideHistory preparePricingOverrideHistory(MeetingPackageDecisionOverrideHistory overrideHistory) {
        PricingOverrideHistory pricingOverrideHistory = new PricingOverrideHistory();
        pricingOverrideHistory.setOriginalPrice(getOriginalPrice(overrideHistory));
        pricingOverrideHistory.setUpdatedBy(overrideHistory.getUserName());
        pricingOverrideHistory.setUpdatedOn(dateService.formatDateToPropertyTimeZone(DateUtil.convertLocalDateTimeToJavaUtilDate(overrideHistory.getCreateDate())));
        pricingOverrideHistory.setOverrideDetails(getOverrideDetails(overrideHistory));
        return pricingOverrideHistory;
    }

    private OverrideDetails getOverrideDetails(MeetingPackageDecisionOverrideHistory overrideHistory) {
        switch (overrideHistory.getNewOverride()) {
            case PENDING:
                return getOverrideDetailsForPendingOverride(overrideHistory);
            case USER:
                return new OverrideDetails(BigDecimalUtil.round(overrideHistory.getNewBar(), DEFAULT_PRECISION).doubleValue(), null, null, false);
            case FLOOR:
                return new OverrideDetails(null, BigDecimalUtil.round(overrideHistory.getNewFloorRate(), DEFAULT_PRECISION).doubleValue(), null, false);
            case CEIL:
                return new OverrideDetails(null, null, BigDecimalUtil.round(overrideHistory.getNewCeilingRate(), DEFAULT_PRECISION).doubleValue(), false);
            case FLOORANDCEIL:
                return new OverrideDetails(null, BigDecimalUtil.round(overrideHistory.getNewFloorRate(), DEFAULT_PRECISION).doubleValue(), BigDecimalUtil.round(overrideHistory.getNewCeilingRate(), DEFAULT_PRECISION).doubleValue(), false);
            default:
                return newOverrideIsPending(overrideHistory) ? new OverrideDetails(BigDecimalUtil.round(overrideHistory.getOldBar(), DEFAULT_PRECISION).doubleValue(), null, null, false) : new OverrideDetails(BigDecimalUtil.round(overrideHistory.getNewBar(), DEFAULT_PRECISION).doubleValue(), null, null, false);
        }

    }

    private static boolean newOverrideIsPending(MeetingPackageDecisionOverrideHistory overrideHistory) {
        return DecisionOverrideType.PENDING.name().equalsIgnoreCase(overrideHistory.getNewOverride());
    }

    private OverrideDetails getOverrideDetailsForPendingOverride(MeetingPackageDecisionOverrideHistory overrideHistory) {
        switch (overrideHistory.getOldOverride()) {
            case FLOORANDCEIL:
                return new OverrideDetails(null, BigDecimalUtil.round(overrideHistory.getOldFloorRate(), DEFAULT_PRECISION).doubleValue(), BigDecimalUtil.round(overrideHistory.getOldCeilingRate(), DEFAULT_PRECISION).doubleValue(), true);
            case CEIL:
                return new OverrideDetails(null, null, BigDecimalUtil.round(overrideHistory.getOldCeilingRate(), DEFAULT_PRECISION).doubleValue(), true);
            case FLOOR:
                return new OverrideDetails(null, BigDecimalUtil.round(overrideHistory.getOldFloorRate(), DEFAULT_PRECISION).doubleValue(), null, true);
            case USER:
                return new OverrideDetails(BigDecimalUtil.round(overrideHistory.getOldBar(), DEFAULT_PRECISION).doubleValue(), null, null, true);
            default:
                return new OverrideDetails();

        }
    }

    private Double getOriginalPrice(MeetingPackageDecisionOverrideHistory overrideHistory) {
        return newOverrideIsPending(overrideHistory) ? BigDecimalUtil.round(overrideHistory.getOriginalDecision(), DEFAULT_PRECISION).doubleValue() : BigDecimalUtil.round(overrideHistory.getOldBar(), DEFAULT_PRECISION).doubleValue();

    }

    private List<MeetingPackageDecisionOverrideHistory> processOverrideHistoryWithReplaceOverride(List<MeetingPackageDecisionOverrideHistory> meetingPackageDecisionOverrideHistory) {
        List<MeetingPackageDecisionOverrideHistory> overrideHistoryWithReplaceOverride = new ArrayList<>();
        meetingPackageDecisionOverrideHistory.forEach(overrideHistory -> {
            overrideHistoryWithReplaceOverride.add(overrideHistory);
            if (!oldOverrideIsNone(overrideHistory) && !newOverrideIsPending(overrideHistory) && !isOldAndNewOverrideEqual(overrideHistory)) {
                overrideHistoryWithReplaceOverride.add(createReplaceOverrideDto(overrideHistory));
            }
        });
        return overrideHistoryWithReplaceOverride;
    }

    private static boolean isOldAndNewOverrideEqual(MeetingPackageDecisionOverrideHistory overrideHistory) {
        return overrideHistory.getOldOverride().equals(overrideHistory.getNewOverride());
    }

    private static boolean oldOverrideIsNone(MeetingPackageDecisionOverrideHistory overrideHistory) {
        return DecisionOverrideType.NONE.name().equalsIgnoreCase(overrideHistory.getOldOverride());
    }

    private MeetingPackageDecisionOverrideHistory createReplaceOverrideDto(MeetingPackageDecisionOverrideHistory overrideHistory) {
        MeetingPackageDecisionOverrideHistory replaceOverrideDto = new MeetingPackageDecisionOverrideHistory();
        replaceOverrideDto.setNewOverride(DecisionOverrideType.PENDING.name());
        replaceOverrideDto.setOriginalDecision(overrideHistory.getOldBar());
        replaceOverrideDto.setCreateDate(overrideHistory.getCreateDate());
        replaceOverrideDto.setNewBar(overrideHistory.getNewBar());
        replaceOverrideDto.setOldBar(overrideHistory.getOldBar());
        replaceOverrideDto.setNewCeilingRate(overrideHistory.getNewCeilingRate());
        replaceOverrideDto.setOldCeilingRate(overrideHistory.getOldCeilingRate());
        replaceOverrideDto.setNewFloorRate(overrideHistory.getNewFloorRate());
        replaceOverrideDto.setOldFloorRate(overrideHistory.getOldFloorRate());
        replaceOverrideDto.setOldOverride(overrideHistory.getOldOverride());
        replaceOverrideDto.setUserName(overrideHistory.getUserName());
        return replaceOverrideDto;
    }

    private static List<Date> getOccupancyDates(List<PricingOverride> overrides) {
        return overrides.stream()
                .map(PricingOverride::getStartDate)
                .map(LocalDateUtils::toDate)
                .collect(Collectors.toList());
    }

    private List<MeetingPackageBarDecisionOverride> prepareMeetingPackageBarDecisionOverridesToSave(List<PricingOverride> overrides,
                                                                                                    Map<Date, MeetingPackageBarDecision> meetingPackageBarDecisionByOccupancyDate) {
        return overrides.stream()
                .map(override -> createMeetingPackageBarDecisionOverride(meetingPackageBarDecisionByOccupancyDate, override))
                .collect(Collectors.toList());
    }

    private List<PricingOverride> filterMeetingPackageBarDecisionOverridesToSave(List<PricingOverride> overrides,
                                                                                 Map<Date, MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrideByOccupancyDate) {
        return overrides.stream()
                .filter(override -> shouldCreateMeetingPackageBarDecisionOverride(meetingPackageBarDecisionOverrideByOccupancyDate, override))
                .collect(Collectors.toList());
    }

    private boolean shouldCreateMeetingPackageBarDecisionOverride(Map<Date, MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrideByOccupancyDate,
                                                                  PricingOverride override) {
        Date occupancyDate = LocalDateUtils.toDate(override.getStartDate());
        if (meetingPackageBarDecisionOverrideByOccupancyDate.containsKey(occupancyDate)) {
            MeetingPackageBarDecisionOverride oldMeetingPackageBarDecisionOverride = meetingPackageBarDecisionOverrideByOccupancyDate.get(occupancyDate);
            return (isNull(override.getSpecificOverride()) && isNull(override.getFloorOverride()) && isNull(override.getCeilingOverride())) ||
                    !BigDecimalUtil.equals(override.getSpecificOverride(), oldMeetingPackageBarDecisionOverride.getNewBar()) ||
                    !BigDecimalUtil.equals(override.getFloorOverride(), oldMeetingPackageBarDecisionOverride.getNewFloorRate()) ||
                    !BigDecimalUtil.equals(override.getCeilingOverride(), oldMeetingPackageBarDecisionOverride.getNewCeilRate());
        }

        return nonNull(override.getSpecificOverride()) ||
                nonNull(override.getFloorOverride()) ||
                nonNull(override.getCeilingOverride());
    }

    private Map<Date, MeetingPackageBarDecisionOverride> getMeetingPackageBarDecisionOverrideByOccupancyDate(PricingOverrideGroupKey pricingOverrideGroupKey,
                                                                                                             List<Date> occupancyDates) {
        return meetingPackageBarDecisionOverrideRepository
                .getLatestMeetingPackageBarDecisionOverridesBy(pricingOverrideGroupKey.getProductId(), pricingOverrideGroupKey.getMeetingRoomId(), occupancyDates)
                .stream()
                .collect(Collectors.toMap(MeetingPackageBarDecisionOverride::getOccupancyDate, Function.identity()));
    }

    private Map<Date, MeetingPackageBarDecision> getMeetingPackageBarDecisionByOccupancyDate(PricingOverrideGroupKey pricingOverrideGroupKey, List<Date> occupancyDates) {
        return meetingProductBarDecisionRepository.getMeetingPackageBarDecisionsBy(
                        pricingOverrideGroupKey.getProductId(),
                        pricingOverrideGroupKey.getMeetingRoomId(),
                        occupancyDates)
                .stream()
                .collect(Collectors.toMap(MeetingPackageBarDecision::getOccupancyDate, Function.identity()));
    }

    private static MeetingPackageBarDecisionOverride createMeetingPackageBarDecisionOverride(Map<Date, MeetingPackageBarDecision> meetingPackageBarDecisionByOccupancyDate,
                                                                                             PricingOverride override) {
        MeetingPackageBarDecision meetingPackageBarDecision = meetingPackageBarDecisionByOccupancyDate.get(LocalDateUtils.toDate(override.getStartDate()));
        MeetingPackageBarDecisionOverride meetingPackageBarDecisionOverride = new MeetingPackageBarDecisionOverride();
        meetingPackageBarDecisionOverride.setDecisionId(meetingPackageBarDecision.getDecisionId());
        meetingPackageBarDecisionOverride.setMeetingPackageProduct(meetingPackageBarDecision.getMeetingPackageProduct());
        meetingPackageBarDecisionOverride.setFunctionSpaceFunctionRoom(meetingPackageBarDecision.getFunctionSpaceFunctionRoom());
        meetingPackageBarDecisionOverride.setOccupancyDate(meetingPackageBarDecision.getOccupancyDate());
        meetingPackageBarDecisionOverride.setUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        meetingPackageBarDecisionOverride.setOldOverrideType(meetingPackageBarDecision.getDecisionOverrideType());
        meetingPackageBarDecisionOverride.setNewOverrideType(getOverrideTypeForMeetingPackageBarDecisionOverride(override));
        meetingPackageBarDecisionOverride.setOldBar(meetingPackageBarDecision.getFinalBar());
        meetingPackageBarDecisionOverride.setNewBar(override.getSpecificOverride());
        meetingPackageBarDecisionOverride.setOldFloorRate(meetingPackageBarDecision.getFloorRate());
        meetingPackageBarDecisionOverride.setNewFloorRate(override.getFloorOverride());
        meetingPackageBarDecisionOverride.setOldCeilRate(meetingPackageBarDecision.getCeilRate());
        meetingPackageBarDecisionOverride.setNewCeilRate(override.getCeilingOverride());
        meetingPackageBarDecisionOverride.setCreateDate(LocalDateTime.now());
        return meetingPackageBarDecisionOverride;
    }

    private static DecisionOverrideType getOverrideTypeForMeetingPackageBarDecisionOverride(PricingOverride override) {
        return getDecisionOverrideType(override, DecisionOverrideType.PENDING);
    }

    private static DecisionOverrideType getOverrideTypeForMeetingPackageBarDecision(PricingOverride override) {
        return getDecisionOverrideType(override, DecisionOverrideType.NONE);
    }

    private static DecisionOverrideType getDecisionOverrideType(PricingOverride override,
                                                                DecisionOverrideType defaultOverrideType) {
        if (nonNull(override.getFloorOverride()) && nonNull(override.getCeilingOverride())) {
            return DecisionOverrideType.FLOORANDCEIL;
        }

        if (nonNull(override.getFloorOverride())) {
            return DecisionOverrideType.FLOOR;
        }

        if (nonNull(override.getCeilingOverride())) {
            return DecisionOverrideType.CEIL;
        }

        if (nonNull(override.getSpecificOverride())) {
            return DecisionOverrideType.USER;
        }

        return defaultOverrideType;
    }

    public boolean isMeetingPackagePricingEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.MEETING_PACKAGE_PRICING_ENABLED);
    }

    private boolean shouldRecalculateMeetingRoomPricesOnOverride() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.RECALCULATE_MEETING_ROOM_PRICE_ON_OVERRIDE);
    }

    private boolean syncFlagIsOff() {
        return isEmpty(syncFlagService.getStalenessFlags());
    }

    public void createLastGoodDecisions(Integer lastGoodDecisionId, Integer newDecisionID) {
        updateMeetingPackageBarDecision(newDecisionID, lastGoodDecisionId);
        insertIntoMeetingPackagePaceBarDecision();
    }

    private void updateMeetingPackageBarDecision(Integer newDecisionID, Integer lastGoodDecisionId) {
        final int recordsUpdated = meetingProductBarDecisionRepository.updateMeetingPackageBarDecisionWithLastKnownGoodDecisions(
                dateService.getOptimizationWindowStartDate(),
                dateService.getDecisionUploadWindowEndDate(),
                newDecisionID,
                lastGoodDecisionId
        );
        logger.info("Total records updated in MP_Decision_Bar " + recordsUpdated);
    }

    private void insertIntoMeetingPackagePaceBarDecision() {
        final int recordsUpdated = meetingPackagePaceBarDecisionRepository.insertPaceMeetingPackageUploadDifferentialData(
                dateService.getOptimizationWindowStartDate(),
                dateService.getDecisionUploadWindowEndDate()
        );
        logger.info("Total records updated in MP_Pace_Decision_Bar_Differential " + recordsUpdated);
    }

    public List<PricingOverrideResult> applyPricingOverride(List<PricingOverride> pricingOverrides) {
        return pricingOverrides.stream()
                .map(MeetingPackageDecisionService::calculatePricingOverrideResult)
                .collect(Collectors.toList());
    }

    private static PricingOverrideResult calculatePricingOverrideResult(PricingOverride pricingOverride) {
        PricingOverrideResult pricingOverrideResult = new PricingOverrideResult();
        pricingOverrideResult.setProductId(pricingOverride.getProductId());
        pricingOverrideResult.setOccupancyDate(pricingOverride.getStartDate());
        pricingOverrideResult.setMeetingRoomId(pricingOverride.getMeetingRoomId());
        pricingOverrideResult.setMeetingRoomPrice(calculateFinalBar(pricingOverride));
        return pricingOverrideResult;
    }

    private static BigDecimal calculateFinalBar(PricingOverride pricingOverride) {
        if (nonNull(pricingOverride.getSpecificOverride())) {
            return pricingOverride.getSpecificOverride();
        }

        if (nonNull(pricingOverride.getFloorOverride()) &&
                (pricingOverride.getFloorOverride().compareTo(pricingOverride.getMeetingRoomPrice()) > 0)) {
            return pricingOverride.getFloorOverride();
        }

        if (nonNull(pricingOverride.getCeilingOverride()) &&
                (pricingOverride.getCeilingOverride().compareTo(pricingOverride.getMeetingRoomPrice()) < 0)) {
            return pricingOverride.getCeilingOverride();
        }

        return pricingOverride.getMeetingRoomPrice();
    }


    public void deleteMPDecisionBarOutputsByProduct(MeetingPackageProduct meetingPackageProduct, LocalDate caughtUpDate) {
        meetingProductBarDecisionRepository.deleteMPDecisionBarOutputsByProduct(meetingPackageProduct, caughtUpDate);
    }

    public void deleteMPDecisionBarOutputOverridesByProduct(MeetingPackageProduct meetingPackageProduct, LocalDate caughtUpDate) {
        meetingPackageBarDecisionOverrideRepository.deleteMPDecisionBarOutputOverridesByProduct(meetingPackageProduct, caughtUpDate);
    }

    public void deleteMPPaceDecisionBarOutputByProduct(MeetingPackageProduct meetingPackageProduct, LocalDate caughtUpDate) {
        meetingPackagePaceBarDecisionRepository.deleteMPPaceDecisionBarOutputByProduct(meetingPackageProduct, caughtUpDate);
    }

    public List<MeetingPackageAdjustmentOverrideHistory> getMeetingPackageAdjustmentOverrideHistory(Integer productId, Date occupancyDate) {
        List<MeetingPackageProductRateOffsetOverride> overrides =
                meetingPackageProductRateOffsetOverrideRepository.findAllAdjustmentsByProductAndOccupancyDate(productId, occupancyDate);
        List<User> users = getUsers(overrides);

        Map<Integer, String> userIdToNameMap = users.stream()
                .collect(Collectors.toMap(User::getId, User::getName, (u1, u2) -> u1));

        List<MeetingPackageAdjustmentOverrideHistory> historyList = new ArrayList<>();

        overrides.forEach(override -> {
            String offsetType = override.getOffsetMethod() != null ? getOffsetMethod(override) : "";
            BigDecimal adjustedOffset = override.getOffsetValue().setScale(2, RoundingMode.HALF_UP);
            String updatedBy = userIdToNameMap.getOrDefault(override.getLastUpdatedByUserId(), "");
            String createdBy = userIdToNameMap.getOrDefault(override.getCreatedByUserId(), "");

            if (override.getStatus() == TenantStatusEnum.DELETED) {
                MeetingPackageAdjustmentOverrideHistory removedDto = new MeetingPackageAdjustmentOverrideHistory();
                removedDto.setOffsetType(offsetType);
                removedDto.setAdjustedOffset(adjustedOffset);
                removedDto.setRemoved(true);
                removedDto.setUpdatedBy(updatedBy);
                removedDto.setUpdatedOn(getFormattedDate(LocalDateUtils.toDate(override.getLastUpdatedDate())));
                historyList.add(removedDto);

                MeetingPackageAdjustmentOverrideHistory createdDto = new MeetingPackageAdjustmentOverrideHistory();
                createdDto.setOffsetType(offsetType);
                createdDto.setAdjustedOffset(adjustedOffset);
                createdDto.setRemoved(false);
                createdDto.setUpdatedBy(createdBy);
                createdDto.setUpdatedOn(getFormattedDate(LocalDateUtils.toDate(override.getCreateDate())));
                historyList.add(createdDto);
            } else {
                MeetingPackageAdjustmentOverrideHistory dto = new MeetingPackageAdjustmentOverrideHistory();
                dto.setOffsetType(offsetType);
                dto.setAdjustedOffset(adjustedOffset);
                dto.setRemoved(false);
                dto.setUpdatedBy(updatedBy);
                dto.setUpdatedOn(getFormattedDate(LocalDateUtils.toDate(override.getLastUpdatedDate())));
                historyList.add(dto);
            }
        });

        historyList.sort((h1, h2) -> {
            Date d1 = parseDate(h1.getUpdatedOn());
            Date d2 = parseDate(h2.getUpdatedOn());
            int cmp = d2.compareTo(d1);
            if (cmp != 0) {
                return cmp;
            }
            return Boolean.compare(h1.isRemoved(), h2.isRemoved());
        });
        return historyList;
    }

    private String getOffsetMethod(MeetingPackageProductRateOffsetOverride override) {
        if (AgileRatesOffsetMethod.FIXED.equals(override.getOffsetMethod())){
            return "Fixed";
        }
        return "Percentage";
    }

    private Date parseDate(String dateStr) {
        try {
            return new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss z").parse(dateStr);
        } catch (ParseException e) {
            return new Date(0);
        }
    }


    protected List<User> getUsers(List<MeetingPackageProductRateOffsetOverride> overrides) {
        Set<Integer> userIdsSet = new HashSet<>();
        userIdsSet.addAll(overrides.stream().map(MeetingPackageProductRateOffsetOverride::getLastUpdatedByUserId).collect(toSet()));
        userIdsSet.addAll(overrides.stream().map(MeetingPackageProductRateOffsetOverride::getCreatedByUserId).collect(toSet()));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(userIdsSet)) {
            return userService.getTenantUsersById(userIdsSet);
        } else {
            return new ArrayList<>();
        }
    }

    public String getFormattedDate(Date date) {
        return dateService.formatDateToPropertyTimeZone(date);
    }

    private List<Integer> getIPProductIdsForLinkedProducts(List<MeetingPackageBarDecision> meetingPackageBarDecisions) {
        return  meetingPackageBarDecisions.stream()
                .filter(d -> PRODUCT_TYPE_LINKED.equals(d.getMeetingPackageProduct().getType()))
                .map(d -> d.getMeetingPackageProduct().getDependentProductId())
                .distinct()
                .collect(Collectors.toList());
    }

    private List<MeetingPackageBarDecision> getParentDecisionsForLinkedProducts(
            Date startDate,
            Date endDate,
            List<Integer> meetingRoomIds,
            List<Integer> parentProductIds,
            List<MeetingPackageBarDecision> alreadyFetchedDecisions) {

        Set<Integer> alreadyFetchedProductIds = alreadyFetchedDecisions.stream()
                .map(d -> d.getMeetingPackageProduct().getId())
                .collect(Collectors.toSet());

        List<MeetingPackageBarDecision> alreadyFetchedParents = alreadyFetchedDecisions.stream()
                .filter(d -> parentProductIds.contains(d.getMeetingPackageProduct().getId()))
                .collect(Collectors.toList());

        List<Integer> missingParentIds = parentProductIds.stream()
                .filter(id -> !alreadyFetchedProductIds.contains(id))
                .collect(Collectors.toList());

        if (missingParentIds.isEmpty()) {
            return alreadyFetchedParents;
        }

        MeetingPackageSearchCriteria parentSearchCriteria =
                buildSearchCriteria(startDate, endDate, meetingRoomIds, missingParentIds);
        List<MeetingPackageBarDecision> missingParents =
                meetingProductBarDecisionRepository.searchByFilterCriteria(parentSearchCriteria);

        List<MeetingPackageBarDecision> allParentDecisions = new ArrayList<>(alreadyFetchedParents);
        allParentDecisions.addAll(missingParents);
        return allParentDecisions;
    }

    public Optional<Integer> getChangesSinceDecisionId(String datesWithFilter, Date changesSinceDate) {
        DatesWithFilter filter = DatesWithFilter.fromParam(datesWithFilter);
        if (isNull(filter)) {
            return Optional.empty();
        }
        switch (filter) {
            case CHANGES_SINCE:
                if (isNull(changesSinceDate)) {
                    return Optional.empty();
                }
                return Optional.ofNullable(decisionService.getLastDecisionIdSinceDate(changesSinceDate));
            case CHANGES_SINCE_LAST_BDE:
                return Optional.ofNullable(decisionService.getLastBDEDecisionId());
            case CHANGES_SINCE_LAST_IDP:
                return Optional.ofNullable(decisionService.getLastCDPDecisionId());
            default:
                return Optional.empty();
        }
    }

    public List<MeetingPackagePaceBarDecision> getMeetingPackagePaceDecisions(Date startDate,
                                                                              Date endDate,
                                                                              List<Integer> meetingRoomIds,
                                                                              List<Integer> productIds,
                                                                              Integer decisionId) {
        return meetingPackagePaceBarDecisionRepository.getLatestDecisionUptoDecisionId(
                fromDate(startDate), fromDate(endDate), productIds, meetingRoomIds, decisionId);
    }

    private MeetingPackagePaceSearchCriteria buildPaceSearchCriteria(Date startDate,
                                                                     Date endDate,
                                                                     List<Integer> meetingRoomIds,
                                                                     List<Integer> productIds,
                                                                     Integer decisionId) {
        MeetingPackagePaceSearchCriteria searchCriteria = new MeetingPackagePaceSearchCriteria();
        searchCriteria.setDecisionId(decisionId);
        searchCriteria.setStartDate(startDate);
        searchCriteria.setEndDate(endDate);
        searchCriteria.setMeetingRoomIds(meetingRoomIds);
        searchCriteria.setProductIds(productIds);
        return searchCriteria;
    }

    protected void filterUnchangedPrices(List<MeetingPackagePricingPerDay> perDayPrices,
                                       List<MeetingPackagePaceBarDecision> paceDecisions) {
        Map<MeetingPackagePricingDecisionKey, MeetingPackagePaceBarDecision> paceDecisionsByKey = paceDecisions.stream()
                .collect(toMap(MeetingPackagePricingDecisionKey::new,
                               Function.identity(),
                               maxBy(comparing(MeetingPackagePaceBarDecision::getDecisionId))));
        perDayPrices.removeIf(perDayDetail -> {
            perDayDetail.getPriceDetails().removeIf(productDetail -> {
                productDetail.getMeetingRoomPrices().removeIf(meetingRoomDetail ->
                        isPriceUnchanged(perDayDetail, productDetail, meetingRoomDetail, paceDecisionsByKey));
                return isEmpty(productDetail.getMeetingRoomPrices());
            });
            return isEmpty(perDayDetail.getPriceDetails());
        });
    }

    private boolean isPriceUnchanged(MeetingPackagePricingPerDay perDayDetail,
                                     MeetingPackagePriceDetail productDetail,
                                     MeetingRoomPrice meetingRoomDetail,
                                     Map<MeetingPackagePricingDecisionKey, MeetingPackagePaceBarDecision> paceDecisionsByKey) {
        MeetingPackagePricingDecisionKey decisionKey = new MeetingPackagePricingDecisionKey(
                productDetail.getProductId(),
                meetingRoomDetail.getMeetingRoomId(),
                fromDate(perDayDetail.getDate()));
        MeetingPackagePaceBarDecision matchingPaceDecision = paceDecisionsByKey.get(decisionKey);
        return matchingPaceDecision != null &&
                BigDecimalUtil.equals(matchingPaceDecision.getFinalBar(), meetingRoomDetail.getMeetingRoomPrice());
    }

}

@AllArgsConstructor
@EqualsAndHashCode
@Getter
class PricingOverrideGroupKey {
    private Integer productId;
    private Integer meetingRoomId;
}
