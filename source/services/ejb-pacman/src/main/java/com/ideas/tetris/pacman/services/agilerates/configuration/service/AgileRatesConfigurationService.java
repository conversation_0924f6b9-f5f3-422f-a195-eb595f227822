package com.ideas.tetris.pacman.services.agilerates.configuration.service;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.currencyexchange.CurrencyExchangeService;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.rateCodeVendorMapping.RateCodeVendorMapping;
import com.ideas.tetris.pacman.services.accommodation.rateCodeVendorMapping.RateCodeVendorMappingService;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.*;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.*;
import com.ideas.tetris.pacman.services.bestavailablerate.CPRecommendationService;
import com.ideas.tetris.pacman.services.bestavailablerate.DecisionOverrideType;
import com.ideas.tetris.pacman.services.bestavailablerate.PrettyPricingService;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingRule;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BrokenHierarchyDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BrokenHierarchySeasonalAdjustmentDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CeilingFloor;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CeilingFloorDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientPropertyAttributePairing;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.configautomation.dto.AgileRatesDTARangeResponseDTO;
import com.ideas.tetris.pacman.services.configautomation.dto.HierarchyDTO;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dailybar.entity.DailyBarDecisions;
import com.ideas.tetris.pacman.services.datafeed.service.AgileRatesDataFeedService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demandoverride.DemandDetailsService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.extendedstay.unqualifiedrate.entity.ExtendedStayRateUnqualifiedOverride;
import com.ideas.tetris.pacman.services.hiltoncpmigration.entity.CPMigrationConfig;
import com.ideas.tetris.pacman.services.hiltoncpmigration.entity.ExtendedStayRateMapping;
import com.ideas.tetris.pacman.services.hiltoncpmigration.service.ExtendedStayRateMappingImportService;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.linkedsrp.service.LinkedSRPService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.FixedAboveBarConfigurationService;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarConfig;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarRateChart;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.product.*;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.rdl.entity.WebrateTypeProduct;
import com.ideas.tetris.pacman.services.rdl.entity.dto.WebrateTypeProductDTO;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.unqualifiedrate.serivce.RateUnqualifiedService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateType;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.CONSIDER_RESTRICTION_SEASON_DATE_FOR_AGILE_QUALIFIED_FPLOS;
import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.agilerates.configuration.constants.Constants.DEFAULT_MAX_ROOMS;
import static com.ideas.tetris.pacman.services.agilerates.configuration.constants.Constants.DEFAULT_MIN_ROOMS;
import static com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductHierarchy.*;
import static com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationQueries.*;
import static com.ideas.tetris.pacman.services.product.Product.*;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.negativeValueToZero;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.round;
import static com.ideas.tetris.pacman.util.Runner.runIfElse;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.*;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class AgileRatesConfigurationService {

    static final String PRODUCT_PARAMETER = "product";
    static final String PRODUCTS_PARAMETER = "products";
    static final String ARRIVAL_DATE_PARAMETER = "arrivalDate";
    static final String ACCOM_TYPE_IDS = "accomTypeIDs";
    static final String AMERICAS = "Americas";
    static final String EMEA = "Europe, Middle East & Africa";
    static final String NAME = "name";
    static final String MIN_LOS = "minLOS";
    static final String MAX_LOS = "maxLOS";
    static final String APAC = "Asia Pacific";
    public static final String TS = "TS";
    public static final String PP = "PP";
    public static final String AP = "AP";
    static final BigDecimal BIG_DECIMAL_ZERO = new BigDecimal("0.00");
    static final String EXTENDED_STAY_PRODUCT_NAME = "LV";
    static final String EXTENDED_STAY_PRODUCT_DESCRIPTION = "Extended Stay Tier ";
    static final int RATE_LEVEL_SNAP_INDEX = 2;
    static final int MIN_LOS_SNAP_INDEX = 13;
    static final int MAX_LOS_SNAP_INDEX = 14;
    static final String HCRS = "HCRS";
    static final String PCRS = "PCRS";
    static final String TRUE = "1";
    static final String FALSE = "0";
    static final String BREAKFAST_PACKAGE_NAME = "Breakfast";
    static final String BREAKFAST_PACKAGE_DESCRIPTION = "Breakfast package per adult fixed";
    static final Integer BREAKFAST_PACKAGE_CHARGE_TYPE = AgileRatesChargeType.PER_ADULT.getId();
    static final Integer BREAKFAST_PACKAGE_OFFSET_METHOD = AgileRatesOffsetMethod.FIXED.getId();
    static final AgileRatesOffsetMethod BREAKFAST_PACKAGE_OFFSET = AgileRatesOffsetMethod.FIXED;
    static final AgileRatesChargeType BREAKFAST_CHARGE_TYPE = AgileRatesChargeType.PER_ADULT;
    static final Integer DEFAULT_RATE_TYPE_MIN_LOS = Integer.valueOf(1);
    static final Integer DEFAULT_RATE_TYPE_MAX_LOS = Integer.valueOf(365);
    static final List<AgileRatesProductTypeEnum> AGILE_RATES_PACKAGED_PRODUCT_TYPE_ENUM_LIST = asList(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED, AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED);
    static final String RATE_TYPE_BEST_FLEXIBLE_CODE = "BEST_FLEXIBLE";
    static final String RATE_TYPE_BEST_FLEXIBLE_DESCRIPTION = "Best Flexible";
    static final String RATE_TYPE_NA_CODE = "RATE_SHOPPING_DATA_NA";
    private static final String DEFAULTS = "defaults";
    public static final String BEST_FLEXIBLE_TYPE_CODE = "BEST_FLEXIBLE";
    public static final String SEMI_FLEXIBLE_TYPE_CODE = "SEMI_FLEXIBLE";
    public static final String ADVANCE_PURCHASE_TYPE_CODE = "ANY_NON_QUALIFIED";
    public static final String BEST_FLEXIBLE_LOYALTY_TYPE_CODE = "BEST_FLEXIBLE_LOYALTY";
    public static final String SEMI_FLEXIBLE_LOYALTY_TYPE_CODE = "SEMI_FLEXIBLE_LOYALTY";
    public static final String ADVANCE_PURCHASE_LOYALTY_TYPE_CODE = "ANY_NON_QUALIFIED_LOYALTY";

    static final List<String> WEB_RATE_TYPES_FOR_UI = List.of(BEST_FLEXIBLE_TYPE_CODE,
            ADVANCE_PURCHASE_TYPE_CODE,
            SEMI_FLEXIBLE_TYPE_CODE,
            BEST_FLEXIBLE_LOYALTY_TYPE_CODE,
            SEMI_FLEXIBLE_LOYALTY_TYPE_CODE,
            ADVANCE_PURCHASE_LOYALTY_TYPE_CODE);

    static final String UNASSIGNED_TRANSIENT_OR_QUALIFIED_LINKED_RATE_CODES = "SELECT DISTINCT Rate_Code FROM [dbo].[Reservation_Night] " +
            "WHERE Mkt_Seg_ID IN (SELECT DISTINCT msd.Mkt_Seg_ID FROM [dbo].[Mkt_Seg_Details] msd " +
            "WHERE Status_ID=1 AND (Qualified=0 OR (Qualified=1 AND Link=1))) " +
            "AND Rate_Code NOT IN " +
            "(SELECT DISTINCT prc.Rate_Code FROM [dbo].[Product_Rate_Code] prc " +
            "WHERE Product_ID NOT IN (SELECT Product_ID FROM [dbo].[Product] WHERE Status_ID = 2)) ORDER BY Rate_Code ASC";
    static final String FIND_UNASSIGNED_TRANSIENT_OR_QUALIFIED_LINKED_RATE_CODES_FROM_MKT_SEG_DETAILS_PROPOSED = "SELECT DISTINCT Rate_Code FROM [dbo].[Reservation_Night] " +
            " WHERE Mkt_Seg_ID IN (SELECT DISTINCT msdp.Mkt_Seg_ID " +
            " FROM [dbo].Mkt_Seg_Details_Proposed msdp " +
            " WHERE Status_ID=1 AND (Qualified=0 OR (Qualified=1 AND Link=1))) " +
            " AND Rate_Code NOT IN " +
            " (SELECT DISTINCT prc.Rate_Code FROM [dbo].[Product_Rate_Code] prc WHERE Product_ID NOT IN " +
            " (SELECT Product_ID FROM [dbo].[Product] WHERE Status_ID = 2)) ORDER BY Rate_Code ASC";

    static final String FIND_RATE_CODES_FOR_MKT_SEG_CODE = "SELECT DISTINCT Rate_Code FROM [dbo].[Reservation_Night] " +
            "WHERE Mkt_Seg_ID IN (SELECT Mkt_Seg_ID FROM Mkt_Seg WHERE Mkt_Seg_Code=:marketSegmentCode) " +
            "ORDER BY Rate_Code ASC;";

    static final String MARKET_SEGMENT_RATE_CODES_FROM_INDIVIDUAL_TRANS = "SELECT DISTINCT(Rate_Code) " +
            "FROM [dbo].[Individual_Trans] " +
            "WHERE mkt_seg_id in " +
            "(SELECT mkt_seg_id " +
            "FROM [dbo].[Mkt_Seg] " +
            "WHERE Mkt_Seg_Code in (:marketSegments))";
    static final String INSERT_RECOVERY_STATE =
            "INSERT INTO Recovery_State VALUES (:name, 0, :userID, GETDATE(), :userID, GETDATE())";
    static final String SET_DEFAULT_RECOVERY_STATE =
            "UPDATE Recovery_State SET Is_Default = 0, Last_Updated_By_User_ID = :userID, Last_Updated_DTTM = GETDATE() WHERE Is_Default = 1\n" +
                    "UPDATE Recovery_State SET Is_Default = 1, Last_Updated_By_User_ID = :userID, Last_Updated_DTTM = GETDATE() WHERE Recovery_State_Name = :name";
    static final String RECOVERY_STATE_EXISTS_IN_AGILE_CONFIG_TABLE =
            "IF EXISTS (SELECT TOP 1 1 FROM Agile_Product_Config WHERE Recovery_State = :recoveryState)\n" +
                    "SELECT CAST(1 AS BIT)\n" +
                    "ELSE\n" +
                    "SELECT CAST(0 AS BIT)";

    static final Integer DEFAULT_MIN_DTA = 0;
    static final Integer DEFAULT_MIN_LOS = 1;
    static final Integer DEFAULT_MAX_LOS = null;
    private static final Logger LOGGER = Logger.getLogger(AgileRatesConfigurationService.class);
    private static final Integer DEFAULT_MAX_DTA = null;
    private static final int STATUS_CODE_ACTIVE = 1;
    static final String FIND_LOWEST_FLOOR_FOR_SYSTEM_DEFAULT_PRODUCT =
            "SELECT MIN(floorColumn) AS floorVal " +
                    "FROM cp_cfg_base_at " +
                    "UNPIVOT (floorColumn FOR col IN " +
                    "(sunday_floor_rate_w_tax, monday_floor_rate_w_tax, tuesday_floor_rate_w_tax, wednesday_floor_Rate_w_tax, thursday_floor_rate_w_tax, friday_floor_rate_w_tax, saturday_floor_rate_w_tax)) as u " +
                    "WHERE ((start_date is NULL and end_date is NULL) OR (end_date >= :date)) AND (product_id = :productID);";

    static final String UPDATE_ALL_RELATIVE_TO_PRIMARY_FLOOR_PRODUCTS_FLOOR_RATE =
            "DECLARE @lowestPrimaryFloorRate AS INT; " +
                    "SET @lowestPrimaryFloorRate = (SELECT MIN(floorColumn) AS floorVal " +
                    "        FROM cp_cfg_base_at " +
                    "        UNPIVOT (floorColumn FOR col IN " +
                    "                (sunday_floor_rate_w_tax, monday_floor_rate_w_tax, tuesday_floor_rate_w_tax, wednesday_floor_Rate_w_tax, thursday_floor_rate_w_tax, friday_floor_rate_w_tax, saturday_floor_rate_w_tax)) as u " +
                    "                WHERE ((start_date is NULL and end_date is NULL) OR (end_date >= :date)) AND (product_id = :productID)); " +
                    "UPDATE Product " +
                    "SET Product_Floor_Rate = (1 + (Floor_Percentage / 100)) * @lowestPrimaryFloorRate " +
                    "where Floor_Type = 1 and product_id in (:productIds)";

    static final String UPDATE_PRODUCT_MIN_LOS_AND_MAX_LOS_BY_NAME =
            "Update Product SET Min_LOS = :minLOS, Max_LOS = :maxLOS WHERE name = :name";

    static final String UPDATE_RATE_SHOPPING_MIN_LOS_AND_MAX_LOS_BY_NAME_FOR_INDEPENDENT_PRODUCTS =
            "Update Product SET Rate_Shopping_LOS_Min = :minLOS, Rate_Shopping_LOS_Max = :maxLOS WHERE name = :name and Code = 'INDEPENDENT'";

    private static final Integer FLUSH_AND_CLEAR_BATCH_SIZE = 20;
    private static final BigDecimal PRODUCT_FLOOR_THRESHOLD_USD = new BigDecimal("50.00");
    public static final String RATE_QUALIFIED_IDS = "rateQualifiedIds";
    public static final String RATE_QUALIFIED_ID = "rateQualifiedId";
    public static final String PRODUCT_NAME = "productName";

    @Autowired
    DemandDetailsService demandDetailsService;
    @Autowired
	protected AccommodationService accommodationService;
    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
	protected CrudService tenantCrudService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	protected CrudService globalCrudService;
    @RatchetCrudServiceBean.Qualifier
	@Qualifier("ratchetCrudServiceBean")
    @Autowired
    CrudService ratchetCrudService;
    @Autowired
    SyncEventAggregatorService syncEventAggregatorService;
    @Autowired
    DateService dateService;
    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    CPRecommendationService cpRecommendationService;
    @Autowired
    RateUnqualifiedService rateUnqualifiedService;
    @Autowired
    HospitalityRoomsService hospitalityRoomsService;
    @Autowired
    ClientService clientService;
    @Autowired
    ExternalSystemHelper externalSystemHelper;
    @Autowired
    PropertyService propertyService;
    @Autowired
    LinkedSRPService linkedSRPService;
    @Autowired
    ExtendedStayRateMappingImportService extendedStayRateMappingImportService;
    @Autowired
    CurrencyExchangeService currencyExchangeService;
    @Autowired
    AccommodationMappingService accommodationMappingService;
    @Autowired
    VirtualPropertyMappingService virtualPropertyMappingService;
    @Autowired
    AgileRatesConfigurationVirtualPropertyService agileRatesConfigurationVPService;
    @Autowired
    AnalyticalMarketSegmentService analyticalMarketSegmentService;
    @Autowired
    PerPersonPricingService perPersonPricingService;
    @Autowired
    AgileRatesDataFeedService agileRatesDataFeedService;

    @Autowired
    RateCodeVendorMappingService rateCodeVendorMappingService;

    @Autowired
    AgileRatesHierarchyValidationService agileRatesHierarchyValidationService;

    @Autowired
    PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Autowired
    PrettyPricingService prettyPricingService;

    @Autowired
    ProductHierarchyValidationService productHierarchyValidationService;

    @Autowired
    FixedAboveBarConfigurationService fixedAboveBarConfigurationService;

    public List<AgileRatesDTARange> findAllDTARanges() {
        return tenantCrudService.findByNamedQuery(AgileRatesDTARange.GET_AGILE_RATES_ORDERED_BY_MIN_DTA);
    }

    public void saveDTARanges(List<AgileRatesDTARange> agileRatesDTARanges) {
        tenantCrudService.save(agileRatesDTARanges);
        updateDTAOffsets(agileRatesDTARanges);
        validateLinkedProducts();
        registerSyncEvent();
    }

    public void updateDTAOffsets(List<AgileRatesDTARange> agileRatesDTARanges) {
        //Update offsets for all Products with DTA offsets
        //Find all DTA offsets; filter out past seasons
        List<ProductRateOffset> allDTAOffsets = findAllProductRateOffsets().stream().filter(productRateOffset ->
                        productRateOffset.getAgileRatesDTARange() != null &&
                                (productRateOffset.getStartDate() == null || !productRateOffset.getStartDate().isBefore(dateService.getCaughtUpLocalDate())))
                .collect(Collectors.toList());
        if (isNotEmpty(allDTAOffsets)) {
            //Find all Products with DTA offsets
            Set<Product> allProductsDTAOffsets = allDTAOffsets.stream().map(ProductRateOffset::getProduct).collect(toSet());
            for (Product product : allProductsDTAOffsets) {
                AgileRatesProductConfigurationDTO dto = loadAgileRatesProductConfigurationByProductId(product.getId());
                updateOffsetsAndOverrides(dto, agileRatesDTARanges);
                saveAgileRatesProductConfiguration(dto);
            }
        }
    }

    public void deleteDTARanges(List<AgileRatesDTARange> agileRatesDTARanges) {
        tenantCrudService.delete(agileRatesDTARanges);
    }

    public void deleteProductRateOffsets(List<ProductRateOffset> agileRatesProductRateOffsets) {
        tenantCrudService.delete(agileRatesProductRateOffsets);
    }

    public void deleteProductRateOffsetOverrides(List<ProductRateOffsetOverride> productRateOffsetOverrides) {
        tenantCrudService.delete(productRateOffsetOverrides);
    }

    public void inactivateProductRateOffsetOverrides(List<ProductRateOffsetOverride> productRateOffsetOverrides) {
        productRateOffsetOverrides.stream().forEach(productRateOffsetOverride -> productRateOffsetOverride.setStatusId(2));
        tenantCrudService.save(productRateOffsetOverrides);
    }

    public List<AgileRatesPackage> findAllPackages() {
        return this.findAllPackagesAndChargeTypes().stream()
                .map(agileRatesPackageDTO ->
                {
                    AgileRatesPackage agileRatesPackage = new AgileRatesPackage();
                    agileRatesPackage.setId(agileRatesPackageDTO.getAgileRatesPackageId());
                    agileRatesPackage.setChargeType(agileRatesPackageDTO.getOccupancyType());
                    agileRatesPackage.setName(agileRatesPackageDTO.getName());
                    agileRatesPackage.setDescription(agileRatesPackageDTO.getDescription());
                    agileRatesPackage.setOffsetMethod(agileRatesPackageDTO.getOffsetMethod());
                    agileRatesPackage.setOffsetValue(agileRatesPackageDTO.getSundayOffsetValue());
                    agileRatesPackage.setAgileRatePackageChargeTypeId(agileRatesPackageDTO.getAgileRatesPackageChargeTypeId());
                    return agileRatesPackage;
                }).collect(Collectors.toList());
    }

    public List<AgileRatesPackage> findAllPackagesHeader() {
        return tenantCrudService.findByNamedQuery(AgileRatesPackage.GET_AGILE_RATES_PACKAGES_ORDERED_BY_NAME);
    }

    private List<AgileRatesPackageDTO> findAllPackagesAndChargeTypes() {
        return tenantCrudService.findByNamedQuery(AgileRatesPackageChargeType.ALL_RATES_PACKAGE_AND_CHARGE_TYPE);
    }

    public List<AgileRatesPackageChargeType> findAllAgileRatesPackageChargeTypes() {
        return tenantCrudService.findAll(AgileRatesPackageChargeType.class);
    }

    public void savePackageHeaderAndDefaultPackageChargeType(AgileRatesPackage agileRatesPackage) {
        AgileRatesPackage existingAgileRatesPackage = findAllPackagesHeader().stream().filter(a -> a.getId().equals(agileRatesPackage.getId()))
                .findFirst()
                .orElse(null);

        AgileRatesChargeType xChargeType = existingAgileRatesPackage == null ? null : existingAgileRatesPackage.getChargeType();

        LOGGER.info("Saving AgileRatePackage for Property: " + PacmanWorkContextHelper.getPropertyCode());
        tenantCrudService.save(agileRatesPackage);
        tenantCrudService.getEntityManager().flush();

        //if there are changes other than ChargeType then no need to update default entries in AgileRatesPackageChargeType
        if (xChargeType == null) {
            //check all existing seasons and add entries with zero offset
            this.saveDefaultAgileRatesPackage(agileRatesPackage, this.addNewPackageInExistingSeasons(agileRatesPackage));

        } else if (!agileRatesPackage.getChargeType().equals(xChargeType)
                || !configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CHILD_AGE_BUCKET_PACKAGES_ENABLED)) {

            tenantCrudService.executeUpdateByNamedQuery(AgileRatesPackageChargeType.DELETE_BY_AGILE_RATES_PACKAGE_ID,
                    QueryParameter.with(AgileRatesPackageChargeType.AGILE_RATES_PACKAGE_ID, agileRatesPackage.getId()).parameters());

            this.saveDefaultAgileRatesPackage(agileRatesPackage);
        }
        registerSyncEvent();
    }

    public void deletePackage(AgileRatesPackage agileRatesPackage) {
        //find the associated product packages and remove them before deleting the package
        List<ProductPackage> productPackages = tenantCrudService.findByNamedQuery(ProductPackage.BY_PACKAGE, QueryParameter.with("agileRatesPackage", agileRatesPackage).parameters());

        //but first loop through the products to see if they still are valid after we remove the package from them.
        //the product will be made invalid, if it doesn't have any remaining packages associated with it.
        for (ProductPackage productPackage : productPackages) {
            Product product = productPackage.getProduct();
            List<ProductPackage> packages = tenantCrudService.findByNamedQuery(ProductPackage.BY_PRODUCT, QueryParameter.with(PRODUCT_PARAMETER, product).parameters());

            //get the product's remaining packages while making sure we filter out the current package that we are deleting
            List<ProductPackage> remainingPackages = packages.stream().filter(productPackage1 -> !productPackage1.equals(productPackage)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(remainingPackages)) {
                //no remaining packages were found for this product, so set this products status to Invalid since it needs at least one package associated with it
                product.setInvalidReason(InvalidReason.MISSING_PACKAGE);
                tenantCrudService.save(product);
                deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled(product);
            }
        }

        //delete the product package associations now
        tenantCrudService.delete(productPackages);

        //delete AgileRatesPackageChargeType
        tenantCrudService.executeUpdateByNamedQuery(AgileRatesPackageChargeType.DELETE_BY_AGILE_RATES_PACKAGE_ID,
                QueryParameter.with(AgileRatesPackageChargeType.AGILE_RATES_PACKAGE_ID, agileRatesPackage.getId()).parameters());
        tenantCrudService.flushAndClear();

        //delete the actual package
        tenantCrudService.delete(agileRatesPackage);

        //revalidate all products
        validateLinkedProducts();

        registerSyncEvent();
    }

    public void saveAgileRatesPackageChargeTypes(List<AgileRatesPackageChargeType> packageChargeTypes) {
        tenantCrudService.save(packageChargeTypes);
        registerSyncEvent();
    }

    public List<Product> findAllProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL_EXCLUDING_EXTENDED_STAY_TYPE);
    }

    public List<Product> findLinkedProductsByChildPricingType(ChildPricingType childPricingType) {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL_LINKED_PRODUCTS_BY_CHILD_PRICING_TYPE,
                QueryParameter.with("childPricingType", childPricingType.getId()).parameters());
    }

    public List<Product> findSmallGroupProductsByChildPricingType(ChildPricingType childPricingType) {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL_SMALL_GROUP_PRODUCTS_BY_CHILD_PRICING_TYPE,
                QueryParameter.with("childPricingType", childPricingType.getId()).parameters());
    }


    public List<Product> findAllActiveUploadedAgileRatesProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_AND_ACTIVE_UPLOADED_PRODUCTS);
    }

    public List<Product> findAllActiveUploadedreportEnabledProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL_SYSTEM_DEFAULT_OR_ACTIVE_UPLOADED_REPORT_ENABLED_PRODUCTS);
    }

    public List<String> getDefaultProductName() {
        return tenantCrudService.findByNativeQuery("select name from Product as defaultProdName where System_Default=1 ");

    }

    public List<Product> findAgileAndSystemDefaultProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_OR_AGILE_RATES);
    }

    public List<Product> findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts() {
        return tenantCrudService.findByNamedQuery(
                Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_SMALL_GROUP_PRODUCTS);
    }

    public List<Product> findAgileAndSystemDefaultProductsAndIndependentProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_PRODUCTS);
    }

    public List<Product> getApplicableProducts() {
        List<Product> allProducts = findAgileAndSystemDefaultProductsAndIndependentProducts();
        List<Product> qualifiedSortedProducts;
        boolean isIndependentProductsEnabled = isIndependentProductsToggleEnabled();
        boolean isRDLEnabled = isRDLEnabled();
        if (isIndependentProductsEnabled && isRDLEnabled) {
            qualifiedSortedProducts = allProducts;
        } else if (isIndependentProductsEnabled) {
            qualifiedSortedProducts = getQualifiedProducts(allProducts, product -> product.isSystemDefault() || product.isIndependentProduct());
        } else if (isRDLEnabled) {
            qualifiedSortedProducts = getQualifiedProducts(allProducts, product -> product.isSystemDefault() || product.isAgileRatesProduct());
        } else {
            qualifiedSortedProducts = getQualifiedProducts(allProducts, Product::isSystemDefault);
        }
        qualifiedSortedProducts.sort(Comparator.comparing(Product::getDisplayOrder));
        return qualifiedSortedProducts;
    }

    private static List<Product> getQualifiedProducts(List<Product> allProducts, Predicate<Product> productPredicate) {
        return allProducts.stream()
                .filter(productPredicate)
                .collect(Collectors.toList());
    }

    public List<Product> findAllAgileAndSystemDefaultAndIndependentProductsIncludeInactive() {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_PRODUCTS);
    }

    public List<Product> findAgileAndSystemDefaultProductsAndSmallGroupProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_AGILE_RATES_SMALL_GROUP_PRODUCTS);
    }

    public List<Product> findSystemDefaultProductAndIndependentProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_INDEPENDENT_PRODUCTS);
    }

    public List<Product> findAllActiveAgileRatesProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_OR_AGILE_RATES_WITH_ACTIVE_STATUS,
                QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<Product> findAllActiveAgileRatesAndIndependentProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_OR_AGILE_RATES_INDEPENDENT_PRODUCT_WITH_ACTIVE_STATUS,
                QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }
    public List<Product> findAllActiveAgileRatesIndependentAndSmallGroupProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_SMALL_GROUP_PRODUCTS_BY_STATUS,
                QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<Product> findAllActiveIsReportEnabledProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL_SYSTEM_DEFAULT_OR_IS_REPORT_ENABLED_PRODUCTS,
                QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public boolean isReportEnabledforOtherProducts() {
        List<String> productCodes;
        productCodes = tenantCrudService.findByNamedQuery(ProductCode.GET_ALL_REPORT_ENABLED_TOGGLES);
        return productCodes.size() > 1;
    }

    public void updateIsReportEnableInProductCode(String productCodeName, Boolean isReportEnabled) {
        tenantCrudService.executeUpdateByNamedQuery(
                ProductCode.UPDATE_REPORT_ENABLED,
                QueryParameter.with("productCodeName", productCodeName)
                        .and("val", isReportEnabled)
                        .parameters());
    }

    public List<Product> findAgileRatesProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.AGILE_RATES_PRODUCT_CODE).parameters());
    }

    public List<Product> findSmallGroupProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters());
    }

    public List<Product> findIndependentlyPricedProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.INDEPENDENT_PRODUCT_CODE).parameters());
    }

    public List<Product> findActiveUploadEnabledSmallGroupProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_PRODUCTS_BY_CODE_STATUS_UPLOAD,
                QueryParameter.with("code", Product.GROUP_PRODUCT_CODE)
                        .and("status", TenantStatusEnum.ACTIVE)
                        .and("isUpload", true).parameters());
    }

    public List<String> findAllAgileRatesProductRateCodes() {
        List<Product> fixedAgileRatesProducts = findAgileRatesProducts();
        if (CollectionUtils.isNotEmpty(fixedAgileRatesProducts)) {
            List<ProductRateCode> productRateCodesByProductList = findProductRateCodesByProductList(fixedAgileRatesProducts);
            return productRateCodesByProductList.stream()
                    .map(ProductRateCode::getRateCode)
                    .collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    public List<AccomType> findAllRoomTypes() {
        return accommodationService.getAllAssignedAccomTypesIncludingInactive();
    }

    public List<String> findAllUnassignedTransientOrQualifiedLinkedRateCodes() {
        List<String> rateCodes;
        if (forecastGroupsExists()) {
            rateCodes = tenantCrudService.findByNativeQuery(UNASSIGNED_TRANSIENT_OR_QUALIFIED_LINKED_RATE_CODES);
        } else {
            rateCodes = tenantCrudService.findByNativeQuery(FIND_UNASSIGNED_TRANSIENT_OR_QUALIFIED_LINKED_RATE_CODES_FROM_MKT_SEG_DETAILS_PROPOSED);
        }

        return rateCodes.stream()
                .filter(rateCode -> !StringUtils.isBlank(rateCode))
                .collect(Collectors.toList());
    }

    public List<String> findAllUnassignedRateCodesFromPrimaryProduct(Product primaryProduct, List<Product> relatedProducts) {
        List<String> rateCodesList = new ArrayList<>();

        List<ProductRateCode> productRateCodesByProduct = findProductRateCodesByProduct(primaryProduct);
        rateCodesList.addAll(productRateCodesByProduct.stream()
                .map(ProductRateCode::getRateCode)
                .collect(Collectors.toList()));

        //Remove all rate codes assigned to any children of primary product
        if (CollectionUtils.isNotEmpty(relatedProducts)) {
            List<ProductRateCode> productRateCodesByProductList = findProductRateCodesByProductList(relatedProducts);
            rateCodesList.removeAll(productRateCodesByProductList.stream()
                    .map(ProductRateCode::getRateCode)
                    .collect(Collectors.toList()));
        }

        return rateCodesList;
    }

    public boolean forecastGroupsExists() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Object countOfActiveFgs = tenantCrudService.findByNamedQuerySingleResult(ForecastGroup.COUNT_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).and("statusId", STATUS_CODE_ACTIVE).parameters());
        return null != countOfActiveFgs && (Long) countOfActiveFgs > 0;
    }

    public List<String> findAllRateCodesForMarketSegmentsByIndividualTrans(List<String> marketSegments) {
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put("marketSegments", marketSegments);
        return tenantCrudService.findByNativeQuery(MARKET_SEGMENT_RATE_CODES_FROM_INDIVIDUAL_TRANS, parameters);
    }

    public List<String> findAllRateCodesForMarketSegment(String marketSegmentCode) {
        return tenantCrudService.findByNativeQuery(FIND_RATE_CODES_FOR_MKT_SEG_CODE,
                QueryParameter.with("marketSegmentCode", marketSegmentCode).parameters());
    }

    public AgileRatesProductConfigurationDTO loadAgileRatesProductConfigurationByProductId(Integer productId) {
        Product product = tenantCrudService.find(Product.class, productId);
        if (product == null) {
            return null;
        }
        AgileRatesProductConfigurationDTO dto = new AgileRatesProductConfigurationDTO(product);
        dto.setProductType(AgileRatesProductTypeEnum.fromValue(product.getType()));
        Integer baseProductId = product.getDependentProductId();
        if (baseProductId != null) {
            dto.setBaseProduct(tenantCrudService.find(Product.class, baseProductId));
        }
        dto.setRateCodes(findRateCodesByProduct(product));
        List<AccomType> roomTypesByProduct = findRoomTypesByProduct(product);
        dto.setRoomTypes(roomTypesByProduct);
        dto.setPackageElements(findPackagesByProduct(product));
        List<ProductRateOffset> productRateOffsetsByProduct = findProductRateOffsetsByProduct(product);
        dto.setDefaultOffsets(productRateOffsetsByProduct.stream().filter(productRateOffset -> productRateOffset.getStartDate() == null).collect(Collectors.toList()));
        dto.setProductSeasons(loadAgileRatesSeasonByProductId(dto, productRateOffsetsByProduct));

        if (isRDLEnabled() && product.isAgileRatesProduct()) {
            setRateTypeDataForLinkedProduct(dto, productId);
        }

        return dto;
    }

    public Set<AgileRatesSeason> loadAgileRatesSeasonByProductId(AgileRatesProductConfigurationDTO dto, List<ProductRateOffset> offsetsByProduct) {
        Set<AgileRatesSeason> seasons = new HashSet<>();

        List<ProductRateOffset> seasonOffsets = offsetsByProduct.stream()
                .filter(productRateOffset -> productRateOffset.getStartDate() != null)
                .collect(Collectors.toList());

        for (ProductRateOffset offset : seasonOffsets) {
            if (seasons.stream().noneMatch(season -> offset.getStartDate() != null && season.getStartDate() != null && offset.getStartDate().equals(new LocalDate(season.getStartDate().toString())))) {
                seasons.add(createNewSeason(dto, offset));
            } else {
                // somewhere in this flow - we lose the dow offset
                seasons.stream()
                        .filter(season -> offset.getStartDate().equals(new LocalDate(season.getStartDate().toString())))
                        .findAny()
                        .ifPresent(agileRatesSeason -> {
                            agileRatesSeason.getSeasonRateOffsets().add(offset);
                        });
            }
        }
        return seasons;
    }

    public boolean isDowOffset(ProductRateOffset offset) {
        return offset.allSeasonDowOffsetsNotEqual();
    }

    private AgileRatesSeason createNewSeason(AgileRatesProductConfigurationDTO dto, ProductRateOffset offset) {
        AgileRatesSeason season = new AgileRatesSeason(dto != null ? dto.getProduct() : offset.getProduct());
        season.setDowOffset(offset.isSeasonDowOffset());
        season.setDtaOffset(offset.getAgileRatesDTARange() != null);
        season.setRoomClassOffset(offset.getAccomClass() != null);
        season.setName(offset.getSeasonName());
        season.setJavaStartDate(java.time.LocalDate.parse(offset.getStartDate().toString()));
        season.setJavaEndDate(java.time.LocalDate.parse(offset.getEndDate().toString()));
        season.getSeasonRateOffsets().add(offset);
        return season;
    }

    public void saveAgileRatesProductConfiguration(AgileRatesProductConfigurationDTO dto) {
        //if we are not fencing, make sure to wipe/reset the values related to fencing
        if (!dto.isFencingSelected()) {
            dto.setMinimumDaysAdvancedBooking(DEFAULT_MIN_DTA);
            dto.setMaximumDaysAdvancedBooking(DEFAULT_MAX_DTA);
            dto.setMinimumDaysLengthOfStay(DEFAULT_MIN_LOS);
            dto.setMaximumDaysLengthOfStay(DEFAULT_MAX_LOS);
        }

        //if we are not packaging, make sure to wipe/reset the values related to packages
        if (!dto.isPackagesSelected()) {
            dto.setPackageElements(Collections.emptyList());
        }

        Product product = dto.getProduct();
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);

        if (dto.getDefaultOffsets().isEmpty() && !product.isDefaultInactive()) {
            product.setInvalidReason(InvalidReason.MISSING_RATE_OFFSETS);
        } else if (dto.getProduct().getId() == null) {
            product.setActive(false);
        }
        setProductMinimumPriceChangeToNullNonOptimized(product);

        if (product.getDisplayOrder() == null) {
            product.setDisplayOrder(findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts().size() + 1);
        }

        boolean isNewProduct = !product.isPersisted();
        if (isNewProduct) {
            // set Default value for Product code and MinRooms and MaxRooms
            ProductCode pc = findProductCodeByName(Product.AGILE_RATES_PRODUCT_CODE);
            product.setProductCode(pc);
            product.setMinRooms(DEFAULT_MIN_ROOMS);
            product.setMaxRooms(DEFAULT_MAX_ROOMS);
        }

        tenantCrudService.save(product);
        saveRateUnqualifiedForHilton(product.getName(), product.getDescription());
        updateProductRateCodes(product, dto.getRateCodes());
        updateProductRoomTypes(product, dto.getRoomTypes());
        updateProductPackages(product, dto.getPackageElements());
        deleteRoomTypesForRateProtectProduct(product);

        //update default offsets
        updateProductRateOffsets(dto.getDefaultOffsets(), getPersistedDefaultOffsets(product));

        //update season offsets
        updateProductRateOffsets(
                dto.getProductSeasons().stream().flatMap(season -> season.getSeasonRateOffsets().stream()).collect(Collectors.toList()),
                getFilteredOffsetsByProduct(product, offset -> offset.getStartDate() != null));

        //update product rate offset overrides for advanced booking changes
        if (!isNewProduct && dto.isAdvancedBookingChanged()) {
            LocalDate caughtUpLocalDate = dateService.getCaughtUpLocalDate();
            updateProductRateOffsetOverrides(product, caughtUpLocalDate);
        }

        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        boolean isRDLEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);

        if (isIndependentProductsEnabled || isRDLEnabled) {
            accommodationMappingService.updateCompetitorAccomClassReferenceForProduct(PacmanWorkContextHelper.getPropertyId(), product);
            accommodationMappingService.createDefaultChannelForProduct(product);
        }

        if (!isNewProduct && dto.isSendAdjustmentByChangedStatus()) {
            //Persisted product that changed from send by Adjustment to Price
            LocalDate caughtUpLocalDate = dateService.getCaughtUpLocalDate();
            deleteDecisionDailyBarByProduct(product, caughtUpLocalDate);
            if (cpRecommendationService.isIHotelierEnabledAndProductSendByPrice(product)) {
                deleteDecisionDailyBarNonHiltonCRSByProduct(product, caughtUpLocalDate);
                deleteNonHiltonCRSPaceDailyBarByProduct(product, caughtUpLocalDate);
                deletePaceDailyBarByProduct(product, caughtUpLocalDate);
            }
        }

        boolean shouldSaveRateTypeDataForLinkedProduct = isRDLEnabled() && product.isAgileRatesProduct();
        if (shouldSaveRateTypeDataForLinkedProduct) {
            //if we are updating the data, clear stale data first
            deleteWebrateTypeProductForProductId(product.getId());
            saveWebrateTypeDataForLinkedProduct(dto);
        }

        //validate all products
        validateLinkedProducts();

        if (!isNewProduct && dto.isDefaultInactive()) {
            setInvalidReasonForRateProtectProducts(product);
        }

        registerSyncEvent();
    }

    public void deleteRoomTypesForRateProtectProduct(Product product) {
        List<ProductAccomType> productRoomTypesByProduct = findProductRoomTypesByProduct(product);
        List<AccomType> productAccomTypes = productRoomTypesByProduct.stream()
                .map(ProductAccomType::getAccomType).collect(Collectors.toList());
        List<Product> dependantProducts = findProductDependencies(product);
        dependantProducts.forEach(dependantProduct -> {
            if (AgileRatesProductTypeEnum.FIXED_ABOVE_BAR.getValue().equals(dependantProduct.getCode())) {
                List<ProductAccomType> productRoomTypesForDependantProduct = findProductRoomTypesByProduct(dependantProduct);
                List<ProductAccomType> productAccomTypesToBeDeleted = new ArrayList<>();
                productRoomTypesForDependantProduct.forEach(dependantProductAccomType -> {
                    if (!productAccomTypes.contains(dependantProductAccomType.getAccomType())) {
                        productAccomTypesToBeDeleted.add(dependantProductAccomType);
                    }
                });
                if (!productAccomTypesToBeDeleted.isEmpty()) {
                    tenantCrudService.delete(productAccomTypesToBeDeleted);
                }
            }
        });
    }

    public void saveSmallGroupProductConfiguration(AgileRatesProductConfigurationDTO dto) {
        if (!dto.isFencingSelected()) {
            dto.setMinimumDaysAdvancedBooking(DEFAULT_MIN_DTA);
            dto.setMaximumDaysAdvancedBooking(DEFAULT_MAX_DTA);
            dto.setMinimumDaysLengthOfStay(DEFAULT_MIN_LOS);
            dto.setMaximumDaysLengthOfStay(DEFAULT_MAX_LOS);
        }

        Product product = dto.getProduct();
        if (product != null) {
            ProductCode pc = findProductCodeByName(Product.GROUP_PRODUCT_CODE);
            product.setProductCode(pc);
            product.setCode(Product.GROUP_PRODUCT_CODE);
            product.setType(AgileRatesProductTypeEnum.SMALL_GROUP.getValue());
        }
        if (dto.getDefaultOffsets().isEmpty() && !product.isDefaultInactive()) {
            product.setInvalidReason(InvalidReason.MISSING_RATE_OFFSETS);
        } else if (dto.getProduct().getId() == null) {
            product.setActive(false);
        }

        if (product.getDisplayOrder() == null) {
            product.setDisplayOrder(findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts().size() + 1);
        }

        tenantCrudService.save(product);
        saveRateUnqualifiedForHilton(product.getName(), product.getDescription());
        updateProductRoomTypes(product, dto.getRoomTypes());

        //update default offsets
        updateProductRateOffsets(dto.getDefaultOffsets(), getPersistedDefaultOffsets(product));

        //update season offsets
        updateProductRateOffsets(
                dto.getProductSeasons().stream().flatMap(season -> season.getSeasonRateOffsets().stream()).collect(Collectors.toList()),
                getFilteredOffsetsByProduct(product, offset -> offset.getStartDate() != null));

        boolean isNewProduct = !product.isPersisted();
        //update product rate offset overrides for advanced booking changes
        if (!isNewProduct && dto.isAdvancedBookingChanged()) {
            LocalDate caughtUpLocalDate = dateService.getCaughtUpLocalDate();
            updateProductRateOffsetOverrides(product, caughtUpLocalDate);
        }

        if (!isNewProduct && dto.isSendAdjustmentByChangedStatus()) {
            //Persisted product that changed from send by Adjustment to Price
            LocalDate caughtUpLocalDate = dateService.getCaughtUpLocalDate();
            deleteDecisionDailyBarByProduct(product, caughtUpLocalDate);
            if (cpRecommendationService.isIHotelierEnabledAndProductSendByPrice(product)) {
                deleteDecisionDailyBarNonHiltonCRSByProduct(product, caughtUpLocalDate);
                deleteNonHiltonCRSPaceDailyBarByProduct(product, caughtUpLocalDate);
                deletePaceDailyBarByProduct(product, caughtUpLocalDate);
            }
        }

        //validate all products
        Integer changedProductId = product.getId();
        validateSmallGroupProductsWithProductId(changedProductId);

        registerSmallGroupProductChangedSyncEvent();
        if (isLongTermBDEProcessingOnConfigChangeEnabled() && product.isActive()) {
            enableLTBDEForPricing();
        }
    }

    private boolean isLongTermBDEProcessingOnConfigChangeEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_LTBDE_ON_CONFIG_CHANGE);
    }

    public void validateSmallGroupProductsWithProductId(Integer changedProductId) {
        List<Product> allSmallGroupProducts = findSmallGroupProducts();
        List<ProductAccomType> allProductAccomTypes = findAllProductAccomType();

        if (allSmallGroupProducts.isEmpty()) {
            return;
        }

        //save Invalid Reason before it gets reset in setActive
        Map<Integer, InvalidReason> smallGroupInvalidReasonMap = getInvalidReasonMap(allSmallGroupProducts);

        for (Product p : allSmallGroupProducts) {
            if (TenantStatusEnum.INVALID.equals(p.getStatus())) {
                p.setActive(false);
            }
        }

        // invalidation is ordered from least to most critical
        invalidateUnfencedProductWithFencedParent(allSmallGroupProducts);
        invalidateMissingSeasonOffsets(allSmallGroupProducts);
        invalidateMissingRateOffsets(allSmallGroupProducts, allProductAccomTypes);
        invalidateSeqMinMaxRooms(allSmallGroupProducts, changedProductId);
        invalidateMissingMinMaxRooms(allSmallGroupProducts);
        invalidateOverlapMinMaxRooms(allSmallGroupProducts, changedProductId, smallGroupInvalidReasonMap);

        tenantCrudService.save(allSmallGroupProducts);
    }

    public void validateSmallGroupProductsWhenChangingOptimizationLevel() {
        List<Product> allSmallGroupProducts = findSmallGroupProducts();
        List<ProductAccomType> allProductAccomTypes = findAllProductAccomType();

        if (allSmallGroupProducts.isEmpty()) {
            return;
        }

        for (Product p : allSmallGroupProducts) {
            if (TenantStatusEnum.INVALID.equals(p.getStatus())) {
                p.setActive(false);
            }
        }

        // invalidation is ordered from least to most critical
        invalidateUnfencedProductWithFencedParent(allSmallGroupProducts);
        invalidateMissingSeasonOffsets(allSmallGroupProducts);
        invalidateMissingRateOffsets(allSmallGroupProducts, allProductAccomTypes);
        invalidateMissingMinMaxRooms(allSmallGroupProducts);

        tenantCrudService.save(allSmallGroupProducts);
        registerSmallGroupProductChangedSyncEvent();
    }

    private Map<Integer, InvalidReason> getInvalidReasonMap(List<Product> smallGroupProducts) {
        Map<Integer, InvalidReason> smallGroupInvalidReasonMap = new HashMap<>();
        smallGroupProducts.forEach(product -> {
                    smallGroupInvalidReasonMap.put(product.getId(), product.getInvalidReason());
                }
        );
        return smallGroupInvalidReasonMap;
    }

    public List<Product> findAllSmallGroupProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_BY_CODE,
                QueryParameter.with(Product.CODE_PARAM, Product.GROUP_PRODUCT_CODE).parameters());
    }

    private void saveRateUnqualifiedForHilton(String rateName, String description) {
        if (PacmanWorkContextHelper.isHiltonClientCode()) {
            rateUnqualifiedService.saveRateUnqualified(rateName, description);
        }
    }

    private void setProductMinimumPriceChangeToNullNonOptimized(Product product) {
        if (!product.isOptimized()) {
            product.setMinimumPriceChange(null);
        }
    }

    public boolean allSeasonsInvalid(List<AgileRatesSeason> futureAndPresentSeasons) {
        return futureAndPresentSeasons.stream()
                .allMatch(season -> season.getSeasonRateOffsets().stream().anyMatch(ProductRateOffset::allDowOffsetsNull));
    }

    public boolean seasonInvalid(AgileRatesSeason season) {
        return season.getSeasonRateOffsets().stream().anyMatch(ProductRateOffset::allDowOffsetsNull);
    }

    public List<AgileRatesSeason> getFutureAndPresentSeasons(Product product) {
        return loadAgileRatesSeasonByProductId(null, findProductRateOffsetsByProduct(product)).stream()
                .filter(season -> !isPastSeason(season))
                .collect(Collectors.toList());
    }

    public List<ProductRateOffset> getPersistedDefaultOffsets(Product product) {
        return getFilteredOffsetsByProduct(product, productRateOffset -> productRateOffset.getStartDate() == null);
    }

    public void deleteLinkedProduct(Product product, LocalDate systemDate) {
        assert (product.isPersisted());
        assert (Product.AGILE_RATES_PRODUCT_CODE.equals(product.getCode()));

        setInvalidReasonForDependentProducts(product);

        markDeletedProductRateOffsetOverridesByProduct(product);

        //non-base decision tables (CP_Decision_Bar_Output, CP_Decision_Bar_Output_OVR, CP_Unqualified_Demand_FCST_Price, Decision_Dailybar_Output)
        //need to have future records deleted (Arrival_DT >= :currentDate) so we don't send them out again.
        deleteFutureProductDependentNonBaseDecisionEntities(product, systemDate);

        deleteProductRateCodesForProduct(product);

        deleteProductPackagesForProduct(product);

        deleteProductAccomTypesForProduct(product);
        deleteOffsetMappingsForProduct(product);

        if (isRDLEnabled()) {
            deleteWebrateTypeProductForProductId(product.getId());
        }

        product.setStatus(TenantStatusEnum.DELETED);
        product.setUpload(false);
        product.setDisplayOrder(-1);
        tenantCrudService.save(product);
        deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled(product);
        deleteAllExistingOffsetsForProduct(product);

        accommodationMappingService.deleteWebrateCompetitorChannelMappings(product);
        accommodationMappingService.deleteWebrateOverrideCompetitor(product);
        accommodationMappingService.deleteWebrateCompetitorsAccomClass(product);
        accommodationMappingService.deleteWebrateDefaultChannel(product);
        accommodationMappingService.deleteWebrateOverrideChannel(product);

        validateLinkedProducts();

        registerSyncEvent();
    }

    private void setInvalidReasonForRateProtectProducts(Product product) {
        List<Product> productDependencies = findProductDependencies(product)
                .stream().filter(prod -> AgileRatesProductTypeEnum.FIXED_ABOVE_BAR.getValue()
                        .equals(prod.getCode())).collect(Collectors.toUnmodifiableList());
        if (isNotEmpty(productDependencies)) {
            productDependencies.forEach(p -> {
                p.setDependentProductId(null);
                p.setInvalidReason(InvalidReason.MISSING_BASE_PRODUCT);
            });
            tenantCrudService.save(productDependencies);
        }
    }

    public void deleteSmallGroupProduct(Product product, LocalDate systemDate) {
        assert (product.isPersisted());
        assert (Product.GROUP_PRODUCT_CODE.equals(product.getCode()));

        markDeletedProductRateOffsetOverridesByProduct(product);
        //non-base decision tables (CP_Decision_Bar_Output, CP_Decision_Bar_Output_OVR, CP_Unqualified_Demand_FCST_Price, Decision_Dailybar_Output)
        //need to have future records deleted (Arrival_DT >= :currentDate) so we don't send them out again.
        deleteFutureProductDependentNonBaseDecisionEntities(product, systemDate);

        deleteProductAccomTypesForProduct(product);
        deleteOffsetMappingsForProduct(product);

        product.setStatus(TenantStatusEnum.DELETED);
        product.setUpload(false);
        product.setDisplayOrder(-1);
        tenantCrudService.save(product);
        deleteAllExistingOffsetsForProduct(product);

        validateSmallGroupProductsWithProductId(product.getId());

        registerSmallGroupProductChangedSyncEvent();
    }

    public void deleteAllOffsetsExceptSeasonalForProduct(Product product) {
        Collection<ProductRateOffset> offsets = findProductRateOffsetsByProduct(product).stream()
                .filter(o -> o.getStartDate() == null)
                .collect(Collectors.toList());
        tenantCrudService.delete(offsets);
    }

    public void deleteAllExistingOffsetsForProduct(Product product) {
        tenantCrudService.delete(findProductRateOffsetsByProduct(product));
    }

    public void shiftProductDisplayOrder(List<Product> productList) {
        if (isNotEmpty(productList)) {
            productList.forEach(p -> p.setDisplayOrder(p.getDisplayOrder() - 1));
            tenantCrudService.save(productList);
        }
    }

    public void swapProductDisplayOrder(Product product, Product otherProduct) {
        if (otherProduct != null) {
            int oldRank = product.getDisplayOrder();
            product.setDisplayOrder(otherProduct.getDisplayOrder());
            otherProduct.setDisplayOrder(oldRank);

            Collection<Product> collection = Arrays.asList(product, otherProduct);
            tenantCrudService.save(collection);
        }
    }

    private void markDeletedProductRateOffsetOverridesByProduct(Product product) {
        List<ProductRateOffsetOverride> productRateOffsetOverrides = findProductRateOffsetOverrides(product);
        productRateOffsetOverrides.forEach(productRateOffsetOverride ->
                productRateOffsetOverride.setStatusId(TenantStatusEnum.DELETED.getId()));
        tenantCrudService.save(productRateOffsetOverrides);
    }

    public AgileRateProductResponseDTO deleteFutureProductDependentNonBaseDecisionEntities(Integer productId) {
        Product product = tenantCrudService.find(Product.class, productId);
        deleteFutureProductDependentNonBaseDecisionEntities(product, dateService.getCaughtUpLocalDate());
        return createAgileRateProductResponseDTO(List.of(product.getName()), List.of());
    }

    public void deleteFutureProductDependentNonBaseDecisionEntities(Product product, LocalDate systemDate) {
        deleteCPDecisionBarOutputsByProduct(product, systemDate);
        deleteCPPaceDecisionBarOutputByProduct(product, systemDate);
        deleteCPDecisionBarOutputOverridesByProduct(product, systemDate);
        deleteDecisionDailyBarByProduct(product, systemDate);
        deleteCPUnqualifedDemandForecastPrice(product, systemDate);
        if (cpRecommendationService.isIHotelierEnabledAndProductSendByAdjustment(product)) {
            deleteDecisionDailyBarNonHiltonCRSByProduct(product, systemDate);
            deleteNonHiltonCRSPaceDailyBarByProduct(product, systemDate);
        }
    }

    private void deleteCPDecisionBarOutputsByProduct(Product product, LocalDate systemDate) {
        executeUpdateByPropertyIdAndProduct(product, systemDate, CPDecisionBAROutput.DELETE_BY_PRODUCT);
    }

    private void deleteCPPaceDecisionBarOutputByProduct(Product product, LocalDate systemDate) {
        deleteByProductDifferentialPace(product, systemDate, CPPaceDecisionBAROutputDifferential.DELETE_BY_PRODUCT);
    }

    private void deleteCPDecisionBarOutputOverridesByProduct(Product product, LocalDate systemDate) {
        executeUpdateByPropertyIdAndProduct(product, systemDate, CPDecisionBAROutputOverride.DELETE_BY_PRODUCT);
    }

    private void deleteDecisionDailyBarByProduct(Product product, LocalDate systemDate) {
        tenantCrudService.executeUpdateByNamedQuery(
                DailyBarDecisions.DELETE_BY_PRODUCT,
                QueryParameter.with(PRODUCT_ID, product.getId())
                        .and("date", systemDate)
                        .parameters());
    }

    private void deleteDecisionDailyBarNonHiltonCRSByProduct(Product product, LocalDate systemDate) {
        tenantCrudService.executeUpdateByNamedQuery(
                DecisionDailybarOutputNonHiltonCRS.DELETE_BY_PRODUCT,
                QueryParameter.with(PRODUCT_ID, product.getId())
                        .and("date", systemDate)
                        .parameters());
    }

    protected void deleteDecisionDailyBarNonHiltonCRSByProduct(Product product, LocalDate systemDate, List<Integer> accomTypeIDs) {
        tenantCrudService.executeUpdateByNamedQuery(
                DecisionDailybarOutputNonHiltonCRS.DELETE_BY_PRODUCT_AND_ACCOM_TYPES,
                QueryParameter.with(PRODUCT_ID, product.getId())
                        .and("date", systemDate)
                        .and(ACCOM_TYPE_IDS, accomTypeIDs)
                        .parameters());
    }

    private void deleteNonHiltonCRSPaceDailyBarByProduct(Product product, LocalDate systemDate) {
        tenantCrudService.executeUpdateByNamedQuery(
                PaceDailyBarOutputNonHiltonCRS.DELETE_BY_PRODUCT,
                QueryParameter.with(PRODUCT_ID, product.getId())
                        .and("date", systemDate)
                        .parameters());
    }

    private void deletePaceDailyBarByProduct(Product product, LocalDate systemDate) {
        tenantCrudService.executeUpdateByNamedQuery(
                PaceDailyBarOutput.DELETE_BY_PRODUCT,
                QueryParameter.with(PRODUCT_ID, product.getId())
                        .and("date", systemDate)
                        .parameters());
    }

    private void deleteCPUnqualifedDemandForecastPrice(Product product, LocalDate systemDate) {
        tenantCrudService.executeUpdateByNamedQuery(
                CPUnqualifedDemandForecastPrice.DELETE_BY_PRODUCT,
                QueryParameter.with(PRODUCT_PARAMETER, product)
                        .and(ARRIVAL_DATE_PARAMETER, systemDate)
                        .parameters());
    }

    private void executeUpdateByPropertyIdAndProduct(Product product, LocalDate systemDate, String deleteByProductQueryString) {
        tenantCrudService.executeUpdateByNamedQuery(
                deleteByProductQueryString,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and(PRODUCT_PARAMETER, product)
                        .and(ARRIVAL_DATE_PARAMETER, systemDate)
                        .parameters());
    }

    private void deleteByProductDifferentialPace(Product product, LocalDate systemDate, String deleteByProductQueryString) {
        tenantCrudService.executeUpdateByNamedQuery(
                deleteByProductQueryString,
                QueryParameter.with(PRODUCT_PARAMETER, product)
                        .and(ARRIVAL_DATE_PARAMETER, systemDate)
                        .parameters());
    }

    public void deleteFutureProductDependentNonBaseDecisionEntities(Product product, LocalDate systemDate, List<Integer> accomTypeIDs) {
        deleteCPDecisionBarOutputsByProduct(product, systemDate, accomTypeIDs);
        deleteDecisionDailyBarByProduct(product, systemDate, accomTypeIDs);
    }

    protected void deleteCPDecisionBarOutputsByProduct(Product product, LocalDate systemDate, List<Integer> accomTypeIDs) {
        tenantCrudService.executeUpdateByNamedQuery(
                CPDecisionBAROutput.DELETE_BY_PRODUCT_AND_ACCOM_TYPES,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(PRODUCT_PARAMETER, product)
                        .and(ARRIVAL_DATE_PARAMETER, systemDate)
                        .and(ACCOM_TYPE_IDS, accomTypeIDs)
                        .parameters());
    }

    protected void deleteDecisionDailyBarByProduct(Product product, LocalDate systemDate, List<Integer> accomTypeIDs) {
        tenantCrudService.executeUpdateByNamedQuery(
                DailyBarDecisions.DELETE_BY_PRODUCT_AND_ACCOM_TYPES,
                QueryParameter.with(PRODUCT_ID, product.getId())
                        .and("date", systemDate)
                        .and(ACCOM_TYPE_IDS, accomTypeIDs)
                        .parameters());
    }

    protected void deleteProductRateOffsetOverrides(Product product, LocalDate systemDate, List<Integer> accomClassIds) {
        tenantCrudService.executeUpdateByNamedQuery(
                ProductRateOffsetOverride.DELETE_BY_PRODUCT_AND_ACCOM_TYPES,
                QueryParameter.with(PRODUCT_ID, product.getId())
                        .and("occupancyDate", systemDate)
                        .and(ACCOM_CLASS_IDS, accomClassIds)
                        .parameters());
    }

    protected void setInvalidReasonForDependentProducts(Product product) {
        List<Product> productDependencies = findProductDependencies(product);
        if (isNotEmpty(productDependencies)) {
            productDependencies.forEach(p -> {
                p.setDependentProductId(null);
                p.setInvalidReason(InvalidReason.MISSING_BASE_PRODUCT);
                p.setRateShoppingLOSMin(-1);
                p.setRateShoppingLOSMax(-1);
            });
            tenantCrudService.save(productDependencies);
        }
    }

    public List<AgileRatesPackage> findPackagesByProduct(Product product) {
        return findProductPackagesByProduct(product).parallelStream()
                .map(ProductPackage::getAgileRatesPackage)
                .sorted(Comparator.comparing(AgileRatesPackage::getName))
                .collect(Collectors.toList());
    }

    public List<Product> findProductDependencies(Product product) {
        return tenantCrudService.findByNamedQuery(Product.GET_BY_DEPENDENT_PRODUCT_ID,
                QueryParameter.with(Product.DEPENDENT_PRODUCT_ID_PARAM, product.getId()).parameters());
    }

    public List<Product> findProductDependencies(String productName) {
        Product product = tenantCrudService.findAll(Product.class).stream().filter(p -> p.getName().equals(productName)).findFirst().orElse(null);

        if (product == null) {
            return Collections.emptyList();
        }
        return findProductDependencies(product);
    }

    public List<ProductPackage> findProductPackagesByProduct(Product product) {
        return findAllByProduct(product, ProductPackage.BY_PRODUCT);
    }

    public List<ProductAccomType> findProductRoomTypesByProduct(Product product) {
        return findAllByProduct(product, ProductAccomType.BY_PRODUCT);
    }

    public List<ProductRateCode> findAllProductRateCodes() {
        return tenantCrudService.findAll(ProductRateCode.class);
    }

    public List<ProductRateCode> findProductRateCodesByProduct(Product product) {
        return findAllByProduct(product, ProductRateCode.BY_PRODUCT);
    }

    public List<ProductRateCode> findProductRateCodesByProductList(List<Product> products) {
        return tenantCrudService.findByNamedQuery(
                ProductRateCode.BY_PRODUCTS,
                QueryParameter.with(PRODUCTS_PARAMETER, products).parameters());
    }

    public List<ProductRateOffset> findProductRateOffsetsByProduct(Product product) {
        return findAllByProduct(product, ProductRateOffset.BY_PRODUCT);
    }

    public List<ProductRateOffset> findAllProductRateOffsets() {
        return tenantCrudService.findAll(ProductRateOffset.class);
    }

    public List<ProductRateOffsetOverride> findAllProductRateOffsetOverrides() {
        return tenantCrudService.findAll(ProductRateOffsetOverride.class);
    }

    public List<ProductRateOffsetOverride> findAllActiveFutureProductRateOffsetOverrides(LocalDate systemDate) {
        return tenantCrudService.findByNamedQuery(ProductRateOffsetOverride.ACTIVE_AFTER_SYSTEM_DATE,
                QueryParameter.with("systemDate", systemDate).parameters());
    }

    private <T> List<T> findAllByProduct(Product product, String namedQuery) {
        return tenantCrudService.findByNamedQuery(
                namedQuery,
                QueryParameter.with(PRODUCT_PARAMETER, product).parameters());
    }

    public List<String> findRateCodesByProduct(Product product) {
        return findProductRateCodesByProduct(product).parallelStream().
                map(ProductRateCode::getRateCode)
                .sorted()
                .collect(Collectors.toList());
    }

    public List<AccomType> findRoomTypesByProduct(Product product) {
        return findProductRoomTypesByProduct(product).parallelStream()
                .map(ProductAccomType::getAccomType)
                .sorted()
                .collect(Collectors.toList());
    }

    public List<ProductPackage> findAllProductPackage() {
        return tenantCrudService.findAll(ProductPackage.class);
    }

    public List<ProductAccomType> findAllProductAccomType() {
        return tenantCrudService.findAll(ProductAccomType.class);
    }

    public void updateProductPackages(Product product, Collection<AgileRatesPackage> packages) {
        Set<AgileRatesPackage> desiredPackages = new HashSet<>(packages);
        Set<AgileRatesPackage> currentPackages = new HashSet<>();
        List<ProductPackage> productPackagesByProduct = findProductPackagesByProduct(product);
        for (ProductPackage productPackage : productPackagesByProduct) {
            AgileRatesPackage agileRatesPackage = productPackage.getAgileRatesPackage();
            if (desiredPackages.contains(agileRatesPackage)) {
                currentPackages.add(agileRatesPackage);
            } else {
                tenantCrudService.delete(productPackage);
            }
        }
        for (AgileRatesPackage agileRatesPackage : desiredPackages) {
            if (!currentPackages.contains(agileRatesPackage)) {
                ProductPackage productPackage = new ProductPackage();
                productPackage.setProduct(product);
                productPackage.setAgileRatesPackage(agileRatesPackage);
                tenantCrudService.save(productPackage);
            }
        }
    }

    public void updateProductRateCodes(Product product, Collection<String> rateCodes) {
        rateCodes = rateCodes != null ? rateCodes : Collections.emptyList();

        Set<String> desiredRateCodes = new HashSet<>(rateCodes);
        Set<String> currentRateCodes = new HashSet<>();
        List<ProductRateCode> productRateCodesByProduct = findProductRateCodesByProduct(product);
        for (ProductRateCode productRateCode : productRateCodesByProduct) {
            String rateCode = productRateCode.getRateCode();
            if (desiredRateCodes.contains(rateCode)) {
                currentRateCodes.add(rateCode);
            } else {
                tenantCrudService.delete(productRateCode);
            }
        }
        for (String rateCode : desiredRateCodes) {
            if (!currentRateCodes.contains(rateCode)) {
                ProductRateCode productRateCode = new ProductRateCode();
                productRateCode.setProduct(product);
                productRateCode.setRateCode(rateCode);
                tenantCrudService.save(productRateCode);
            }
        }
    }

    public void deleteProductAccomTypesForProduct(Product product) {
        tenantCrudService.delete(findProductRoomTypesByProduct(product));
    }

    public void deleteProductRateCodesForProduct(Product product) {
        tenantCrudService.delete(findProductRateCodesByProduct(product));
    }

    public void deleteProductRateCodesForProductAndRelatedProducts(List<Product> products) {
        tenantCrudService.delete(findProductRateCodesByProductList(products));
    }

    public void deleteRoundRulesForProduct(Product product) {
        tenantCrudService.executeUpdateByNamedQuery(PrettyPricingRuleRow.DELETE_PRETTY_PRICE_RULES_FOR_PROPERTY_AND_PRODUCT,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("productId", product.getId()).parameters());
    }

    public void deleteProductPackagesForProduct(Product product) {
        tenantCrudService.delete(findProductPackagesByProduct(product));
    }

    public void deleteProductRateCodesByProductRateCodes(List<ProductRateCode> productRateCodes) {
        tenantCrudService.delete(productRateCodes);
        registerSyncEvent();
    }

    public void updateProductRateOffsets(Collection<ProductRateOffset> desiredRateOffsets, List<ProductRateOffset> persistedOffsets) {
        Set<ProductRateOffset> persistedDesiredRateOffsets = desiredRateOffsets.stream()
                .filter(ProductRateOffset::isPersisted)
                .collect(toSet());

        for (ProductRateOffset productRateOffset : persistedOffsets) {
            if (!persistedDesiredRateOffsets.contains(productRateOffset)) {
                tenantCrudService.delete(productRateOffset);
            }
        }

        tenantCrudService.save(desiredRateOffsets.stream().filter(proo -> proo.getOffsetMethod() != null).collect(Collectors.toSet()));
    }

    public void updateProductRateOffsetOverrides(Product product, LocalDate systemDate) {
        List<AgileRatesDTARange> allDTARanges = findAllDTARanges();

        List<AgileRatesDTARange> dtaRangesForProduct = allDTARanges.stream()
                .filter(range -> range.getMinDaysToArrival() <= (product.getMaxDTA() == null ? Integer.MAX_VALUE : product.getMaxDTA()) &&
                        (range.getMaxDaysToArrival() == null ? Integer.MAX_VALUE : range.getMaxDaysToArrival()) >= product.getMinDTA())
                .collect(Collectors.toList());

        //soft deleting overrides >= to system date and that no longer apply to the Advance Booking Range for the Product
        List<ProductRateOffsetOverride> invalidProductRateOffsetOverrides = findProductRateOffsetOverrides(product)
                .stream()
                .filter(proo -> !dtaRangesForProduct.contains(proo.getAgileRatesDTARange()) && proo.getOccupancyDate().compareTo(systemDate) >= 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(invalidProductRateOffsetOverrides)) {
            invalidProductRateOffsetOverrides.forEach(proo -> proo.setStatusId(TenantStatusEnum.DELETED.getId()));
            tenantCrudService.save(invalidProductRateOffsetOverrides);
        }
    }

    public void updateProductRoomTypes(Product product, Collection<AccomType> roomTypes) {
        Set<AccomType> desiredRoomTypes = new HashSet<>(roomTypes);
        Set<AccomType> currentRoomTypes = new HashSet<>();
        List<ProductAccomType> productRoomTypesByProduct;
        if(Objects.equals(product.getCode(), AGILE_RATES_PRODUCT_CODE) || Objects.equals(product.getCode(), INDEPENDENT_PRODUCT_CODE)) {
            List<Product> productsToCheck = AgileRatesUtils.getChildren(product, getAllProducts());
            productsToCheck.add(product);
            productRoomTypesByProduct = findProductRoomTypeByProducts(new HashSet<>(productsToCheck));
        } else {
            productRoomTypesByProduct = findProductRoomTypesByProduct(product);
        }
        for (ProductAccomType productAccomType : productRoomTypesByProduct) {
            AccomType roomType = productAccomType.getAccomType();
            if (desiredRoomTypes.contains(roomType)) {
                currentRoomTypes.add(roomType);
            } else {
                tenantCrudService.delete(productAccomType);
            }
        }
        for (AccomType roomType : desiredRoomTypes) {
            if (!currentRoomTypes.contains(roomType)) {
                ProductAccomType productAccomType = new ProductAccomType();
                productAccomType.setProduct(product);
                productAccomType.setAccomType(roomType);
                tenantCrudService.save(productAccomType);
            }
        }
    }

    public AgileRatesPackageWarningResults getPackageWarnings(AgileRatesPackage agileRatesPackage, boolean isDelete) {
        //check to see if the package name is unique only when adding or editing a package.
        if (!isDelete) {
            AgileRatesPackage existingPackage = tenantCrudService.findByNamedQuerySingleResult(AgileRatesPackage.FIND_BY_NAME, QueryParameter.with("name", agileRatesPackage.getName()).parameters());
            if (existingPackage != null && !existingPackage.equals(agileRatesPackage)) {
                //package is not unique
                return new AgileRatesPackageWarningResults(AgileRatesPackageWarning.DUPLICATE_NAME);
            }
        }

        //next check to see if the package has been associated with any products
        //and only do this check if the package has been saved
        if (agileRatesPackage.isPersisted()) {
            List<ProductPackage> productPackages = tenantCrudService.findByNamedQuery(ProductPackage.BY_PACKAGE, QueryParameter.with("agileRatesPackage", agileRatesPackage).parameters());
            if (isNotEmpty(productPackages)) {
                //products were found that are currently using the package
                List<Product> productsInUse =
                        productPackages.stream()
                                .map(ProductPackage::getProduct)
                                .filter(product -> !TenantStatusEnum.DELETED.equals(product.getStatus()))
                                .collect(Collectors.toList());
                return new AgileRatesPackageWarningResults(AgileRatesPackageWarning.IN_USE, productsInUse);
            }
        }

        //no warnings found, so return a empty warning results
        return new AgileRatesPackageWarningResults();
    }

    public void saveProduct(Product product, boolean enableLTBDEForPricing) {
        tenantCrudService.save(product);
        if (product.isGroupProduct()) {
            registerSmallGroupProductChangedSyncEvent();
            if (enableLTBDEForPricing && isLongTermBDEProcessingOnConfigChangeEnabled()) {
                enableLTBDEForPricing();
            }
        } else if (product.isIndependentProduct()) {
            registerIndependentProductChangedSyncEvent();
        } else {
            registerSyncEvent();
        }
    }

    public void saveProducts(List<Product> products) {
        tenantCrudService.save(products);
    }

    public void saveProductOnToggleDisable(Product product) {
        tenantCrudService.save(product);
    }

    public void saveSeasonForLinkedProduct(AgileRatesSeason season) {
        java.time.LocalDate startDate = season.getOriginalJavaStartDate() != null ? season.getOriginalJavaStartDate() : season.getJavaStartDate();
        updateProductRateOffsets(season.getSeasonRateOffsets(), getPersistedSeasonOffsets(season.getProduct(), DateUtil.convertJavaToJodaLocalDate(startDate)));
        validateLinkedProducts();
        registerSyncEvent();
    }

    public List<ProductRateOffset> getPersistedSeasonOffsets(Product product, LocalDate startDate) {
        return getFilteredOffsetsByProduct(product, productRateOffset -> productRateOffset.getStartDate() != null).stream()
                .filter(offset -> offset.getStartDate().equals(startDate))
                .collect(Collectors.toList());
    }

    private List<ProductRateOffset> getFilteredOffsetsByProduct(Product product, Predicate<ProductRateOffset> filter) {
        return findProductRateOffsetsByProduct(product).stream()
                .filter(filter)
                .collect(Collectors.toList());
    }

    public void deleteProductSeasonsForLinkedProduct(Collection<AgileRatesSeason> productSeasons) {
        for (AgileRatesSeason season : productSeasons) {
            tenantCrudService.delete(season.getSeasonRateOffsets());
        }
        validateLinkedProducts();
        registerSyncEvent();
    }

    public void registerSyncEvent() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.AGILE_RATES_CHANGED);
    }

    public void registerIndependentProductChangedSyncEvent() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    public void registerSmallGroupProductChangedSyncEvent() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.GROUP_PRODUCT_CHANGED);
    }

    private void enableLTBDEForPricing() {
        pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
    }

    public AgileRatesProductConfigurationDTO copyLinkedProduct(Product originalProduct, String newProductName, int newDisplayOrder, java.time.LocalDate systemDate) {
        AgileRatesProductConfigurationDTO originalDto = loadAgileRatesProductConfigurationByProductId(originalProduct.getId());

        Product newProduct = new Product(originalProduct);
        newProduct.setName(newProductName.trim());
        newProduct.setDisplayOrder(newDisplayOrder);

        AgileRatesProductConfigurationDTO dto = new AgileRatesProductConfigurationDTO(newProduct);
        dto.setPackageElements(originalDto.getPackageElements());
        dto.setRoomTypes(originalDto.getRoomTypes());
        dto.setBaseProduct(originalDto.getBaseProduct());
        if (!originalProduct.isFreeNightEnabled()) {
            dto.setDefaultOffsets(copyProductRateOffsets(originalDto.getDefaultOffsets(), newProduct));
        }
        dto.setProductSeasons(copyProductSeasons((Set<AgileRatesSeason>) originalDto.getProductSeasons(), newProduct, systemDate));
        dto.setProductType(originalDto.getProductType());

        if (isRDLEnabled()) {
            dto.setRateType(RATE_TYPE_NA_CODE);
        }

        return dto;
    }

    public AgileRatesProductConfigurationDTO copySmallGroupProduct(Product originalProduct, String newProductName, int newDisplayOrder, java.time.LocalDate systemDate) {
        AgileRatesProductConfigurationDTO originalDto = loadAgileRatesProductConfigurationByProductId(originalProduct.getId());

        Product newProduct = new Product(originalProduct);
        newProduct.setName(newProductName.trim());
        newProduct.setDisplayOrder(newDisplayOrder);

        //Reset Min and Max Rooms since no overlapping values are allowed
        newProduct.setMinRooms(Integer.valueOf(-1));
        newProduct.setMaxRooms(Integer.valueOf(-1));

        AgileRatesProductConfigurationDTO dto = new AgileRatesProductConfigurationDTO(newProduct);
        dto.setRoomTypes(originalDto.getRoomTypes());
        dto.setBaseProduct(originalDto.getBaseProduct());
        dto.setDefaultOffsets(copyProductRateOffsets(originalDto.getDefaultOffsets(), newProduct));
        dto.setProductSeasons(copyProductSeasons((Set<AgileRatesSeason>) originalDto.getProductSeasons(), newProduct, systemDate));
        dto.setProductType(originalDto.getProductType());

        return dto;
    }

    public Collection<ProductRateOffset> copyProductRateOffsets(Collection<ProductRateOffset> productRateOffsets, Product product) {
        return productRateOffsets.stream().map(p -> {
            ProductRateOffset pro = new ProductRateOffset(p);
            pro.setProduct(product);
            return pro;
        }).collect(toSet());
    }

    private Set<AgileRatesSeason> copyProductSeasons(Set<AgileRatesSeason> productSeasons, Product product, java.time.LocalDate systemDate) {
        //filter out past seasons
        List<AgileRatesSeason> filteredSeasons = productSeasons.stream().filter(season -> !season.getJavaEndDate().isBefore(systemDate)).collect(Collectors.toList());
        return filteredSeasons.stream().map(s -> {
            AgileRatesSeason season = new AgileRatesSeason(s, systemDate);
            season.setProduct(product);
            for (ProductRateOffset offset : season.getSeasonRateOffsets()) {
                offset.setProduct(product);
            }
            return season;
        }).collect(toSet());
    }

    public List<ProductGroup> getAllProductsInProductGroup(Product product, List<ProductGroup> productGroupsList) {
        if (productGroupsList.isEmpty()) {
            productGroupsList = getProductGroups();
        }
        // find the product we care about
        ProductGroup productGroup = productGroupsList.stream()
                .filter(p -> p.getProduct().equals(product))
                .findFirst()
                .orElse(null);
        // find the agileratesproductgroup it's in and find other products in the same group - careful not to double select our product sent in
        if (productGroup != null) {
            AgileRatesProductGroup agileRatesProductGroup = productGroup.getAgileRatesProductGroup();
            return productGroupsList.stream()
                    .filter(p -> p.getAgileRatesProductGroup().equals(agileRatesProductGroup))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public boolean isProductInProductGroup(List<Product> products) {
        return getProductGroups()
                .stream()
                .anyMatch(p -> products.contains(p.getProduct()));
    }

    public void deleteProductFromProductGroup(Product product, List<ProductGroup> productGroupList) {
        // will remove a product from a product group or the entire group should there only be 1 product in the list
        List<ProductGroup> productGroupsToDelete = new ArrayList<>();
        List<AgileRatesProductGroup> groupToDelete = new ArrayList<>();
        if (productGroupList.size() <= 2 && !productGroupList.isEmpty()) {
            productGroupsToDelete.addAll(productGroupList);
            groupToDelete.add(productGroupList.get(0).getAgileRatesProductGroup());
        } else {
            ProductGroup productGroupToDelete = productGroupList.stream().filter(p -> p.getProduct().equals(product)).findFirst().orElse(null);
            if (productGroupToDelete != null) {
                productGroupsToDelete.add(productGroupToDelete);
            }
        }

        deleteProductGroups(groupToDelete, productGroupsToDelete);
    }

    public boolean willProductBeValid(AgileRatesProductConfigurationDTO dto) {
        return !(isBaseProductInvalid(dto) ||
                willHaveMissingRateOffsets(dto) ||
                willAllSeasonsBeInvalid(dto) ||
                dto.getOptimizedChangedStatus() ||
                dto.isAdjustmentTypeChangedStatus());
    }

    protected boolean isBaseProductInvalid(AgileRatesProductConfigurationDTO dto) {
        return !dto.getProduct().isSystemDefault() && dto.getProduct().getDependentProductId() == null;
    }

    protected boolean willHaveMissingRateOffsets(AgileRatesProductConfigurationDTO dto) {
        Product product = dto.getProduct();
        List<ProductRateOffset> allRateOffsets = new ArrayList<>(dto.getDefaultOffsets());
        List<AgileRatesDTARange> allDTARanges = findAllDTARanges();
        List<AgileRatesDTARange> productDTARanges = getAdjustedDTARanges(dto.getProduct(), allDTARanges);
        List<AccomType> allAccomTypes = new ArrayList<>(dto.getRoomTypes());

        for (ProductRateOffset offset : allRateOffsets) {
            if (offset.allDowOffsetsNull() && offset.getProduct().equals(product)) {
                return true;
            }
        }

        int numDefaultOffsets = expectedNumberOfDefaultOffsets(product, null, allAccomTypes, productDTARanges);

        return allRateOffsets.stream()
                .filter(productRateOffset -> productRateOffset.getProduct().equals(product) && productRateOffset.getStartDate() == null)
                .collect(Collectors.toList())
                .size() != numDefaultOffsets
                &&
                !product.isSystemDefaultOrIndependentProduct();
    }

    protected boolean willAllSeasonsBeInvalid(AgileRatesProductConfigurationDTO dto) {
        if (dto.getProduct().isDefaultInactive()) {
            List<AgileRatesSeason> futureAndPresentSeasons = getFutureAndPresentSeasons(dto.getProduct());
            //filter out invalid seasons
            List<AgileRatesSeason> validFuturePresentSeasons = futureAndPresentSeasons.stream().filter(season -> !seasonInvalid(season)).collect(Collectors.toList());
            //Return false if there are no valid future or present seasons
            if (!validFuturePresentSeasons.isEmpty()) {
                //Return false if there are valid non-DTA or Room Class seasons
                List<AgileRatesSeason> nonDtaRoomClassSeasons = validFuturePresentSeasons.stream().filter(season -> !season.isDtaOffset() && !season.isRoomClassOffset() && !seasonInvalid(season)).collect(Collectors.toList());
                if (nonDtaRoomClassSeasons.isEmpty()) {
                    List<AgileRatesSeason> dtaSeasons = validFuturePresentSeasons.stream().filter(AgileRatesSeason::isDtaOffset).collect(Collectors.toList());
                    List<AgileRatesSeason> roomClassSeasons = validFuturePresentSeasons.stream().filter(AgileRatesSeason::isRoomClassOffset).collect(Collectors.toList());

                    List<AgileRatesDTARange> allDTARanges = findAllDTARanges();
                    List<AgileRatesDTARange> productDTARanges = getAdjustedDTARanges(dto.getProduct(), allDTARanges);
                    boolean dtaRangesWillBeAdded = dtaRangesWillBeAdded(dto, productDTARanges, dateService.getCaughtUpLocalDate());
                    boolean roomClassesWillBeAdded = roomClassesWillBeAdded(dto, dateService.getCaughtUpLocalDate());

                    //Return true if both DTA and Room Classes will be added
                    //Return true if all of your seasons are DTA seasons and DTA Ranges will be added
                    //Return true if all of your seasons are Room Class seasons and Room Classes will be added
                    return dtaRangesWillBeAdded && roomClassesWillBeAdded ||
                            (dtaRangesWillBeAdded && dtaSeasons.size() == validFuturePresentSeasons.size()) ||
                            (roomClassesWillBeAdded && roomClassSeasons.size() == validFuturePresentSeasons.size());
                }
            }
        }
        return false;
    }

    public List<Product> childrenThatWillBeInvalid(AgileRatesProductConfigurationDTO dto, List<Product> children) {
        List<ProductAccomType> allProductAccomType = findAllProductAccomType();

        Set<AccomType> baseProductAccomTypes = new HashSet<>(dto.getRoomTypes());

        List<Product> invalidChildren = new ArrayList<>();
        for (Product child : children) {
            Set<AccomType> childProductAccomTypes = allProductAccomType.stream()
                    .filter(productAccomType -> productAccomType.getProduct().equals(child))
                    .map(ProductAccomType::getAccomType)
                    .collect(toSet());

            if (!baseProductAccomTypes.containsAll(childProductAccomTypes)) {
                invalidChildren.add(child);
            }

        }
        return invalidChildren;
    }

    public List<Product> optimizedChildrenWithFixedOffsetParentToBeInvalidated(AgileRatesProductConfigurationDTO dto, List<Product> children) {
        List<Product> invalidChildren = new ArrayList<>();

        if (!dto.isOptimized() && dto.getOffsetMethod().equals(AgileRatesOffsetMethod.FIXED)) {
            invalidChildren.addAll(children.stream().filter(Product::isOptimized).collect(Collectors.toList()));
        }
        return invalidChildren;
    }

    public Set<String> getAllAccomTypesFromActiveProducts() {
        List<ProductAccomType> allProductAccomTypes = tenantCrudService.findByNamedQuery(ProductAccomType.HAVING_PRODUCT_STATUS_ACTIVE);
        return allProductAccomTypes.stream().map(productAccomType -> productAccomType.getAccomType().getAccomTypeCode()).collect(toSet());
    }

    public void validateLinkedProducts() {
        List<Product> allProducts = findAllProducts();
        List<ProductAccomType> allProductAccomTypes = findAllProductAccomType();

        List<Product> independentProducts = filterProducts(allProducts, Product::isIndependentProduct);
        List<Product> barAndAgileRateProducts = allProducts;

        barAndAgileRateProducts.removeAll(independentProducts);

        validateProductsOnOptimizedState(barAndAgileRateProducts, independentProducts, allProductAccomTypes);

        List<Product> freeNightProducts = barAndAgileRateProducts.stream().filter(Product::isFreeNightEnabled).collect(toList());

        for (Product product : freeNightProducts) {
            AgileRatesProductConfigurationDTO dto = loadAgileRatesProductConfigurationByProductId(product.getId());
            if (CollectionUtils.isEmpty(dto.getRateCodes())) {
                product.setInvalidReason(InvalidReason.MISSING_RATE_CODES);
            }
        }

        tenantCrudService.save(barAndAgileRateProducts);

        barAndAgileRateProducts.forEach(this::deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled);
    }

    private void validateProductsOnOptimizedState(List<Product> barAndAgileRateProducts, List<Product> independentProducts, List<ProductAccomType> allProductAccomTypes) {

        for (Product p : barAndAgileRateProducts) {
            if (TenantStatusEnum.INVALID.equals(p.getStatus())) {
                p.setActive(false);
            }
        }

        List<Product> agileRatesProducts = barAndAgileRateProducts.stream().filter(product -> product.getCode().equals(Product.AGILE_RATES_PRODUCT_CODE)).collect(Collectors.toList());
        //ranked from least to greatest importance
        invalidateUnfencedProductWithFencedParent(agileRatesProducts);
        invalidateProductAccomTypes(barAndAgileRateProducts, allProductAccomTypes);
        invalidateMissingRateCodes(barAndAgileRateProducts);
        invalidateMissingSeasonOffsets(barAndAgileRateProducts);
        invalidateMissingRateOffsets(barAndAgileRateProducts, allProductAccomTypes);
        invalidateMissingPackage(agileRatesProducts);
        invalidateMissingBaseProduct(barAndAgileRateProducts, independentProducts);
        invalidateOptimizedChildrenProductsThatHaveFixedParent(agileRatesProducts);
        invalidateMissingBasesProductForRateProtectProducts(barAndAgileRateProducts);

        //This should happen last ALWAYS
        invalidateDependentProducts(barAndAgileRateProducts);
    }

    public void invalidateMissingBasesProductForRateProtectProducts(List<Product> allProducts) {
        for (Product p : allProducts) {
            if (AgileRatesProductTypeEnum.FIXED_ABOVE_BAR.getValue().equals(p.getCode())
                    && p.getDependentProductId() == null) {
                p.setInvalidReason(InvalidReason.MISSING_BASE_PRODUCT);
                p.setRateShoppingLOSMin(-1);
                p.setRateShoppingLOSMax(-1);
            }
        }
    }

    public void deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled(Product product) {
        if ((Product.AGILE_RATES_PRODUCT_CODE.equals(product.getCode()) && product.previousUploadState() && !product.isUpload())
        || Product.GROUP_PRODUCT_CODE.equals(product.getCode())) {
            softDeleteAllFutureOffsetOverrides(findProductRateOffsetOverrides(product));
            deleteFutureDecisionsForNonUploadableProducts(product);
        }
    }

    public void deleteFutureDecisionsForNonUploadableProducts(Product product) {
        cpRecommendationService.deleteFutureDecisionsForNonUploadableProducts(product);
    }

    public void deleteFutureDecisionDailyBarOutput(Product product, LocalDate caughtUpLocalDate) {
        cpRecommendationService.deleteFutureDecisionDailyBarOutput(product, caughtUpLocalDate);
    }

    public void deleteFutureESRateUnqualifiedOverrides(LocalDate caughtUpLocalDate) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("caughtUpDate", caughtUpLocalDate);
        tenantCrudService.executeUpdateByNamedQuery(ExtendedStayRateUnqualifiedOverride.DELETE_FUTURE_ES_RATE_UNQUALIFIED_OVERRIDE, parameters);
    }

    public void deleteDailyBarConfiguration() {
        // Delete the DailyBarConfig (and flush so that this delete happens before the rate charts)
        tenantCrudService.deleteAll(DailyBarConfig.class);
        tenantCrudService.flush();
        // Delete DailyBarRateChart
        tenantCrudService.deleteAll(DailyBarRateChart.class);
    }

    public List<Product> findOldExtendedStayProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL_EXTENDED_STAY_TYPE);
    }

    @VisibleForTesting
    public void invalidateOptimizedChildrenProductsThatHaveFixedParent(List<Product> products) {
        for (Product p : products) {
            if (!p.isSystemDefault() && p.isOptimized() && hasFixedDependentId(products, p)) {
                p.setInvalidReason(InvalidReason.INVALID_BASE_PRODUCT_ADJUSTMENT_TYPE);
            }
        }
    }

    @VisibleForTesting
    public boolean hasFixedDependentId(List<Product> products, Product p) {
        Optional<Product> baseProduct = products.stream().filter(product -> !product.isSystemDefaultOrIndependentProduct()).filter(product -> product.getId().equals(p.getDependentProductId())).findFirst();
        if (baseProduct.isPresent()) {
            return baseProduct.get().getOffsetMethod().equals(AgileRatesOffsetMethod.FIXED);
        } else {
            return false;
        }
    }

    public void setProductsToInactiveAndSave(List<Product> products) {
        products.forEach(product -> {
            product.setActive(Boolean.FALSE);
            deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled(product);
        });
        tenantCrudService.save(products);
        registerSyncEvent();
    }

    //Invalid base product
    protected void invalidateDependentProducts(List<Product> allProducts) {
        List<Product> invalidProducts = new ArrayList<>();

        for (Product p : allProducts) {
            if (TenantStatusEnum.INVALID.equals(p.getStatus())) {
                invalidProducts.add(p);
            }
        }

        invalidateDependentProductsHelper(allProducts, invalidProducts);
    }

    //Recursive solution to invalidating children
    private void invalidateDependentProductsHelper(List<Product> allProducts, List<Product> invalidProducts) {
        if (!invalidProducts.isEmpty()) {
            List<Product> newInvalidProducts = new ArrayList<>();

            for (Product invalidProduct : invalidProducts) {
                for (Product product : allProducts) {
                    if (!product.isSystemDefault() &&
                            (product.getDependentProductId() == null || product.getDependentProductId().equals(invalidProduct.getId())) &&
                            !product.getStatus().equals(TenantStatusEnum.INVALID)) {
                        product.setInvalidReason(InvalidReason.INVALID_BASE_PRODUCT);
                        newInvalidProducts.add(product);
                    }
                }
            }
            invalidateDependentProductsHelper(allProducts, newInvalidProducts);
        }
    }

    //Missing package
    protected void invalidateMissingPackage(List<Product> allProducts) {
        List<ProductPackage> allPackages = findAllProductPackage();

        for (Product p : allProducts) {
            if ((AgileRatesProductTypeEnum.FENCED_AND_PACKAGED.getValue().equals(p.getType()) || AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue().equals(p.getType())) &&
                    allPackages.stream().noneMatch(productPackage -> productPackage.getProduct().equals(p))) {
                p.setInvalidReason(InvalidReason.MISSING_PACKAGE);
            }
        }
    }

    //Missing base product
    protected void invalidateMissingBaseProduct(List<Product> barAndAgileRateProducts, List<Product> independentProducts) {
        List<Product> allProducts = new ArrayList<>();
        allProducts.addAll(barAndAgileRateProducts);
        allProducts.addAll(independentProducts);

        for (Product p : allProducts) {
            if (p.isAgileRatesProduct() && allProducts.stream().noneMatch(product -> product.getId().equals(p.getDependentProductId()))) {
                p.setInvalidReason(InvalidReason.MISSING_BASE_PRODUCT);
                p.setRateShoppingLOSMin(-1);
                p.setRateShoppingLOSMax(-1);
            }
        }
    }

    //Unfenced product with a Fenced Parent
    protected void invalidateUnfencedProductWithFencedParent(List<Product> allProducts) {
        for (Product p : allProducts) {
            if (!p.isSystemDefault() &&
                    (AgileRatesProductTypeEnum.UNFENCED_AND_NO_PACKAGED.equals(AgileRatesProductTypeEnum.fromValue(p.getType())) ||
                            AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.equals(AgileRatesProductTypeEnum.fromValue(p.getType()))) &&
                    (allProducts.stream().anyMatch(product -> product.getId().equals(p.getDependentProductId()) &&
                            (AgileRatesProductTypeEnum.FENCED_AND_PACKAGED.equals(AgileRatesProductTypeEnum.fromValue(product.getType())) ||
                                    AgileRatesProductTypeEnum.FENCED_AND_NO_PACKAGE.equals(AgileRatesProductTypeEnum.fromValue(product.getType())))))) {
                p.setInvalidReason(InvalidReason.INVALID_FENCED_PARENT);
            }
        }
    }

    //Missing seasons
    public void invalidateMissingSeasonOffsets(List<Product> allProducts) {
        for (Product product : allProducts) {
            if (product.isDefaultInactive()) {
                List<AgileRatesSeason> futureAndPresentSeasons = getFutureAndPresentSeasons(product);
                if (futureAndPresentSeasons.isEmpty() || allSeasonsInvalid(futureAndPresentSeasons)) {
                    product.setInvalidReason(InvalidReason.MISSING_SEASON_OFFSETS);
                }
            }
        }
    }

    //Missing rate codes
    public void invalidateMissingRateCodes(List<Product> allProducts) {
        List<ProductRateCode> allProductRateCodes = findAllProductRateCodes();

        List<Product> productsToValidate = allProducts.stream()
                .filter(p -> !p.isSystemDefault() && !p.isGroupProduct())
                .collect(Collectors.toList());

        for (Product p : productsToValidate) {
            List<ProductRateCode> productRateCodes = allProductRateCodes.stream()
                    .filter(productRateCode -> productRateCode.getProduct().getId().equals(p.getId()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(productRateCodes)) {
                p.setInvalidReason(InvalidReason.MISSING_RATE_CODES);
            }
        }
    }

    //Missing Min and Max Rooms
    public void invalidateMissingMinMaxRooms(List<Product> allProducts) {
        for (Product product : allProducts) {
            if ((product.getMinRooms() == -1) && (product.getMaxRooms() == -1)) {
                product.setInvalidReason(InvalidReason.MISSING_MIN_MAX_ROOMS);
            }
        }
    }

    public boolean isPastSeason(AgileRatesSeason season) {
        return season.getEndDate() != null && season.getEndDate().isBefore(dateService.getCaughtUpLocalDate());
    }

    //Missing rate offsets
    public void invalidateMissingRateOffsets(List<Product> allProducts, List<ProductAccomType> allProductAccomTypes) {
        List<ProductRateOffset> allRateOffsets = findAllProductRateOffsets();
        List<AgileRatesDTARange> allDTARanges = findAllDTARanges();

        for (ProductRateOffset offset : allRateOffsets) {
            if (offset.getStartDate() == null && offset.allDowOffsetsNull()) {
                for (Product p : allProducts) {
                    setProductInvalidReasonMissingRateOffsets(offset, p);
                }
            }
        }

        for (Product product : allProducts) {
            List<AgileRatesDTARange> productDTARanges = getAdjustedDTARanges(product, allDTARanges);
            int numDefaultOffsets = expectedNumberOfDefaultOffsets(product, allProductAccomTypes, null, productDTARanges);
            if (product.getIsFixedAboveBar()) {
                if (!product.isDefaultInactive()
                        && !product.isSystemDefaultOrIndependentProduct()
                        && allRateOffsets.stream()
                        .filter(offset -> offset.getOffsetMethod().equals(product.getOffsetMethod()) || offset.getOffsetMethod().equals(AgileRatesOffsetMethod.SET))
                        .filter(productRateOffset -> productRateOffset.getProduct().equals(product) && productRateOffset.getStartDate() == null)
                        .count() != (numDefaultOffsets * 2)) {
                    product.setInvalidReason(InvalidReason.MISSING_RATE_OFFSETS);
                }
            } else if (!product.isDefaultInactive() &&
                    allRateOffsets.stream()
                            .filter(productRateOffset -> productRateOffset.getProduct().equals(product) && productRateOffset.getStartDate() == null)
                            .count() != numDefaultOffsets
                    &&
                    !product.isSystemDefaultOrIndependentProduct()) {
                product.setInvalidReason(InvalidReason.MISSING_RATE_OFFSETS);
            }
        }
    }

    public void setProductInvalidReasonMissingRateOffsets(ProductRateOffset offset, Product p) {
        if (offset.getProduct().equals(p)) {
            p.setInvalidReason(InvalidReason.MISSING_RATE_OFFSETS);
        }
    }

    protected int expectedNumberOfDefaultOffsets(Product product, List<ProductAccomType> allProductAccomTypes, List<AccomType> allAccomTypes, List<AgileRatesDTARange> allDTARanges) {
        if (product.isDefaultInactive()) {
            return 0;
        }

        Set<AccomClass> filteredAccomClasses;
        if (allProductAccomTypes == null) {
            filteredAccomClasses = allAccomTypes.stream().map(AccomType::getAccomClass).collect(toSet());
        } else {
            filteredAccomClasses = allProductAccomTypes.stream().filter(productAccomType -> productAccomType.getProduct().equals(product)).map(productAccomType -> productAccomType.getAccomType().getAccomClass()).collect(toSet());
        }
        int numAccomTypes = product.isRoomClassOffset() ? filteredAccomClasses.size() : 1;
        int numDTARanges = 1;

        if (product.isDtaOffset()) {
            if (AgileRatesProductTypeEnum.FENCED_AND_PACKAGED.equals(AgileRatesProductTypeEnum.fromValue(product.getType())) ||
                    AgileRatesProductTypeEnum.FENCED_AND_NO_PACKAGE.equals(AgileRatesProductTypeEnum.fromValue(product.getType()))) {
                numDTARanges = allDTARanges.stream()
                        .filter(range -> range.getMinDaysToArrival() <= (product.getMaxDTA() == null ? Integer.MAX_VALUE : product.getMaxDTA()) &&
                                (range.getMaxDaysToArrival() == null ? Integer.MAX_VALUE : range.getMaxDaysToArrival()) >= product.getMinDTA())
                        .collect(Collectors.toList())
                        .size();
            } else {
                numDTARanges = allDTARanges.size();
            }
        }

        return numAccomTypes * numDTARanges;
    }

    public void invalidateProductAccomTypes(List<Product> allProducts, List<ProductAccomType> allProductAccomTypes) {
        Set<AccomType> allAccomTypes = new HashSet<>(findAllRoomTypes());

        Product barProduct = allProducts.stream().filter(Product::isSystemDefault).findAny().orElse(new Product());

        invalidateProductAccomTypesHelper(allProducts, allProductAccomTypes, barProduct, allAccomTypes);
    }

    private void invalidateProductAccomTypesHelper(List<Product> allProducts, List<ProductAccomType> allProductAccomType, Product baseProduct, Set<AccomType> baseAccomTypes) {
        //Check if current product has any Accom Types associated, if none then invalid
        if (baseAccomTypes.isEmpty()) {
            baseProduct.setInvalidReason(InvalidReason.MISSING_ACCOM_TYPES);
            return;
        }

        //Check if dependent products have only accom types in the set of the base product's accom types
        for (Product dependentProduct : allProducts) {
            if (baseProduct.getId().equals(dependentProduct.getDependentProductId())) {
                //Get accom types associated with dependant product

                Set<AccomType> dependentAccomTypes = allProductAccomType.stream()
                        .filter(productAccomType -> productAccomType.getProduct().equals(dependentProduct))
                        .map(ProductAccomType::getAccomType)
                        .collect(toSet());

                //Check if dependent accom types are within base accom types, if not then mark invalid and save
                if (!baseAccomTypes.containsAll(dependentAccomTypes)) {
                    dependentProduct.setInvalidReason(InvalidReason.INVALID_BASE_ACCOM_TYPES);
                }

                invalidateProductAccomTypesHelper(allProducts, allProductAccomType, dependentProduct, dependentAccomTypes);
            }
        }
    }

    public boolean dtaRangesWillBeRemoved(AgileRatesProductConfigurationDTO dto, List<AgileRatesDTARange> allDTARanges, LocalDate date) {
        if (!dto.isDtaOffset(date)) {
            return false;
        }
        List<AgileRatesDTARange> currentDTARanges = getCurrentDTARanges(dto);
        currentDTARanges.removeAll(getAdjustedDTARanges(dto, allDTARanges));
        return !currentDTARanges.isEmpty();
    }

    public boolean dtaRangesWillBeAdded(AgileRatesProductConfigurationDTO dto, List<AgileRatesDTARange> allDTARanges, LocalDate date) {
        if (!dto.isDtaOffset(date)) {
            return false;
        }
        List<AgileRatesDTARange> adjustedDTARanges = getAdjustedDTARanges(dto, allDTARanges);
        adjustedDTARanges.removeAll(getCurrentDTARanges(dto));
        return !adjustedDTARanges.isEmpty();
    }

    public boolean roomClassesWillBeRemoved(AgileRatesProductConfigurationDTO dto, LocalDate date) {
        if (!dto.isRoomClassOffset(date)) {
            return false;
        }

        Set<AccomClass> currentAccomClasses = dto.getRoomTypes().stream().map(AccomType::getAccomClass).collect(toSet());
        Set<AccomClass> oldAccomClasses = dto.getDefaultOffsets().stream().map(ProductRateOffset::getAccomClass).collect(toSet());

        //add season accom classes to oldAcomClasses
        dto.getProductSeasons().stream().filter(season -> !isPastSeason(season))
                .forEach(season -> season.getSeasonRateOffsets()
                        .forEach(productRateOffset -> oldAccomClasses.add(productRateOffset.getAccomClass())));

        oldAccomClasses.removeAll(currentAccomClasses);
        oldAccomClasses.remove(null);
        return !oldAccomClasses.isEmpty();
    }

    public boolean roomClassesWillBeAdded(AgileRatesProductConfigurationDTO dto, LocalDate date) {
        if (!dto.isRoomClassOffset(date)) {
            return false;
        }

        Set<AccomClass> currentAccomClasses = dto.getRoomTypes().stream().map(AccomType::getAccomClass).collect(toSet());
        Set<AccomClass> oldAccomClasses = dto.getDefaultOffsets().stream().map(ProductRateOffset::getAccomClass).collect(toSet());

        //add season accom classes to oldAcomClasses
        dto.getProductSeasons()
                .forEach(season -> season.getSeasonRateOffsets()
                        .forEach(productRateOffset -> oldAccomClasses.add(productRateOffset.getAccomClass())));

        currentAccomClasses.removeAll(oldAccomClasses);
        currentAccomClasses.remove(null);
        return !currentAccomClasses.isEmpty();
    }

    public List<AgileRatesDTARange> getAdjustedDTARanges(AgileRatesProductConfigurationDTO dto, List<AgileRatesDTARange> allDTARanges) {
        return allDTARanges.stream()
                .filter(range -> dtaRangeWithinAdvancedBooking(
                        dto.getMinimumDaysAdvancedBooking(),
                        dto.getMaximumDaysAdvancedBooking(),
                        range.getMinDaysToArrival(),
                        range.getMaxDaysToArrival()))
                .collect(Collectors.toList());
    }

    public List<AgileRatesDTARange> getAdjustedDTARanges(Product product, List<AgileRatesDTARange> allDTARanges) {
        return allDTARanges.stream()
                .filter(range -> dtaRangeWithinAdvancedBooking(
                        product.getMinDTA(),
                        product.getMaxDTA(),
                        range.getMinDaysToArrival(),
                        range.getMaxDaysToArrival()))
                .collect(Collectors.toList());
    }

    public boolean dtaRangeWithinAdvancedBooking(
            Integer minAdvancedBooking,
            Integer maxAdvancedBooking,
            Integer dtaRangeMin,
            Integer dtaRangeMax) {
        minAdvancedBooking = Optional.ofNullable(minAdvancedBooking).orElse(0);
        maxAdvancedBooking = getValueOrElseIntegerMax(maxAdvancedBooking);
        dtaRangeMax = getValueOrElseIntegerMax(dtaRangeMax);
        return (dtaRangeMin <= maxAdvancedBooking && dtaRangeMax >= minAdvancedBooking);
    }

    private Integer getValueOrElseIntegerMax(Integer value) {
        return Optional.ofNullable(value).orElse(Integer.MAX_VALUE);
    }

    public List<AgileRatesDTARange> getCurrentDTARanges(AgileRatesProductConfigurationDTO dto) {

        //get a list of all offsets to check current dtaRanges checking seasons in the case that default is inactive
        List<ProductRateOffset> allDtoOffsets = new ArrayList<>();
        allDtoOffsets.addAll(dto.getDefaultOffsets());
        allDtoOffsets.addAll(dto.getProductSeasons().stream()
                .flatMap(season -> season.getSeasonRateOffsets().stream())
                .collect(Collectors.toList()));

        //use an ordered set to sort and make distinct
        Set<AgileRatesDTARange> currentDTARanges = new TreeSet<>(Comparator.comparing(AgileRatesDTARange::getMinDaysToArrival));

        currentDTARanges.addAll(allDtoOffsets.stream()
                .map(ProductRateOffset::getAgileRatesDTARange)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        return new ArrayList<>(currentDTARanges);
    }

    public void updateOffsetsAndOverrides(AgileRatesProductConfigurationDTO dto, List<AgileRatesDTARange> allDTARanges) {
        Product product = dto.getProduct();
        LocalDate caughtUpLocalDate = dateService.getCaughtUpLocalDate();
        if (dto.isDtaOffset(caughtUpLocalDate)) {
            removeDTARelatedOffsets(dto);
        }

        if (dto.getOptimizedChangedStatus() || dto.isAdjustmentTypeChangedStatus() || dto.isGroupProduct()) {
            resetOffsets(dto);
            if (dto.isPersistedProduct()) {
                deleteAllExistingOffsetsForProduct(dto.getProduct());
                dto.setProductSeasons(new HashSet<>());
                setProductRateOffsetOverridesToInactiveOnOptimizedToggleOrOffsetChange(product);
                deleteNewFutureDecisionsByProduct(product, caughtUpLocalDate);
            }
        } else if (dto.isPersistedProduct() && dto.isSendAdjustmentByChangedStatus()) {
            deleteNewFutureDecisionsByProduct(product, caughtUpLocalDate);
        } else if (product.isDefaultInactive()) {
            resetOffsets(dto);
            if (dto.isPersistedProduct()) {
                deleteAllOffsetsExceptSeasonalForProduct(dto.getProduct());
            }
        } else {
            dto.setDefaultOffsets(createOffsetList(dto, null, dto.getDefaultOffsets(), allDTARanges));
        }

        updateOffsetsAndOverridesIfRoomClassOrTypeChanged(dto, product, caughtUpLocalDate);

        updateOverridesIfIsOverridableChanged(dto, product, caughtUpLocalDate);

        dto.getProductSeasons().stream()
                .filter(season -> !isPastSeason(season))
                .forEach(season -> season.setSeasonRateOffsets(createOffsetList(dto, season, season.getSeasonRateOffsets(), allDTARanges)));
    }

    public void updateOffsetsAndOverridesIfRoomClassOrTypeChanged(AgileRatesProductConfigurationDTO dto, Product product, LocalDate caughtUpLocalDate) {
        if (dto.isPersistedProduct() && isNotEmpty(dto.getRoomTypesRemovedList())) {
            List<Integer> accomTypeIDs = dto.getRoomTypesRemovedList().stream().map(AccomType::getId).collect(Collectors.toList());
            deleteFutureProductDependentNonBaseDecisionEntities(product, caughtUpLocalDate, accomTypeIDs);
            if (cpRecommendationService.isIHotelierEnabledAndProductSendByAdjustment(product)) {
                deleteDecisionDailyBarNonHiltonCRSByProduct(product, caughtUpLocalDate, accomTypeIDs);
            }
        }

        if (dto.isPersistedProduct() && isNotEmpty(dto.getRoomClassesRemovedList())) {
            List<Integer> accomClassIds = dto.getRoomClassesRemovedList().stream().map(AccomClass::getId).collect(Collectors.toList());
            deleteProductRateOffsetOverrides(product, caughtUpLocalDate, accomClassIds);
        }
    }

    private void updateOverridesIfIsOverridableChanged(AgileRatesProductConfigurationDTO dto, Product product, LocalDate caughtUpLocalDate) {
        if (dto.getProduct().getIsOverridable().equals(OverridableProductEnum.NO_OVERRIDES) && dto.getProduct().isPersisted()) {
            List<Integer> accomClassIds = dto.getRoomTypes().stream().map(at -> at.getAccomClass().getId()).collect(Collectors.toList());
            deleteProductRateOffsetOverrides(product, caughtUpLocalDate, accomClassIds);
        }
    }

    private void resetOffsets(AgileRatesProductConfigurationDTO dto) {
        dto.setDefaultOffsets(Collections.emptyList());
        Product product = dto.getProduct();
        product.setDowOffset(false);
        product.setRoomClassOffset(false);
        product.setDtaOffset(false);
        if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_AGILE_RATES_ROOM_CLASS)) {
            product.setRoomClassOffset(product.isOptimized());
        }
    }

    public boolean hasRoomClassProducts() {
        return findAllProductRateOffsets().stream()
                .anyMatch(pro -> pro.getAccomClass() != null);
    }

    public void changedRoomClassConfig(LocalDate systemDate, Set<AccomClass> originalAccomClasses, Set<AccomClass> changedAccomClasses) {
        handleProductOverridesForRoomConfigChanges(systemDate, originalAccomClasses, changedAccomClasses);
        changedRoomClassConfig(systemDate);
    }

    /*  While deleting overrides we want to ensure following:
     1. Override is deleted if product-class association is gone
     2. If optimization is at property level:
         - Either all room classes have overrides or
         - No room class has override
     */
    @VisibleForTesting
	public
    void handleProductOverridesForRoomConfigChanges(LocalDate systemDate, Set<AccomClass> originalAccomClasses, Set<AccomClass> changedAccomClasses) {

        final Set<Product> linkedAndSmallGroupProducts = getLinkedAndSmallGroupProducts();

        if (CollectionUtils.isEmpty(linkedAndSmallGroupProducts))
            return;

        final List<ProductRateOffsetOverride> allActiveFutureOverrides = findAllActiveFutureProductRateOffsetOverrides(systemDate)
                .stream().filter(override -> linkedAndSmallGroupProducts.contains(override.getProduct())).collect(Collectors.toList());

        deleteOverridesForDeletedAccomClasses(originalAccomClasses, changedAccomClasses, allActiveFutureOverrides);

        final List<ProductAccomType> productAccomTypes = findProductRoomTypeByProducts(linkedAndSmallGroupProducts);

        linkedAndSmallGroupProducts.forEach(product -> {

            List<ProductRateOffsetOverride> overridesForCurrentProduct = allActiveFutureOverrides.stream()
                    .filter(override -> override.getProduct().equals(product)).collect(Collectors.toList());

            List<ProductAccomType> productAccomTypesForCurrentProduct = productAccomTypes.stream()
                    .filter(productAccomType -> productAccomType.getProduct().equals(product)).collect(Collectors.toList());

            deleteOverridesForCurrentProduct(overridesForCurrentProduct, productAccomTypesForCurrentProduct);

        });
    }

    private void deleteOverridesForCurrentProduct(List<ProductRateOffsetOverride> overridesForCurrentProduct, List<ProductAccomType> productAccomTypesForCurrentProduct) {
        if (overridesForCurrentProduct.isEmpty()) {
            return;
        }

        Set<AccomClass> accomClassesAssociatedWithCurrentProduct = productAccomTypesForCurrentProduct.stream()
                .map(pat -> pat.getAccomType().getAccomClass()).collect(Collectors.toSet());

        List<ProductRateOffsetOverride> overridesForCurrentProductWithNonAssociatedClasses =
                overridesForCurrentProduct.stream()
                        .filter(override -> !accomClassesAssociatedWithCurrentProduct.contains(override.getAccomClass()))
                        .collect(Collectors.toList());

        if (!overridesForCurrentProductWithNonAssociatedClasses.isEmpty()) {
            softDeleteAllFutureOffsetOverrides(overridesForCurrentProductWithNonAssociatedClasses);
            overridesForCurrentProduct.removeAll(overridesForCurrentProductWithNonAssociatedClasses);
        }

        // we need to soft-delete all product overrides if optimization is at property level and not all classes have overrides present
        if (getOptimizationLevel() == OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES &&
                differentOverridesFound(overridesForCurrentProduct, accomClassesAssociatedWithCurrentProduct)) {
            softDeleteAllFutureOffsetOverrides(overridesForCurrentProduct);
        }
    }

    private boolean differentOverridesFound(List<ProductRateOffsetOverride> overridesForCurrentProduct,
                                            Set<AccomClass> accomClassesAssociatedWithCurrentProduct) {

        // group by date, since we want to check count mismatch for a given date
        Map<LocalDate, List<ProductRateOffsetOverride>> productOverridesGroupedByDate =
                overridesForCurrentProduct.stream().collect(Collectors.groupingBy(ProductRateOffsetOverride::getOccupancyDate));

        for (LocalDate occupancyDate : productOverridesGroupedByDate.keySet()) {
            List<ProductRateOffsetOverride> productOverridesForASingleDate = productOverridesGroupedByDate.get(occupancyDate);
            Map<AccomClass, List<ProductRateOffsetOverride>> overridesForCurrentProductAndSingleDateGroupByAccomClass =
                    productOverridesForASingleDate.stream().collect(Collectors.groupingBy(ProductRateOffsetOverride::getAccomClass));
            // this would happen when a class was not associated with current product and now gets associated with it due to new associated accom type added
            if (!overridesForCurrentProductAndSingleDateGroupByAccomClass.keySet().equals(accomClassesAssociatedWithCurrentProduct)) {
                return true;
            }
            Set<Integer> numberOfDifferentOverrideCountsAcrossClasses = overridesForCurrentProductAndSingleDateGroupByAccomClass.values().stream()
                    .map(List::size).collect(toSet());
            // this checks if for any occupancy date, currently present overrides for all classes are not same
            if (numberOfDifferentOverrideCountsAcrossClasses.size() > 1) {
                return true;
            }
        }
        return false;
    }

    private void deleteOverridesForDeletedAccomClasses(Set<AccomClass> originalAccomClasses, Set<AccomClass> changedAccomClasses,
                                                       List<ProductRateOffsetOverride> allActiveFutureOverrides) {
        Set<AccomClass> deletedAccomClasses = getDeletedAccomClasses(originalAccomClasses, changedAccomClasses);
        if (!deletedAccomClasses.isEmpty()) {
            List<ProductRateOffsetOverride> overridesToBeDeleted = allActiveFutureOverrides.stream()
                    .filter(override -> deletedAccomClasses.contains(override.getAccomClass()))
                    .collect(Collectors.toList());
            softDeleteAllFutureOffsetOverrides(overridesToBeDeleted);
            allActiveFutureOverrides.removeAll(overridesToBeDeleted);
        }
    }

    @VisibleForTesting
	public
    Set<Product> getLinkedAndSmallGroupProducts() {
        return findAllProducts().stream().filter(p -> p.isAgileRatesProduct() || p.isGroupProduct()).collect(toSet());
    }

    @VisibleForTesting
	public
    Set<AccomClass> getDeletedAccomClasses(Set<AccomClass> originalAccomClasses, Set<AccomClass> changedAccomClasses) {
        return originalAccomClasses.stream().filter(accomClass -> !changedAccomClasses.contains(accomClass)).collect(Collectors.toSet());
    }

    public void changedRoomClassConfig(LocalDate systemDate) {
        cleanUpOffsets(systemDate);
        validateLinkedProducts();
        registerSyncEvent();
    }

    public void cleanUpOffsets(LocalDate systemDate) {
        List<AccomClass> accomClasses = findAllProductAccomType().stream()
                .map(pat -> pat.getAccomType().getAccomClass())
                .distinct()
                .collect(Collectors.toList());
        List<ProductRateOffset> productRateOffsetsToDelete = findAllProductRateOffsets()
                .stream()
                .filter(pro -> !accomClasses.contains(pro.getAccomClass()) && pro.getAccomClass() != null)
                .collect(Collectors.toList());

        //soft deleting overrides >= to system date
        List<ProductRateOffsetOverride> productRateOffsetOverrides = findAllProductRateOffsetOverrides()
                .stream()
                .filter(proo -> !accomClasses.contains(proo.getAccomClass()) && proo.getAccomClass() != null && proo.getOccupancyDate().compareTo(systemDate) >= 0)
                .collect(Collectors.toList());

        productRateOffsetOverrides.forEach(proo -> proo.setStatusId(TenantStatusEnum.DELETED.getId()));

        tenantCrudService.save(productRateOffsetOverrides);
        tenantCrudService.delete(productRateOffsetsToDelete);
    }

    private void removeDTARelatedOffsets(AgileRatesProductConfigurationDTO dto) {
        Integer minAdvancedBooking = Optional.ofNullable(dto.getMinimumDaysAdvancedBooking()).orElse(0);
        Integer maxAdvancedBooking = getValueOrElseIntegerMax(dto.getMaximumDaysAdvancedBooking());
        List<AgileRatesDTARange> dtaRangesToBeRemoved =
                getCurrentDTARanges(dto).stream()
                        .filter(range -> minAdvancedBooking > getValueOrElseIntegerMax(range.getMaxDaysToArrival()) || maxAdvancedBooking < range.getMinDaysToArrival())
                        .collect(Collectors.toList());


        if (isNotEmpty(dto.getDefaultOffsets())) {
            List<ProductRateOffset> productRateOffsets = new ArrayList<>(dto.getDefaultOffsets());
            List<ProductRateOffset> offsetsToRemove = dtaRangesToBeRemoved.stream()
                    .flatMap(dtaRange -> dto.getDefaultOffsets().stream().filter(offset -> dtaRange.equals(offset.getAgileRatesDTARange())))
                    .collect(Collectors.toList());
            productRateOffsets.removeAll(offsetsToRemove);
            dto.setDefaultOffsets(productRateOffsets);
        }

        if (isNotEmpty(dto.getProductSeasons())) {
            for (AgileRatesSeason season : dto.getProductSeasons()) {
                List<ProductRateOffset> productSeasonRateOffsets = new ArrayList<>(season.getSeasonRateOffsets());
                List<ProductRateOffset> seasonOffsetsToRemove = productSeasonRateOffsets.stream().filter(productRateOffset -> dtaRangesToBeRemoved.contains(productRateOffset.getAgileRatesDTARange())).collect(Collectors.toList());
                productSeasonRateOffsets.removeAll(seasonOffsetsToRemove);
                season.setSeasonRateOffsets(productSeasonRateOffsets);
            }
        }
    }

    public List<ProductRateOffset> createOffsetList(AgileRatesProductConfigurationDTO dto, AgileRatesSeason season, Collection<ProductRateOffset> productRateOffsets, List<AgileRatesDTARange> allDTARanges) {
        Product product = dto.getProduct();
        List<ProductRateOffset> newOffsets = new ArrayList<>();
        List<ProductRateOffset> offsets = new ArrayList<>();
        boolean isDtaOffset = season != null ? season.isDtaOffset() : product.isDtaOffset();
        boolean isRoomClassOffset = season != null ? season.isRoomClassOffset() : product.isRoomClassOffset();

        if (isNotEmpty(productRateOffsets)) {
            offsets.addAll(productRateOffsets);
        }

        offsets.forEach(offset -> offset.setOffsetMethod(dto.getOffsetMethod()));

        Set<AccomClass> filteredAccomClasses = dto.getRoomTypes().stream().map(AccomType::getAccomClass).collect(toSet());

        //Check if default inactive
        if (season == null && dto.getProduct().isDefaultInactive()) {
            return newOffsets;
        }

        //There are 8 cases of offsets being created
        //Room Class & DTA & New
        //Room Class & DTA & Not new
        //Not room class & DTA & New
        //Not room class & DTA & Not new
        //Room Class & Not DTA & New
        //Room Class & Not DTA & Not new
        //Not room class & Not DTA & New
        //Not room class & Not DTA & Not new

        if (isDtaOffset) {
            for (AgileRatesDTARange range : getAdjustedDTARanges(dto, allDTARanges)) {
                if (isRoomClassOffset) {
                    for (AccomClass accomClass : filteredAccomClasses) {
                        Optional<ProductRateOffset> offsetOptional = offsets.stream()
                                .filter(productRateOffset -> (productRateOffset.getAgileRatesDTARange() == null || range.equals(productRateOffset.getAgileRatesDTARange())) &&
                                        (productRateOffset.getAccomClass() == null || accomClass.equals(productRateOffset.getAccomClass())))
                                .findFirst();

                        addOffsetToList(product, season, offsetOptional, newOffsets, accomClass, range);
                    }
                } else {
                    Optional<ProductRateOffset> offsetOptional = offsets.stream()
                            .filter(productRateOffset -> range.equals(productRateOffset.getAgileRatesDTARange()) || productRateOffset.getAgileRatesDTARange() == null)
                            .findFirst();

                    addOffsetToList(product, season, offsetOptional, newOffsets, null, range);
                }
            }
        } else {
            if (isRoomClassOffset) {
                for (AccomClass accomClass : filteredAccomClasses) {
                    Optional<ProductRateOffset> offsetOptional = offsets.stream()
                            .filter(productRateOffset -> productRateOffset.getAccomClass() == null || accomClass.equals(productRateOffset.getAccomClass()))
                            .findFirst();
                    addOffsetToList(product, season, offsetOptional, newOffsets, accomClass, null);
                }
            } else {
                Optional<ProductRateOffset> offsetOptional = offsets.stream().findFirst();
                addOffsetToList(product, season, offsetOptional, newOffsets, null, null);
            }
        }

        return newOffsets;
    }

    public void setProductRateOffsetOverridesToInactiveOnOptimizedToggleOrOffsetChange(Product product) {
        softDeleteAllFutureOffsetOverrides(findProductRateOffsetOverrides(product));
    }

    private List<ProductRateOffsetOverride> findProductRateOffsetOverrides(Product product) {
        return tenantCrudService.findByNamedQuery(
                ProductRateOffsetOverride.BY_PRODUCT,
                QueryParameter.with(PRODUCT_PARAMETER, product)
                        .parameters());
    }

    private void softDeleteAllFutureOffsetOverrides(List<ProductRateOffsetOverride> productRateOffsetOverrides) {
        var caughtUpDate = dateService.getCaughtUpLocalDate();

        List<ProductRateOffsetOverride> futureOverridesToMarkDeleted = productRateOffsetOverrides.stream()
                .filter(proo -> proo.getOccupancyDate().isEqual(caughtUpDate) || proo.getOccupancyDate().isAfter(caughtUpDate)).collect(Collectors.toList());

        futureOverridesToMarkDeleted.forEach(proo -> proo.setStatusId(2));

        tenantCrudService.save(futureOverridesToMarkDeleted);

    }

    public boolean hasRemovedNonOptimizedAgileRatesOverridesSinceLastBDE() {
        Date systemDate = getLatestSystemDate();
        List<ProductRateOffsetOverride> productRateOffsetOverrides = tenantCrudService.findByNamedQuery(ProductRateOffsetOverride.NON_OPTIMIZED_REMOVED_AFTER_DATE,
                QueryParameter.with("systemDate", systemDate).parameters());

        return isNotEmpty(productRateOffsetOverrides);
    }

    public Date getLatestSystemDate() {
        FileMetadata latestFileMetadata = (FileMetadata) tenantCrudService.findByNamedQuery(FileMetadata.GET_LATEST_PROCESSED_SNAPSHOT, 1).get(0);

        LocalDateTime snapshotTime = new LocalDateTime(latestFileMetadata.getSnapshotTm());
        LocalDateTime snapshotDateTime = new LocalDateTime(latestFileMetadata.getSnapshotDt());
        snapshotDateTime = snapshotDateTime.plusHours(snapshotTime.getHourOfDay());
        snapshotDateTime = snapshotDateTime.plusMinutes(snapshotTime.getMinuteOfHour());
        snapshotDateTime = snapshotDateTime.plusSeconds(snapshotTime.getSecondOfMinute());
        return snapshotDateTime.toDate();
    }

    public void deleteNewFutureDecisionsByProduct(Product product, LocalDate systemDate) {
        tenantCrudService.executeUpdateByNamedQuery(
                CPDecisionBAROutput.DELETE_BY_PRODUCT,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and(PRODUCT_PARAMETER, product)
                        .and(ARRIVAL_DATE_PARAMETER, systemDate)
                        .parameters());
    }

    private void addOffsetToList(Product product, AgileRatesSeason season, Optional<ProductRateOffset> offsetOptional, List<ProductRateOffset> offsets, AccomClass accomClass, AgileRatesDTARange range) {
        if (offsetOptional.isPresent()) {
            offsetOptional.get().setAccomClass(accomClass);
            offsetOptional.get().setAgileRatesDTARange(range);
            offsets.add(offsetOptional.get());
        } else {
            offsets.add(createNewProductRateOffset(product, season, accomClass, range));
        }
    }


    private ProductRateOffset createNewProductRateOffset(Product product, AgileRatesSeason season, AccomClass roomClass, AgileRatesDTARange dta) {
        final ProductRateOffset offset = new ProductRateOffset();
        offset.setAccomClass(roomClass);
        offset.setAgileRatesDTARange(dta);
        offset.setProduct(product);
        offset.setOffsetMethod(product.getOffsetMethod());

        if (season != null) {
            offset.setSeasonName(season.getName());
            offset.setStartDate(season.getJavaStartDate() == null ? null : season.getStartDate());
            offset.setEndDate(season.getJavaEndDate() == null ? null : season.getEndDate());
        }

        return offset;
    }

    public void updateOptimizedProductsOnRoomClassParamChange(boolean roomClassParam) {
        updateOptimizedProductsOnParamChange(product -> product.setRoomClassOffset(roomClassParam), roomClassParam);
    }

    private void updateOptimizedProductsOnParamChange(Consumer<Product> productSetter, boolean roomClassParam) {
        setOptimizedProductsField(productSetter);
        if (!roomClassParam) {
            deleteOptimizedProductRateOffsets();
        }

        validateLinkedProducts();
        registerSyncEvent();

        validateSmallGroupProductsWhenChangingOptimizationLevel();
    }

    public void deleteOptimizedProductRateOffsets() {
        List<ProductRateOffset> allOptimizedProductRateOffsets = findAllProductRateOffsets()
                .stream()
                .filter(productRateOffset -> productRateOffset.getProduct().isOptimized())
                .collect(Collectors.toList());

        tenantCrudService.delete(allOptimizedProductRateOffsets);
    }

    private void setOptimizedProductsField(Consumer<Product> optimizedProductSetter) {
        List<Product> allProducts = findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts();
        List<Product> optimizedProducts = new ArrayList<>();
        for (Product product : allProducts) {
            if (product.isOptimized()) {
                optimizedProductSetter.accept(product);
                optimizedProducts.add(product);
            }
        }
        tenantCrudService.save(optimizedProducts);
    }

    public List<PricingBaseAccomType> getPricingBaseAccomTypesByProductAndAccomTypes(List<AccomType> accomTypes, int productID) {
        List<Integer> accomClassIds = accomTypes.stream()
                .map(at -> at.getAccomClass().getId())
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accomClassIds)) {
            return new ArrayList<>();
        }
        return tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASSES_AND_PRODUCT,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(ACCOM_CLASS_IDS, accomClassIds)
                        .and("productID", productID).parameters());
    }

    public OptimizationLevel getOptimizationLevel() {
        if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_AGILE_RATES_ROOM_CLASS)) {
            return OptimizationLevel.PER_ROOM_CLASS;
        }
        return OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES;
    }

    public void setOptimizationLevel(OptimizationLevel optimizationLevel) {
        configParamsService.updateParameterValue(
                FeatureTogglesConfigParamName.ANALYTICAL_AGILE_RATES_ROOM_CLASS.getParameterName(),
                OptimizationLevel.PER_ROOM_CLASS.equals(optimizationLevel));
    }

    public void saveAgileProductRestrictionAssociationForFPLOS(Integer productId,
                                                               Set<RateQualified> newAssociatedQualifiedRates) {
        //get the old associations
        List<AgileProductRestrictionAssociation> oldAssociations = getAgileProductRestrictionAssociations(productId);
        List<Integer> oldAssociatedQualifiedRateIds = getOldAssociatedQualifiedRateIds(oldAssociations);
        List<Integer> newAssociatedQualifiedRateIds = getNewAssociatedQualifiedRateIds(newAssociatedQualifiedRates);
        //find new RateQualified ids by comparing old and new associations
        Collection<Integer> newlyAddedQualifiedRateIds = CollectionUtils.subtract(newAssociatedQualifiedRateIds, oldAssociatedQualifiedRateIds);
        //find deleted RateQualified ids by comparing old and new associations
        Collection<Integer> deletedQualifiedRateIds = CollectionUtils.subtract(oldAssociatedQualifiedRateIds, newAssociatedQualifiedRateIds);
        identifyAndSaveNewAssociation(productId, newAssociatedQualifiedRates, newlyAddedQualifiedRateIds);
        identifyAndDeleteAssociation(oldAssociations, deletedQualifiedRateIds);
        markLinkedSrpsToProcessInNextProcessing(newAssociatedQualifiedRates, newlyAddedQualifiedRateIds);
    }

    private void identifyAndDeleteAssociation(List<AgileProductRestrictionAssociation> oldAssociations, Collection<Integer> deletedQualifiedRateIds) {
        List<AgileProductRestrictionAssociation> deletedAssociation = identifyAssociationForDelete(oldAssociations, deletedQualifiedRateIds);
        updateRatePlanWithOriginalManagedInG3Status(deletedQualifiedRateIds);
        tenantCrudService.delete(deletedAssociation);
    }

    private void identifyAndSaveNewAssociation(Integer productId, Set<RateQualified> newAssociatedQualifiedRates, Collection<Integer> newlyAddedQualifiedRateIds) {
        List<AgileProductRestrictionAssociation> newAssociationForSave = identifyNewAssociationForSave(productId, newAssociatedQualifiedRates, newlyAddedQualifiedRateIds);
        tenantCrudService.save(newAssociationForSave);
        activateManagedInG3ForRateQualifiedIds(newlyAddedQualifiedRateIds);
        activateYieldableForRateQualifiedIds(newlyAddedQualifiedRateIds);
    }

    private List<AgileProductRestrictionAssociation> identifyAssociationForDelete(List<AgileProductRestrictionAssociation> oldAssociations, Collection<Integer> deletedQualifiedRateIds) {
        return oldAssociations.stream()
                .filter(oldAssociation -> deletedQualifiedRateIds.contains(oldAssociation.getRateQualified().getId()))
                .collect(Collectors.toList());
    }

    private void updateRatePlanWithOriginalManagedInG3Status(Collection<Integer> deletedQualifiedRateIds) {
        if (isNotEmpty(deletedQualifiedRateIds)) {
            tenantCrudService.executeUpdateByNamedQuery(AgileProductRestrictionAssociation.UPDATE_ORIGINAL_MANAGED_IN_G3_AND_YIELDABLE_TO_RATE_QUALIFIED,
                    QueryParameter.with("rateQualifiedIds", deletedQualifiedRateIds).parameters());
        }
    }

    private void activateManagedInG3ForRateQualifiedIds(Collection<Integer> newlyAddedQualifiedRateIds) {
        if (isNotEmpty(newlyAddedQualifiedRateIds)) {
            tenantCrudService.executeUpdateByNamedQuery(RateQualified.SET_G3_MANAGED_BY_RATE_QUALIFIED_ID,
                    QueryParameter.with("managedInG3", !considerRestrictionSeasonDatesForAgileQualifiedFPLOS())
                            .and("rateQualifiedIds", newlyAddedQualifiedRateIds).parameters());
        }
    }

    private void activateYieldableForRateQualifiedIds(Collection<Integer> newlyAddedQualifiedRateIds) {
        if (isNotEmpty(newlyAddedQualifiedRateIds)) {
            tenantCrudService.executeUpdateByNamedQuery(RateQualified.SET_YIELDABLE_BY_RATE_QUALIFIED_ID,
                    QueryParameter.with("yieldable", 1)
                            .and("rateQualifiedIds", newlyAddedQualifiedRateIds).parameters());
        }
    }

    private List<AgileProductRestrictionAssociation> identifyNewAssociationForSave(Integer productId, Set<RateQualified> newAssociatedQualifiedRates, Collection<Integer> newlyAddedQualifiedRateIds) {
        Product product = tenantCrudService.find(Product.class, productId);
        return newAssociatedQualifiedRates.stream()
                .filter(associatedQualifiedRate -> newlyAddedQualifiedRateIds.contains(associatedQualifiedRate.getId()))
                .map(associatedQualifiedRate -> newAgileProductRestrictionAssociation(product, associatedQualifiedRate))
                .collect(Collectors.toList());
    }

    private List<Integer> getNewAssociatedQualifiedRateIds(Set<RateQualified> newAssociatedQualifiedRates) {
        return newAssociatedQualifiedRates.stream()
                .map(RateQualified::getId)
                .collect(Collectors.toList());
    }

    private List<Integer> getOldAssociatedQualifiedRateIds(List<AgileProductRestrictionAssociation> oldAssociations) {
        return oldAssociations.stream()
                .map(oldAssociation -> oldAssociation.getRateQualified().getId())
                .collect(Collectors.toList());
    }

    private AgileProductRestrictionAssociation newAgileProductRestrictionAssociation(Product product,
                                                                                     RateQualified associatedQualifiedRate) {
        AgileProductRestrictionAssociation agileProductRestrictionAssociation = new AgileProductRestrictionAssociation();
        agileProductRestrictionAssociation.setProduct(product);
        agileProductRestrictionAssociation.setRateQualified(associatedQualifiedRate);
        agileProductRestrictionAssociation.setManagedInG3(associatedQualifiedRate.getManagedInG3());
        agileProductRestrictionAssociation.setYieldable(associatedQualifiedRate.getYieldable());
        return agileProductRestrictionAssociation;
    }

    public List<AgileProductRestrictionAssociation> getAgileProductRestrictionAssociations(Integer productId) {
        return tenantCrudService.findByNamedQuery(AgileProductRestrictionAssociation.GET_BY_PRODUCT_ID,
                QueryParameter.with(PRODUCT_ID, productId).parameters());
    }

    public List<AgileProductRestrictionAssociation> getAgileProductRestrictionAssociationsForRateQualifiedIds(List<Integer> rateQualifiedIds) {
        return tenantCrudService.findByNamedQuery(AgileProductRestrictionAssociation.GET_BY_RATE_QUALIFIED_IDS,
                QueryParameter.with(RATE_QUALIFIED_IDS, rateQualifiedIds).parameters());
    }

    public List<RateQualified> getAvailableRateQualifiedForProduct(Integer productId) {
        return tenantCrudService.findByNamedQuery(AgileProductRestrictionAssociation.GET_AVAILABLE_RATE_PLANS_FOR_PRODUCT,
                QueryParameter.with(PRODUCT_ID, productId).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AgileProductRestrictionAssociation> getAllProductRestrictionAssociations() {
        return tenantCrudService.findByNamedQuery(AgileProductRestrictionAssociation.GET_ALL);
    }

    public List<AgileProductRestrictionAssociation> getLinkedProductsAndSmallGroupProductsRestrictionAssociations() {
        return tenantCrudService.findByNamedQuery(AgileProductRestrictionAssociation.GET_RESTRICTIONS_FOR_LINKED_PRODUCTS_AND_SMALL_GROUP_PRODUCTS);
    }

    public void saveYieldabilityOfRatePlan(List<AgileProductRestrictionAssociation> agileProductAssociatedRatePlans) {
        List<RateQualified> qualifiedRatePlans = agileProductAssociatedRatePlans.stream()
                .map(AgileProductRestrictionAssociation::getRateQualified)
                .collect(Collectors.toList());
        tenantCrudService.save(qualifiedRatePlans);
    }

    public List<ProductHierarchy> getProductHierarchies() {
        return tenantCrudService.findAll(ProductHierarchy.class);
    }

    public List<ProductHierarchy> getAllHierarchiesForProduct(Product product) {
        return getProductHierarchies().stream().
                filter(p -> p.getToProduct().equals(product) ||
                        p.getFromProduct().equals(product))
                .collect(Collectors.toList());
    }

    public void removeHierarchiesForProduct(Product product) {
        List<ProductHierarchy> hierarchiesForProduct = getAllHierarchiesForProduct(product);
        if (!hierarchiesForProduct.isEmpty()) {
            saveAgileRatesProductHierarchies(Collections.emptyList(), hierarchiesForProduct);
        }
    }

    public void saveAgileRatesProductHierarchies(List<ProductHierarchy> listToBeSaved, List<ProductHierarchy> listToBeDeleted) {
        if (isNotEmpty(listToBeSaved)) {
            createProductHierarchies(listToBeSaved, listToBeDeleted);
            tenantCrudService.save(listToBeSaved);
        }
        if (isNotEmpty(listToBeDeleted)) {
            tenantCrudService.delete(listToBeDeleted);
        }
        if (isNotEmpty(listToBeSaved) || isNotEmpty(listToBeDeleted)) {
            validateLinkedProducts();
            registerSyncEvent();
        }
    }

    private void createProductHierarchies(List<ProductHierarchy> listToBeSaved, List<ProductHierarchy> listToBeDeleted) {
        Map<Integer, AccomClass> accomClassMap = accommodationService.getAllActiveAccomClasses().stream().collect(Collectors.toMap(AccomClass::getId, Function.identity()));
        List<ProductMinPriceDiff> productMinPriceDiffsListToDelete = new ArrayList<>();
        for (ProductHierarchy productHierarchy : listToBeSaved) {
            List<ProductMinPriceDiff> productMinPriceDiffsList = new ArrayList<>();
            Product fromProduct = productHierarchy.getFromProduct();
            Product toProduct = productHierarchy.getToProduct();
            List<Integer> accomClassIdsForFromProduct = demandDetailsService.getAccomClassIdsForProduct(fromProduct.getId());
            List<Integer> accomClassIdsForToProduct = demandDetailsService.getAccomClassIdsForProduct(toProduct.getId());
            accomClassIdsForFromProduct.retainAll(accomClassIdsForToProduct);
            if (CollectionUtils.isNotEmpty(accomClassIdsForFromProduct)) {
                for (Integer accomClassId : accomClassIdsForFromProduct) {
                    buildProductHierarchy(accomClassMap, productMinPriceDiffsList, productHierarchy, accomClassId);
                }
            } else {
                listToBeDeleted.add(productHierarchy);
            }

            List<ProductMinPriceDiff> accomClassesNoLongerValidForHierarchyRelation = productHierarchy.getProductMinPriceDiffList().stream()
                    .filter(h -> !h.isUpdated())
                    .collect(Collectors.toList());

            productHierarchy.setProductMinPriceDiffList(productMinPriceDiffsList);

            productMinPriceDiffsListToDelete.addAll(accomClassesNoLongerValidForHierarchyRelation);
        }
        tenantCrudService.delete(productMinPriceDiffsListToDelete);
    }

    private void buildProductHierarchy(Map<Integer, AccomClass> accomClassMap,
                                       List<ProductMinPriceDiff> productMinPriceDiffsList,
                                       ProductHierarchy productHierarchy, Integer accomClassId) {
        Optional<ProductMinPriceDiff> minPriceDiff = productHierarchy.getProductMinPriceDiffList().stream()
                .filter(min -> accomClassId.equals(min.getAccomClass().getId())).findFirst();
        ProductMinPriceDiff productMinPriceDiff = new ProductMinPriceDiff();
        if (minPriceDiff.isPresent()) {
            productMinPriceDiff = minPriceDiff.get();
        } else {
            productMinPriceDiff = new ProductMinPriceDiff();
        }
        productMinPriceDiff.setProductHierarchy(productHierarchy);
        BigDecimal minimumDifference = productHierarchy.getMinimumDifference();
        productMinPriceDiff.setSundayDiffWithTax(minimumDifference);
        productMinPriceDiff.setMondayDiffWithTax(minimumDifference);
        productMinPriceDiff.setTuesdayDiffWithTax(minimumDifference);
        productMinPriceDiff.setWednesdayDiffWithTax(minimumDifference);
        productMinPriceDiff.setThursdayDiffWithTax(minimumDifference);
        productMinPriceDiff.setFridayDiffWithTax(minimumDifference);
        productMinPriceDiff.setSaturdayDiffWithTax(minimumDifference);
        productMinPriceDiff.setAccomClass(accomClassMap.get(accomClassId));
        productMinPriceDiff.setProductHierarchy(productHierarchy);
        productMinPriceDiff.setUpdated(true);
        productMinPriceDiffsList.add(productMinPriceDiff);
    }

    private void deleteProductHierarchies(List<ProductHierarchy> listToBeDeleted) {
        List<Integer> hierarchyListIds = listToBeDeleted.stream().map(ProductHierarchy::getId).collect(Collectors.toList());
        tenantCrudService.executeUpdateByNativeQuery("delete from Product_MinDiff where Product_Hierarchy_ID in (:hierarchyListIds)",
                QueryParameter.with("hierarchyListIds", hierarchyListIds).parameters());
    }

    public List<ProductGroup> getProductGroups() {
        return tenantCrudService.findAll(ProductGroup.class);
    }

    public List<AgileRatesProductGroup> getAgileRatesProductGroups() {
        return tenantCrudService.findAll(AgileRatesProductGroup.class);
    }

    public void saveProductGroups(List<AgileRatesProductGroup> groupsToBeSaved, List<ProductGroup> productsToBeSaved) {
        // add in AgileRatesProductGroups then related ProductGroups
        if (isNotEmpty(groupsToBeSaved)) {
            tenantCrudService.save(groupsToBeSaved);
        }
        if (isNotEmpty(productsToBeSaved)) {
            tenantCrudService.save(productsToBeSaved);
        }

        if (isNotEmpty(groupsToBeSaved) || isNotEmpty(productsToBeSaved)) {
            registerSyncEvent();
        }
    }

    public void deleteProductGroups(List<AgileRatesProductGroup> groupsToBeDeleted, List<ProductGroup> productsToBeDeleted) {
        // delete related ProductGroups then delete AgileRatesProductGroups
        if (isNotEmpty(productsToBeDeleted)) {
            tenantCrudService.delete(productsToBeDeleted);
        }
        if (isNotEmpty(groupsToBeDeleted)) {
            tenantCrudService.delete(groupsToBeDeleted);
        }

        if (isNotEmpty(groupsToBeDeleted) || isNotEmpty(productsToBeDeleted)) {
            registerSyncEvent();
        }
    }

    public List<ProductRateOffset> getProductRateOffset() {
        return tenantCrudService.findAll(ProductRateOffset.class);
    }

    public List<ProductAccomType> findProductRoomTypeByProducts(Set<Product> products) {
        return tenantCrudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS, QueryParameter.with("products", products).parameters());
    }

    public List<ProductAccomType> findProductRoomTypeByProductsWithAccomTypes(Set<Product> products) {
        return tenantCrudService.findByNamedQuery(ProductAccomType.BY_PRODUCTS_WITH_ACCOM_TYPES, QueryParameter.with("products", products).parameters());
    }

    public List<ProductRateOffset> getProductRateOffsetsFutureSeasons(LocalDate systemDate) {
        return tenantCrudService.findByNamedQuery(ProductRateOffset.FIND_FUTURE_SEASONS, QueryParameter.with("systemDate", systemDate).parameters());
    }

    public List<ProductRateOffset> getProductRateOffsetForProduct(Product product) {
        return tenantCrudService.findByNamedQuery(ProductRateOffset.BY_PRODUCT, QueryParameter.with("product", product).parameters());
    }

    public List<ProductRateOffset> findProductRateOffsetFor(Set<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return new ArrayList<>();
        }
        return tenantCrudService.findByNamedQuery(ProductRateOffset.BY_PRODUCTS, QueryParameter.with("products", products).parameters());
    }

    public boolean isProductGroupValid(List<Product> products, List<ProductRateOffset> productRateOffsets) {
        if (products.size() < 2) {
            // minimum of 2 products required in product group
            return false;
        }

        List<List<ProductRateOffset>> firstOffsetsBySeason = new ArrayList<>();
        List<List<ProductRateOffset>> secondOffsetsBySeason = new ArrayList<>();

        for (int i = 0; i < products.size() - 1; i++) {
            for (int j = 1; j < products.size(); j++) {
                int finalI = i;
                List<ProductRateOffset> firstProductOffsets = productRateOffsets.stream()
                        .filter(offset -> offset.getProduct().equals(products.get(finalI)))
                        .collect(Collectors.toList());
                int finalJ = j;
                List<ProductRateOffset> secondProductOffsets = productRateOffsets.stream()
                        .filter(offset -> offset.getProduct().equals(products.get(finalJ)))
                        .collect(Collectors.toList());

                if (isSeasonalProduct(firstProductOffsets)) {
                    firstOffsetsBySeason = separateOffsetsBySeasons(firstProductOffsets);
                } else {
                    firstProductOffsets.removeIf(offset -> offset.getStartDate() != null);
                }
                if (isSeasonalProduct(secondProductOffsets)) {
                    secondOffsetsBySeason = separateOffsetsBySeasons(secondProductOffsets);
                } else {
                    secondProductOffsets.removeIf(offset -> offset.getStartDate() != null);
                }
                if (!productsHaveOverlappingRange(firstProductOffsets, secondProductOffsets, firstOffsetsBySeason, secondOffsetsBySeason)) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean productsHaveOverlappingRange(List<ProductRateOffset> firstOffsetList, List<ProductRateOffset> secondOffsetList,
                                                List<List<ProductRateOffset>> firstOffsetsBySeason, List<List<ProductRateOffset>> secondOffsetsBySeason) {
        if (firstOffsetsBySeason.isEmpty() && secondOffsetsBySeason.isEmpty()) { // both are nonseasonal products
            return doesOverlapExistByOffsetType(firstOffsetList, secondOffsetList);
        } else if (!firstOffsetsBySeason.isEmpty() && !secondOffsetsBySeason.isEmpty()) {
            for (List<ProductRateOffset> firstSeason : firstOffsetsBySeason) { // both are seasonal products
                for (List<ProductRateOffset> secondSeason : secondOffsetsBySeason) {
                    if (!doesOverlapExistByOffsetType(firstSeason, secondSeason)) {
                        return false;
                    }
                }
            }
        } else {
            return seasonalProductsHaveOverlappingRange(firstOffsetsBySeason, secondOffsetsBySeason, firstOffsetList, secondOffsetList);
        }
        return true;
    }

    public boolean seasonalProductsHaveOverlappingRange(List<List<ProductRateOffset>> firstOffsetsBySeason, List<List<ProductRateOffset>> secondOffsetsBySeason,
                                                        List<ProductRateOffset> firstOffsetList, List<ProductRateOffset> secondOffsetList) {

        if (firstOffsetsBySeason.isEmpty()) { // only second product is seasonal
            for (List<ProductRateOffset> secondSeason : secondOffsetsBySeason) {
                if (!doesOverlapExistByOffsetType(firstOffsetList, secondSeason)) {
                    return false;
                }
            }
        } else { // only first product is seasonal
            for (List<ProductRateOffset> firstSeason : firstOffsetsBySeason) {
                if (!doesOverlapExistByOffsetType(firstSeason, secondOffsetList)) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean doesOverlapExistByOffsetType(List<ProductRateOffset> firstOffsetList, List<ProductRateOffset> secondOffsetList) {
        // clean up the offsets here
        firstOffsetList = getNonOverlappingOffsets(firstOffsetList, secondOffsetList);
        secondOffsetList = getNonOverlappingOffsets(secondOffsetList, firstOffsetList);
        if (firstOffsetList.isEmpty() || secondOffsetList.isEmpty()) {
            return false;
        }

        return compareOffsetsByType(firstOffsetList, secondOffsetList);
    }

    public boolean compareOffsetsByType(List<ProductRateOffset> firstOffsetList, List<ProductRateOffset> secondOffsetList) {
        if (firstOffsetList.get(0).getAgileRatesDTARange() != null && secondOffsetList.get(0).getAgileRatesDTARange() != null) {
            return areOffsetsInRangeForDTAOffsets(firstOffsetList, secondOffsetList,
                    firstOffsetList.get(0).getAccomClass() != null && secondOffsetList.get(0).getAccomClass() != null);
        } else if (firstOffsetList.get(0).getAccomClass() != null && secondOffsetList.get(0).getAccomClass() != null) {
            return areOffsetsInRangeForRcOffsets(firstOffsetList, secondOffsetList);
        } else {
            for (ProductRateOffset firstOffset : firstOffsetList) {
                for (ProductRateOffset secondOffset : secondOffsetList) {
                    if (!offsetsHaveOverlappingRange(firstOffset, secondOffset)) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    public boolean areOffsetsInRangeForDTAOffsets(List<ProductRateOffset> firstOffsetList, List<ProductRateOffset> secondOffsetList, boolean isRcOffset) {
        if (isRcOffset) {
            for (ProductRateOffset firstOffset : firstOffsetList) {
                ProductRateOffset secondOffset = secondOffsetList
                        .stream()
                        .filter(o -> o.getAgileRatesDTARange().equals(firstOffset.getAgileRatesDTARange()))
                        .filter(o -> o.getAccomClass().equals(firstOffset.getAccomClass()))
                        .findAny().orElse(null);
                if (secondOffset != null && !offsetsHaveOverlappingRange(firstOffset, secondOffset)) {
                    return false;
                }
            }
        } else {
            for (ProductRateOffset firstOffset : firstOffsetList) {
                ProductRateOffset secondOffset = secondOffsetList
                        .stream()
                        .filter(o -> o.getAgileRatesDTARange().equals(firstOffset.getAgileRatesDTARange()))
                        .findAny().orElse(null);
                if (secondOffset != null && !offsetsHaveOverlappingRange(firstOffset, secondOffset)) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean areOffsetsInRangeForRcOffsets(List<ProductRateOffset> firstOffsetList, List<ProductRateOffset> secondOffsetList) {
        for (ProductRateOffset firstOffset : firstOffsetList) {
            ProductRateOffset secondOffset = secondOffsetList
                    .stream()
                    .filter(o -> o.getAccomClass().equals(firstOffset.getAccomClass()))
                    .findAny().orElse(null);
            if (secondOffset != null && !offsetsHaveOverlappingRange(firstOffset, secondOffset)) {
                return false;
            }
        }
        return true;
    }

    public boolean offsetsHaveOverlappingRange(ProductRateOffset firstOffset, ProductRateOffset secondOffset) {
        List<BigDecimal> firstOffsetFloorVals = getFloorOffsetValues(Arrays.asList(firstOffset));
        List<BigDecimal> secondOffsetFloorVals = getFloorOffsetValues(Arrays.asList(secondOffset));
        List<BigDecimal> firstOffsetCeilVals = getCeilingOffsetValues(Arrays.asList(firstOffset));
        List<BigDecimal> secondOffsetCeilVals = getCeilingOffsetValues(Arrays.asList(secondOffset));

        boolean hasOverlap = false; // all offsets must overlap at least once
        for (int i = 0; i < firstOffsetFloorVals.size(); i++) {
            if (firstOffsetFloorVals.get(i) != null && secondOffsetFloorVals.get(i) != null) {
                hasOverlap = true;
                if (Math.max(firstOffsetFloorVals.get(i).doubleValue(), secondOffsetFloorVals.get(i).doubleValue())
                        > Math.min(firstOffsetCeilVals.get(i).doubleValue(), secondOffsetCeilVals.get(i).doubleValue())) {
                    return false; // determine if there's an offset value that doesn't overlap
                }
            }
        }
        return hasOverlap;
    }

    public AgileRatesHierarchyDTO createHierarchyDTOFromEntity(ProductHierarchy entity) {
        List<ProductMinPriceDiff> productMinPriceDiffList = entity.getProductMinPriceDiffList();
        return new AgileRatesHierarchyDTO(0, entity.getFromProduct(), entity.getToProduct(), CollectionUtils.isNotEmpty(productMinPriceDiffList) ? productMinPriceDiffList.get(0).getSundayDiffWithTax() : BigDecimal.ZERO, false);
    }

    public List<ProductHierarchy> findInvalidExistingHierarchies(List<ProductHierarchy> hierarchiesToValidate, Product product, Collection<ProductRateOffset> newOffsets,
                                                                 List<ProductRateOffset> allProductRateOffsets, List<Product> allProducts) {
        List<AgileRatesHierarchyDTO> dtoList = new ArrayList<>();
        List<ProductHierarchy> invalidHierarchies = new ArrayList<>();
        hierarchiesToValidate.forEach(entity -> dtoList.add(createHierarchyDTOFromEntity(entity)));

        // find all existing offsets, remove existing offsets that belong to the current product, and add in the modified ones for revalidation
        if (allProductRateOffsets.isEmpty()) {
            allProductRateOffsets = getProductRateOffset();
        }
        allProductRateOffsets.removeIf(offset -> offset.getProduct().equals(product));
        allProductRateOffsets.addAll(newOffsets);
        for (AgileRatesHierarchyDTO dto : dtoList) {
            Product selectedProduct = dto.getSelectedProduct().getId().equals(product.getId()) ? product : dto.getSelectedProduct();
            Product relatedProduct = dto.getSelectedRelationshipProduct().getId().equals(product.getId()) ? product : dto.getSelectedRelationshipProduct();
            if (!isHierarchyValid(selectedProduct, relatedProduct, allProductRateOffsets, dto.getMinimumDifference(), allProducts, new ArrayList<>())) {
                invalidHierarchies.add(hierarchiesToValidate.stream()
                        .filter(h -> h.getFromProduct().equals(dto.getSelectedProduct()) && h.getToProduct().equals(dto.getSelectedRelationshipProduct()))
                        .findAny()
                        .orElse(null));
            }
        }
        return invalidHierarchies;
    }

    public boolean isEditingHierarchyValidAccordingToSeasonalAdjustments(Product selectedProduct, Product relatedProduct, List<Product> allProducts,
                                                                         BigDecimal minimumDifference, Collection<ProductRateOffset> productRateOffsetList,
                                                                         List<String> validationMessages) {
        Map<Integer, Product> groupedProducts = allProducts.stream()
                .collect(toMap(Product::getId, p -> p));
        if (!shouldSeasonOffsetBeValidated(selectedProduct, selectedProduct, groupedProducts.get(selectedProduct.getDependentProductId()), groupedProducts.get(relatedProduct.getDependentProductId()))) {
            return true;
        }
        Map<Product, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>>> groupedOffsets = productRateOffsetList.stream()
                .filter(offset -> Objects.equals(offset.getProduct().getId(), selectedProduct.getId()) ||
                        Objects.equals(offset.getProduct().getId(), relatedProduct.getId()))
                .collect(groupingBy(ProductRateOffset::getProduct, groupingBy(offset -> Pair.of(offset.getStartDate(), offset.getEndDate()))));

        List<String> seasonalWarningMessages = new ArrayList<>();

        List<ProductRateOffset> seasonalAdjustmentsForSelectedProduct = getSeasonalAdjustmentsForProductFromGroup(selectedProduct, groupedOffsets);
        List<ProductRateOffset> seasonalAdjustmentsForRelatedProduct = getSeasonalAdjustmentsForProductFromGroup(relatedProduct, groupedOffsets);

        validateSeasonalAdjustments(seasonalAdjustmentsForSelectedProduct, groupedOffsets, relatedProduct, minimumDifference, seasonalWarningMessages, true);
        validateSeasonalAdjustments(seasonalAdjustmentsForRelatedProduct, groupedOffsets, selectedProduct, minimumDifference, seasonalWarningMessages, false);

        validationMessages.addAll(seasonalWarningMessages.stream()
                .distinct()
                .collect(toList()));

        return CollectionUtils.isEmpty(seasonalWarningMessages);
    }

    private void validateSeasonalAdjustments(List<ProductRateOffset> seasonalAdjustmentsForFirstProduct,
                                             Map<Product, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>>> groupedOffsets,
                                             Product secondProduct, BigDecimal minimumDifference, List<String> warningMessages, boolean isFirstProductSelected) {
        seasonalAdjustmentsForFirstProduct.forEach(offset -> {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> filteredRelatedProductsSeasonalAdjustments
                    = filterNonEditingOffsetsToCoverFullPeriodOfEditing(offset, groupedOffsets.get(secondProduct));
            filteredRelatedProductsSeasonalAdjustments.values()
                    .stream()
                    .flatMap(List::stream)
                    .filter(relatedOffset -> Objects.equals(relatedOffset.getAccomClass(), offset.getAccomClass()))
                    .forEach(secondOffset -> {
                                if (isFirstProductSelected) {
                                    verifyOffsetsOverlap(offset, secondOffset, minimumDifference, warningMessages);
                                } else {
                                    verifyOffsetsOverlap(secondOffset, offset, minimumDifference, warningMessages);
                                }
                            }
                    );
        });
    }

    private List<ProductRateOffset> getSeasonalAdjustmentsForProductFromGroup(Product product, Map<Product, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>>> groupedOffsets) {
        return groupedOffsets.get(product).entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getKey().getLeft()))
                .map(Map.Entry::getValue)
                .flatMap(List::stream)
                .collect(toList());
    }

    public boolean isHierarchyBeetweenIPsValid(Product selectedProduct, Product relatedProduct, BigDecimal minDifference, List<String> messages) {
        Set<AccomType> sharedAccomTypes = findSharedAccomTypes(selectedProduct, relatedProduct);
        if (CollectionUtils.isEmpty(sharedAccomTypes)) {
            return true;
        }
        Set<String> hierarchyMessages = new HashSet<>();
        OccupancyType baseOccupancyType = getBaseOccupancyType();
        Map<Integer, Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>>> ceilingFloorValuesForProducts = retrieveFloorCeilingMappingsForProducts(List.of(selectedProduct.getId(), relatedProduct.getId()))
                .stream()
                .filter(pricingBaseAccomType -> sharedAccomTypes.contains(pricingBaseAccomType.getAccomType()))
                .collect(groupingBy(PricingBaseAccomType::getProductID, groupingBy(ceilingFloor -> Pair.of(ceilingFloor.getStartDate(), ceilingFloor.getEndDate()))));

        Map<Integer, Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>>> groupedOffsetsForProducts = retrieveOffsetsForProducts(List.of(selectedProduct.getId(), relatedProduct.getId()))
                .stream()
                .filter(offset -> sharedAccomTypes.contains(offset.getAccomType()))
                .filter(offset -> Objects.equals(offset.getOccupancyType(), baseOccupancyType))
                .collect(groupingBy(CPConfigOffsetAccomType::getProductID, groupingBy(ceilingFloor -> Pair.of(ceilingFloor.getStartDate(), ceilingFloor.getEndDate()))));

        boolean isValid = agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(ceilingFloorValuesForProducts.get(selectedProduct.getId()),
                ceilingFloorValuesForProducts.get(relatedProduct.getId()),
                groupedOffsetsForProducts.getOrDefault(selectedProduct.getId(), Collections.emptyMap()),
                groupedOffsetsForProducts.getOrDefault(relatedProduct.getId(), Collections.emptyMap()),
                findPriceExcludedAccomClasses(),
                minDifference, hierarchyMessages,
                container -> true);

        messages.addAll(hierarchyMessages);
        return isValid;
    }

    public List<PricingBaseAccomType> retrieveFloorCeilingMappingsForProducts(List<Integer> productIDs) {
        return tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_ALL_BY_PRODUCT_IDS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(PRODUCT_IDs, productIDs)
                        .parameters());
    }

    public Set<AccomType> findSharedAccomTypes(Product fromProduct, Product toProduct) {
        if (fromProduct.isSystemDefault()) {
            return getAccomTypesForProduct(toProduct);
        } else if (toProduct.isSystemDefault()) {
            return getAccomTypesForProduct(fromProduct);
        } else {
            Set<AccomType> accomTypesForSelectedProduct = getAccomTypesForProduct(fromProduct);
            Set<AccomType> accomTypesForRelatedProduct = getAccomTypesForProduct(toProduct);
            return accomTypesForSelectedProduct.stream()
                    .filter(accomTypesForRelatedProduct::contains)
                    .collect(Collectors.toSet());
        }
    }

    private Set<AccomType> getAccomTypesForProduct(Product product) {
        return findProductRoomTypesByProduct(product).stream()
                .map(ProductAccomType::getAccomType)
                .collect(Collectors.toSet());
    }

    public boolean isHierarchyValid(Product selectedProduct, Product relatedProduct, Collection<ProductRateOffset> productRateOffsetList, BigDecimal minimumDifference, List<Product> allProducts, List<String> validationMessages) {
        return isHierarchyValid(selectedProduct, relatedProduct, productRateOffsetList, minimumDifference, allProducts, true, validationMessages);
    }

    @SuppressWarnings("squid:S3776")
    public boolean isHierarchyValid(Product selectedProduct, Product relatedProduct, Collection<ProductRateOffset> productRateOffsetList, BigDecimal minimumDifference, List<Product> allProducts, boolean validateAccomTypes, List<String> validationMessages) {
        // hierarchy cannot be formed between BAR and non BAR-child
        if (relatedProduct.isSystemDefault() && selectedProduct.getDependentProductId() != null && !selectedProduct.getDependentProductId().equals(relatedProduct.getId())) {
            return false;
        }

        List<AccomType> sharedAccomTypes = new ArrayList<>();
        if (validateAccomTypes) {
            // hierarchy must have overlapping accom types
            List<AccomType> accomTypesForSelectedProduct = findProductRoomTypesByProduct(selectedProduct).stream()
                    .map(ProductAccomType::getAccomType)
                    .collect(Collectors.toList());
            List<AccomType> accomTypesForRelatedProduct = relatedProduct.isSystemDefault() ? new ArrayList<>() : findProductRoomTypesByProduct(relatedProduct).stream()
                    .map(ProductAccomType::getAccomType)
                    .collect(Collectors.toList());

            //If BAR is selected, use the Selected Product's ATs since BAR contains all ATs. Note: The UI does not allow BAR to be the selected product
            if (relatedProduct.isSystemDefault()) {
                sharedAccomTypes = accomTypesForSelectedProduct;
            } else {
                sharedAccomTypes = accomTypesForSelectedProduct.stream()
                        .filter(accomTypesForRelatedProduct::contains)
                        .collect(Collectors.toList());
            }

            if (sharedAccomTypes.isEmpty()) {
                return false;
            }
        }

        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        if (isIndependentProductsEnabled) {
            if (!selectedProduct.isAgileRatesProduct() && !relatedProduct.isAgileRatesProduct()) {
                return SystemConfig.isSeasonHierarchyValidationEnabled() ?
                        isHierarchyBeetweenIPsValid(selectedProduct, relatedProduct, minimumDifference, validationMessages) :
                        isHierarchyValidGivenIndependentProducts(selectedProduct, relatedProduct, minimumDifference, sharedAccomTypes, validationMessages);
            }
        }

        // find offsets for each product
        List<ProductRateOffset> selectedOffsets = productRateOffsetList.stream()
                .filter(offset -> offset.getProduct().equals(selectedProduct))
                .collect(Collectors.toList());
        List<ProductRateOffset> relatedOffsets = productRateOffsetList.stream()
                .filter(offset -> offset.getProduct().equals(relatedProduct))
                .collect(Collectors.toList());

        Product selectedDependentProduct = allProducts.stream()
                .filter(product -> product.getId().equals(selectedProduct.getDependentProductId()))
                .findFirst().orElse(null);
        Product relatedDependentProduct = allProducts.stream()
                .filter(product -> product.getId().equals(relatedProduct.getDependentProductId()))
                .findFirst().orElse(null);

        boolean isLinkedProductHierarchyEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.LINKED_PRODUCT_HIERARCHY_ENABLED);

        if (selectedProduct.getDependentProductId() != null && selectedProduct.getDependentProductId().equals(relatedProduct.getId())) {
            // selected is child
            return isHierarchyValidDependentProducts(selectedProduct, relatedProduct, minimumDifference, selectedOffsets, relatedOffsets, true, validationMessages);
        } else if (relatedProduct.getDependentProductId() != null && relatedProduct.getDependentProductId().equals(selectedProduct.getId())) {
            // relationship is child
            return isHierarchyValidDependentProducts(relatedProduct, selectedProduct, minimumDifference, relatedOffsets, selectedOffsets, false, validationMessages);
        } else if ((!isIndependentProductsEnabled && !isLinkedProductHierarchyEnabled) || (selectedProduct.getDependentProductId() != null && relatedProduct.getDependentProductId() != null
                && relatedProduct.getDependentProductId().equals(selectedProduct.getDependentProductId())
                && (selectedDependentProduct != null && selectedDependentProduct.isSystemDefaultOrIndependentProduct())
                && (relatedDependentProduct != null && relatedDependentProduct.isSystemDefaultOrIndependentProduct()))) {
            // products have the same primary product parent and they are BAR or an Independent Product; Note: products of different families should've not been allowed be selected
            return isHierarchyValidGivenDifferentOffsetTypes(selectedProduct, relatedProduct, minimumDifference, selectedOffsets, relatedOffsets, validationMessages);
        } else {
            // This should only be executed if isIndependentProductsEnabled or isLinkedProductHierarchyEnabled.
            // products have different dependent products and are not direct children of BAR or Independent Product
            return isHierarchyValidGivenDifferentOffsetTypesForNonImmediateFamilyProducts(selectedProduct, relatedProduct, minimumDifference, selectedOffsets, relatedOffsets, new ArrayList<>(productRateOffsetList), allProducts, validationMessages);
        }
    }

    public List<BrokenHierarchySeasonalAdjustmentDetails> getInvalidHierarchiesAccordingToSeasonOffsetsValidation(Product editingProduct, List<ProductRateOffset> offsets) {
        List<ProductHierarchy> allHierarchiesForProduct = getAllHierarchiesForProduct(editingProduct);
        if (CollectionUtils.isEmpty(allHierarchiesForProduct)) {
            return Collections.emptyList();
        }
        Set<Product> allProductsWithinHierarchies = new HashSet<>(getAllImpactedProductsWithinHierarchies(allHierarchiesForProduct, editingProduct));
        List<ProductRateOffset> productRateOffsetForProducts = findProductRateOffsetFor(allProductsWithinHierarchies);
        Map<Product, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>>> groupedOffsets = productRateOffsetForProducts.stream()
                .collect(groupingBy(ProductRateOffset::getProduct, groupingBy(offset -> Pair.of(offset.getStartDate(), offset.getEndDate()))));
        Map<Integer, Product> allProducts = findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts()
                .stream()
                .collect(Collectors.toMap(Product::getId, p -> p));
        return allHierarchiesForProduct.stream()
                .map(productHierarchy -> {
                    boolean isEditingProductSelected = editingProduct.getId().equals(productHierarchy.getFromProduct().getId());
                    Product nonEditingProduct = isEditingProductSelected ?
                            productHierarchy.getToProduct() :
                            productHierarchy.getFromProduct();
                    boolean shouldSeasonOffsetBeValidated = shouldSeasonOffsetBeValidated(editingProduct, nonEditingProduct, allProducts.get(editingProduct.getDependentProductId()), allProducts.get(nonEditingProduct.getDependentProductId()));
                    if (!shouldSeasonOffsetBeValidated) {
                        return null;
                    }
                    Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> offsetsForNonEditingProduct = groupedOffsets.get(nonEditingProduct);
                    BigDecimal minDifference = BigDecimalUtil.zeroIfNull(productHierarchy.getMinimumDifference());
                    return constructBrokenHierarchies(offsets, offsetsForNonEditingProduct, productHierarchy, minDifference, isEditingProductSelected);
                })
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(toList());
    }

    private List<BrokenHierarchySeasonalAdjustmentDetails> constructBrokenHierarchies(List<ProductRateOffset> offsets, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> offsetsForNonEditingProduct,
                                                                                      ProductHierarchy productHierarchy, BigDecimal minDifference, boolean isEditingProductSelected) {
        return offsets.stream()
                .map(editingOffset -> {
                    Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> filteredNonEditingOffsets =
                            filterNonEditingOffsetsToCoverFullPeriodOfEditing(editingOffset, offsetsForNonEditingProduct);
                    return filteredNonEditingOffsets.values()
                            .stream()
                            .flatMap(List::stream)
                            .filter(nonEditingOffset -> Objects.equals(editingOffset.getAccomClass(), nonEditingOffset.getAccomClass()))
                            .filter(nonEditingOffset -> isEditingProductSelected ?
                                    !verifyOffsetsOverlap(editingOffset, nonEditingOffset, minDifference, new ArrayList<>()) :
                                    !verifyOffsetsOverlap(nonEditingOffset, editingOffset, minDifference, new ArrayList<>()))
                            .map(nonEditingOffset ->
                                    createBrokenHierarchySeasonalAdjustmentDetails(productHierarchy, editingOffset, nonEditingOffset, isEditingProductSelected))
                            .collect(toList());
                })
                .flatMap(List::stream)
                .collect(toList());
    }

    private BrokenHierarchySeasonalAdjustmentDetails createBrokenHierarchySeasonalAdjustmentDetails(ProductHierarchy productHierarchy,
                                                                                                    ProductRateOffset editingOffset, ProductRateOffset nonEditingOffset, boolean isEditingProductSelected) {
        String editingSeasonName = editingOffset.getSeasonName();
        String nonEditingSeasonName = StringUtils.isEmpty(nonEditingOffset.getSeasonName()) ?
                DEFAULTS :
                nonEditingOffset.getSeasonName();
        BrokenHierarchySeasonalAdjustmentDetails details = new BrokenHierarchySeasonalAdjustmentDetails();
        details.setProductHierarchy(productHierarchy);
        if (isEditingProductSelected) {
            details.setSelectedSeasonName(editingSeasonName);
            details.setRelatedSeasonName(nonEditingSeasonName);
        } else {
            details.setSelectedSeasonName(nonEditingSeasonName);
            details.setRelatedSeasonName(editingSeasonName);
        }
        details.setAccomClassName(Objects.nonNull(editingOffset.getAccomClass()) ?
                editingOffset.getAccomClass().getName() :
                null);
        return details;
    }

    private boolean shouldSeasonOffsetBeValidated(Product firstProduct, Product secondProduct, Product firstDependentProduct, Product secondDependentProduct) {
        return firstProduct.getDependentProductId() != null && secondProduct.getDependentProductId() != null
                && secondProduct.getDependentProductId().equals(firstProduct.getDependentProductId())
                && (firstDependentProduct != null && firstDependentProduct.isSystemDefaultOrIndependentProduct())
                && (secondDependentProduct != null && secondDependentProduct.isSystemDefaultOrIndependentProduct());
    }

    private Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> filterNonEditingOffsetsToCoverFullPeriodOfEditing(ProductRateOffset editingOffset,
                                                                                                                       Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> nonEditingOffsets) {
        LocalDate seasonStartDate = editingOffset.getStartDate();
        LocalDate seasonEndDate = editingOffset.getEndDate();

        List<Pair<LocalDate, LocalDate>> seasonDateRangesForNonEditingProduct = nonEditingOffsets.keySet()
                .stream()
                .filter(pair -> nonNull(pair.getLeft()) && areDatesOverlapped(seasonStartDate, seasonEndDate, pair.getLeft(), pair.getRight()))
                .collect(toList());

        boolean shouldBeDefaultsConsidered = !editingSeasonDatesCoverAllDateRangesOfNonEditingSeasons(seasonStartDate, seasonEndDate, seasonDateRangesForNonEditingProduct);

        return nonEditingOffsets.entrySet()
                .stream()
                .filter(entry ->
                {
                    boolean isSeasonalDateRangeSuitable = seasonDateRangesForNonEditingProduct.contains(entry.getKey());
                    if (!shouldBeDefaultsConsidered) {
                        return isSeasonalDateRangeSuitable;
                    }
                    return isDefaultDateRange(entry.getKey()) || isSeasonalDateRangeSuitable;
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private boolean isDefaultDateRange(Pair<LocalDate, LocalDate> dateRange) {
        return Objects.isNull(dateRange.getLeft());
    }

    private boolean editingSeasonDatesCoverAllDateRangesOfNonEditingSeasons(LocalDate seasonStartDate, LocalDate seasonEndDate,
                                                                            List<Pair<LocalDate, LocalDate>> dateRanges) {
        for (LocalDate currentDate = seasonStartDate;
             currentDate.isBefore(seasonEndDate) || currentDate.isEqual(seasonEndDate);
             currentDate = currentDate.plusDays(1)) {
            boolean isCovered = false;
            for (Pair<LocalDate, LocalDate> pair : dateRanges) {
                if (dateBelongsToDateRange(currentDate, pair)) {
                    isCovered = true;
                }
            }
            if (!isCovered) {
                return false;
            }
        }
        return true;
    }

    private boolean dateBelongsToDateRange(LocalDate date, Pair<LocalDate, LocalDate> dateRange) {
        return (date.isAfter(dateRange.getLeft()) || date.isEqual(dateRange.getLeft())) &&
                (date.isBefore(dateRange.getRight()) || date.isEqual(dateRange.getRight()));
    }

    private boolean areDatesOverlapped(LocalDate startDateOfEditingSeason, LocalDate endDateOfEditingSeason,
                                       LocalDate startDateOfNonEditingSeason, LocalDate endDateOfNonEditingSeason) {
        return ((startDateOfEditingSeason.isBefore(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(startDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(startDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isBefore(endDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(endDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isBefore(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isAfter(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isBefore(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason)));
    }

    public boolean isHierarchyValidGivenIndependentProducts(Product selectedProduct, Product relatedProduct, BigDecimal minimumDifference, List<AccomType> sharedAccomTypes, List<String> validationMessages) {
        List<PricingBaseAccomType> allFloorCeilingMappings = retrieveAllFloorCeilingMappingsForProperty();
        List<AccomType> finalSharedAccomTypes = sharedAccomTypes;
        List<PricingBaseAccomType> selectedProductCFMappings = allFloorCeilingMappings.stream()
                .filter(cf -> cf.getProductID().equals(selectedProduct.getId()))
                .filter(cf -> cf.getStartDate() == null && cf.getEndDate() == null)
                .filter(cf -> finalSharedAccomTypes.contains(cf.getAccomType()))
                .collect(Collectors.toList());
        List<PricingBaseAccomType> relatedProductCFMappings = allFloorCeilingMappings.stream()
                .filter(cf -> cf.getProductID().equals(relatedProduct.getId()))
                .filter(cf -> cf.getStartDate() == null && cf.getEndDate() == null)
                .filter(cf -> finalSharedAccomTypes.contains(cf.getAccomType()))
                .collect(Collectors.toList());

        return validateProductCFMappings(selectedProductCFMappings, relatedProductCFMappings, minimumDifference, validationMessages);
    }

    public List<PricingBaseAccomType> retrieveFloorCeilingMappingsByPropertyIdAndProductIds(Set<Integer> productIds) {
        return tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_BY_PROPERTY_ID_AND_PRODUCT_IDs,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(PRODUCT_IDs, productIds).parameters());
    }

    public void removeHierarchies(List<ProductHierarchy> productHierarchies) {
        if (CollectionUtils.isEmpty(productHierarchies)) {
            return;
        }
        tenantCrudService.delete(productHierarchies);
    }

    public boolean validateProductCFMappings(List<PricingBaseAccomType> selectedProductCFMappings,
                                             List<PricingBaseAccomType> relatedProductCFMappings, BigDecimal minimumDifference,
                                             List<String> validationMessages) {
        Set<AccomClass> priceExcludedAccomClasses = findPriceExcludedAccomClasses();
        for (PricingBaseAccomType cf : selectedProductCFMappings) {
            PricingBaseAccomType otherCf = relatedProductCFMappings.stream()
                    .filter(item -> item.getAccomType().equals(cf.getAccomType()))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(otherCf) && !(doCFMappingsOverlap(cf, otherCf, minimumDifference, validationMessages,
                    priceExcludedAccomClasses.contains(cf.getAccomType().getAccomClass())))) {
                return false;
            }
        }

        return true;
    }

    public Set<AccomClass> findPriceExcludedAccomClasses() {
        List<PricingAccomClass> excludedAccomClasses = tenantCrudService.findByNamedQuery(
                PricingAccomClass.FIND_BY_PROPERTY_ID_AND_PRICE_EXCLUDED,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        return excludedAccomClasses.stream()
                .map(PricingAccomClass::getAccomClass)
                .collect(Collectors.toSet());
    }

    public boolean doCFMappingsOverlap(PricingBaseAccomType selectedCFMapping, PricingBaseAccomType relatedCFMapping,
                                       BigDecimal minimumDifference, List<String> validationMessages,
                                       boolean isAccomClassPriceExcluded) {
        List<BigDecimal> floorValuesForSelected = getFloorWithTaxValuesForCFMapping(selectedCFMapping);
        List<BigDecimal> ceilingValuesForSelected = getCeilingWithTaxValuesForCFMapping(selectedCFMapping);
        List<BigDecimal> floorValuesForRelated = getFloorWithTaxValuesForCFMapping(relatedCFMapping);
        List<BigDecimal> ceilingValuesForRelated = getCeilingWithTaxValuesForCFMapping(relatedCFMapping);

        boolean hasOverlap = false;
        for (int i = 0; i < floorValuesForSelected.size(); i++) {
            if (floorValuesForSelected.get(i) != null && floorValuesForRelated.get(i) != null) {
                hasOverlap = true;
                // 1) floor of selected + minimum difference must be <= floor of related
                // 2) ceiling of selected + minimum difference must be <= ceiling of related
                // 3) overlap must exist
                //overlapAmount = Ceiling Of Related Product (Greater than product) - Floor of Selected Product (Less than product)
                double overlapAmount = ceilingValuesForRelated.get(i).doubleValue() - floorValuesForSelected.get(i).doubleValue();
                if (isAccomClassPriceExcluded && floorValuesForSelected.get(i).compareTo(floorValuesForRelated.get(i)) > 0) {
                    validationMessages.add("invalid.hierarchy.excluded.value");
                }
                if (floorValuesForSelected.get(i).compareTo(floorValuesForRelated.get(i)) > 0 && !isAccomClassPriceExcluded) {
                    validationMessages.add("invalid.hierarchy.floor.value");
                }
                if (ceilingValuesForSelected.get(i).compareTo(ceilingValuesForRelated.get(i)) > 0 && !isAccomClassPriceExcluded) {
                    validationMessages.add("invalid.hierarchy.ceiling.value");
                }
                if (isOverlapNotExist(floorValuesForSelected.get(i).doubleValue(), ceilingValuesForSelected.get(i).doubleValue(),
                        floorValuesForRelated.get(i).doubleValue(), ceilingValuesForRelated.get(i).doubleValue()) && !isAccomClassPriceExcluded) {
                    validationMessages.add("invalid.hierarchy.overlap.value");
                }
                if (overlapAmount < minimumDifference.doubleValue() && !isAccomClassPriceExcluded) {
                    validationMessages.add("invalid.hierarchy.mindiff.value");
                }
                if ((overlapAmount < 0 && !isAccomClassPriceExcluded) || CollectionUtils.isNotEmpty(validationMessages)) {
                    return false;
                }
            }
        }
        return hasOverlap;
    }

    public List<BigDecimal> getFloorWithTaxValuesForCFMapping(PricingBaseAccomType cfMapping) {
        List<BigDecimal> floorWithTaxValues = new ArrayList<>();
        floorWithTaxValues.add(cfMapping.getSundayFloorRateWithTax());
        floorWithTaxValues.add(cfMapping.getMondayFloorRateWithTax());
        floorWithTaxValues.add(cfMapping.getTuesdayFloorRateWithTax());
        floorWithTaxValues.add(cfMapping.getWednesdayFloorRateWithTax());
        floorWithTaxValues.add(cfMapping.getThursdayFloorRateWithTax());
        floorWithTaxValues.add(cfMapping.getFridayFloorRateWithTax());
        floorWithTaxValues.add(cfMapping.getSaturdayFloorRateWithTax());
        return floorWithTaxValues;
    }

    public List<BigDecimal> getCeilingWithTaxValuesForCFMapping(PricingBaseAccomType cfMapping) {
        List<BigDecimal> ceilingWithTaxValues = new ArrayList<>();
        ceilingWithTaxValues.add(cfMapping.getSundayCeilingRateWithTax());
        ceilingWithTaxValues.add(cfMapping.getMondayCeilingRateWithTax());
        ceilingWithTaxValues.add(cfMapping.getTuesdayCeilingRateWithTax());
        ceilingWithTaxValues.add(cfMapping.getWednesdayCeilingRateWithTax());
        ceilingWithTaxValues.add(cfMapping.getThursdayCeilingRateWithTax());
        ceilingWithTaxValues.add(cfMapping.getFridayCeilingRateWithTax());
        ceilingWithTaxValues.add(cfMapping.getSaturdayCeilingRateWithTax());
        return ceilingWithTaxValues;
    }

    public List<List<ProductRateOffset>> separateOffsetsBySeasons(List<ProductRateOffset> offsetsBySeason) {
        List<List<ProductRateOffset>> allProductRateOffsetsBySeason = new ArrayList<>();
        offsetsBySeason.removeIf(offset -> offset.getEndDate().isBefore(dateService.getCaughtUpLocalDate()));
        while (!offsetsBySeason.isEmpty()) {
            List<ProductRateOffset> tempList = offsetsBySeason
                    .stream()
                    .filter(offset -> offsetsBySeason.get(0).getStartDate().equals(offset.getStartDate()))
                    .filter(offset -> offsetsBySeason.get(0).getEndDate().equals(offset.getEndDate()))
                    .collect(Collectors.toList());
            allProductRateOffsetsBySeason.add(tempList);
            offsetsBySeason.removeIf(tempList::contains);
        }
        return allProductRateOffsetsBySeason;
    }

    public boolean isSeasonalProduct(List<ProductRateOffset> offsets) {
        return offsets
                .stream()
                .filter(offset -> offset.getStartDate() != null)
                .filter(offset -> offset.getEndDate() != null).count() == offsets.size();
    }

    private boolean isHierarchyValidDependentProducts(Product childProduct, Product parentProduct, BigDecimal minimumDifference,
                                                      List<ProductRateOffset> childOffsets, List<ProductRateOffset> parentOffsets, boolean isChildSelectedProduct, List<String> validationMessages) {
        if (parentProduct.isSystemDefaultOrIndependentProduct()) {
            boolean areMinAdjustmentsValid = areChildOffsetsValidForHierarchy(getFloorOffsetValues(childOffsets), true, minimumDifference);
            if (!areMinAdjustmentsValid) {
                validationMessages.add("invalid.hierarchy.min.adjustment.value.linked.product");
            }
            boolean areMaxAdjustmentsValid = areChildOffsetsValidForHierarchy(getCeilingOffsetValues(childOffsets), true, minimumDifference);
            if (!areMaxAdjustmentsValid) {
                validationMessages.add("invalid.hierarchy.max.adjustment.value.linked.product");
            }
            return areMinAdjustmentsValid && areMaxAdjustmentsValid;
        }
        if (isChildSelectedProduct) {
            return areChildOffsetsValidForHierarchy(getFloorOffsetValues(childOffsets), true, minimumDifference)
                    && isHierarchyValidGivenDifferentOffsetTypes(childProduct, parentProduct, minimumDifference, childOffsets, parentOffsets, validationMessages);
        } else {
            return areChildOffsetsValidForHierarchy(getFloorOffsetValues(childOffsets), false, minimumDifference)
                    && isHierarchyValidGivenDifferentOffsetTypes(parentProduct, childProduct, minimumDifference, parentOffsets, childOffsets, validationMessages);
        }
    }

    private boolean areChildOffsetsValidForHierarchy(List<BigDecimal> childOffsetValues, boolean mustBeLessThanZero, BigDecimal minimumDifference) {
        childOffsetValues.removeIf(Objects::isNull);
        if (mustBeLessThanZero) {
            return childOffsetValues.stream().noneMatch(val -> val.compareTo(minimumDifference.negate()) > 0);
        } else {
            return childOffsetValues.stream().noneMatch(val -> val.compareTo(minimumDifference.negate()) < 0);
        }
    }

    public boolean isHierarchyValidGivenDifferentOffsetTypes(Product selectedProduct, Product relatedProduct, BigDecimal minimumDifference,
                                                             List<ProductRateOffset> selectedOffsets, List<ProductRateOffset> relatedOffsets, List<String> validationMessages) {
        if (selectedOffsets.isEmpty() || relatedOffsets.isEmpty()) {
            return false;
        }

        // seasonal product flow
        if (isSeasonalProduct(selectedOffsets) || isSeasonalProduct(relatedOffsets)) {
            return isHierarchyValidSeasonalProducts(selectedOffsets, relatedOffsets, minimumDifference, selectedProduct, relatedProduct, validationMessages);
        }

        selectedOffsets = getNonOverlappingOffsets(selectedOffsets, relatedOffsets);
        relatedOffsets = getNonOverlappingOffsets(relatedOffsets, selectedOffsets);
        selectedOffsets.removeIf(offset -> offset.getStartDate() != null);
        relatedOffsets.removeIf(offset -> offset.getStartDate() != null);

        if (selectedOffsets.isEmpty() || relatedOffsets.isEmpty()) {
            return false;
        }

        // non seasonal products flow
        return separateOffsetsAndDetermineIfHierarchyIsValid(selectedOffsets, relatedOffsets, minimumDifference,
                Arrays.asList(selectedProduct.isDtaOffset(), selectedProduct.isRoomClassOffset(), selectedProduct.isDowOffset()),
                Arrays.asList(relatedProduct.isDtaOffset(), relatedProduct.isRoomClassOffset(), relatedProduct.isDowOffset()),
                validationMessages);
    }

    public boolean isHierarchyValidGivenDifferentOffsetTypesForNonImmediateFamilyProducts(Product selectedProduct, Product relatedProduct, BigDecimal minimumDifference,
                                                                                          List<ProductRateOffset> selectedOffsets, List<ProductRateOffset> relatedOffsets,
                                                                                          List<ProductRateOffset> allProductRateOffsets, List<Product> allProducts,
                                                                                          List<String> validationMessages) {
        if (selectedOffsets.isEmpty() || relatedOffsets.isEmpty()) {
            return false;
        }

        //Get product branch for each Product
        List<Product> selectedProductBranch = getProductBranch(selectedProduct, allProducts);
        List<Product> relatedProductBranch = getProductBranch(relatedProduct, allProducts);

        //Get product rate offsets for each product branch excluding the primary product
        List<ProductRateOffset> selectedProductBranchOffsets = getProductBranchOffsets(selectedProductBranch, allProductRateOffsets);
        List<ProductRateOffset> relatedProductBranchOffsets = getProductBranchOffsets(relatedProductBranch, allProductRateOffsets);

        // seasonal product flow; check each branch to see if seasons are configured
        if (isSeasonalProduct(selectedProductBranchOffsets) || isSeasonalProduct(relatedProductBranchOffsets)) {
            return isHierarchyValidSeasonalProductsForProductsNonImmediateFamilyProducts(selectedProductBranchOffsets, relatedProductBranchOffsets, minimumDifference, selectedProductBranch, relatedProductBranch, validationMessages);
        }

        List<ProductRateOffset> generatedSelectedOffsets = createDefaultOffsetListBasedOnBranch(selectedProductBranch, selectedProductBranchOffsets);
        List<ProductRateOffset> generatedRelatedOffsets = createDefaultOffsetListBasedOnBranch(relatedProductBranch, relatedProductBranchOffsets);

        selectedOffsets = getNonOverlappingOffsets(generatedSelectedOffsets, generatedRelatedOffsets);
        relatedOffsets = getNonOverlappingOffsets(generatedRelatedOffsets, generatedSelectedOffsets);
        selectedOffsets.removeIf(offset -> offset.getStartDate() != null);
        relatedOffsets.removeIf(offset -> offset.getStartDate() != null);

        if (selectedOffsets.isEmpty() || relatedOffsets.isEmpty()) {
            return false;
        }

        // non seasonal products flow
        return separateOffsetsAndDetermineIfHierarchyIsValid(selectedOffsets, relatedOffsets, minimumDifference,
                Arrays.asList(selectedProduct.isDtaOffset(), selectedProduct.isRoomClassOffset(), selectedProduct.isDowOffset()),
                Arrays.asList(relatedProduct.isDtaOffset(), relatedProduct.isRoomClassOffset(), relatedProduct.isDowOffset()), validationMessages);
    }

    public List<ProductRateOffset> createDefaultOffsetListBasedOnBranch(List<Product> productBranch, List<ProductRateOffset> productBranchOffsets) {
        List<ProductRateOffset> calculatedOffsets = new ArrayList<>();
        Product lastProductInBranch = productBranch.get(productBranch.size() - 1);
        for (Product branchProduct : productBranch) {
            //skip primary product as it doesnt have Product Rate Offsets
            if (!branchProduct.isSystemDefaultOrIndependentProduct()) {
                List<ProductRateOffset> branchProductOffsets = productBranchOffsets.stream().filter(productRateOffset -> productRateOffset.getProduct().equals(branchProduct)).collect(Collectors.toList());
                for (ProductRateOffset offset : branchProductOffsets) {
                    ProductRateOffset existingCalculatedOffset = getExistingCalculatedOffset(calculatedOffsets, branchProduct, offset);

                    if (existingCalculatedOffset != null) {
                        existingCalculatedOffset.setProduct(offset.getProduct());
                        existingCalculatedOffset.setAgileRatesDTARange(offset.getAgileRatesDTARange());
                        existingCalculatedOffset.setSundayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getSundayOffsetValueCeiling(), offset.getSundayOffsetValueCeiling()));
                        existingCalculatedOffset.setSundayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getSundayOffsetValueFloor(), offset.getSundayOffsetValueFloor()));
                        existingCalculatedOffset.setMondayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getMondayOffsetValueCeiling(), offset.getMondayOffsetValueCeiling()));
                        existingCalculatedOffset.setMondayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getMondayOffsetValueFloor(), offset.getMondayOffsetValueFloor()));
                        existingCalculatedOffset.setTuesdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getTuesdayOffsetValueCeiling(), offset.getTuesdayOffsetValueCeiling()));
                        existingCalculatedOffset.setTuesdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getTuesdayOffsetValueFloor(), offset.getTuesdayOffsetValueFloor()));
                        existingCalculatedOffset.setWednesdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getWednesdayOffsetValueCeiling(), offset.getWednesdayOffsetValueCeiling()));
                        existingCalculatedOffset.setWednesdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getWednesdayOffsetValueFloor(), offset.getWednesdayOffsetValueFloor()));
                        existingCalculatedOffset.setThursdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getThursdayOffsetValueCeiling(), offset.getThursdayOffsetValueCeiling()));
                        existingCalculatedOffset.setThursdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getThursdayOffsetValueFloor(), offset.getThursdayOffsetValueFloor()));
                        existingCalculatedOffset.setFridayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getFridayOffsetValueCeiling(), offset.getFridayOffsetValueCeiling()));
                        existingCalculatedOffset.setFridayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getFridayOffsetValueFloor(), offset.getFridayOffsetValueFloor()));
                        existingCalculatedOffset.setSaturdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getSaturdayOffsetValueCeiling(), offset.getSaturdayOffsetValueCeiling()));
                        existingCalculatedOffset.setSaturdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getSaturdayOffsetValueFloor(), offset.getSaturdayOffsetValueFloor()));
                        calculatedOffsets.add(existingCalculatedOffset);
                    } else {
                        ProductRateOffset newCalculatedOffset = offset.copy();
                        calculatedOffsets.add(newCalculatedOffset);
                    }
                }
            }
        }
        return calculatedOffsets.stream()
                .filter(productRateOffset -> productRateOffset.getProduct().getId().equals(lastProductInBranch.getId()))
                .collect(toList());
    }

    @SuppressWarnings("squid:S3776")
    public ProductRateOffset getExistingCalculatedOffset(List<ProductRateOffset> calculatedOffsets, Product product, ProductRateOffset offset) {
        ProductRateOffset matchingOffset = null;
        if (CollectionUtils.isNotEmpty(calculatedOffsets)) {
            List<ProductRateOffset> existingCalculatedOffsetsForParent = calculatedOffsets.stream().filter(productRateOffset ->
                    productRateOffset.getProduct().getId().equals(product.getDependentProductId())).collect(Collectors.toList());

            //If there is only 1, we need to use it
            if (existingCalculatedOffsetsForParent.size() == 1) {
                return existingCalculatedOffsetsForParent.get(0).copy();
            }

            AccomClass expectedAccomClass = offset.getAccomClass();
            AgileRatesDTARange expectedAgileRatesDTARange = offset.getAgileRatesDTARange();

            if (expectedAccomClass == null && expectedAgileRatesDTARange == null) {
                matchingOffset = calculatedOffsets.stream().filter(productRateOffset ->
                        productRateOffset.getAccomClass() == null
                                && productRateOffset.getAgileRatesDTARange() == null).findFirst().orElse(null);
            } else if (expectedAccomClass != null && expectedAgileRatesDTARange != null) {
                matchingOffset = calculatedOffsets.stream().filter(productRateOffset ->
                        productRateOffset.getAccomClass() != null
                                && productRateOffset.getAgileRatesDTARange() != null
                                && productRateOffset.getAccomClass().equals(expectedAccomClass)
                                && productRateOffset.getAgileRatesDTARange().equals(expectedAgileRatesDTARange)).findFirst().orElse(null);
            } else if (expectedAccomClass != null && expectedAgileRatesDTARange == null) {
                matchingOffset = calculatedOffsets.stream().filter(productRateOffset ->
                                productRateOffset.getAgileRatesDTARange() == null
                                        && productRateOffset.getAccomClass() != null
                                        && productRateOffset.getAccomClass().equals(expectedAccomClass))
                        .findFirst().orElse(null);
            } else if (expectedAccomClass == null && expectedAgileRatesDTARange != null) {
                matchingOffset = calculatedOffsets.stream().filter(productRateOffset ->
                                productRateOffset.getAccomClass() == null
                                        && productRateOffset.getAgileRatesDTARange() != null
                                        && productRateOffset.getAgileRatesDTARange().equals(expectedAgileRatesDTARange))
                        .findFirst().orElse(null);
            }

            if (matchingOffset == null) {
                matchingOffset = calculateLowestCeilingHighestFloorOffset(existingCalculatedOffsetsForParent);
            }
        }
        return matchingOffset;
    }

    @SuppressWarnings("squid:S3776")
    public ProductRateOffset getExistingSeasonCalculatedOffset(List<ProductRateOffset> calculatedOffsets, Product product, ProductRateOffset offset) {
        ProductRateOffset matchingOffset = null;
        if (CollectionUtils.isNotEmpty(calculatedOffsets)) {
            List<ProductRateOffset> existingCalculatedOffsetsForParent = calculatedOffsets.stream().filter(productRateOffset ->
                    productRateOffset.getProduct().getId().equals(product.getDependentProductId())
                            && ((productRateOffset.getStartDate() == null && productRateOffset.getEndDate() == null)
                            || (productRateOffset.getStartDate().equals(offset.getStartDate())
                            && productRateOffset.getEndDate().equals(offset.getEndDate())))).collect(Collectors.toList());

            //If there is only 1, we need to use it
            if (CollectionUtils.isEmpty(existingCalculatedOffsetsForParent)) {
                return null;
            } else if (existingCalculatedOffsetsForParent.size() == 1) {
                return existingCalculatedOffsetsForParent.get(0);
            }

            AccomClass expectedAccomClass = offset.getAccomClass();
            AgileRatesDTARange expectedAgileRatesDTARange = offset.getAgileRatesDTARange();

            if (expectedAccomClass == null && expectedAgileRatesDTARange == null) {
                matchingOffset = existingCalculatedOffsetsForParent.stream().filter(productRateOffset ->
                        productRateOffset.getAccomClass() == null
                                && productRateOffset.getAgileRatesDTARange() == null).findFirst().orElse(null);
            } else if (expectedAccomClass != null && expectedAgileRatesDTARange != null) {
                matchingOffset = existingCalculatedOffsetsForParent.stream().filter(productRateOffset ->
                                productRateOffset.getAccomClass() != null
                                        && productRateOffset.getAgileRatesDTARange() != null
                                        && productRateOffset.getAccomClass().equals(expectedAccomClass)
                                        && productRateOffset.getAgileRatesDTARange().equals(expectedAgileRatesDTARange))
                        .findFirst().orElse(null);
            } else if (expectedAccomClass != null && expectedAgileRatesDTARange == null) {
                matchingOffset = existingCalculatedOffsetsForParent.stream().filter(productRateOffset ->
                                productRateOffset.getAgileRatesDTARange() == null
                                        && productRateOffset.getAccomClass() != null
                                        && productRateOffset.getAccomClass().equals(expectedAccomClass))
                        .findFirst().orElse(null);
            } else if (expectedAccomClass == null && expectedAgileRatesDTARange != null) {
                matchingOffset = existingCalculatedOffsetsForParent.stream().filter(productRateOffset ->
                                productRateOffset.getAccomClass() == null
                                        && productRateOffset.getAgileRatesDTARange() != null
                                        && productRateOffset.getAgileRatesDTARange().equals(expectedAgileRatesDTARange))
                        .findFirst().orElse(null);
            }

            if (matchingOffset == null) {
                matchingOffset = calculateLowestCeilingHighestFloorOffset(existingCalculatedOffsetsForParent);
            }
        }
        return matchingOffset;
    }

    private ProductRateOffset calculateLowestCeilingHighestFloorOffset(List<ProductRateOffset> existingCalculatedOffsetsForParent) {
        //Copy the first product rate offset.
        ProductRateOffset copy = existingCalculatedOffsetsForParent.get(0).copy();

        copy.setSundayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSundayOffsetValueFloor).sorted(Comparator.nullsLast((o1, o2) -> o2.compareTo(o1))).findFirst().orElse(null));
        copy.setSundayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSundayOffsetValueCeiling).sorted(Comparator.nullsLast((o1, o2) -> o1.compareTo(o2))).findFirst().orElse(null));
        copy.setSundayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSundayOffsetValueCeiling).sorted(Comparator.nullsLast((o1, o2) -> o1.compareTo(o2))).findFirst().orElse(null));
        copy.setMondayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getMondayOffsetValueFloor).sorted(Comparator.nullsLast((o1, o2) -> o2.compareTo(o1))).findFirst().orElse(null));
        copy.setMondayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getMondayOffsetValueCeiling).sorted(Comparator.nullsLast((o1, o2) -> o1.compareTo(o2))).findFirst().orElse(null));
        copy.setTuesdayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getTuesdayOffsetValueFloor).sorted(Comparator.nullsLast((o1, o2) -> o2.compareTo(o1))).findFirst().orElse(null));
        copy.setTuesdayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getTuesdayOffsetValueCeiling).sorted(Comparator.nullsLast((o1, o2) -> o1.compareTo(o2))).findFirst().orElse(null));
        copy.setWednesdayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getWednesdayOffsetValueFloor).sorted(Comparator.nullsLast((o1, o2) -> o2.compareTo(o1))).findFirst().orElse(null));
        copy.setWednesdayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getWednesdayOffsetValueCeiling).sorted(Comparator.nullsLast((o1, o2) -> o1.compareTo(o2))).findFirst().orElse(null));
        copy.setThursdayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getThursdayOffsetValueFloor).sorted(Comparator.nullsLast((o1, o2) -> o2.compareTo(o1))).findFirst().orElse(null));
        copy.setThursdayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getThursdayOffsetValueCeiling).sorted(Comparator.nullsLast((o1, o2) -> o1.compareTo(o2))).findFirst().orElse(null));
        copy.setFridayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getFridayOffsetValueFloor).sorted(Comparator.nullsLast((o1, o2) -> o2.compareTo(o1))).findFirst().orElse(null));
        copy.setFridayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getFridayOffsetValueCeiling).sorted(Comparator.nullsLast((o1, o2) -> o1.compareTo(o2))).findFirst().orElse(null));
        copy.setSaturdayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSaturdayOffsetValueFloor).sorted(Comparator.nullsLast((o1, o2) -> o2.compareTo(o1))).findFirst().orElse(null));
        copy.setSaturdayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSaturdayOffsetValueCeiling).sorted(Comparator.nullsLast((o1, o2) -> o1.compareTo(o2))).findFirst().orElse(null));

        return copy;
    }

    @SuppressWarnings("squid:S3776")
    public List<List<ProductRateOffset>> createSeasonOffsetListBasedOnBranch(List<Product> productBranch, List<List<ProductRateOffset>> productSeasonBranchOffsetsLists, List<ProductRateOffset> allBranchOffsets) {
        List<List<ProductRateOffset>> calculatedOffsetsList = new ArrayList<>();

        for (List<ProductRateOffset> productSeasonBranchOffsets : productSeasonBranchOffsetsLists) {
            List<ProductRateOffset> calculatedOffsets = new ArrayList<>();
            for (ProductRateOffset productSeasonBranchOffset : productSeasonBranchOffsets) {
                //Skip if matching season already exists
                ProductRateOffset existingCalculatedSeason = calculatedOffsets.stream().filter(productRateOffset ->
                        productRateOffset.getStartDate().equals(productSeasonBranchOffset.getStartDate())
                                && productRateOffset.getEndDate().equals(productSeasonBranchOffset.getEndDate())).findFirst().orElse(null);
                if (existingCalculatedSeason == null) {
                    //If it doesnt already exist, create a new calculated season using its start/end dates and using default values if they dont exist for the product
                    LocalDate startDate = productSeasonBranchOffset.getStartDate();
                    LocalDate endDate = productSeasonBranchOffset.getEndDate();
                    AgileRatesDTARange agileRatesDTARange = productSeasonBranchOffset.getAgileRatesDTARange();
                    AccomClass accomClass = productSeasonBranchOffset.getAccomClass();

                    for (Product branchProduct : productBranch) {
                        //skip primary product as it doesnt have Product Rate Offsets
                        if (!branchProduct.isSystemDefaultOrIndependentProduct()) {
                            //Find matching season; if not find default for product
                            ProductRateOffset productSeasonOrDefaultRateOffset = productSeasonBranchOffsets.stream().filter(productRateOffset ->
                                            productRateOffset.getProduct().equals(branchProduct)
                                                    && productRateOffset.getStartDate().equals(startDate)
                                                    && productRateOffset.getEndDate().equals(endDate)).findFirst()
                                    .orElseGet(() -> findDefaultProductRateOffset(branchProduct, allBranchOffsets));

                            ProductRateOffset existingCalculatedOffset = getExistingSeasonCalculatedOffset(calculatedOffsets, branchProduct, productSeasonOrDefaultRateOffset);

                            if (existingCalculatedOffset != null) {
                                existingCalculatedOffset.setProduct(productSeasonOrDefaultRateOffset.getProduct());
                                existingCalculatedOffset.setSundayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getSundayOffsetValueCeiling(), productSeasonOrDefaultRateOffset.getSundayOffsetValueCeiling()));
                                existingCalculatedOffset.setSundayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getSundayOffsetValueFloor(), productSeasonOrDefaultRateOffset.getSundayOffsetValueFloor()));
                                existingCalculatedOffset.setMondayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getMondayOffsetValueCeiling(), productSeasonOrDefaultRateOffset.getMondayOffsetValueCeiling()));
                                existingCalculatedOffset.setMondayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getMondayOffsetValueFloor(), productSeasonOrDefaultRateOffset.getMondayOffsetValueFloor()));
                                existingCalculatedOffset.setTuesdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getTuesdayOffsetValueCeiling(), productSeasonOrDefaultRateOffset.getTuesdayOffsetValueCeiling()));
                                existingCalculatedOffset.setTuesdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getTuesdayOffsetValueFloor(), productSeasonOrDefaultRateOffset.getTuesdayOffsetValueFloor()));
                                existingCalculatedOffset.setWednesdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getWednesdayOffsetValueCeiling(), productSeasonOrDefaultRateOffset.getWednesdayOffsetValueCeiling()));
                                existingCalculatedOffset.setWednesdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getWednesdayOffsetValueFloor(), productSeasonOrDefaultRateOffset.getWednesdayOffsetValueFloor()));
                                existingCalculatedOffset.setThursdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getThursdayOffsetValueCeiling(), productSeasonOrDefaultRateOffset.getThursdayOffsetValueCeiling()));
                                existingCalculatedOffset.setThursdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getThursdayOffsetValueFloor(), productSeasonOrDefaultRateOffset.getThursdayOffsetValueFloor()));
                                existingCalculatedOffset.setFridayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getFridayOffsetValueCeiling(), productSeasonOrDefaultRateOffset.getFridayOffsetValueCeiling()));
                                existingCalculatedOffset.setFridayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getFridayOffsetValueFloor(), productSeasonOrDefaultRateOffset.getFridayOffsetValueFloor()));
                                existingCalculatedOffset.setSaturdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getSaturdayOffsetValueCeiling(), productSeasonOrDefaultRateOffset.getSaturdayOffsetValueCeiling()));
                                existingCalculatedOffset.setSaturdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getSaturdayOffsetValueFloor(), productSeasonOrDefaultRateOffset.getSaturdayOffsetValueFloor()));
                            } else {
                                ProductRateOffset newCalculatedOffset = productSeasonOrDefaultRateOffset.copy();
                                newCalculatedOffset.setStartDate(startDate);
                                newCalculatedOffset.setEndDate(endDate);
                                newCalculatedOffset.setAccomClass(accomClass);
                                newCalculatedOffset.setAgileRatesDTARange(agileRatesDTARange);
                                calculatedOffsets.add(newCalculatedOffset);
                            }
                        }
                    }
                }
            }
            calculatedOffsetsList.add(calculatedOffsets);
        }
        return calculatedOffsetsList;
    }

    public ProductRateOffset findDefaultProductRateOffset(Product product, List<ProductRateOffset> allBranchOffsets) {
        return allBranchOffsets.stream().filter(productRateOffset -> productRateOffset.getProduct().equals(product)
                && productRateOffset.getStartDate() == null
                && productRateOffset.getEndDate() == null).findFirst().orElse(null);
    }

    public BigDecimal calculateNewOffset(BigDecimal existingOffset, BigDecimal offset) {
        BigDecimal existingValue = BigDecimal.ONE.add(BigDecimalUtil.divide(existingOffset, BigDecimal.valueOf(100)));
        BigDecimal offsetValue = BigDecimal.ONE.add(BigDecimalUtil.divide(offset, BigDecimal.valueOf(100)));
        return BigDecimalUtil.multiply(BigDecimal.ONE.subtract(existingValue.multiply(offsetValue)), new BigDecimal(-100));
    }

    private List<ProductRateOffset> getProductBranchOffsets(List<Product> productBranch, List<ProductRateOffset> allProductRateOffsets) {
        List<Product> branchWithoutPrimaryProduct = productBranch.stream().filter(product -> !product.isSystemDefaultOrIndependentProduct()).collect(Collectors.toList());
        return allProductRateOffsets.stream().filter(productRateOffset -> branchWithoutPrimaryProduct.contains(productRateOffset.getProduct())).collect(Collectors.toList());
    }

    public List<Product> getProductBranch(Product product, List<Product> allProducts) {
        //This method returns the selected product's branch from its position up to the primary product
        if (product.isSystemDefaultOrIndependentProduct()) {
            return Arrays.asList(product);
        }

        //Add selected product first
        List<Product> productBranch = new ArrayList<>();
        productBranch.add(0, product);

        //Add dependent product above it
        Product dependentProduct = getDependentProduct(product, allProducts);
        productBranch.add(0, dependentProduct);

        //Add remaining dependent products up the branch
        while (dependentProduct != null && !dependentProduct.isSystemDefaultOrIndependentProduct()) {
            dependentProduct = getDependentProduct(dependentProduct, allProducts);
            productBranch.add(0, dependentProduct);
        }

        return productBranch;
    }

    private Product getDependentProduct(Product product, List<Product> allProducts) {
        return allProducts.stream().filter(p -> p.getId().equals(product.getDependentProductId())).findFirst().orElse(null);
    }

    public boolean isHierarchyValidSeasonalProducts(List<ProductRateOffset> selectedOffsets, List<ProductRateOffset> relatedOffsets,
                                                    BigDecimal minimumDifference,
                                                    Product selectedProduct, Product relatedProduct, List<String> validationMessages) {
        List<List<ProductRateOffset>> selectedOffsetsBySeason = new ArrayList<>();
        List<List<ProductRateOffset>> relatedOffsetsBySeason = new ArrayList<>();

        if (isSeasonalProduct(selectedOffsets)) {
            selectedOffsetsBySeason = separateOffsetsBySeasons(selectedOffsets);
        } else {
            selectedOffsets.removeIf(offset -> offset.getStartDate() != null);
        }
        if (isSeasonalProduct(relatedOffsets)) {
            relatedOffsetsBySeason = separateOffsetsBySeasons(relatedOffsets);
        } else {
            relatedOffsets.removeIf(offset -> offset.getStartDate() != null);
        }

        if (!selectedOffsetsBySeason.isEmpty() && !relatedOffsetsBySeason.isEmpty()) {
            for (List<ProductRateOffset> selOffsetsForSeason : selectedOffsetsBySeason) {
                boolean isSelDtaOffset = selOffsetsForSeason.get(0).getAgileRatesDTARange() != null;
                boolean isSelRcOffset = selOffsetsForSeason.get(0).getAccomClass() != null;
                boolean isSelDowOffset = selOffsetsForSeason.get(0).allNonOptimizedOffsetsNotEqual();
                if (!doOffsetsOverlapForSeasonalProducts(selOffsetsForSeason, relatedOffsetsBySeason, minimumDifference,
                        isSelDtaOffset, isSelRcOffset, isSelDowOffset, validationMessages)) {
                    return false;
                }
            }
        } else if (!selectedOffsetsBySeason.isEmpty()) {
            return doOffsetsOverlapWithOneSeasonalProduct(selectedOffsetsBySeason, relatedOffsets, relatedProduct, minimumDifference, true, validationMessages);
        } else {
            return doOffsetsOverlapWithOneSeasonalProduct(relatedOffsetsBySeason, selectedOffsets, selectedProduct, minimumDifference, false, validationMessages);
        }
        return true;
    }

    public boolean isHierarchyValidSeasonalProductsForProductsNonImmediateFamilyProducts(List<ProductRateOffset> selectedBranchOffsets, List<ProductRateOffset> relatedBranchOffsets,
                                                                                         BigDecimal minimumDifference,
                                                                                         List<Product> selectedProductBranch, List<Product> relatedProductBranch, List<String> validationMessages) {
        List<List<ProductRateOffset>> generatedSelectedOffsetsBySeason = new ArrayList<>();
        List<List<ProductRateOffset>> generatedRelatedOffsetsBySeason = new ArrayList<>();

        //see if any of the selected branch offsets has seasons
        if (isSeasonalProduct(selectedBranchOffsets)) {
            List<List<ProductRateOffset>> selectedOffsetsBySeason = separateOffsetsBySeasons(selectedBranchOffsets);
            generatedSelectedOffsetsBySeason = createSeasonOffsetListBasedOnBranch(selectedProductBranch, selectedOffsetsBySeason, selectedBranchOffsets);
        } else {
            selectedBranchOffsets.removeIf(offset -> offset.getStartDate() != null);
            //create defaults for comparison
            selectedBranchOffsets = createDefaultOffsetListBasedOnBranch(selectedProductBranch, selectedBranchOffsets);
        }
        if (isSeasonalProduct(relatedBranchOffsets)) {
            List<List<ProductRateOffset>> relatedOffsetsBySeason = separateOffsetsBySeasons(relatedBranchOffsets);
            generatedRelatedOffsetsBySeason = createSeasonOffsetListBasedOnBranch(relatedProductBranch, relatedOffsetsBySeason, relatedBranchOffsets);
        } else {
            relatedBranchOffsets.removeIf(offset -> offset.getStartDate() != null);
            //create defaults for comparison
            relatedBranchOffsets = createDefaultOffsetListBasedOnBranch(relatedProductBranch, relatedBranchOffsets);
        }

        if (!generatedSelectedOffsetsBySeason.isEmpty() && !generatedRelatedOffsetsBySeason.isEmpty()) {
            for (List<ProductRateOffset> selOffsetsForSeason : generatedSelectedOffsetsBySeason) {
                boolean isSelDtaOffset = selOffsetsForSeason.get(0).getAgileRatesDTARange() != null;
                boolean isSelRcOffset = selOffsetsForSeason.get(0).getAccomClass() != null;
                boolean isSelDowOffset = selOffsetsForSeason.get(0).allNonOptimizedOffsetsNotEqual();
                if (!doOffsetsOverlapForSeasonalProducts(selOffsetsForSeason, generatedRelatedOffsetsBySeason, minimumDifference,
                        isSelDtaOffset, isSelRcOffset, isSelDowOffset, validationMessages)) {
                    return false;
                }
            }
        } else if (!generatedSelectedOffsetsBySeason.isEmpty()) {
            return doOffsetsOverlapWithOneSeasonalProduct(generatedSelectedOffsetsBySeason, relatedBranchOffsets, relatedProductBranch.get(relatedProductBranch.size() - 1), minimumDifference, true, validationMessages);
        } else {
            return doOffsetsOverlapWithOneSeasonalProduct(generatedRelatedOffsetsBySeason, selectedBranchOffsets, selectedProductBranch.get(selectedProductBranch.size() - 1), minimumDifference, false, validationMessages);
        }
        return true;
    }

    private boolean doOffsetsOverlapForSeasonalProducts(List<ProductRateOffset> selOffsetsForSeason, List<List<ProductRateOffset>> relatedOffsetsBySeason,
                                                        BigDecimal minimumDifference, boolean isSelDtaOffset, boolean isSelRcOffset, boolean isSelDowOffset, List<String> validationMessages) {
        // both products are seasonal
        for (List<ProductRateOffset> relOffsetsForSeason : relatedOffsetsBySeason) {
            boolean isRelDtaOffset = relOffsetsForSeason.get(0).getAgileRatesDTARange() != null;
            boolean isRelRcOffset = relOffsetsForSeason.get(0).getAccomClass() != null;
            boolean isRelDowOffset = relOffsetsForSeason.get(0).allNonOptimizedOffsetsNotEqual();
            selOffsetsForSeason = getNonOverlappingOffsets(selOffsetsForSeason, relOffsetsForSeason);
            relOffsetsForSeason = getNonOverlappingOffsets(relOffsetsForSeason, selOffsetsForSeason);
            if (selOffsetsForSeason.isEmpty() || relOffsetsForSeason.isEmpty()) {
                return false;
            }
            if (!separateOffsetsAndDetermineIfHierarchyIsValid(selOffsetsForSeason, relOffsetsForSeason,
                    minimumDifference,
                    Arrays.asList(isSelDtaOffset, isSelRcOffset, isSelDowOffset),
                    Arrays.asList(isRelDtaOffset, isRelRcOffset, isRelDowOffset), validationMessages)) {
                return false;
            }
        }

        return true;
    }

    private boolean doOffsetsOverlapWithOneSeasonalProduct(List<List<ProductRateOffset>> seasonalProductOffsets,
                                                           List<ProductRateOffset> nonSeasonalProductOffsets, Product nonSeasonalProduct,
                                                           BigDecimal minimumDifference,
                                                           boolean isSelectedProductSeasonal, List<String> validationMessages) {
        for (List<ProductRateOffset> offsetsForSeason : seasonalProductOffsets) {
            boolean isSelDtaOffset = offsetsForSeason.get(0).getAgileRatesDTARange() != null;
            boolean isSelRcOffset = offsetsForSeason.get(0).getAccomClass() != null;
            boolean isSelDowOffset = offsetsForSeason.get(0).allNonOptimizedOffsetsNotEqual();
            offsetsForSeason = getNonOverlappingOffsets(offsetsForSeason, nonSeasonalProductOffsets);
            nonSeasonalProductOffsets = getNonOverlappingOffsets(nonSeasonalProductOffsets, offsetsForSeason);
            if (offsetsForSeason.isEmpty() || nonSeasonalProductOffsets.isEmpty()) {
                return false;
            }
            if (isSelectedProductSeasonal && !separateOffsetsAndDetermineIfHierarchyIsValid(offsetsForSeason, nonSeasonalProductOffsets,
                    minimumDifference,
                    Arrays.asList(isSelDtaOffset, isSelRcOffset, isSelDowOffset),
                    Arrays.asList(nonSeasonalProduct.isDtaOffset(), nonSeasonalProduct.isRoomClassOffset(), nonSeasonalProduct.isDowOffset()), validationMessages)
                    ||
                    !isSelectedProductSeasonal && !separateOffsetsAndDetermineIfHierarchyIsValid(nonSeasonalProductOffsets, offsetsForSeason,
                            minimumDifference,
                            Arrays.asList(nonSeasonalProduct.isDtaOffset(), nonSeasonalProduct.isRoomClassOffset(), nonSeasonalProduct.isDowOffset()),
                            Arrays.asList(isSelDtaOffset, isSelRcOffset, isSelDowOffset), validationMessages)
            ) {
                return false;
            }
        }
        return true;
    }

    private boolean separateOffsetsAndDetermineIfHierarchyIsValid(List<ProductRateOffset> selectedOffsets, List<ProductRateOffset> relatedOffsets,
                                                                  BigDecimal minimumDifference,
                                                                  List<Boolean> selOffsetFlags, List<Boolean> relOffsetFlags, List<String> validationMessages) {
        // seloffsets -> dta, rc, dow
        // reloffsets -> dta, rc, dow
        if (selOffsetFlags.get(0) && relOffsetFlags.get(0)) {
            if (selOffsetFlags.get(1) && relOffsetFlags.get(1)) {
                // handles dta/rc/dow and dta/rc offset combinations
                return verifyOffsetsOverlapForDtaRcDowOffsets(selectedOffsets, relatedOffsets, minimumDifference, selOffsetFlags.get(2), relOffsetFlags.get(2), validationMessages);
            } else if (selOffsetFlags.get(2) && relOffsetFlags.get(2)) {
                // handles dta/dow offset combinations
                return verifyOffsetsOverlapForDtaDowOffsets(selectedOffsets, relatedOffsets, minimumDifference, validationMessages);
            } else {
                // handles dta offsets
                return verifyOffsetsOverlapForDtaOffsets(selectedOffsets, relatedOffsets, minimumDifference, validationMessages);
            }
        } else if (selOffsetFlags.get(1) && relOffsetFlags.get(1)) {
            // handles rc/dow and rc offset combinations
            return verifyOffsetsOverlapForRcDowOffsets(selectedOffsets, relatedOffsets, minimumDifference, selOffsetFlags.get(2), relOffsetFlags.get(2), validationMessages);
        } else if (selOffsetFlags.get(2) && relOffsetFlags.get(2)) {
            // handles dow offsets
            return verifyOffsetsOverlap(selectedOffsets.get(0), relatedOffsets.get(0), minimumDifference, validationMessages);
        } else {
            // handles non matching combinations of offsets
            return areAllOffsetsValid(selectedOffsets, relatedOffsets, minimumDifference, validationMessages);
        }
    }

    private boolean verifyOffsetsOverlapForDtaRcDowOffsets(List<ProductRateOffset> selectedOffsets, List<ProductRateOffset> relatedOffsets,
                                                           BigDecimal minimumDifference,
                                                           boolean isSelectedDowOffset, boolean isRelatedDowOffset, List<String> validationMessages) {
        if (isSelectedDowOffset && isRelatedDowOffset) {
            for (ProductRateOffset selectedOffset : selectedOffsets) {
                ProductRateOffset relatedOffset = relatedOffsets
                        .stream()
                        .filter(o -> selectedOffset.getAgileRatesDTARange().equals(o.getAgileRatesDTARange()))
                        .filter(o -> selectedOffset.getAccomClass().equals(o.getAccomClass()))
                        .findAny().orElse(null);
                if (!verifyOffsetsOverlap(selectedOffset, relatedOffset, minimumDifference, validationMessages)) {
                    return false;
                }
            }
        } else {
            for (ProductRateOffset selectedOffset : selectedOffsets) {
                ProductRateOffset relatedOffset = relatedOffsets
                        .stream()
                        .filter(o -> selectedOffset.getAgileRatesDTARange().equals(o.getAgileRatesDTARange()))
                        .filter(o -> selectedOffset.getAccomClass().equals(o.getAccomClass()))
                        .findAny().orElse(null);
                if (relatedOffset != null && !verifyOffsetsOverlap(selectedOffset, relatedOffset, minimumDifference, validationMessages)) {
                    return false;
                }
            }
        }
        return true;
    }

    public List<ProductRateOffset> getNonOverlappingOffsets(List<ProductRateOffset> firstOffsetsList, List<ProductRateOffset> secondOffsetsList) {
        // remove non overlapping RCs
        if (!firstOffsetsList.isEmpty() && !secondOffsetsList.isEmpty()
                && firstOffsetsList.get(0).getAccomClass() != null && secondOffsetsList.get(0).getAccomClass() != null) {
            firstOffsetsList = getOffsetListWithOverlappingRC(firstOffsetsList, secondOffsetsList);
        }

        // remove non overlapping DTAs
        if (!firstOffsetsList.isEmpty() && !secondOffsetsList.isEmpty()
                && firstOffsetsList.get(0).getAgileRatesDTARange() != null && secondOffsetsList.get(0).getAgileRatesDTARange() != null) {
            firstOffsetsList = getOffsetListWithOverlappingDTA(firstOffsetsList, secondOffsetsList);
        }

        return firstOffsetsList;
    }

    private boolean verifyOffsetsOverlapForDtaDowOffsets(List<ProductRateOffset> selectedOffsets,
                                                         List<ProductRateOffset> relatedOffsets,
                                                         BigDecimal minimumDifference, List<String> validationMessages) {
        for (ProductRateOffset selectedOffset : selectedOffsets) {
            ProductRateOffset relatedOffset = relatedOffsets
                    .stream()
                    .filter(o -> selectedOffset.getAgileRatesDTARange().equals(o.getAgileRatesDTARange()))
                    .findAny().orElse(null);
            if (!verifyOffsetsOverlap(selectedOffset, relatedOffset, minimumDifference, validationMessages)) {
                return false;
            }
        }
        return true;
    }

    private boolean verifyOffsetsOverlapForDtaOffsets(List<ProductRateOffset> selectedOffsets,
                                                      List<ProductRateOffset> relatedOffsets,
                                                      BigDecimal minimumDifference,
                                                      List<String> validationMessages) {
        for (ProductRateOffset selectedOffset : selectedOffsets) {
            ProductRateOffset relatedOffset = relatedOffsets
                    .stream()
                    .filter(o -> selectedOffset.getAgileRatesDTARange().equals(o.getAgileRatesDTARange()))
                    .findAny().orElse(null);
            if (relatedOffset != null && !verifyOffsetsOverlap(selectedOffset, relatedOffset, minimumDifference, validationMessages)) {
                return false;
            }
        }
        return true;
    }

    private boolean verifyOffsetsOverlapForRcDowOffsets(List<ProductRateOffset> selectedOffsets, List<ProductRateOffset> relatedOffsets,
                                                        BigDecimal minimumDifference,
                                                        boolean isSelectedDowOffset, boolean isRelatedDowOffset,
                                                        List<String> validationMessages) {
        if (isSelectedDowOffset && isRelatedDowOffset) {
            for (ProductRateOffset selectedOffset : selectedOffsets) {
                ProductRateOffset relatedOffset = relatedOffsets
                        .stream()
                        .filter(o -> selectedOffset.getAccomClass().equals(o.getAccomClass()))
                        .findAny().orElse(null);
                if (!verifyOffsetsOverlap(selectedOffset, relatedOffset, minimumDifference, validationMessages)) {
                    return false;
                }
            }
        } else {
            for (ProductRateOffset selectedOffset : selectedOffsets) {
                ProductRateOffset relatedOffset = relatedOffsets
                        .stream()
                        .filter(o -> selectedOffset.getAccomClass().equals(o.getAccomClass()))
                        .findAny().orElse(null);
                if (relatedOffset != null && !verifyOffsetsOverlap(selectedOffset, relatedOffset, minimumDifference, validationMessages)) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean verifyOffsetsOverlap(ProductRateOffset selectedOffset, ProductRateOffset relatedOffset,
                                        BigDecimal minimumDifference, List<String> validationMessages) {
        ProductRateOffset linkedProductOffset = null;
        ProductRateOffset optimizedProductOffset = null;
        if (selectedOffset.getProduct().isNonOptimizedProduct() || relatedOffset.getProduct().isNonOptimizedProduct()) {
            linkedProductOffset = selectedOffset.getProduct().isNonOptimizedProduct() ? selectedOffset : relatedOffset;
            optimizedProductOffset = selectedOffset.getProduct().isOptimized() ? selectedOffset : relatedOffset;
        }

        if (linkedProductOffset != null) {
            return verifyOffsetsOverlapWithLinkedProduct(linkedProductOffset, optimizedProductOffset, minimumDifference, validationMessages);
        } else {
            return verifyOffsetsOverlapGivenOptimizedProducts(selectedOffset, relatedOffset, minimumDifference, validationMessages);
        }
    }

    private boolean verifyOffsetsOverlapWithLinkedProduct(ProductRateOffset linkedProductOffset, ProductRateOffset optimizedProductOffset,
                                                          BigDecimal minimumDifference, List<String> validationMessages) {
        List<BigDecimal> linkedFloorOffsetVals = getFloorOffsetValues(Arrays.asList(linkedProductOffset));
        List<BigDecimal> optimizedFloorOffsetVals = getFloorOffsetValues(Arrays.asList(optimizedProductOffset));
        List<BigDecimal> optimizedCeilOffsetVals = getCeilingOffsetValues(Arrays.asList(optimizedProductOffset));

        boolean hasMatchingDOW = false;
        for (int i = 0; i < linkedFloorOffsetVals.size(); i++) {
            if (linkedFloorOffsetVals.get(i) != null && optimizedFloorOffsetVals.get(i) != null) {
                hasMatchingDOW = true;
                // 1) the linked product's value must have some overlap of the floor and ceiling of the optimized product's floor and ceiling
                if (!(linkedFloorOffsetVals.get(i).doubleValue() >= optimizedFloorOffsetVals.get(i).doubleValue()
                        || linkedFloorOffsetVals.get(i).doubleValue() <= optimizedCeilOffsetVals.get(i).doubleValue())) {
                    validationMessages.add("invalid.hierarchy.overlap.adjustment.value.linked.product");
                    return false;
                }
                if ((Math.abs(optimizedCeilOffsetVals.get(i).doubleValue() - linkedFloorOffsetVals.get(i).doubleValue())
                        < minimumDifference.doubleValue())) {
                    // 2) if the relationship is linked < optimized there must be at least a difference = minimum difference
                    // between the linked product's offset and optimized product's floor
                    validationMessages.add("invalid.hierarchy.mindiff.value.linked.product");
                    return false;
                }
            }
        }
        return hasMatchingDOW;
    }

    private boolean verifyOffsetsOverlapGivenOptimizedProducts(ProductRateOffset selectedOffset, ProductRateOffset relatedOffset,
                                                               BigDecimal minimumDifference, List<String> validationMessages) {
        List<BigDecimal> selectedFloorOffsetVals = getFloorOffsetValues(Arrays.asList(selectedOffset));
        List<BigDecimal> relatedFloorOffsetVals = getFloorOffsetValues(Arrays.asList(relatedOffset));
        List<BigDecimal> selectedCeilOffsetVals = getCeilingOffsetValues(Arrays.asList(selectedOffset));
        List<BigDecimal> relatedCeilOffsetVals = getCeilingOffsetValues(Arrays.asList(relatedOffset));

        boolean hasMatchingDOW = false;
        for (int i = 0; i < selectedFloorOffsetVals.size(); i++) {
            if (selectedFloorOffsetVals.get(i) != null && relatedFloorOffsetVals.get(i) != null) {
                hasMatchingDOW = true;
                // 1) floor of selected + minimum difference must be <= floor of related
                // 2) ceiling of selected + minimum difference must be <= ceiling of related
                // 3) overlap must exist with an overlap >= minimum difference
                //overlapAmount = Ceiling Of Related Product (Greater than product) - Floor of Selected Product (Less than product)
                double overlapAmount = relatedCeilOffsetVals.get(i).doubleValue() - selectedFloorOffsetVals.get(i).doubleValue();
                if (selectedFloorOffsetVals.get(i).doubleValue() > relatedFloorOffsetVals.get(i).doubleValue()) {
                    validationMessages.add("invalid.hierarchy.min.adjustment.value.linked.product");
                }
                if (selectedCeilOffsetVals.get(i).doubleValue() > relatedCeilOffsetVals.get(i).doubleValue()) {
                    validationMessages.add("invalid.hierarchy.max.adjustment.value.linked.product");
                }
                if (isOverlapNotExist(selectedFloorOffsetVals.get(i).doubleValue(), selectedCeilOffsetVals.get(i).doubleValue(),
                        relatedFloorOffsetVals.get(i).doubleValue(), relatedCeilOffsetVals.get(i).doubleValue())) {
                    validationMessages.add("invalid.hierarchy.overlap.adjustment.value.linked.product");
                }
                if (overlapAmount < minimumDifference.doubleValue()) {
                    validationMessages.add("invalid.hierarchy.mindiff.value.linked.product");
                }
                if (overlapAmount < 0 || CollectionUtils.isNotEmpty(validationMessages)) {
                    return false;
                }
            }
        }
        return hasMatchingDOW;
    }

    @VisibleForTesting
	public
    boolean isOverlapNotExist(double selectedFloorValue, double selectedCeilingValue, double relatedFloorValue, double relatedCeilingValue) {
        return (!isValueBetween(relatedFloorValue, selectedFloorValue, selectedCeilingValue)
                && !isValueBetween(relatedCeilingValue, selectedFloorValue, selectedCeilingValue))
                && (!isValueBetween(selectedFloorValue, relatedFloorValue, relatedCeilingValue)
                && !isValueBetween(selectedCeilingValue, relatedFloorValue, relatedCeilingValue));
    }

    public boolean isValueBetween(Double value, Double lowerBound, Double upperBound) {
        return lowerBound <= value && value <= upperBound;
    }

    public boolean areAllOffsetsValid(List<ProductRateOffset> selectedOffsets, List<ProductRateOffset> relatedOffsets,
                                      BigDecimal minimumDifference, List<String> validationMessages) {
        for (ProductRateOffset selectedOffset : selectedOffsets) {
            for (ProductRateOffset relatedOffset : relatedOffsets) {
                if (!verifyOffsetsOverlap(selectedOffset, relatedOffset, minimumDifference, validationMessages)) {
                    return false;
                }
            }
        }
        return true;
    }

    private List<ProductRateOffset> getOffsetListWithOverlappingRC(List<ProductRateOffset> offsetsToUpdate, List<ProductRateOffset> relatedOffsets) {
        List<AccomClass> relatedAccomClasses = relatedOffsets.stream()
                .map(ProductRateOffset::getAccomClass)
                .collect(Collectors.toList());

        return offsetsToUpdate.stream()
                .filter(offset -> relatedAccomClasses.contains(offset.getAccomClass()))
                .collect(Collectors.toList());
    }

    private List<ProductRateOffset> getOffsetListWithOverlappingDTA(List<ProductRateOffset> offsetsToUpdate, List<ProductRateOffset> relatedOffsets) {
        List<AgileRatesDTARange> relatedDTARanges = relatedOffsets.stream()
                .map(ProductRateOffset::getAgileRatesDTARange)
                .collect(Collectors.toList());

        return offsetsToUpdate.stream()
                .filter(offset -> relatedDTARanges.contains(offset.getAgileRatesDTARange()))
                .collect(Collectors.toList());
    }

    private List<BigDecimal> getFloorOffsetValues(List<ProductRateOffset> offsetValues) {
        List<BigDecimal> floorOffsetValues = new ArrayList<>();
        for (ProductRateOffset offset : offsetValues) {
            floorOffsetValues.add(offset.getSundayOffsetValueFloor());
            floorOffsetValues.add(offset.getMondayOffsetValueFloor());
            floorOffsetValues.add(offset.getTuesdayOffsetValueFloor());
            floorOffsetValues.add(offset.getWednesdayOffsetValueFloor());
            floorOffsetValues.add(offset.getThursdayOffsetValueFloor());
            floorOffsetValues.add(offset.getFridayOffsetValueFloor());
            floorOffsetValues.add(offset.getSaturdayOffsetValueFloor());
        }
        return floorOffsetValues;
    }

    private List<BigDecimal> getCeilingOffsetValues(List<ProductRateOffset> offsetValues) {
        List<BigDecimal> ceilOffsetValues = new ArrayList<>();
        for (ProductRateOffset offset : offsetValues) {
            ceilOffsetValues.add(offset.getSundayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getMondayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getTuesdayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getWednesdayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getThursdayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getFridayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getSaturdayOffsetValueCeiling());
        }
        return ceilOffsetValues;
    }

    @ForTesting
    public void deleteAllProductGroups() {
        tenantCrudService.deleteAll(ProductGroup.class);
        tenantCrudService.deleteAll(AgileRatesProductGroup.class);
    }

    @ForTesting
    public void deleteAllProductHierarchies() {
        tenantCrudService.deleteAll(ProductMinPriceDiff.class);
        tenantCrudService.deleteAll(ProductHierarchy.class);
    }

    @ForTesting
    public void deleteAllNonDefaultProducts(int productId) {
        tenantCrudService.executeUpdateByNativeQuery("delete Product_Package where Product_ID > " + productId);
        tenantCrudService.executeUpdateByNativeQuery("delete Product_Rate_Code where Product_ID > " + productId);
        tenantCrudService.executeUpdateByNativeQuery("delete Product_AT where Product_ID > " + productId);
        tenantCrudService.executeUpdateByNativeQuery("delete Product_Rate_Offset where Product_ID > " + productId);
        tenantCrudService.executeUpdateByNativeQuery("Delete from Agile_Product_Restriction_Association where Product_ID > " + productId);
        tenantCrudService.executeUpdateByNativeQuery("delete Product where Product_ID > " + productId);
    }

    public void createAgileProductLV1ForOperaProperty() {
        LOGGER.info("Create agile product LV1 started for property: " + PacmanWorkContextHelper.getPropertyCode());
        if (isOperaPropertyInHilton()) {
            tenantCrudService.executeUpdateByNativeQuery(CREATE_AGILE_PRODUCT_LV1, QueryParameter.with("userID", PacmanWorkContextHelper.getUserId()).parameters());
            registerSyncEvent();
            LOGGER.info("Agile product LV1 created successfully for property: " + PacmanWorkContextHelper.getPropertyCode());
        } else {
            LOGGER.info("Property: " + PacmanWorkContextHelper.getPropertyCode() + " is non opera, LV1 not created");
        }
    }

    public boolean isProductAssociationBackupExists() {
        String backedUpTable = tenantCrudService.findByNativeQuerySingleResult(FIND_TABLE_BY_NAME, null, row -> (String) row[0]);
        return backedUpTable != null;
    }

    public boolean isValidNonESAgileProductExists() {
        return !tenantCrudService.findByNativeQuery(GET_ALL_AGILE_RATES_EXCEPT_EXTENDED_STAY_AND_LV1).isEmpty();
    }

    public boolean isAgileProductRestrictionAssociationExists() {
        Integer count = tenantCrudService.findByNativeQuerySingleResult(GET_COUNT_AGILE_PRODUCT_RESTRICTION_ASSOCIATION, null);
        return count > 0;
    }

    public boolean isGenericFlowEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_GENERIC_PRODUCT_FLOW);
    }

    public void restoreProductAssociationFromBackup() {
        LOGGER.info("Started restoring the product association from backup.");
        tenantCrudService.executeUpdateByNativeQuery(RESTORE_PRODUCT_ASSOCIATION_FROM_BACKUP);
    }

    public void backupProductAssociation() {
        if (isAgileProductRestrictionAssociationExists()) {
            LOGGER.info("Started backup of the product association.");
            tenantCrudService.executeUpdateByNativeQuery(BACKUP_PRODUCT_ASSOCIATION);
        }
    }

    public void restoreRateUnqualifiedForAgileProducts() {
        LOGGER.info("Started restore of the Rate_Unqualified table fro agile products.");
        StringBuilder restoreRateUnqualifiedQuery = getStringBuilderForPopulatingAgileProducts();
        List<Product> existingAgileProductList = getExistingAgileProductList();
        existingAgileProductList.forEach(product -> {
            restoreRateUnqualifiedQuery.append(POPULATE_RATE_UNQUALIFIED.replace(":name", product.getName()));
        });
        tenantCrudService.executeUpdateByNativeQuery(restoreRateUnqualifiedQuery.toString(), QueryParameter.with("userID", PacmanWorkContextHelper.getUserId())
                .and("propertyID", PacmanWorkContextHelper.getPropertyId()).parameters());
        LOGGER.info("Completed restore of the Rate_Unqualified table fro agile products.");
    }

    public String createGlobalAgileProductConfig(List<AgileProductConfig> agileProductConfigList) {
        if (agileProductConfigList == null || agileProductConfigList.isEmpty()) {
            return "Config data not received, no changes made";
        }
        List<AgileProductConfig> existingAgileProductConfigList = getExistingAgileProductConfig();
        agileProductConfigList.forEach(agileProductConfig -> {
            existingAgileProductConfigList.forEach(existingAgileProductConfig -> {
                if (agileProductConfig.equals(existingAgileProductConfig)) {
                    agileProductConfig.setId(existingAgileProductConfig.getId());
                    agileProductConfig.setCreatedByUserId(existingAgileProductConfig.getCreatedByUserId());
                    agileProductConfig.setCreateDate(existingAgileProductConfig.getCreateDate());
                }
            });
        });
        globalCrudService.save(agileProductConfigList);
        return "Global agile product configuration table updated successfully";
    }

    public String deleteGlobalAgileProductConfig(List<AgileProductConfig> agileProductConfigList) {
        if (agileProductConfigList == null || agileProductConfigList.isEmpty()) {
            return "Config data not received, no changes made";
        }
        List<AgileProductConfig> existingAgileProductConfigList = getExistingAgileProductConfig();
        List<AgileProductConfig> finalAgileProductUploadList = new ArrayList<AgileProductConfig>();
        agileProductConfigList.forEach(agileProductConfig -> {
            existingAgileProductConfigList.forEach(existingAgileProductConfig -> {
                if (agileProductConfig.equals(existingAgileProductConfig)) {
                    finalAgileProductUploadList.add(existingAgileProductConfig);
                }
            });
        });
        if (finalAgileProductUploadList != null && !finalAgileProductUploadList.isEmpty()) {
            globalCrudService.delete(finalAgileProductUploadList);
            return "Agile product config deleted successfully: " + finalAgileProductUploadList.toString();
        }
        return "No config found with given parameters";
    }

    public String createRecoveryState(String recoveryStateName) {
        if (recoveryStateName == null || recoveryStateName.isEmpty()) {
            return "Recovery state name is blank, no changes made";
        }
        RecoveryState recoveryState = globalCrudService.findByNamedQuerySingleResult(RecoveryState.BY_NAME, QueryParameter.with("name", recoveryStateName).parameters());
        if (recoveryState == null) {
            globalCrudService.executeUpdateByNativeQuery(INSERT_RECOVERY_STATE, QueryParameter.with("name", recoveryStateName).and("userID", PacmanWorkContextHelper.getUserId()).parameters());
            return "Recovery state " + recoveryStateName + " created successfully.";
        } else {
            return "Recovery state " + recoveryStateName + " already exists";
        }
    }

    public String setDefaultRecoveryState(String recoveryStateName) {
        RecoveryState recoveryState = globalCrudService.findByNamedQuerySingleResult(RecoveryState.BY_NAME, QueryParameter.with("name", recoveryStateName).parameters());
        if (recoveryState != null) {
            if (!recoveryState.isSystemDefault()) {
                globalCrudService.executeUpdateByNativeQuery(SET_DEFAULT_RECOVERY_STATE, QueryParameter.with("name", recoveryStateName).and("userID", PacmanWorkContextHelper.getUserId()).parameters());
                return "Recovery state " + recoveryStateName + " set as default";
            } else {
                return "Recovery state " + recoveryStateName + " is already set as default";
            }
        } else {
            return "Recovery state " + recoveryStateName + " not found";
        }
    }

    public String deleteRecoveryState(String recoveryStateName) {
        RecoveryState recoveryState = globalCrudService.findByNamedQuerySingleResult(RecoveryState.BY_NAME, QueryParameter.with("name", recoveryStateName).parameters());
        if (recoveryState == null) {
            return "Recovery state " + recoveryStateName + " not found";
        } else {
            if (!recoveryState.isSystemDefault()) {
                globalCrudService.delete(recoveryState);
                return "Recovery state " + recoveryStateName + " deleted successfully";
            } else {
                return "Cannot delete recovery state " + recoveryStateName + " as it is set as default";
            }
        }
    }

    public String deleteExistingAgileProductsHilton() {
        LOGGER.info("Delete agile products started for property: " + PacmanWorkContextHelper.getPropertyCode());
        List<Product> oldAgileProductList = getExistingAgileProductList();

        if (!oldAgileProductList.isEmpty()) {
            deleteAgileProducts(oldAgileProductList);
        } else {
            LOGGER.warn("Old agile products not found for property: " + PacmanWorkContextHelper.getPropertyCode());
            return "Old agile products not found for property: " + PacmanWorkContextHelper.getPropertyCode();
        }

        LOGGER.info("Old agile products deleted successfully for property: " + PacmanWorkContextHelper.getPropertyCode());
        return "Old agile products deleted successfully for property: " + PacmanWorkContextHelper.getPropertyCode();
    }

    public String deleteExistingAgileProductsAndCreateNewProductsHilton(String recoveryState, BigDecimal packageValue) {
        LOGGER.info("Delete and recreate products started for property: " + PacmanWorkContextHelper.getPropertyCode());
        String message = deleteAndRecreateProducts(recoveryState, packageValue);
        setForceFullDecisions();
        return message;
    }

    public String reconfigureAgileProductsHilton(String recoveryState, BigDecimal packageValue) {
        LOGGER.info("Products reconfiguration started for property: " + PacmanWorkContextHelper.getPropertyCode());
        String message = reconfigureAgileProducts(recoveryState, packageValue);
        setForceFullDecisions();
        return message;
    }

    public String initiateRecoveryStateTransition(String recoveryState, BigDecimal packageValue) {
        LOGGER.info("Recovery state transition started for property: " + PacmanWorkContextHelper.getPropertyCode());
        return propertyService.isPropertyVirtual(PacmanWorkContextHelper.getPropertyId()) ?
                reconfigureAgileProductsForVirtualProperty(recoveryState, packageValue) :
                reconfigureAgileProducts(recoveryState, packageValue);
    }

    private String reconfigureAgileProducts(String recoveryState, BigDecimal packageValue) {
        StringBuilder message = new StringBuilder(StringUtils.EMPTY);
        recoveryState = validateRecoveryState(recoveryState, message);
        String globalArea = getGlobalArea();
        validateGlobalArea(globalArea);
        String externalSystem = getExternalSystem();
        String brandCode = getBrandCode();

        List<AgileProductConfig> newAgileProductConfigList = getNewAgileProductConfigListWithReplacingBrandCode(recoveryState, externalSystem, globalArea, brandCode);
        List<Product> oldAgileProductList = getExistingAgileProductList();

        List<Product> productsToBeDeleted = getProductsToBeDeleted(oldAgileProductList, newAgileProductConfigList);
        List<AgileProductConfig> productsToBeCreated = getProductsToBeCreated(oldAgileProductList, newAgileProductConfigList);
        List<AgileProductConfig> productsToBeUpdated = getProductsToBeUpdated(oldAgileProductList, newAgileProductConfigList, null);

        LOGGER.info("Products to be deleted: " + productsToBeDeleted.stream().map(Product::getName).collect(Collectors.toList()));
        message.append("Products to be deleted: ").append(productsToBeDeleted.stream().map(Product::getName).collect(Collectors.toList())).append("\n");
        LOGGER.info("Products to be created: " + productsToBeCreated);
        message.append("Products to be created: ").append(productsToBeCreated).append("\n");
        LOGGER.info("Products to be updated: " + productsToBeUpdated);
        message.append("Products to be updated: ").append(productsToBeUpdated).append("\n");

        List<String> hospitalityRoomTypeCodesList = getHospitalityRoomTypeCodesList(true);

        deleteUnwantedProducts(message, productsToBeDeleted);
        createNewProducts(recoveryState, packageValue, message, brandCode, productsToBeCreated, hospitalityRoomTypeCodesList, null);
        updateExistingProducts(recoveryState, packageValue, message, brandCode, productsToBeUpdated, hospitalityRoomTypeCodesList, null, null);

        return message.toString();
    }

    private void deleteUnwantedProducts(StringBuilder message, List<Product> productsToBeDeleted) {
        if (!productsToBeDeleted.isEmpty()) {
            LOGGER.info("Delete agile products started for property: " + PacmanWorkContextHelper.getPropertyCode());
            deleteAgileProducts(productsToBeDeleted);
            LOGGER.info("Old agile products deleted successfully for property: " + PacmanWorkContextHelper.getPropertyCode());
            message.append("Old agile products deleted successfully for property: ").append(PacmanWorkContextHelper.getPropertyCode()).append("\n");
        } else {
            LOGGER.warn("Old agile products not found for property: " + PacmanWorkContextHelper.getPropertyCode());
            message.append("Old agile products not found for property: ").append(PacmanWorkContextHelper.getPropertyCode()).append("\n");
        }
    }

    private void createNewProducts(String recoveryState, BigDecimal packageValue, StringBuilder message, String brandCode, List<AgileProductConfig> productsToBeCreated, List<String> hospitalityRoomTypeCodesList, VirtualPropertyMapping vpm) {
        if (!productsToBeCreated.isEmpty()) {
            message.append("Agile product creation flow:-\n");
            LOGGER.info("Create agile products started for property: " + PacmanWorkContextHelper.getPropertyCode() + ", and Recovery State: " + recoveryState);
            StringBuilder finalQueryForPopulatingAgileProducts = getStringBuilderForPopulatingAgileProducts();
            if (null != vpm) {
                agileRatesConfigurationVPService.runAgilePopulationPerPropertyAttributeSet(message, finalQueryForPopulatingAgileProducts, true, packageValue,
                        new VPRecoveryStateTransitionRequest(true, vpm, productsToBeCreated, VPRecoveryStateTransitionRequest.RequestType.CREATE, recoveryState, null));
            } else {
                message.append(createAgileProducts(finalQueryForPopulatingAgileProducts, hospitalityRoomTypeCodesList, brandCode, false, packageValue, externalSystemHelper.isHilstar(), externalSystemHelper.isPCRS(), false, message.toString(), productsToBeCreated)).append("\n");
            }
        } else {
            LOGGER.warn("New agile products not found for property: " + PacmanWorkContextHelper.getPropertyCode());
            message.append("New agile products not found for property: ").append(PacmanWorkContextHelper.getPropertyCode()).append("\n");
        }
    }

    private void updateExistingProducts(String recoveryState, BigDecimal packageValue, StringBuilder message, String brandCode, List<AgileProductConfig> productsToBeUpdated, List<String> hospitalityRoomTypeCodesList, VirtualPropertyMapping vpm,
                                        Set<String> cleanedUpProducts) {
        if (!productsToBeUpdated.isEmpty()) {
            message.append("Agile product update flow:-\n");
            LOGGER.info("Update agile products started for property: " + PacmanWorkContextHelper.getPropertyCode() + ", and Recovery State: " + recoveryState);
            StringBuilder finalQueryForPopulatingAgileProducts = getStringBuilderForPopulatingAgileProducts();
            if (null != vpm) {
                agileRatesConfigurationVPService.runAgilePopulationPerPropertyAttributeSet(message, finalQueryForPopulatingAgileProducts, true, packageValue,
                        new VPRecoveryStateTransitionRequest(true, vpm, productsToBeUpdated, VPRecoveryStateTransitionRequest.RequestType.UPDATE, recoveryState, cleanedUpProducts));
                cleanedUpProducts.addAll(productsToBeUpdated.stream().map(productConfig -> productConfig.getName()).collect(toSet()));
            } else {
                message.append(createAgileProducts(finalQueryForPopulatingAgileProducts, hospitalityRoomTypeCodesList, brandCode, false, packageValue, externalSystemHelper.isHilstar(), externalSystemHelper.isPCRS(), true, message.toString(), productsToBeUpdated)).append("\n");
            }
        } else {
            LOGGER.warn("No product update required for property: " + PacmanWorkContextHelper.getPropertyCode());
            message.append("No product update required for property: ").append(PacmanWorkContextHelper.getPropertyCode()).append("\n");
        }
    }

    private List<AgileProductConfig> getNewAgileProductConfigList(String recoveryState, String externalSystem, String globalArea) {
        List<AgileProductConfig> newAgileProductConfigList = getAgileProductConfig(recoveryState, externalSystem, globalArea);
        validateAgileProductConfigList(recoveryState, globalArea, externalSystem, newAgileProductConfigList);
        return newAgileProductConfigList;
    }

    private List<AgileProductConfig> getNewAgileProductConfigListWithReplacingBrandCode(String recoveryState, String externalSystem, String globalArea, String brandCode) {
        List<AgileProductConfig> newAgileProductConfigList = getNewAgileProductConfigList(recoveryState, externalSystem, globalArea);
        newAgileProductConfigList.forEach(agileProductConfig -> agileProductConfig.setName(agileProductConfig.getName().replace(":brandCode", brandCode)));
        return newAgileProductConfigList;
    }

    private List<Product> getProductsToBeDeleted(List<Product> oldAgileProductList, List<AgileProductConfig> newAgileProductConfigList) {
        return oldAgileProductList.stream().filter(oldAgileProduct -> !isProductPresentInNewProductConfigList(newAgileProductConfigList, oldAgileProduct)).collect(Collectors.toList());
    }

    private List<AgileProductConfig> getProductsToBeCreated(List<Product> oldAgileProductList, List<AgileProductConfig> newAgileProductConfigList) {
        return newAgileProductConfigList.stream().filter(newAgileProductConfig -> !isProductConfigPresentInOldProductList(oldAgileProductList, newAgileProductConfig, null)).collect(Collectors.toList());
    }

    private List<AgileProductConfig> getProductsToBeUpdated(List<Product> oldAgileProductList, List<AgileProductConfig> newAgileProductConfigList, VirtualPropertyMapping vpm) {
        List<AgileProductConfig> productsToBeUpdated = new ArrayList<AgileProductConfig>();
        newAgileProductConfigList.forEach(newAgileProductConfig -> {
            if (isProductConfigPresentInOldProductList(oldAgileProductList, newAgileProductConfig, vpm)) {
                productsToBeUpdated.add(newAgileProductConfig);
            }
        });
        return productsToBeUpdated;
    }

    private boolean isProductConfigPresentInOldProductList(List<Product> oldAgileProductList, AgileProductConfig newAgileProductConfig, VirtualPropertyMapping vpm) {
        String brandCode = null != vpm ? vpm.getBrandCode() : EMPTY_STRING;
        return oldAgileProductList.stream().anyMatch(oldAgileProduct -> oldAgileProduct.getName().trim().equalsIgnoreCase(newAgileProductConfig.getName().replace(":brandCode", brandCode).trim()));
    }

    private boolean isProductPresentInNewProductConfigList(List<AgileProductConfig> newAgileProductConfigList, Product oldAgileProduct) {
        return newAgileProductConfigList.stream().anyMatch(newAgileProductConfig -> newAgileProductConfig.getName().trim().equalsIgnoreCase(oldAgileProduct.getName().trim()));
    }

    public String getBrandCode() {
        String brandCode = StringUtils.EMPTY;
        if (externalSystemHelper.isPCRS()) {
            brandCode = getBrandCodeForWorkContext();
            validateBrandCode(brandCode);
        }
        return brandCode;
    }

    private List<Product> getExistingAgileProductList() {
        List<Product> oldAgileProductList = new ArrayList<>();
        List<String> globalConfigProductNameList = globalCrudService.findByNamedQuery(AgileProductConfig.GET_ALL_PRODUCT_NAMES);
        List<String> oldAgileProductNameList = new ArrayList<String>();
        oldAgileProductNameList.addAll(globalConfigProductNameList.stream().map(agileProduct -> agileProduct.replace(":brandCode", "%")).collect(Collectors.toList()));
        oldAgileProductNameList = oldAgileProductNameList.stream().distinct().collect(Collectors.toList());

        oldAgileProductNameList.forEach(oldAgileProductName -> {
            Product oldAgileProduct = tenantCrudService.findByNamedQuerySingleResult(Product.GET_ALL_LIKE_NAME, QueryParameter.with("name", oldAgileProductName).parameters());
            if (oldAgileProduct != null) {
                oldAgileProductList.add(oldAgileProduct);
            }
        });

        return oldAgileProductList;
    }

    private String getExternalSystem() {
        if (externalSystemHelper.isHilstar()) {
            return HCRS;
        }

        if (externalSystemHelper.isPCRS()) {
            return PCRS;
        }

        LOGGER.error("Invalid external system " + externalSystemHelper.getExternalSystemAsString() + " for property: " + PacmanWorkContextHelper.getPropertyCode() + ", no agile products created");
        throw new TetrisException(ErrorCode.EXTERNAL_SYSTEM_INVALID,
                String.format("Invalid external system %s for property: %s, agile products not created", externalSystemHelper.getExternalSystemAsString(), PacmanWorkContextHelper.getPropertyCode()));
    }

    private void validateBrandCode(String finalBrandCode) {
        if (finalBrandCode == null || finalBrandCode.isEmpty()) {
            LOGGER.error("Brand code not found for property: " + PacmanWorkContextHelper.getPropertyCode() + ", no agile products created");
            throw new TetrisException(ErrorCode.BRAND_CODE_NOT_FOUND,
                    String.format("Brand code not found for property: %s, agile products not created", PacmanWorkContextHelper.getPropertyCode()));
        }
    }

    private String deleteAndRecreateProducts(String recoveryState, BigDecimal packageValue) {
        String deleteMessage = deleteExistingAgileProductsHilton();
        String createMessage = createAgileProductsForHiltonCPMigration(recoveryState, packageValue);
        return deleteMessage + "\n" + createMessage;
    }


    public String createAgileProductsForHiltonCPMigration() {
        return createAgileProductsForHiltonCPMigration(null, null);
    }

    public String createAgileProductsForHiltonCPMigration(String recoveryState, BigDecimal packageValue) {
        LOGGER.info("Create agile products started for property: " + PacmanWorkContextHelper.getPropertyCode() + ", and Recovery State: " + recoveryState);
        StringBuilder message = new StringBuilder(StringUtils.EMPTY);

        recoveryState = validateRecoveryState(recoveryState, message);

        String globalArea = getGlobalArea();
        validateGlobalArea(globalArea);

        StringBuilder finalQueryForPopulatingAgileProducts = getStringBuilderForPopulatingAgileProducts();
        List<String> hospitalityRoomTypeCodesList = getHospitalityRoomTypeCodesList(false);

        populateAgileProductsAsPerExternalSystem(finalQueryForPopulatingAgileProducts, hospitalityRoomTypeCodesList, recoveryState, globalArea, message, packageValue);
        return message.toString();
    }

    private String validateRecoveryState(String recoveryState, StringBuilder message) {
        if (recoveryState == null) {
            recoveryState = globalCrudService.findByNamedQuerySingleResult(CPMigrationConfig.GET_RECOVERY_STATE_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
            if (recoveryState == null) {
                throw new TetrisException(ErrorCode.CONFIGURATION_MISSING_EXCEPTION, "Recovery state is not configured for this property. Please do so from Hilton CP Migration UI.");
            }
        }
        if (!getRecoveryStates().contains(recoveryState)) {
            if (!recoveryStateExistsInAgileConfigTable(recoveryState)) {
                throw new TetrisException(ErrorCode.CONFIGURATION_MISSING_EXCEPTION, "No product found in Agile_Product_Config with recovery state: " + recoveryState);
            }
            createRecoveryState(recoveryState);
            LOGGER.warn("Recovery state: " + recoveryState + " not found for property: " + PacmanWorkContextHelper.getPropertyCode() + ", creating entry in Recovery_State table.");
            message.setLength(0);
            message.append("Recovery state: ").append(recoveryState).append(" not found for property: ").append(PacmanWorkContextHelper.getPropertyCode()).append(", creating entry in Recovery_State table.");
        }
        return recoveryState;
    }

    private void validateGlobalArea(String globalArea) {
        if (globalArea == null || globalArea.isEmpty()) {
            LOGGER.error("Global area not assigned for property: " + PacmanWorkContextHelper.getPropertyCode() + ", agile products not created");
            throw new TetrisException(ErrorCode.GLOBAL_AREA_NOT_FOUND,
                    String.format("Global area not assigned for property: %s, agile products not created", PacmanWorkContextHelper.getPropertyCode()));
        }
    }

    public StringBuilder getStringBuilderForPopulatingAgileProducts() {
        StringBuilder finalQueryForPopulatingAgileProducts = new StringBuilder();
        finalQueryForPopulatingAgileProducts.append("----Get caughtup date: ").append("\n");
        finalQueryForPopulatingAgileProducts.append(GET_CAUGHTUP_DATE_BY_PROPERTY);
        finalQueryForPopulatingAgileProducts.append("----Create file metadata if required: ").append("\n");
        finalQueryForPopulatingAgileProducts.append(POPULATE_FILE_METADATA);
        return finalQueryForPopulatingAgileProducts;
    }

    private void populateAgileProductsAsPerExternalSystem(StringBuilder finalQueryForPopulatingAgileProducts, List<String> hospitalityRoomTypeCodesList, String recoveryState, String globalArea, StringBuilder message, BigDecimal packageValue) {
        if (externalSystemHelper.isHilstar()) {
            message.append(populateAgileProducts(finalQueryForPopulatingAgileProducts, hospitalityRoomTypeCodesList, recoveryState, globalArea, null, HCRS, false, packageValue));
        } else if (externalSystemHelper.isPCRS()) {
            String finalBrandCode = getBrandCodeForWorkContext();
            if (finalBrandCode != null && !finalBrandCode.isEmpty()) {
                message.append(populateAgileProducts(finalQueryForPopulatingAgileProducts, hospitalityRoomTypeCodesList, recoveryState, globalArea, finalBrandCode, PCRS, false, packageValue));
            } else {
                LOGGER.error("Brand code not found for property: " + PacmanWorkContextHelper.getPropertyCode() + ", no agile products created");
                throw new TetrisException(ErrorCode.BRAND_CODE_NOT_FOUND,
                        String.format("Brand code not found for property: %s, agile products not created", PacmanWorkContextHelper.getPropertyCode()));
            }
        }
    }

    public String createAgileProductsForHiltonCPMigrationExtendedStay() {
        if (!isExtendedStayProperty()) {
            return "Property: " + PacmanWorkContextHelper.getPropertyCode() + " is not configured as extended stay, no changes made.";
        }

        LOGGER.info("Create extended stay agile products started for property: " + PacmanWorkContextHelper.getPropertyCode());
        String message;
        StringBuilder finalQueryForPopulatingAgileProducts = getStringBuilderForPopulatingAgileProducts();
        List<String> hospitalityRoomTypeCodesList = getHospitalityRoomTypeCodesList(false);
        if (isOperaPropertyInHilton()) {
            LOGGER.info("Property: " + PacmanWorkContextHelper.getPropertyCode() + " is Opera");
            message = "Extended stay opera: ";
        } else {
            LOGGER.info("Property: " + PacmanWorkContextHelper.getPropertyCode() + " is Non Opera");
            message = "Extended stay non opera: ";
        }
        message = message + populateAgileProducts(finalQueryForPopulatingAgileProducts, hospitalityRoomTypeCodesList, null, null, null, null, true, null);
        return message + checkAndEnableDailyLOSUpdateToggleForESProducts();
    }

    public String updateAdjustmentAndDailyLOSForExistingCoreAgileProducts() {
        if (!isExtendedStayProperty()) {
            return "Property: " + PacmanWorkContextHelper.getPropertyCode() + " is not configured as extended stay, no changes made.";
        }
        LOGGER.info("Update adjustment for existing core products for extended stay property started: " + PacmanWorkContextHelper.getPropertyCode());
        String message;
        message = enableAdjustmentToggleIfRequired();
        tenantCrudService.executeUpdateByNativeQuery(UPDATE_ADJUSTMENT_CORE_PRODUCTS);
        LOGGER.info("Successfully updated Send_Adjustment for core products. Property: " + PacmanWorkContextHelper.getPropertyCode());
        message += "\nSuccessfully updated Send_Adjustment for core products. Property: " + PacmanWorkContextHelper.getPropertyCode() + "\n";
        return message + checkAndEnableDailyLOSUpdateToggleForESProducts();
    }

    private String populateAgileProducts(StringBuilder finalQueryForPopulatingAgileProducts, List<String> hospitalityRoomTypeCodesList, String recoveryState, String globalArea, String finalBrandCode, String externalSystem, boolean isExtendedStayProductCreationFlow, BigDecimal packageValue) {
        boolean isHilstarOrExtendedStayProductCreationFlow = (externalSystemHelper.isHilstar() || isExtendedStayProductCreationFlow);
        String message = StringUtils.EMPTY;
        List<AgileProductConfig> agileProductConfigList;

        if (!isExtendedStayProductCreationFlow) {
            agileProductConfigList = getAgileProductConfig(recoveryState, externalSystem, globalArea);
        } else {
            agileProductConfigList = getExtendedStayAgileProductConfig();
        }

        validateAgileProductConfigList(recoveryState, globalArea, externalSystem, agileProductConfigList);

        return createAgileProducts(finalQueryForPopulatingAgileProducts, hospitalityRoomTypeCodesList, finalBrandCode, isExtendedStayProductCreationFlow, packageValue, isHilstarOrExtendedStayProductCreationFlow, externalSystemHelper.isPCRS(), false, message, agileProductConfigList);
    }

    private void validateAgileProductConfigList(String recoveryState, String globalArea, String externalSystem, List<AgileProductConfig> agileProductConfigList) {
        if (agileProductConfigList == null || agileProductConfigList.isEmpty()) {
            LOGGER.warn("No product found in Agile_Product_Config table with recovery state: " + recoveryState + " external system: " + externalSystem + " global area: " + globalArea);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "No product found in Agile_Product_Config table with recovery state: " + recoveryState + " external system: " + externalSystem + " global area: " + globalArea);
        }
    }

    public String createAgileProducts(StringBuilder finalQueryForPopulatingAgileProducts, List<String> hospitalityRoomTypeCodesList, String finalBrandCode, boolean isExtendedStayProductCreationFlow, BigDecimal packageValue, boolean isHilstarOrExtendedStayProductCreationFlow, boolean isPCRS, boolean isProductUpdateFlow, String message, List<AgileProductConfig> agileProductConfigList) {
        if (!isExtendedStayProductCreationFlow) {
            message = createOrUpdateBreakFastPackageForHilton(packageValue);
            message += enableAdjustmentToggleIfRequired();
        }

        generateFinalQueryForPopulatingAgileProducts(finalQueryForPopulatingAgileProducts, agileProductConfigList, isHilstarOrExtendedStayProductCreationFlow, isPCRS, isExtendedStayProductCreationFlow, isProductUpdateFlow, finalBrandCode);
        generateFinalQueryForDependentProductAndHierarchy(finalQueryForPopulatingAgileProducts, agileProductConfigList);

        if (isHilstarOrExtendedStayProductCreationFlow) {
            tenantCrudService.executeUpdateByNativeQuery(finalQueryForPopulatingAgileProducts.toString(), QueryParameter.with("userID", PacmanWorkContextHelper.getUserId()).and("propertyID", PacmanWorkContextHelper.getPropertyId())
                    .and("hospitalityRoomTypeCodes", hospitalityRoomTypeCodesList).parameters());
        } else if (isPCRS) {
            tenantCrudService.executeUpdateByNativeQuery(finalQueryForPopulatingAgileProducts.toString().replace(":brandCode", finalBrandCode), QueryParameter.with("userID", PacmanWorkContextHelper.getUserId()).and("propertyID", PacmanWorkContextHelper.getPropertyId())
                    .and("hospitalityRoomTypeCodes", hospitalityRoomTypeCodesList).parameters());
        }

        tenantCrudService.executeUpdateByNativeQuery("exec usp_update_product_mindiff 1");

        if (!isExtendedStayProductCreationFlow) {
            if (isHilstarOrExtendedStayProductCreationFlow) {
                checkForInvalidProducts(agileProductConfigList, EMPTY_STRING, false);
                updateProductFloorRatesForHiltonRST(agileProductConfigList, EMPTY_STRING);
            } else if (isPCRS) {
                checkForInvalidProducts(agileProductConfigList, finalBrandCode, false);
                updateProductFloorRatesForHiltonRST(agileProductConfigList, finalBrandCode);
            }
            if (isRDLEnabled()) {
                List<AgileProductConfig> rdlProductList = agileProductConfigList.stream().filter(config -> config.getWebrateType() != WebrateTypeEnum.WEBRATE_NA).collect(toList());
                message += createProductWebratesForHiltonCpmigration(rdlProductList);
            }
            registerSyncEvent();
        }

        LOGGER.info("Agile products created successfully for property: " + PacmanWorkContextHelper.getPropertyCode());
        return message + "Agile products created successfully for property: " + PacmanWorkContextHelper.getPropertyCode();
    }

    public void createPackageChargeType(AgileRatesPackage agileRatesPackage) {
        if (agileRatesPackage.getId() != null) {
            tenantCrudService.executeUpdateByNamedQuery(AgileRatesPackageChargeType.DELETE_DEFAULT_BY_AGILE_RATES_PACKAGE_ID,
                    QueryParameter.with(AgileRatesPackageChargeType.AGILE_RATES_PACKAGE_ID, agileRatesPackage.getId()).parameters());
            saveDefaultAgileRatesPackage(agileRatesPackage, new ArrayList<>());
        }
    }

    private String createProductWebratesForHiltonCpmigration(List<AgileProductConfig> rdlProductList) {
        StringBuilder message = new StringBuilder("");
        for (AgileProductConfig apc : rdlProductList) {
            Product product = getProductByName(apc.getName());
            AgileRatesProductConfigurationDTO dto = new AgileRatesProductConfigurationDTO(product);
            dto.setRateType(apc.getWebrateType().getCode());
            dto.setRateTypeLOSMax(apc.getWebrateLosMax());
            dto.setRateTypeLOSMin(apc.getWebrateLosMin());

            if (validateRdlForDuplicate(apc.getWebrateLosMin(), apc.getWebrateLosMax(), product.getId(), dto.getRateType())) {
                deleteWebrateTypeProductForProductId(product.getId());
                saveWebrateTypeDataForLinkedProduct(dto);
                LOGGER.info("created webrate" + product.getName());
            } else {
                message.append(" Skipping Webrate creation for ").append(product.getName()).append("due to invalid data\n");
            }
        }
        return message.toString();
    }

    public boolean validateRdlForDuplicate(Integer rateTypeLOSMin, Integer rateTypeLOSMax, Integer productId, String rateType) {
        WebrateType webrateType = findWebrateTypeByRateTypeCode(rateType);
        Integer currLOSMin = rateTypeLOSMin == -1 ? 1 : rateTypeLOSMin;
        Integer currLOSMax = rateTypeLOSMax == -1 ? 365 : rateTypeLOSMax;

        if (webrateType == null || currLOSMax < currLOSMin) {
            return false;
        }

        List<WebrateTypeProductDTO> minMaxLOSForEachProductId = findMinMaxLOSForEachPropertyIdByWebrateTypeId(webrateType.getId());

        for (WebrateTypeProductDTO dto : minMaxLOSForEachProductId) {
            if (!dto.getProductId().equals(productId) &&
                    minMaxLOSAreOverlapped(currLOSMin, currLOSMax, dto.getMinLOS(), dto.getMaxLOS())) {
                return false;
            }
        }
        return true;
    }

    private boolean minMaxLOSAreOverlapped(Integer minProd1, Integer maxProd1, Integer minProd2, Integer maxProd2) {
        return ((minProd1 <= maxProd2) && (maxProd1 >= minProd2));
    }

    public String enableAdjustmentToggleIfRequired() {
        boolean isSendAdjustmentEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED);
        if (!isSendAdjustmentEnabled) {
            configParamsService.addParameterValue(configParamsService.propertyNode(PacmanWorkContextHelper.getWorkContext()), FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED.value(), "true");
            LOGGER.info("pacman.feature.HiltonOptionToSendAdjustmentForAgileEnabled toggle set to true");
            return "pacman.feature.HiltonOptionToSendAdjustmentForAgileEnabled toggle set to true\n";
        } else {
            LOGGER.info("pacman.feature.HiltonOptionToSendAdjustmentForAgileEnabled toggle is already true");
            return "pacman.feature.HiltonOptionToSendAdjustmentForAgileEnabled toggle is already true\n";
        }
    }

    private List<AgileProductConfig> getAgileProductConfig(String recoveryState, String externalSystem, String globalArea) {
        List<AgileProductConfig> agileProductConfigList = globalCrudService.findByNamedQuery(AgileProductConfig.GET_ALL_BY_RECOVERY_STATE_EXTERNAL_SYSTEM_GLOBAL_AREA,
                QueryParameter.with("recoveryState", recoveryState).and("externalSystem", externalSystem).and("globalArea", globalArea).parameters());

        agileProductConfigList.forEach(agileProductConfig -> {
            globalCrudService.detach(agileProductConfig);
        });
        return agileProductConfigList;
    }

    public String updatePackageValueForHilton(){
      return createOrUpdateBreakFastPackageForHilton(getPackageValueFromCPMigrationConfig());
    }

    public String createOrUpdateBreakFastPackageForHilton(BigDecimal packageValue) {
        AgileRatesPackage existingAgileRatesPackage = getExistingAgileRatesPackageByName(BREAKFAST_PACKAGE_NAME);
        if (existingAgileRatesPackage == null) {
            if (packageValue == null) {
                packageValue = getPackageValueFromCPMigrationConfig();
            }
            if (packageValue != null) {
                AgileRatesPackage agileRatesPackage = createAgileRatesPackage(packageValue);
                createPackageChargeType(agileRatesPackage);
                LOGGER.info("Product package created with name: " + BREAKFAST_PACKAGE_NAME + " & value: " + packageValue);
                return "Product package created with name: " + BREAKFAST_PACKAGE_NAME + " & value: " + packageValue + "\n";
            } else {
                LOGGER.info("No package value configured for this property on Hilton CP Migration UI");
                return "No package value configured for this property on Hilton CP Migration UI\n";
            }
        }
        if (packageValue != null) {
            existingAgileRatesPackage.setOffsetValue(packageValue);
            updatePackageChargeType(existingAgileRatesPackage);
            return BREAKFAST_PACKAGE_NAME + " Package updated with value: " + existingAgileRatesPackage.getOffsetValue() + "\n";
        }

        LOGGER.info("Package with name: " + BREAKFAST_PACKAGE_NAME + " already exists with value: " + existingAgileRatesPackage.getOffsetValue());
        return "Package with name: " + BREAKFAST_PACKAGE_NAME + " already exists with value: " + existingAgileRatesPackage.getOffsetValue() + "\n";

    }

    public BigDecimal getPackageValueFromCPMigrationConfig() {
        return globalCrudService.findByNamedQuerySingleResult(CPMigrationConfig.GET_PACKAGE_VALUE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private void updatePackageChargeType(AgileRatesPackage existing) {
        List<AgileRatesPackageChargeType> defaultChargeTypes = getAgileRatesPackageChargeTypes(existing);
        defaultChargeTypes.stream().filter(p -> p.getOccupancyType() == OccupancyType.EXTRA_ADULT).findFirst().ifPresent(
                ct -> AgileRatesPackageChargeType.setOffsetValue(ct, existing.getOffsetValue())
        );
        tenantCrudService.save(defaultChargeTypes);
    }

    private List<AgileRatesPackageChargeType> getAgileRatesPackageChargeTypes(AgileRatesPackage agileRatesPackage) {
        return tenantCrudService.findByNamedQuery(AgileRatesPackageChargeType.GET_DEFAULT_BY_AGILE_RATES_PACKAGE_ID, QueryParameter.with("agileRatesPackageId", agileRatesPackage.getId()).parameters());
    }

    private List<AgileProductConfig> getExtendedStayAgileProductConfig() {
        List<ExtendedStayRateMapping> extendedStayRateMappingList = extendedStayRateMappingImportService.getAllExistingMappings(PacmanWorkContextHelper.getPropertyId());
        if (!extendedStayRateMappingList.isEmpty()) {
            List<AgileProductConfig> agileProductConfigList = new ArrayList<AgileProductConfig>();
            int productSuffix = getProductSuffix();
            int productDescSuffix = 1;
            for (String rateLevel : extendedStayRateMappingList.stream().map(ExtendedStayRateMapping::getNewRateLevel).distinct().collect(Collectors.toList())) {
                agileProductConfigList.add(mapExtendedStayConfigToAgileProductConfig(extendedStayRateMappingList, rateLevel, productSuffix++, productDescSuffix++));
            }
            return agileProductConfigList;
        } else {
            LOGGER.warn("No rate mapping found in Extended_Stay_Rate_Mapping table for property: " + PacmanWorkContextHelper.getPropertyCode());
            throw new TetrisException(ErrorCode.CONFIGURATION_MISSING_EXCEPTION, "No rate mapping found in Extended_Stay_Rate_Mapping table for property: " + PacmanWorkContextHelper.getPropertyCode());
        }
    }

    public int getProductSuffix() {
        return isOperaPropertyInHilton() ? 2 : 1;
    }

    public AgileProductConfig mapExtendedStayConfigToAgileProductConfig(List<ExtendedStayRateMapping> extendedStayRateMappingList, String rateLevel, int productSuffix, int productDescSuffix) {
        AgileProductConfig agileProductConfig = new AgileProductConfig();
        agileProductConfig.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        agileProductConfig.setType(AgileRatesProductTypeEnum.FENCED_AND_NO_PACKAGE.getValue());
        agileProductConfig.setName(EXTENDED_STAY_PRODUCT_NAME + productSuffix);
        agileProductConfig.setSystemDefault(false);
        agileProductConfig.setDescription(EXTENDED_STAY_PRODUCT_DESCRIPTION + productDescSuffix);
        agileProductConfig.setMinDTA(0);
        agileProductConfig.setMaxDTA(365);
        agileProductConfig.setRestrictions(false);
        agileProductConfig.setOffsetForExtraAdult(false);
        agileProductConfig.setOffsetForExtraChild(false);
        agileProductConfig.setDowOffset(false);
        agileProductConfig.setRoomClassOffset(false);
        agileProductConfig.setDtaOffset(false);
        agileProductConfig.setUpload(true);
        agileProductConfig.setStatus(Status.ACTIVE);
        agileProductConfig.setDefaultInactive(false);
        agileProductConfig.setOptimized(true);
        agileProductConfig.setRoundingRule(RoundingRule.PRICE_ROUNDING);
        agileProductConfig.setPubliclyAvailable(true);
        agileProductConfig.setMinimumPriceChange(BIG_DECIMAL_ZERO);
        agileProductConfig.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
        agileProductConfig.setFixedAboveBar(false);
        agileProductConfig.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        agileProductConfig.setCentrallyManaged(false);
        agileProductConfig.setIsOverridable(OverridableProductEnum.ALLOW_OVERRIDES);
        agileProductConfig.setFloorType(FloorType.RELATIVE_TO_PRIMARY_FLOOR);
        agileProductConfig.setFloorPercentage(DEFAULT_FLOOR_PERCENTAGE);

        String rateCodes = rateLevel;
        int minLOS = 0;
        int maxLOS = 0;
        for (ExtendedStayRateMapping extendedStayRateMapping : extendedStayRateMappingList.stream().filter(obj -> obj.getNewRateLevel().equals(rateLevel)).collect(Collectors.toList())) {
            if (extendedStayRateMapping.getOldRateLevel() != null) {
                rateCodes += "," + extendedStayRateMapping.getOldRateLevel();
            }
            minLOS = extendedStayRateMapping.getMinLos();
            maxLOS = extendedStayRateMapping.getMaxLos();
        }
        agileProductConfig.setRateCodes(rateCodes);
        agileProductConfig.setMinLOS(minLOS);
        agileProductConfig.setMaxLOS(maxLOS);

        int tierCount = (int) extendedStayRateMappingList.stream().map(ExtendedStayRateMapping::getExtendedStayTier).distinct().count();
        if (tierCount == 3) {
            switch (productDescSuffix) {
                case 1:
                    agileProductConfig.setMinOffset(new BigDecimal("-20.00"));
                    agileProductConfig.setMaxOffset(new BigDecimal("-5.00"));
                    break;
                case 2:
                    agileProductConfig.setMinOffset(new BigDecimal("-30.00"));
                    agileProductConfig.setMaxOffset(new BigDecimal("-10.00"));
                    break;
                case 3:
                    agileProductConfig.setMinOffset(new BigDecimal("-35.00"));
                    agileProductConfig.setMaxOffset(new BigDecimal("-15.00"));
                    break;
                default:
                    agileProductConfig.setMinOffset(BIG_DECIMAL_ZERO);
                    agileProductConfig.setMaxOffset(BIG_DECIMAL_ZERO);
            }
        } else if (tierCount == 2) {
            switch (productDescSuffix) {
                case 1:
                    agileProductConfig.setMinOffset(new BigDecimal("-20.00"));
                    agileProductConfig.setMaxOffset(new BigDecimal("-5.00"));
                    break;
                case 2:
                    agileProductConfig.setMinOffset(new BigDecimal("-30.00"));
                    agileProductConfig.setMaxOffset(new BigDecimal("-10.00"));
                    break;
                default:
                    agileProductConfig.setMinOffset(BIG_DECIMAL_ZERO);
                    agileProductConfig.setMaxOffset(BIG_DECIMAL_ZERO);
            }
        } else if (tierCount == 1) {
            agileProductConfig.setMinOffset(new BigDecimal("-20.00"));
            agileProductConfig.setMaxOffset(new BigDecimal("-5.00"));
        } else {
            agileProductConfig.setMinOffset(BIG_DECIMAL_ZERO);
            agileProductConfig.setMaxOffset(BIG_DECIMAL_ZERO);
        }

        return agileProductConfig;
    }

    private void generateFinalQueryForPopulatingAgileProducts(StringBuilder finalQueryForPopulatingAgileProducts, List<AgileProductConfig> agileProductConfigList, boolean isHilstarOrExtendedStayProductCreationFlow, boolean isPCRS, boolean isExtendedStayProductCreationFlow, boolean isProductUpdateFlow, String finalBrandCode) {
        for (AgileProductConfig agileProductConfig : agileProductConfigList) {
            finalQueryForPopulatingAgileProducts.append("--Product: ").append(agileProductConfig.getName()).append("\n");
            generateQueryForProductTable(finalQueryForPopulatingAgileProducts, agileProductConfig, isExtendedStayProductCreationFlow, isProductUpdateFlow, false);

            if (isHilstarOrExtendedStayProductCreationFlow) {
                finalQueryForPopulatingAgileProducts.append("----ProductRateCode: ").append(agileProductConfig.getName()).append("\n");
                generateQueryForProductRateCode(finalQueryForPopulatingAgileProducts, agileProductConfig, POPULATE_PRODUCT_RATE_CODE);
                if (isExtendedStayProductCreationFlow) {
                    finalQueryForPopulatingAgileProducts.append("----ProductRateCode_UpdateMigrationDateOfNewExtendedStayRateCodes: ").append(agileProductConfig.getName()).append("\n");
                    generateQueryForUpdatingEffectiveDate(finalQueryForPopulatingAgileProducts, agileProductConfig);
                }
            } else if (isPCRS) {
                finalQueryForPopulatingAgileProducts.append("----ProductRateCode: ").append(agileProductConfig.getName()).append("\n");
                generateQueryForProductRateCode(finalQueryForPopulatingAgileProducts, agileProductConfig, POPULATE_PRODUCT_RATE_CODE_PCRS);
            }

            generateQueryForProductPackageIfRequired(finalQueryForPopulatingAgileProducts, agileProductConfig);

            finalQueryForPopulatingAgileProducts.append("----ProductAT: ").append(agileProductConfig.getName()).append("\n");
            generateQueryForProductAT(finalQueryForPopulatingAgileProducts, agileProductConfig);

            finalQueryForPopulatingAgileProducts.append("----ProductRateOffset: ").append(agileProductConfig.getName()).append("\n");
            generateQueryForProductRateOffset(finalQueryForPopulatingAgileProducts, agileProductConfig);

            if (agileProductConfig.isRestrictions()) {
                if (isHilstarOrExtendedStayProductCreationFlow) {
                    finalQueryForPopulatingAgileProducts.append("----RestrictionAssociation: ").append(agileProductConfig.getName()).append("\n");
                    generateQueryForRestrictionAssociation(finalQueryForPopulatingAgileProducts, agileProductConfig, POPULATE_RESTRICTION_ASSOCIATION);

                    finalQueryForPopulatingAgileProducts.append("----LinkedSRPRestrictionAssociation: ").append(agileProductConfig.getName()).append("\n");
                    generateQueryForLinkedSRPRestrictionAssociation(finalQueryForPopulatingAgileProducts, agileProductConfig, EMPTY_STRING);
                } else if (isPCRS) {
                    finalQueryForPopulatingAgileProducts.append("----RestrictionAssociation: ").append(agileProductConfig.getName()).append("\n");
                    generateQueryForRestrictionAssociation(finalQueryForPopulatingAgileProducts, agileProductConfig, POPULATE_RESTRICTION_ASSOCIATION_PCRS);

                    finalQueryForPopulatingAgileProducts.append("----LinkedSRPRestrictionAssociation: ").append(agileProductConfig.getName()).append("\n");
                    generateQueryForLinkedSRPRestrictionAssociation(finalQueryForPopulatingAgileProducts, agileProductConfig, finalBrandCode);
                }
            }

            finalQueryForPopulatingAgileProducts.append("----RateUnqualified: ").append(agileProductConfig.getName()).append("\n");
            generateQueryForRateUnqualified(finalQueryForPopulatingAgileProducts, agileProductConfig);

            finalQueryForPopulatingAgileProducts.append("\n");
        }
    }

    public void generateFinalQueryForDependentProductAndHierarchy(StringBuilder finalQueryForPopulatingAgileProducts, List<AgileProductConfig> agileProductConfigList) {
        agileProductConfigList.forEach(agileProductConfig -> {
            if (agileProductConfig.getDependentProductName() != null && !agileProductConfig.getDependentProductName().isEmpty()) {
                finalQueryForPopulatingAgileProducts.append("\n----UpdateDependentProduct: ").append(agileProductConfig.getName()).append("\n");
                generateQueryForDependentProduct(finalQueryForPopulatingAgileProducts, agileProductConfig);
            }
            if (agileProductConfig.getToProductHierarchy() != null && !agileProductConfig.getToProductHierarchy().isEmpty()) {
                finalQueryForPopulatingAgileProducts.append("\n----ProductHierarchy: ").append(agileProductConfig.getName()).append("\n");
                generateQueryForProductHierarchy(finalQueryForPopulatingAgileProducts, agileProductConfig);
            }
        });
    }

    // rate qualified and client linked srp mapping
    // srp == product name
    public void checkForInvalidProducts(List<AgileProductConfig> agileProductConfigList, String brandCode, boolean isVirtualProperty) {
        StringBuilder finalQueryForInvalidProducts = new StringBuilder();
        finalQueryForInvalidProducts.append(CREATE_TABLE_VARIABLE_INVALID_PRODUCT);
        insertInvalidProductInQuery(agileProductConfigList, finalQueryForInvalidProducts, isVirtualProperty);
        finalQueryForInvalidProducts.append(READ_FROM_TABLE_VARIABLE_INVALID_PRODUCT);
        updateIsUploadForInvalidProducts(finalQueryForInvalidProducts.toString(), brandCode, isVirtualProperty);
    }

    private void insertInvalidProductInQuery(List<AgileProductConfig> agileProductConfigList, StringBuilder finalQueryForInvalidProducts, boolean isVirtualProperty) {
        String checkRateCodeExistenceInRatchet = isVirtualProperty ? CHECK_RATE_CODE_EXISTENCE_IN_RATCHET_VP : CHECK_RATE_CODE_EXISTENCE_IN_RATCHET;
        agileProductConfigList.forEach(agileProductConfig -> finalQueryForInvalidProducts.append(checkRateCodeExistenceInRatchet.replace(":name", agileProductConfig.getName())));
    }

    private void updateIsUploadForInvalidProducts(String checkRateCodeExitence, String brandCode, boolean isVirtualProperty) {
        StringBuilder finalQueryForUpdatingIsUpload = new StringBuilder();
        List<String> productWithNonExistantSRPInRatchetList = ratchetCrudService.findByNativeQuery(checkRateCodeExitence, QueryParameter.with("propertyCode", PacmanWorkContextHelper.getPropertyCode()).parameters());
        updateIsUploadFalseQuery(finalQueryForUpdatingIsUpload, productWithNonExistantSRPInRatchetList, isVirtualProperty);
        tenantCrudService.executeUpdateByNativeQuery(finalQueryForUpdatingIsUpload.toString().replace(":brandCode", brandCode));
    }

    private void updateIsUploadFalseQuery(StringBuilder finalQueryForUpdatingIsUpload, List<String> productWithNonExistantSRPInRatchetList, boolean isVirtualProperty) {
        runIfElse(isVirtualProperty, () -> productWithNonExistantSRPInRatchetList.forEach(product -> finalQueryForUpdatingIsUpload.append(UPDATE_IS_UPLOAD_FOR_AGILE_PRODUCTS_VP.replace(":productName", product)))
                , () -> productWithNonExistantSRPInRatchetList.forEach(product -> finalQueryForUpdatingIsUpload.append(UPDATE_IS_UPLOAD_FOR_AGILE_PRODUCTS.replace(":productName", "'" + product + "'"))));
    }

    public List<String> getHospitalityRoomTypeCodesList(boolean includeZeroRoomForPricingAndRestrictionsOnly) {
        List<String> list = new ArrayList<>();
        String value = configParamsService.getParameterValue(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode(), IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_TO_INCLUDE, OPERA);
        if (includeZeroRoomForPricingAndRestrictionsOnly) {
            value = getAggregateHospitalityRoomTypeCodesList(value);
        }
        if (!StringUtils.isBlank(value)) {
            String[] individualValues = value.split(",");
            for (String individualValue : individualValues) {
                if (individualValue.trim().length() > 0) {
                    list.add(individualValue.trim());
                }
            }
        }
        if (list.isEmpty()) {
            list.add(EMPTY_STRING);
        }
        return list;
    }

    private String getAggregateHospitalityRoomTypeCodesList(final String roomTypeCodesList) {
        String zeroCapacityRTsWithPricingAndRestrictions = configParamsService.getParameterValue(IntegrationConfigParamName.ZERO_CAPACITY_ROOM_TYPES_REQUIRING_PRICING_AND_RESTRICTIONS, Constants.OPERA);
        if (StringUtils.isBlank(zeroCapacityRTsWithPricingAndRestrictions)) {
            return roomTypeCodesList;
        }
        return StringUtils.isBlank(roomTypeCodesList) ? zeroCapacityRTsWithPricingAndRestrictions : roomTypeCodesList.concat(",").concat(zeroCapacityRTsWithPricingAndRestrictions);
    }

    public void generateQueryForProductTable(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig, boolean isExtendedStayProductCreationFlow, boolean isProductUpdateFlow, boolean isAlreadyCleanedupProduct) {
        if (isExtendedStayProductCreationFlow) {
            generateESQueryForProductTable(finalQueryForPopulatingAgileProducts, agileProductConfig);
            return;
        }

        if (isProductUpdateFlow) {
            generateUpdateQueryForProductTable(finalQueryForPopulatingAgileProducts, agileProductConfig);
            if (!isAlreadyCleanedupProduct) {
                generateCleanUpQueryForProductToBeUpdated(finalQueryForPopulatingAgileProducts, agileProductConfig);
            }
            return;
        }

        String[] productQueryParams = getProductQueryParams();
        String[] productQueryParamValues = getProductQueryParamValues(agileProductConfig);
        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(POPULATE_PRODUCT_TABLE, productQueryParams, productQueryParamValues));
    }

    private void generateESQueryForProductTable(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        CurrencyExchangeService.ExchangeRate exchangeRate = currencyExchangeService.getExchangeRateWrtUSD();
        String floorThresholdInLocalCurrency = exchangeRate.apply(PRODUCT_FLOOR_THRESHOLD_USD).setScale(0, RoundingMode.HALF_UP).toString();
        String[] productQueryParams = getESProductQueryParams();
        String[] productQueryParamValues = getESProductQueryParamValues(agileProductConfig, floorThresholdInLocalCurrency);
        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(POPULATE_ES_PRODUCT_TABLE, productQueryParams, productQueryParamValues));
    }

    private void generateUpdateQueryForProductTable(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        String[] productQueryParams = getProductQueryParams();
        String[] productQueryParamValues = getProductQueryParamValues(agileProductConfig);
        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(UPDATE_PRODUCT_TABLE, productQueryParams, productQueryParamValues));
    }

    private String[] getProductQueryParams() {
        return new String[]{
                ":name", ":code", ":type", ":description", ":minDTA", ":maxDTA",
                ":minLOS", ":maxLOS", ":isExtraAdult", ":isExtraChild", ":isDOWOffset", ":isRCOffset", ":isDTAOffset",
                ":isUpload", ":statusId", ":isDefaultInactive", ":invalidReasonId", ":isOptimized", ":roundingRule",
                ":isPublic", ":minPriceChange", ":offsetMethod", ":isFixedAboveBar", ":sendAdjustment", ":centrallyManaged",
                ":isOverridable", ":floorType", ":floorPercentage", ":rateShoppingLOSMin", ":rateShoppingLOSMax", ":childPricingType"
        };
    }

    private String[] getProductQueryParamValues(AgileProductConfig agileProductConfig) {
        return new String[]{
                agileProductConfig.getName(), agileProductConfig.getCode(), agileProductConfig.getType(), agileProductConfig.getDescription(), agileProductConfig.getMinDTA().toString(), String.valueOf(agileProductConfig.getMaxDTA()),
                agileProductConfig.getMinLOS().toString(), String.valueOf(agileProductConfig.getMaxLOS()), agileProductConfig.isOffsetForExtraAdult() ? TRUE : FALSE, agileProductConfig.isOffsetForExtraChild() ? TRUE : FALSE, agileProductConfig.isDowOffset() ? TRUE : FALSE, agileProductConfig.isRoomClassOffset() ? TRUE : FALSE, agileProductConfig.isDtaOffset() ? TRUE : FALSE,
                agileProductConfig.isUpload() ? TRUE : FALSE, Integer.toString(agileProductConfig.getStatus().getId()), agileProductConfig.isDefaultInactive() ? TRUE : FALSE, agileProductConfig.getInvalidReason() != null ? String.valueOf(agileProductConfig.getInvalidReason().getId()) : "null", agileProductConfig.isOptimized() ? TRUE : FALSE, Integer.toString(agileProductConfig.getRoundingRule().getId()),
                agileProductConfig.isPubliclyAvailable() ? TRUE : FALSE, agileProductConfig.getMinimumPriceChange().toString(), String.valueOf(agileProductConfig.getOffsetMethod().getId()), agileProductConfig.isFixedAboveBar() ? TRUE : FALSE, Integer.toString(agileProductConfig.getDecisionsSentBy().getId()), agileProductConfig.isCentrallyManaged() ? TRUE : FALSE, Integer.toString(agileProductConfig.getIsOverridable().getId()),
                Integer.toString(agileProductConfig.getFloorType().getId()), String.valueOf(agileProductConfig.getFloorPercentage()), Integer.toString(-1), Integer.toString(-1), Integer.toString(1)
        };
    }


    private String[] getESProductQueryParams() {
        return new String[]{
                ":name", ":code", ":type", ":description", ":minDTA", ":maxDTA",
                ":minLOS", ":maxLOS", ":isExtraAdult", ":isExtraChild", ":isDOWOffset", ":isRCOffset", ":isDTAOffset",
                ":isUpload", ":statusId", ":isDefaultInactive", ":invalidReasonId", ":isOptimized", ":roundingRule",
                ":isPublic", ":minPriceChange", ":offsetMethod", ":isFixedAboveBar", ":sendAdjustment",
                ":floorThreshold", ":centrallyManaged", ":isOverridable", ":floorType", ":floorPercentage", ":rateShoppingLOSMin", ":rateShoppingLOSMax", ":childPricingType"
        };
    }

    private String[] getESProductQueryParamValues(AgileProductConfig agileProductConfig, String floorThresholdInLocalCurrency) {
        return new String[]{
                agileProductConfig.getName(), agileProductConfig.getCode(), agileProductConfig.getType(), agileProductConfig.getDescription(), agileProductConfig.getMinDTA().toString(), String.valueOf(agileProductConfig.getMaxDTA()),
                agileProductConfig.getMinLOS().toString(), String.valueOf(agileProductConfig.getMaxLOS()), agileProductConfig.isOffsetForExtraAdult() ? TRUE : FALSE, agileProductConfig.isOffsetForExtraChild() ? TRUE : FALSE, agileProductConfig.isDowOffset() ? TRUE : FALSE, agileProductConfig.isRoomClassOffset() ? TRUE : FALSE, agileProductConfig.isDtaOffset() ? TRUE : FALSE,
                agileProductConfig.isUpload() ? TRUE : FALSE, Integer.toString(agileProductConfig.getStatus().getId()), agileProductConfig.isDefaultInactive() ? TRUE : FALSE, agileProductConfig.getInvalidReason() != null ? String.valueOf(agileProductConfig.getInvalidReason().getId()) : "null", agileProductConfig.isOptimized() ? TRUE : FALSE, Integer.toString(agileProductConfig.getRoundingRule().getId()),
                agileProductConfig.isPubliclyAvailable() ? TRUE : FALSE, agileProductConfig.getMinimumPriceChange().toString(), String.valueOf(agileProductConfig.getOffsetMethod().getId()), agileProductConfig.isFixedAboveBar() ? TRUE : FALSE, Integer.toString(agileProductConfig.getDecisionsSentBy().getId()),
                floorThresholdInLocalCurrency, agileProductConfig.isCentrallyManaged() ? TRUE : FALSE, Integer.toString(agileProductConfig.getIsOverridable().getId()), Integer.toString(agileProductConfig.getFloorType().getId()), String.valueOf(agileProductConfig.getFloorPercentage()), Integer.toString(-1), Integer.toString(-1), Integer.toString(1)
        };
    }

    private void generateCleanUpQueryForProductToBeUpdated(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        finalQueryForPopulatingAgileProducts.append("----CleanUpAccompanyingProductTables: ").append(agileProductConfig.getName()).append("\n");
        String[] productQueryParams = {":name"};
        String[] productQueryParamValues = {agileProductConfig.getName()};
        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(CLEAN_UP_ACCOMPANYING_PRODUCT_TABLES, productQueryParams, productQueryParamValues));
    }

    public void generateQueryForProductRateCode(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig, String populateProductRateCode) {
        String[] productRateCodeQueryParams = {":rateCode", ":name"};
        List<String> rateCodes = Arrays.asList(agileProductConfig.getRateCodes().split(","));

        rateCodes.forEach(rateCode -> {
            String[] productRateCodeQueryParamValues = {rateCode, agileProductConfig.getName()};
            finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(populateProductRateCode, productRateCodeQueryParams, productRateCodeQueryParamValues));
        });
    }

    public void generateQueryForProductPackageIfRequired(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        if (productContainsPackage(agileProductConfig)) {
            finalQueryForPopulatingAgileProducts.append("----ProductPackage: ").append(agileProductConfig.getName()).append("\n");
            generateQueryForProductPackage(finalQueryForPopulatingAgileProducts, agileProductConfig, BREAKFAST_PACKAGE_NAME);
        }
    }

    private boolean productContainsPackage(AgileProductConfig agileProductConfig) {
        return AGILE_RATES_PACKAGED_PRODUCT_TYPE_ENUM_LIST.contains(AgileRatesProductTypeEnum.fromValue(agileProductConfig.getType()));
    }

    private void generateQueryForProductPackage(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig, String packageName) {
        String[] productPackageQueryParams = {":name", ":packageName"};
        String[] productPackageQueryParamValues = {agileProductConfig.getName(), packageName};

        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(POPULATE_PRODUCT_PACKAGE, productPackageQueryParams, productPackageQueryParamValues));
    }

    public void generateQueryForUpdatingEffectiveDate(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        String newRateCode = agileProductConfig.getRateCodes().split(",")[0];
        String[] effectiveDateQueryParams = {":rateCode", ":name"};
        String[] effectiveDateQueryParamValues = {newRateCode, agileProductConfig.getName()};
        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(UPDATE_EFFECTIVE_DATE_EXTENDED_STAY_RATE_CODE, effectiveDateQueryParams, effectiveDateQueryParamValues));
    }

    public void generateQueryForProductAT(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        String[] productATQueryParams = {":name"};
        String[] productATQueryParamValues = {agileProductConfig.getName()};

        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(POPULATE_PRODUCT_AT, productATQueryParams, productATQueryParamValues));
    }

    public void generateQueryForProductRateOffset(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        String[] productRateOffsetQueryParams = {":name", ":minOffset", ":maxOffset"};
        String[] productRateOffsetQueryParamValues = {agileProductConfig.getName(), agileProductConfig.getMinOffset().toString(), agileProductConfig.getMaxOffset().toString()};

        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(POPULATE_PRODUCT_RATE_OFFSET, productRateOffsetQueryParams, productRateOffsetQueryParamValues));
    }

    private void generateQueryForProductHierarchy(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        String[] productHierachyQueryParams = {":name", ":toName"};
        List<String> toProductList = Arrays.asList(agileProductConfig.getToProductHierarchy().split(","));

        toProductList.forEach(toProduct -> {
            String[] productHierarchyQueryParamValues = {agileProductConfig.getName(), toProduct};
            finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(POPULATE_PRODUCT_HIERARCHY, productHierachyQueryParams, productHierarchyQueryParamValues));
        });
    }

    private void generateQueryForDependentProduct(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        String[] dependentProductQueryParams = {":name", ":dependentProductName"};
        String[] dependentProductQueryParamValues = {agileProductConfig.getName(), agileProductConfig.getDependentProductName()};

        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(UPDATE_DEPENDENT_PRODUCT, dependentProductQueryParams, dependentProductQueryParamValues));
    }

    public void generateQueryForRestrictionAssociation(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig, String populateRestrictionAssociation) {
        String[] restrictionAssociationQueryParams = {":rateCode", ":name"};
        List<String> rateCodes = new ArrayList<>(Arrays.asList(agileProductConfig.getRateCodes().split(",")));
        List<String> extraRateCodesForRestrictions;

        if (agileProductConfig.getExtraRateCodesForRestrictions() != null) {
            extraRateCodesForRestrictions = Arrays.asList(agileProductConfig.getExtraRateCodesForRestrictions().split(","));
            rateCodes.addAll(extraRateCodesForRestrictions);
        }

        rateCodes.forEach(rateCode -> {
            String[] restrictionAssociationQueryParamValues = {rateCode, agileProductConfig.getName()};
            finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(populateRestrictionAssociation, restrictionAssociationQueryParams, restrictionAssociationQueryParamValues));
        });
    }

    private void generateQueryForLinkedSRPRestrictionAssociation(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig, String brandCode) {
        String[] linkedSRPRestrictionAssociationQueryParams = {":rateCode", ":name"};
        List<String> rateCodeList = new ArrayList<>(Arrays.asList(agileProductConfig.getRateCodes().split(",")));
        List<String> extraRateCodesForRestrictions;

        if (agileProductConfig.getExtraRateCodesForRestrictions() != null) {
            extraRateCodesForRestrictions = Arrays.asList(agileProductConfig.getExtraRateCodesForRestrictions().split(","));
            rateCodeList.addAll(extraRateCodesForRestrictions);
        }

        for (int i = 0; i < rateCodeList.size(); i += 1) {
            rateCodeList.set(i, LINKED_SRP_PREDICATE.replace(":rateCode", rateCodeList.get(i)));
        }

        String linkedSRPPredicate = Joiner.on(" OR ").join(rateCodeList);
        List<Object[]> linkedSRPList = ratchetCrudService.findByNativeQuery(GET_LINKED_SRP_FOR_PRODUCT.replace(":linkedSRPPredicate", linkedSRPPredicate).replace(":brandCode", brandCode),
                QueryParameter.with("propertyCode", PacmanWorkContextHelper.getPropertyCode()).parameters());

        linkedSRPList.forEach(linkedSRP -> {
            String[] linkedSRPRestrictionAssociationQueryParamValues = {linkedSRP[0].toString(), agileProductConfig.getName()};
            finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(POPULATE_RESTRICTION_ASSOCIATION, linkedSRPRestrictionAssociationQueryParams, linkedSRPRestrictionAssociationQueryParamValues));
        });
    }

    public void generateQueryForRateUnqualified(StringBuilder finalQueryForPopulatingAgileProducts, AgileProductConfig agileProductConfig) {
        String[] rateUnqualifiedQueryParams = {":name"};
        String[] rateUnqualifiedQueryParamValues = {agileProductConfig.getName()};

        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(POPULATE_RATE_UNQUALIFIED, rateUnqualifiedQueryParams, rateUnqualifiedQueryParamValues));
    }

    private void deleteAgileProducts(List<Product> oldAgileProductList) {
        List<Product> products = findAgileRatesProducts();
        Date caughtUpDate = dateService.getCaughtUpDate();

        oldAgileProductList.forEach(oldAgileProduct -> {
            List<Product> productsToUpdateDisplayOrder = products.stream()
                    .filter(p -> p.getDisplayOrder() > oldAgileProduct.getDisplayOrder())
                    .collect(Collectors.toList());
            saveAgileRatesProductHierarchies(Collections.emptyList(), getAllHierarchiesForProduct(oldAgileProduct));
            shiftProductDisplayOrder(productsToUpdateDisplayOrder);
            deleteLinkedProduct(oldAgileProduct, LocalDate.fromDateFields(caughtUpDate));
            deleteProductAssociatedRestrictions(oldAgileProduct);
        });
    }

    private void deleteProductAssociatedRestrictions(Product product) {
        saveAgileProductRestrictionAssociationForFPLOS(product.getId(), Collections.EMPTY_SET);
    }

    public void setForceFullDecisions() {
        boolean forceFullDecisionsEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_FORCE_FULL_DECISIONS.value(), PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());

        if (!forceFullDecisionsEnabled) {
            LOGGER.warn("Force full decisions toggle not enabled for property: " + PacmanWorkContextHelper.getPropertyCode() + ", setting value to true");
            configParamsService.updateParameterValue(FeatureTogglesConfigParamName.ENABLE_FORCE_FULL_DECISIONS.value(), true);
        }

        propertyService.setForceFullDecisions(PacmanWorkContextHelper.getPropertyId(), true);
        LOGGER.info("Force full decisions set for property: " + PacmanWorkContextHelper.getPropertyCode());
    }

    private boolean considerRestrictionSeasonDatesForAgileQualifiedFPLOS() {
        return configParamsService.getBooleanParameterValue(CONSIDER_RESTRICTION_SEASON_DATE_FOR_AGILE_QUALIFIED_FPLOS);
    }

    @ForTesting
    public void deleteModifiedAgileRatesDataForCPGP02ForUITests() {
        tenantCrudService.executeUpdateByNativeQuery("DELETE FROM Product_Rate_Offset_OVR WHERE Created_DTTM > '2020-04-05'");
    }

    public boolean productsExist() {
        return isNotEmpty(findAgileRatesProducts());
    }

    public void createRatesFileMetadata() {
        tenantCrudService.executeUpdateByNativeQuery("IF NOT EXISTS(SELECT * FROM File_Metadata WHERE file_Name = 'Dummy Entry for CP' AND Record_Type_ID = 9) " +
                        " INSERT INTO [dbo].[File_Metadata] \n" +
                        "([Record_Type_ID] ,[IsBDE] ,[File_Name] ,[File_Location] ,[Property_ID] ,[Past_Window_Size] ,[Future_Window_Size] ,[Scope_Start_DT] ,[Scope_End_DT] ,[SnapShot_DT] ,[Snapshot_TM] ,[Prepared_DT] ,[Prepared_TM] ,[Mode] ,[Process_Status_ID] ,[Createdate]) \n" +
                        "VALUES \n" +
                        "(9 ,0 ,'Dummy Entry for CP' ,'Dummy Entry for CP' ,:propertyId ,NULL ,NULL ,NULL ,NULL ,(select max(snapshot_dt) from file_metadata where isbde=1) ,'00:00:00.0000000' ,(select max(snapshot_dt) from file_metadata where isbde=1) ,'00:00:00.0000000' ,NULL ,13 ,getdate())",
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void markLinkedSrpsToProcessInNextProcessing(Set<RateQualified> newAssociatedQualifiedRates, Collection<Integer> newlyAddedQualifiedRateIds) {
        if (considerRestrictionSeasonDatesForAgileQualifiedFPLOS()) {
            List<String> rates = newAssociatedQualifiedRates.stream()
                    .filter(associatedQualifiedRate -> newlyAddedQualifiedRateIds.contains(associatedQualifiedRate.getId()))
                    .map(associatedQualifiedRate -> associatedQualifiedRate.getName())
                    .collect(Collectors.toList());
            if (isNotEmpty(rates)) {
                linkedSRPService.markSrpsToProcessInNextProcessing(rates);
            }
        }
    }

    private String getGlobalArea() {
        return globalCrudService.findByNamedQuerySingleResult(ClientPropertyAttributePairing.GET_GLOBAL_AREA_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<String> getRecoveryStates() {
        return globalCrudService.findByNamedQuery(RecoveryState.GET_ALL_RECOVERY_STATE_NAMES);
    }

    public String getDefaultRecoveryState() {
        return globalCrudService.findByNamedQuerySingleResult(RecoveryState.GET_DEFAULT_RECOVERY_STATE_NAME);
    }

    private String getBrandCodeForWorkContext() {
        String brandCode = globalCrudService.findByNamedQuerySingleResult(ClientPropertyAttributePairing.GET_BRAND_CODE_BY_PROPERTY_ID, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        return "HX".equalsIgnoreCase(brandCode) ? "HH" : brandCode;
    }

    public boolean recoveryStateExistsInAgileConfigTable(String recoveryState) {
        return globalCrudService.findByNativeQuerySingleResult(RECOVERY_STATE_EXISTS_IN_AGILE_CONFIG_TABLE, QueryParameter.with("recoveryState", recoveryState).parameters());
    }

    public List<AgileProductConfig> getExistingAgileProductConfig() {
        return globalCrudService.findAll(AgileProductConfig.class);
    }

    public String checkAndEnableDailyLOSUpdateToggleForESProducts() {
        boolean isDailyLOSUpdateEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.UPDATE_LOS_FROM_SNAP_FOR_ES_PRODUCTS.value(), PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
        if (!isDailyLOSUpdateEnabled) {
            configParamsService.addParameterValue(configParamsService.propertyNode(PacmanWorkContextHelper.getWorkContext()), FeatureTogglesConfigParamName.UPDATE_LOS_FROM_SNAP_FOR_ES_PRODUCTS.value(), "true");
            LOGGER.info("pacman.feature.AgileRatesLOSUpdateFromSnapForES toggle set to true");
            return "\npacman.feature.AgileRatesLOSUpdateFromSnapForES toggle set to true\n";
        } else {
            LOGGER.info("pacman.feature.AgileRatesLOSUpdateFromSnapForES toggle is already true");
            return "\npacman.feature.AgileRatesLOSUpdateFromSnapForES toggle is already true\n";
        }
    }

    public void createProductPackage(StringBuilder finalQueryForPopulatingAgileProducts,
                                     String packageName, String packageDescription, Integer chargeType, Integer offsetMethod, BigDecimal packageValue) {
        String[] productPackageQueryParams = {":packageName", ":packageDescription", ":chargeType", ":offsetMethod", ":packageValue"};
        String[] productPackageQueryParamValues = {packageName, packageDescription, chargeType.toString(), offsetMethod.toString(), packageValue.toString()};

        finalQueryForPopulatingAgileProducts.append(StringUtils.replaceEach(CREATE_AGILE_RATE_PACKAGE, productPackageQueryParams, productPackageQueryParamValues));
    }

    public AgileRatesPackage createAgileRatesPackage(BigDecimal packageValue) {
        AgileRatesPackage agileRatesPackage = new AgileRatesPackage();
        agileRatesPackage.setName(BREAKFAST_PACKAGE_NAME);
        agileRatesPackage.setDescription(BREAKFAST_PACKAGE_DESCRIPTION);
        agileRatesPackage.setChargeType(BREAKFAST_CHARGE_TYPE);
        agileRatesPackage.setOffsetMethod(BREAKFAST_PACKAGE_OFFSET);
        agileRatesPackage.setOffsetValue(packageValue);

        tenantCrudService.save(agileRatesPackage);
        return agileRatesPackage;
    }

    private boolean isOperaPropertyInHilton() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_OPERA_PROPERTY_IN_HILTON.value(), PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
    }

    public boolean isExtendedStayProperty() {
        return configParamsService.getBooleanParameterValue(IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING.value(RATCHET), PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
    }

    public AgileRatesPackage getExistingAgileRatesPackageByName(String packageName) {
        return tenantCrudService.findByNamedQuerySingleResult(AgileRatesPackage.FIND_BY_NAME, QueryParameter.with("name", packageName).parameters());
    }

    @ForTesting
    public void setIsOverridableForAgileProducts(String products, String status) {
        tenantCrudService.executeUpdateByNativeQuery("update [dbo].[Product] set Is_Overridable = " + status + " where Name in " + products);
    }

    public boolean isESRateLevel(String esRateLevel, String propertyCode) {
        List<String> esRateLevelList = getRateLevelsFromExistingESProducts(propertyCode);
        return isNotEmpty(esRateLevelList) && esRateLevelList.contains(esRateLevel);
    }

    private List<String> getRateLevelsFromExistingESProducts(String propertyCode) {
        List<String> esRateLevelList = tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_AGILE_RATE_NAMES_LIKE_DESCRIPTION_PRODUCT_NAME,
                QueryParameter.with("description", EXTENDED_STAY_PRODUCT_DESCRIPTION + "%")
                        .and("propertyCode", propertyCode.concat(UNDERSCORE) + "%")
                        .parameters());
        return esRateLevelList.stream().map(esRtLvl -> esRtLvl.substring(esRtLvl.length() - 1)).collect(Collectors.toList());
    }

    private String getCurrentPropertyCode(boolean isVirtualProperty, String propertyCode) {
        return isVirtualProperty ? propertyCode : PacmanWorkContextHelper.getPropertyCode();
    }

    private boolean validateMinMaxLOSOverlapWithExistingESProduct(List<String[]> esRtLvlRecords, List<Product> existingESProductList, List<String> esRtLvlValues, StringBuilder message, String propertyCode) {
        List<String> esRateLevelList;
        esRateLevelList = getRateLevelsFromExistingESProducts();

        List<Product> excludedESProductList = new ArrayList<>();
        for (String rtLvl : esRateLevelList) {
            if (!esRtLvlValues.contains(rtLvl)) {
                excludedESProductList.add(getExistingESProductByRateLevel(existingESProductList, rtLvl));
            }
        }

        if (excludedESProductList.isEmpty()) {
            return true;
        }
        return !checkIfLOSValuesOverlapForNewAndExistingESProducts(esRtLvlRecords, excludedESProductList, message, propertyCode);
    }

    private boolean checkIfLOSValuesOverlapForNewAndExistingESProducts(List<String[]> esRtLvlRecords, List<Product> excludedESProductList, StringBuilder message, String propertyCode) {
        for (Product excludedESProduct : excludedESProductList) {
            for (String[] esRtLvl : esRtLvlRecords) {
                if ((Integer.parseInt(esRtLvl[MIN_LOS_SNAP_INDEX]) >= excludedESProduct.getMinLOS() && Integer.parseInt(esRtLvl[MIN_LOS_SNAP_INDEX]) <= excludedESProduct.getMaxLOS())
                        || (Integer.parseInt(esRtLvl[MAX_LOS_SNAP_INDEX]) >= excludedESProduct.getMinLOS() && Integer.parseInt(esRtLvl[MAX_LOS_SNAP_INDEX]) <= excludedESProduct.getMaxLOS())) {
                    LOGGER.warn("Existing ES product " + excludedESProduct.getName() + "'s min/max LOS values (" + excludedESProduct.getMinLOS() + "," + excludedESProduct.getMaxLOS().toString() +
                            ") overlap with new values received in snap file for product LV" + esRtLvl[RATE_LEVEL_SNAP_INDEX] + "(" + esRtLvl[MIN_LOS_SNAP_INDEX] + "," + esRtLvl[MAX_LOS_SNAP_INDEX] + ")" +
                            " for property " + propertyCode);
                    message.append("Existing ES product " + excludedESProduct.getName() + "'s min/max LOS values (" + excludedESProduct.getMinLOS() + "," + excludedESProduct.getMaxLOS() +
                            ") overlap with new values received in snap file for product LV" + esRtLvl[RATE_LEVEL_SNAP_INDEX] + "(" + esRtLvl[MIN_LOS_SNAP_INDEX] + "," + esRtLvl[MAX_LOS_SNAP_INDEX] + ")" +
                            " for property " + propertyCode + ".  ");
                    return true;
                }
            }
        }
        return false;
    }

    private List<String> getRateLevelsFromExistingESProducts() {
        List<String> esRateLevelList;
        esRateLevelList = tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_AGILE_RATE_NAMES_LIKE_DESCRIPTION,
                QueryParameter.with("description", EXTENDED_STAY_PRODUCT_DESCRIPTION + "%").parameters());
        esRateLevelList = esRateLevelList.stream().map(esRtLvl -> esRtLvl.substring(esRtLvl.length() - 1)).collect(Collectors.toList());
        return esRateLevelList;
    }

    private Product getExistingESProductByRateLevel(List<Product> existingESProductList, String esRtLvl) {
        return existingESProductList.stream().filter(esProduct -> esProduct.getName().substring(esProduct.getName().length() - 1).equals(esRtLvl)).findFirst().orElse(null);
    }

    private boolean validateMinMaxLOSInSnapFile(List<String[]> esRtLvlRecords, StringBuilder message, String propertyCode) {
        final int LAST_RECORD_INDEX = esRtLvlRecords.size() - 1;

        if (esRtLvlRecords.size() == 2 && !validateConsecutiveRateLevelsInSnapFileIfTwoRecordsReceived(esRtLvlRecords, message, propertyCode)) {
            return false;
        }

        for (int index = 0; index < LAST_RECORD_INDEX; index++) {
            if (!validateMinLOSIsLessThanMaxLOSInSnapFile(esRtLvlRecords, index, message, propertyCode)) {
                return false;
            }
            if (!validateMinLOSIsConsistentAcrossRateLevelsInSnapFile(esRtLvlRecords, index, message, propertyCode)) {
                return false;
            }
            esRtLvlRecords.get(index)[MAX_LOS_SNAP_INDEX] = Integer.toString(Integer.parseInt(esRtLvlRecords.get(index + 1)[MIN_LOS_SNAP_INDEX]) - 1);
        }

        if (!validateMinLOSIsLessThanMaxLOSInSnapFile(esRtLvlRecords, LAST_RECORD_INDEX, message, propertyCode)) {
            return false;
        }

        message.append("ES min/max LOS validation in snap file successful.  ");
        return true;
    }

    private boolean validateMinLOSIsConsistentAcrossRateLevelsInSnapFile(List<String[]> esRtLvlRecords, int index, StringBuilder message, String propertyCode) {
        if (Integer.parseInt(esRtLvlRecords.get(index)[MIN_LOS_SNAP_INDEX]) >= Integer.parseInt(esRtLvlRecords.get(index + 1)[MIN_LOS_SNAP_INDEX])) {
            LOGGER.warn("min LOS value of LV" + esRtLvlRecords.get(index)[RATE_LEVEL_SNAP_INDEX] + " is higher than min LOS of LV" + esRtLvlRecords.get(index + 1)[RATE_LEVEL_SNAP_INDEX] +
                    " in snap file for property " + propertyCode);
            message.append("min LOS value of LV" + esRtLvlRecords.get(index)[RATE_LEVEL_SNAP_INDEX] + " is higher than min LOS of LV" + esRtLvlRecords.get(index + 1)[RATE_LEVEL_SNAP_INDEX] +
                    " in snap file for property " + propertyCode + ".  ");
            return false;
        }
        return true;
    }

    private boolean validateMinLOSIsLessThanMaxLOSInSnapFile(List<String[]> esRtLvlRecords, int index, StringBuilder message, String propertyCode) {
        if (Integer.parseInt(esRtLvlRecords.get(index)[MIN_LOS_SNAP_INDEX]) >= Integer.parseInt(esRtLvlRecords.get(index)[MAX_LOS_SNAP_INDEX])) {
            LOGGER.warn("min LOS value (" + esRtLvlRecords.get(index)[MIN_LOS_SNAP_INDEX] + ") of LV" + esRtLvlRecords.get(index)[RATE_LEVEL_SNAP_INDEX] +
                    " is equal to or higher than its max LOS (" + esRtLvlRecords.get(index)[MAX_LOS_SNAP_INDEX] + ") in snap file for property " + propertyCode);
            message.append("min LOS value (" + esRtLvlRecords.get(index)[MIN_LOS_SNAP_INDEX] + ") of LV" + esRtLvlRecords.get(index)[RATE_LEVEL_SNAP_INDEX] +
                    " is equal to or higher than its max LOS (" + esRtLvlRecords.get(index)[MAX_LOS_SNAP_INDEX] + ") in snap file for property " + propertyCode + ".  ");
            return false;
        }
        return true;
    }

    private boolean validateConsecutiveRateLevelsInSnapFileIfTwoRecordsReceived(List<String[]> esRtLvlRecords, StringBuilder message, String propertyCode) {
        if ((Integer.parseInt(esRtLvlRecords.get(0)[RATE_LEVEL_SNAP_INDEX]) + 1) != Integer.parseInt(esRtLvlRecords.get(1)[RATE_LEVEL_SNAP_INDEX])) {
            LOGGER.warn("Extended stay rate levels in snap file are not consecutive, given rate levels: LV" + esRtLvlRecords.get(0)[RATE_LEVEL_SNAP_INDEX] +
                    " and LV" + esRtLvlRecords.get(1)[RATE_LEVEL_SNAP_INDEX] + ", property: " + propertyCode);
            message.append("Extended stay rate levels in snap file are not consecutive, given rate levels: LV" + esRtLvlRecords.get(0)[RATE_LEVEL_SNAP_INDEX] +
                    " and LV" + esRtLvlRecords.get(1)[RATE_LEVEL_SNAP_INDEX] + ", property: " + propertyCode + ".  ");
            return false;
        }
        return true;
    }

    public IndependentProductConfigurationDTO loadIndependentProductConfigurationByProductId(Integer productId) {
        Product product = tenantCrudService.find(Product.class, productId);
        if (product == null) {
            return null;
        }

        IndependentProductConfigurationDTO dto = new IndependentProductConfigurationDTO(product);
        dto.setRateCodes(findRateCodesByProduct(product));
        dto.setRoomTypes(findRoomTypesByProduct(product));

        if (isRDLEnabled()) {
            setRateTypeDataForIndependentProduct(dto, productId);
        }

        return dto;
    }

    public void saveIndependentProductConfiguration(IndependentProductConfigurationDTO dto, List<Product> allProducts) {
        Product product = dto.getProduct();
        product.setOptimized(!product.isSystemDefault());
        if (!product.isSystemDefault()) {
            dto.setMinimumDaysAdvancedBooking(DEFAULT_MIN_DTA);
            dto.setMaximumDaysAdvancedBooking(DEFAULT_MAX_DTA);
            dto.setMinimumDaysLengthOfStay(dto.getMinimumDaysLengthOfStay());
            dto.setMaximumDaysLengthOfStay(dto.getMaximumDaysLengthOfStay());

            product.setCode(Product.INDEPENDENT_PRODUCT_CODE);

            if (product.getId() == null) {
                product.setActive(false);
            }

            product.setDecisionsSentBy(AgileRatesDecisionsSentBy.PRICE);
        }

        product.setRateShoppingLOSMin(dto.getRateShoppingLOSMin());
        product.setRateShoppingLOSMax(dto.getRateShoppingLOSMax());

        if (product.getDisplayOrder() == null) {
            product.setDisplayOrder(findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts().size() + 1);
        }

        // set Default value for Product code and MinRooms and MaxRooms
        ProductCode pc = findProductCodeByName(Product.INDEPENDENT_PRODUCT_CODE);
        product.setProductCode(pc);
        product.setMinRooms(DEFAULT_MIN_ROOMS);
        product.setMaxRooms(DEFAULT_MAX_ROOMS);

        tenantCrudService.save(product);
        saveRateUnqualifiedForHilton(product.getName(), product.getDescription());

        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        if (!product.isSystemDefault()) {
            updateProductRateCodes(product, dto.getRateCodes());
            updateProductRoomTypes(product, dto.getRoomTypes());
            deleteRoomTypesForRateProtectProduct(product);
            updateCeilingFloorMappings(product);
            updateOffsetMappings(product);

            if (isIndependentProductsEnabled) {
                accommodationMappingService.updateCompetitorAccomClassReferenceForProduct(PacmanWorkContextHelper.getPropertyId(), product);
                accommodationMappingService.createAndSaveDefaultWebrateRankingAccomClassesForProduct(PacmanWorkContextHelper.getPropertyId(), PacmanWorkContextHelper.getUserId(), product);
                accommodationMappingService.createDefaultChannelForProduct(product);
            }

            updateChildrenProductRateShoppingLOS(product, allProducts);

            LocalDate caughtUpLocalDate = dateService.getCaughtUpLocalDate();
            if (CollectionUtils.isNotEmpty(dto.getRoomTypesRemovedList())) {
                //non-base decision tables (CP_Decision_Bar_Output, CP_Decision_Bar_Output_OVR, CP_Unqualified_Demand_FCST_Price, Decision_Dailybar_Output)
                //need to have future records deleted (Arrival_DT >= :currentDate) so we don't send them out again.
                List<Integer> accomTypeIDs = dto.getRoomTypesRemovedList().stream().map(AccomType::getId).collect(Collectors.toList());
                deleteFutureProductDependentNonBaseDecisionEntities(product, caughtUpLocalDate, accomTypeIDs);
                if (cpRecommendationService.isIHotelierEnabledAndProductSendByAdjustment(product)) {
                    deleteDecisionDailyBarNonHiltonCRSByProduct(product, caughtUpLocalDate, accomTypeIDs);
                }
            }

            if (CollectionUtils.isNotEmpty(dto.getRoomClassesRemovedList())) {
                List<Integer> accomClassIds = dto.getRoomClassesRemovedList().stream().map(AccomClass::getId).collect(Collectors.toList());
                deleteProductRateOffsetOverrides(product, caughtUpLocalDate, accomClassIds);
            }

            updateProductHierarchiesOnProductUpdate(dto.getProduct().getId());

            validateIndependentProducts();
        }

        if (isRDLEnabled()) {
            //if we are updating the data, clear stale data first
            deleteWebrateTypeProductForProductId(product.getId());
            saveWebrateTypeDataForIndependentProduct(dto);
        }

        if (isIndependentProductsEnabled || isRDLEnabled()) {
            registerIndependentProductChangedSyncEvent();
        }
    }

    private void updateProductHierarchiesOnProductUpdate(Integer productId) {
        List<ProductHierarchy> impactedProductHierarchies = findImpactedProductHierarchies(productId);
        saveAgileRatesProductHierarchies(impactedProductHierarchies, new ArrayList<>());
    }

    protected void updateChildrenProductRateShoppingLOS(Product product, List<Product> allProducts) {
        List<Product> children = getChildren(product, allProducts);
        for (Product child : children) {
            child.setRateShoppingLOSMin(product.getRateShoppingLOSMin());
            child.setRateShoppingLOSMax(product.getRateShoppingLOSMax());
        }
        tenantCrudService.save(children);
    }

    public static List<Product> getChildren(Product parent, Collection<Product> products) {
        List<Product> children = new ArrayList<>();

        for (Product child : products) {
            if (isNotNewProduct(parent) && parent.getId().equals(child.getDependentProductId())) {
                children.add(child);
            }
        }

        List<Product> descendants = new ArrayList<>();
        for (Product childAsParent : children) {
            descendants.addAll(getChildren(childAsParent, products));
        }

        children.addAll(descendants);
        return children;
    }

    private static boolean isNotNewProduct(Product parent) {
        return parent.getId() != null;
    }

    public void updateProductFloorRatesForHiltonRST(List<AgileProductConfig> newAgileProductConfigList, String brandCode) {
        List<Product> products = getProducts(newAgileProductConfigList, brandCode);
        if (!products.isEmpty()) {
            List<Integer> productIds = products.stream().map(Product::getId).collect(toList());
            tenantCrudService.executeUpdateByNativeQuery(UPDATE_ALL_RELATIVE_TO_PRIMARY_FLOOR_PRODUCTS_FLOOR_RATE,
                    QueryParameter.with("date", dateService.getCaughtUpLocalDate()).and("productID", 1)
                            .and("productIds", productIds).parameters());
        }
    }

    private List<Product> getProducts(List<AgileProductConfig> newAgileProductConfigList, String brandCode) {
        return newAgileProductConfigList.stream().map(agileProductConfig -> getProductByName(agileProductConfig.getName().replace(":brandCode", brandCode))).collect(toList());
    }

    public BigDecimal calculateFloorRelativeToPrimaryProduct(String percentageOffValue, int productID) {
        BigDecimal lowestFloorValue = tenantCrudService.findByNativeQuerySingleResult(FIND_LOWEST_FLOOR_FOR_SYSTEM_DEFAULT_PRODUCT, QueryParameter.with("date", dateService.getCaughtUpLocalDate()).and("productID", productID).parameters(), row -> (BigDecimal) row[0]);
        BigDecimal percentageOffPrimaryFloor = BigDecimal.ONE.add(new BigDecimal(percentageOffValue).divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_UP));
        if (lowestFloorValue == null) {
            return BigDecimal.ZERO;
        }
        return lowestFloorValue.multiply(percentageOffPrimaryFloor).setScale(2, RoundingMode.HALF_UP);
    }

    public BigDecimal calculateFloorByRoundingRuleRelativeToPrimaryProduct(String percentageOffValue, Product childProduct, int baseProductID,
                                                                           Map<Integer, PricingRule> pricingRules, BigDecimal lowestFloorValue) {
        BigDecimal percentageOffPrimaryFloor = BigDecimal.ONE.add(new BigDecimal(percentageOffValue).divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_UP));
        if (lowestFloorValue == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal floor = lowestFloorValue.multiply(percentageOffPrimaryFloor).setScale(2, RoundingMode.HALF_UP);
        return applyProductRoundingRule(baseProductID, childProduct.getRoundingRule(), floor, pricingRules);
    }

    @VisibleForTesting
	public
    BigDecimal getFloorOfBaseProduct(int baseProductID) {
        return tenantCrudService.findByNativeQuerySingleResult(FIND_LOWEST_FLOOR_FOR_SYSTEM_DEFAULT_PRODUCT,
                QueryParameter.with("date", dateService.getCaughtUpLocalDate())
                        .and("productID", baseProductID).parameters(), row -> (BigDecimal) row[0]);
    }

    private BigDecimal applyProductRoundingRule(Integer baseProductID, RoundingRule roundingRule, BigDecimal occupancyTypeRate, Map<Integer, PricingRule> pricingRules) {
        BigDecimal productRoundedRate;
        switch (roundingRule) {
            case NONE:
                productRoundedRate = negativeValueToZero(round(occupancyTypeRate, 2));
                break;
            case UP:
                productRoundedRate =
                        negativeValueToZero(round(occupancyTypeRate, 0, RoundingMode.CEILING))
                                .setScale(2, RoundingMode.UNNECESSARY);
                break;
            case DOWN:
                productRoundedRate =
                        negativeValueToZero(round(occupancyTypeRate, 0, RoundingMode.FLOOR))
                                .setScale(2, RoundingMode.UNNECESSARY);
                break;
            case WHOLE:
                productRoundedRate =
                        negativeValueToZero(round(occupancyTypeRate, 0))
                                .setScale(2, RoundingMode.UNNECESSARY);
                break;
            case PRICE_ROUNDING:
                productRoundedRate = getPricingRule(baseProductID, pricingRules).calculatePrettyPrice(occupancyTypeRate);
                break;
            default:
                throw new TetrisException("Unrecognized product rounding rule: " + roundingRule);
        }
        return productRoundedRate;
    }

    public PricingRule getPricingRule(Integer productId, Map<Integer, PricingRule> pricingRules) {
        if (pricingRules == null || pricingRules.get(productId) == null) {
            return new PricingRule();
        }
        return pricingRules.get(productId);
    }

    public void updateProductFloorRatesRelativeToPrimaryProduct() {
        Map<Integer, List<Product>> parentChildMap = new HashMap<>();
        List<Product> products = findAllActiveAgileRatesIndependentAndSmallGroupProducts();
        Map<Integer, PricingRule> pricingRules = prettyPricingService.getPricingRules();
        products.forEach(product -> {
            if (!product.isSystemDefaultOrIndependentProduct()) {
                Product parentProduct = getLowestBaseProduct(product, products);
                if (Objects.nonNull(parentProduct) && parentChildMap.get(parentProduct.getId()) == null) {
                    parentChildMap.put(parentProduct.getId(), new ArrayList<>(List.of(product)));
                } else if (Objects.nonNull(parentProduct)) {
                    parentChildMap.get(parentProduct.getId()).add(product);
                }
            }
        });

        for (Map.Entry<Integer, List<Product>> entry : parentChildMap.entrySet()) {
            BigDecimal floorOfBaseProduct = getFloorOfBaseProduct(entry.getKey());
            entry.getValue().forEach(product -> {
                if (Objects.nonNull(product.getFloorPercentage())) {
                    BigDecimal floor = calculateFloorByRoundingRuleRelativeToPrimaryProduct(product.getFloorPercentage().toString(), product, entry.getKey(),
                            pricingRules, floorOfBaseProduct);
                    if (!Objects.equals(product.getFloor(), floor)) {
                        product.setFloor(floor);
                        tenantCrudService.save(product);
                    }
                }
            });
        }
    }

    public Product getLowestBaseProduct(Product product, List<Product> productList) {
        if (product.isSystemDefaultOrIndependentProduct()) {
            return product;
        } else {
            return getLowestBaseProduct(productList.stream()
                    .filter(p -> p.getId().equals(product.getDependentProductId()))
                    .findFirst()
                    .get(), productList);
        }
    }

    public List<Product> findAllIndependentProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.INDEPENDENT_PRODUCT_CODE).parameters());
    }

    public List<Product> findAllRateProtectProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_BY_CODE, QueryParameter.with(Product.CODE_PARAM, Product.FIXED_ABOVE_BAR_CODE).parameters());
    }

    public IndependentProductConfigurationDTO copyIndependentProduct(Product originalProduct, String newProductName, int newDisplayOrder) {
        IndependentProductConfigurationDTO originalDTO = loadIndependentProductConfigurationByProductId(originalProduct.getId());

        Product newProduct = new Product(originalProduct);
        newProduct.setName(newProductName.trim());
        newProduct.setDisplayOrder(newDisplayOrder);

        IndependentProductConfigurationDTO newDTO = new IndependentProductConfigurationDTO(newProduct);
        newDTO.setRoomTypes(originalDTO.getRoomTypes());

        if (isRDLEnabled()) {
            newDTO.setRateType(RATE_TYPE_NA_CODE);
        }

        return newDTO;
    }

    public void deleteIndependentProduct(Product product, List<Product> relatedProducts, LocalDate systemDate) {
        assert (product.isPersisted());
        assert (Product.INDEPENDENT_PRODUCT_CODE.equals(product.getCode()));

        //non-base decision tables (CP_Decision_Bar_Output, CP_Decision_Bar_Output_OVR, CP_Unqualified_Demand_FCST_Price, Decision_Dailybar_Output)
        //need to have future records deleted (Arrival_DT >= :currentDate) so we don't send them out again.
        deleteFutureProductDependentNonBaseDecisionEntities(product, systemDate);

        //Move Market Segments to unassigned and the user will need to go in and re-attribute them.
        analyticalMarketSegmentService.unassignMarketSegmentsByRateCodes(findProductRateCodesByProduct(product));
        analyticalMarketSegmentService.unassignMarketSegmentProductMappingByProduct(product);

        saveAgileRatesProductHierarchies(Collections.emptyList(), getAllHierarchiesForProduct(product));

        //Set direct children's Dependent_Product_ID = null and set to invalid
        setInvalidReasonForDependentProducts(product);

        //Delete Rate Codes for all product's in that family
        List<Product> productFamily = new ArrayList<>();
        productFamily.add(product);
        productFamily.addAll(relatedProducts);
        deleteProductRateCodesForProductAndRelatedProducts(productFamily);

        deleteProductAccomTypesForProduct(product);
        deleteCeilingFloorMappingsForProduct(product);
        deleteOffsetMappingsForProduct(product);

        deleteRoundRulesForProduct(product);

        if (isRDLEnabled()) {
            deleteWebrateTypeProductForProductId(product.getId());
        }

        product.setStatus(TenantStatusEnum.DELETED);
        product.setDisplayOrder(-1);
        tenantCrudService.save(product);

        accommodationMappingService.deleteWebrateCompetitorChannelMappings(product);
        accommodationMappingService.deleteWebrateRankingAccomClass(product);
        accommodationMappingService.deleteWebrateRankingAccomClassOverrides(product);
        accommodationMappingService.deleteWebrateOverrideCompetitor(product);
        accommodationMappingService.deleteWebrateCompetitorsAccomClass(product);
        accommodationMappingService.deleteWebrateDefaultChannel(product);
        accommodationMappingService.deleteWebrateOverrideChannel(product);

        validateIndependentProducts();

        setInvalidReasonForRateProtectProducts(product);
        List<Product> updatedRelatedProducts = getAllProducts().stream()
                .filter(product1 -> relatedProducts.stream().anyMatch(product2 -> Objects.equals(product2.getId(), product1.getId())))
                .collect(Collectors.toList());
        invalidateDependentProducts(updatedRelatedProducts);
        tenantCrudService.save(updatedRelatedProducts);
        registerIndependentProductChangedSyncEvent();
    }

    public void validateIndependentProducts() {
        List<Product> allIndependentProducts = findAllIndependentProducts();
        if (allIndependentProducts.isEmpty()) {
            return;
        }
        for (Product p : allIndependentProducts) {
            if (TenantStatusEnum.INVALID.equals(p.getStatus())) {
                p.setActive(false);
            }
        }

        List<AccomType> baseAccomTypes = getPricingAccomClasses().stream()
                .map(PricingAccomClass::getAccomType).collect(Collectors.toList());
        List<ProductAccomType> allProductAccomTypeMappings = findProductRoomTypeByProducts(new HashSet<>(allIndependentProducts));
        List<PricingBaseAccomType> allFloorCeilingMappings = retrieveAllFloorCeilingMappingsForProperty();

        // invalidation is ordered from least to most critical
        invalidateMissingCeilingFloorDefaults(allIndependentProducts, baseAccomTypes, allProductAccomTypeMappings, allFloorCeilingMappings);
        invalidateMissingRateCodes(allIndependentProducts);
        validateConfigureBaseRoomTypes(allProductAccomTypeMappings);
        invalidateMissingAccomTypesForRateProtectProduct();

        tenantCrudService.save(allIndependentProducts);
    }

    private void invalidateMissingAccomTypesForRateProtectProduct() {
        findAllRateProtectProducts().forEach(product -> {
            if (findProductRoomTypesByProduct(product).isEmpty()) {
                product.setInvalidReason(InvalidReason.MISSING_ACCOM_TYPES);
            }
        });
    }

    public void validateConfigureBaseRoomTypes(List<ProductAccomType> allProductAccomTypeMappings) {

        List<AccomClass> accomClassList = findAllAccomClass();

        for (PricingAccomClass pricingAccomClass : getPricingAccomClasses()) {
            if (accomClassList.contains(pricingAccomClass.getAccomClass())) {
                accomClassList.remove(pricingAccomClass.getAccomClass());
            }
        }

        if (CollectionUtils.isNotEmpty(accomClassList)) {

            List<Product> productsToBeInvalid = new ArrayList<>();
            Set<Set<AccomType>> accomTypeSetofSets = accomClassList.stream().map(AccomClass::getAccomTypes).collect(toSet());
            Set<AccomType> accomTypeSet = new LinkedHashSet<>();

            for (Set<AccomType> accomTypes : accomTypeSetofSets) {
                accomTypeSet.addAll(accomTypes);
            }

            for (ProductAccomType productAccomType : allProductAccomTypeMappings) {
                if (accomTypeSet.stream().anyMatch(p -> p.getId().equals(productAccomType.getAccomType().getId()))) {
                    productsToBeInvalid.add(productAccomType.getProduct());
                }
            }

            for (Product product : productsToBeInvalid) {
                product.setInvalidReason(InvalidReason.MISSING_BASE_ROOM_TYPE_CONFIGURATION);
            }
        }
    }

    public List<AccomClass> findAllAccomClass() {
        return tenantCrudService.findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<PricingAccomClass> findAllPricingAccomClass() {
        return tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void invalidateMissingCeilingFloorDefaults(List<Product> allIndependentProducts, List<AccomType> baseAccomTypes,
                                                      List<ProductAccomType> allProductAccomTypeMappings,
                                                      List<PricingBaseAccomType> allFloorCeilingMappings) {
        allIndependentProducts.forEach(product -> {
            List<ProductAccomType> productAccomTypes = allProductAccomTypeMappings.stream()
                    .filter(pat -> pat.getProduct().equals(product)) // filter down to current product
                    .filter(pat -> baseAccomTypes.contains(pat.getAccomType())) // filter down to base at
                    .collect(Collectors.toList());
            List<PricingBaseAccomType> productsFloorCeiling = allFloorCeilingMappings.stream()
                    .filter(pbat -> pbat.getProductID().equals(product.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productsFloorCeiling) ||
                    !(productsFloorCeiling.stream()
                            .filter(pbat -> pbat.getStartDate() == null && pbat.getEndDate() == null)
                            .count() == productAccomTypes.size())) {
                product.setInvalidReason(InvalidReason.INDEPENDENT_PRODUCT_MISSING_CEILING_FLOOR);
            }
        });
    }

    public List<PricingAccomClass> getPricingAccomClasses() {
        return tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void deleteCeilingFloorMappingsForProduct(Product product) {
        tenantCrudService.executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_PRODUCT_ID,
                QueryParameter.with("productId", product.getId()).parameters());
    }

    public void copyDefaultCeilingFloorMappings(Product originalProduct, Product copiedProduct) {
        List<PricingBaseAccomType> originalCeilingFloorMappings = tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(PRODUCT_ID, originalProduct.getId()).parameters());

        List<PricingBaseAccomType> copyCeilingFloorMappings = new ArrayList<>();
        originalCeilingFloorMappings.forEach(pbat -> {
            if (pbat.getStartDate() == null && pbat.getEndDate() == null) {
                PricingBaseAccomType clone = pbat.clone();
                clone.setProductID(copiedProduct.getId());
                copyCeilingFloorMappings.add(clone);
            }
        });

        tenantCrudService.save(copyCeilingFloorMappings);
    }

    public void updateCeilingFloorMappings(Product product) {
        List<AccomType> accomTypesForProduct = findProductRoomTypesByProduct(product).stream()
                .map(ProductAccomType::getAccomType).collect(Collectors.toList());
        List<PricingBaseAccomType> currentCeilingFloorMappings = tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(PRODUCT_ID, product.getId()).parameters());

        List<PricingBaseAccomType> ceilingFloorMappingsToRemove = new ArrayList<>();
        currentCeilingFloorMappings.forEach(pbat -> {
            if (!accomTypesForProduct.contains(pbat.getAccomType())) {
                ceilingFloorMappingsToRemove.add(pbat);
            }
        });

        tenantCrudService.delete(ceilingFloorMappingsToRemove);
    }


    public boolean areSeasonDaysValid(PricingBaseAccomType pricingBaseAccomType) {
        List<DayOfWeek> daysOfWeek = LocalDateUtils.getDayOfWeeks(pricingBaseAccomType.getStartDate(), pricingBaseAccomType.getEndDate());
        return !((daysOfWeek.contains(DayOfWeek.SUNDAY) && isDOWRateInvalid(pricingBaseAccomType.getSundayFloorRate(), pricingBaseAccomType.getSundayCeilingRate()))
                || (daysOfWeek.contains(DayOfWeek.MONDAY) && isDOWRateInvalid(pricingBaseAccomType.getMondayFloorRate(), pricingBaseAccomType.getMondayCeilingRate()))
                || (daysOfWeek.contains(DayOfWeek.TUESDAY) && isDOWRateInvalid(pricingBaseAccomType.getTuesdayFloorRate(), pricingBaseAccomType.getTuesdayCeilingRate()))
                || (daysOfWeek.contains(DayOfWeek.WEDNESDAY) && isDOWRateInvalid(pricingBaseAccomType.getWednesdayFloorRate(), pricingBaseAccomType.getWednesdayCeilingRate()))
                || (daysOfWeek.contains(DayOfWeek.THURSDAY) && isDOWRateInvalid(pricingBaseAccomType.getThursdayFloorRate(), pricingBaseAccomType.getThursdayCeilingRate()))
                || (daysOfWeek.contains(DayOfWeek.FRIDAY) && isDOWRateInvalid(pricingBaseAccomType.getFridayFloorRate(), pricingBaseAccomType.getFridayCeilingRate()))
                || (daysOfWeek.contains(DayOfWeek.SATURDAY) && isDOWRateInvalid(pricingBaseAccomType.getSaturdayFloorRate(), pricingBaseAccomType.getSaturdayCeilingRate())));
    }

    private boolean isDOWRateInvalid(BigDecimal floorRate, BigDecimal ceilingRate) {
        return isNull(floorRate) || isNull(ceilingRate);
    }

    public boolean isFloorCeilingConfigurationComplete(PricingBaseAccomType pricingBaseAccomType) {
        return nonNull(pricingBaseAccomType.getSundayFloorRate()) && nonNull(pricingBaseAccomType.getSundayCeilingRate())
                && nonNull(pricingBaseAccomType.getMondayFloorRate()) && nonNull(pricingBaseAccomType.getMondayCeilingRate())
                && nonNull(pricingBaseAccomType.getTuesdayFloorRate()) && nonNull(pricingBaseAccomType.getTuesdayCeilingRate())
                && nonNull(pricingBaseAccomType.getWednesdayFloorRate()) && nonNull(pricingBaseAccomType.getWednesdayCeilingRate())
                && nonNull(pricingBaseAccomType.getThursdayFloorRate()) && nonNull(pricingBaseAccomType.getThursdayCeilingRate())
                && nonNull(pricingBaseAccomType.getFridayFloorRate()) && nonNull(pricingBaseAccomType.getFridayCeilingRate())
                && nonNull(pricingBaseAccomType.getSaturdayFloorRate()) && nonNull(pricingBaseAccomType.getSaturdayCeilingRate());
    }

    public void copyDefaultOffsetMappings(Product originalProduct, Product newProduct) {
        List<CPConfigOffsetAccomType> originalOffsetMappings = tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", originalProduct.getId()).parameters());

        List<CPConfigOffsetAccomType> copyOffsetMappings = new ArrayList<>();
        originalOffsetMappings.forEach(ccoat -> {
            if (ccoat.getStartDate() == null && ccoat.getEndDate() == null) {
                copyOffsetMappings.add(cloneOffsetAndSetNewProductId(ccoat, newProduct.getId()));
            }
        });
        tenantCrudService.save(copyOffsetMappings);
    }

    public void copyOffsetMappings(Product originalProduct, Product newProduct) {
        List<CPConfigOffsetAccomType> originalOffsetMappings = tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", originalProduct.getId()).parameters());

        saveOffsetsMappings(newProduct.getId(), originalOffsetMappings);
    }

    public void copyOffsetMappingsForValidRoomTypes(Product originalProduct, Product newProduct, List<AccomType> validRoomTypes) {
        List<CPConfigOffsetAccomType> originalOffsetMappings = tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", originalProduct.getId()).parameters());
        originalOffsetMappings.removeIf(offset -> !validRoomTypes.contains(offset.getAccomType()));
        saveOffsetsMappings(newProduct.getId(), originalOffsetMappings);
    }

    public void saveOffsetsMappings(Integer newProductId, List<CPConfigOffsetAccomType> originalOffsetMappings) {
        List<CPConfigOffsetAccomType> copyOffsetMappings = originalOffsetMappings.stream()
                .map(offset -> cloneOffsetAndSetNewProductId(offset, newProductId))
                .collect(Collectors.toList());

        tenantCrudService.save(copyOffsetMappings);
    }

    private CPConfigOffsetAccomType cloneOffsetAndSetNewProductId(CPConfigOffsetAccomType offset, Integer productId) {
        CPConfigOffsetAccomType copy = offset.cloneOffset();
        copy.setProductID(productId);
        return copy;
    }

    public void deleteOffsetMappingsForProduct(Product product) {
        tenantCrudService.executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_PRODUCT_ID,
                QueryParameter.with("productID", product.getId()).parameters());
    }

    public void deleteOffsetsForProducts(List<Product> products) {
        tenantCrudService.executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_PRODUCT_IDs,
                QueryParameter.with("productIDs", products.stream()
                        .map(Product::getId)
                        .collect(toSet())).parameters());
    }

    public void updateOffsetMappings(Product product) {
        List<AccomType> accomTypesForProduct = findProductRoomTypesByProduct(product).stream()
                .map(ProductAccomType::getAccomType).collect(Collectors.toList());
        List<CPConfigOffsetAccomType> currentOffsetMappings = tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomType.FIND_BY_PRODUCT_ID,
                QueryParameter.with("productID", product.getId()).parameters());

        List<CPConfigOffsetAccomType> offsetsToRemove = new ArrayList<>();
        currentOffsetMappings.forEach(offset -> {
            if (!accomTypesForProduct.contains(offset.getAccomType())) {
                offsetsToRemove.add(offset);
            }
        });

        tenantCrudService.delete(offsetsToRemove);
    }

    public Product findProductById(int id) {
        return tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID, (QueryParameter.with("productId", id).parameters()));
    }

    public ProductCode findProductCodeByName(String name) {
        return tenantCrudService.findByNamedQuerySingleResult(ProductCode.BY_NAME, (QueryParameter.with("name", name).parameters()));
    }

    private String reconfigureAgileProductsForVirtualProperty(String recoveryState, BigDecimal packageValue) {
        StringBuilder message = new StringBuilder(StringUtils.EMPTY);
        recoveryState = validateRecoveryState(recoveryState, message);
        List<VirtualPropertyMapping> mappingsForVirtualProperty = virtualPropertyMappingService.getMappingsForVirtualProperty(PacmanWorkContextHelper.getPropertyId());
        if (CollectionUtils.isEmpty(mappingsForVirtualProperty)) {
            message.append("Physical Properties does not found for Virtual Property Id : ").append(PacmanWorkContextHelper.getPropertyId());
            return message.toString();
        }
        String finalRecoveryState = recoveryState;
        List<Product> oldAgileProductList = getExistingAgileProductList();
        Map<VirtualPropertyMapping, VPCreateUpdateDelta> vpmWithDelta = getVPMWithDelta(mappingsForVirtualProperty, finalRecoveryState, oldAgileProductList);

        deleteProducts(message, oldAgileProductList, vpmWithDelta);

        Set<String> alreadyCleanedUpProducts = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);
        vpmWithDelta.forEach((virtualPropertyMapping, vpCreateUpdateDelta) -> {
            LOGGER.info("Products to be created: " + vpCreateUpdateDelta.getProductsTobeCreated());
            message.append("Products to be created: ").append(vpCreateUpdateDelta.getProductsTobeCreated()).append("\n");
            LOGGER.info("Products to be updated: " + vpCreateUpdateDelta.getProductsTobeUpdated());
            message.append("Products to be updated: ").append(vpCreateUpdateDelta.getProductsTobeUpdated()).append("\n");

            List<String> hospitalityRoomTypeCodesList = getHospitalityRoomTypeCodesList(true);

            createNewProducts(finalRecoveryState, packageValue, message, virtualPropertyMapping.getBrandCode(), vpCreateUpdateDelta.getProductsTobeCreated(), hospitalityRoomTypeCodesList, virtualPropertyMapping);
            updateExistingProducts(finalRecoveryState, packageValue, message, virtualPropertyMapping.getBrandCode(), vpCreateUpdateDelta.getProductsTobeUpdated(), hospitalityRoomTypeCodesList, virtualPropertyMapping, alreadyCleanedUpProducts);
        });

        return message.toString();
    }

    private void deleteProducts(StringBuilder message, List<Product> oldAgileProductList, Map<VirtualPropertyMapping, VPCreateUpdateDelta> vpmWithDelta) {
        vpmWithDelta.forEach((key, value) -> {
            removeMatchingProducts(value.getProductsTobeCreated(), oldAgileProductList, key);
            removeMatchingProducts(value.getProductsTobeUpdated(), oldAgileProductList, key);
        });

        List<Product> productsToBeDeleted = new ArrayList<>(oldAgileProductList);
        LOGGER.info("Products to be deleted: " + productsToBeDeleted.stream().map(Product::getName).collect(Collectors.toList()));
        message.append("Products to be deleted: ").append(productsToBeDeleted.stream().map(Product::getName).collect(Collectors.toList())).append("\n");
        deleteUnwantedProducts(message, productsToBeDeleted);
    }

    private Map<VirtualPropertyMapping, VPCreateUpdateDelta> getVPMWithDelta(List<VirtualPropertyMapping> mappingsForVirtualProperty, String finalRecoveryState, List<Product> oldAgileProductList) {
        Map<VirtualPropertyMapping, VPCreateUpdateDelta> vpmWithDelta = new HashMap<>();
        mappingsForVirtualProperty.forEach(vpm -> {
            String globalArea = vpm.getGlobalArea();
            validateGlobalArea(globalArea);
            String externalSystem = getConvertedExternalSystemValue(vpm.getExternalSystem());
            String brandCode = vpm.getBrandCode();

            List<AgileProductConfig> newAgileProductConfigList = getNewAgileProductConfigList(finalRecoveryState, externalSystem, globalArea);
            newAgileProductConfigList.forEach(newAgileProductConfig -> agileRatesConfigurationVPService.prefixPropertyCodeInConfig(vpm, newAgileProductConfig));

            List<AgileProductConfig> toBeUpdated = getProductsToBeUpdated(oldAgileProductList, newAgileProductConfigList, vpm);
            List<AgileProductConfig> toBeCreated = getProductsToBeCreatesForVP(newAgileProductConfigList, toBeUpdated);
            vpmWithDelta.put(vpm, new VPCreateUpdateDelta(toBeCreated, toBeUpdated));
        });
        return vpmWithDelta;
    }

    private void removeMatchingProducts(List<AgileProductConfig> entry, List<Product> oldAgileProductList, VirtualPropertyMapping vpm) {
        entry.forEach(agileProductConfig -> {
            oldAgileProductList.stream().filter(product ->
                            product.getName().trim().equals(agileProductConfig.getName().replace(":brandCode", vpm.getBrandCode())))
                    .findFirst().ifPresent(oldAgileProductList::remove);
        });
    }

    private List<AgileProductConfig> getProductsToBeCreatesForVP(List<AgileProductConfig> newList, List<AgileProductConfig> tobeUpdated) {
        List<AgileProductConfig> toBeCreated = new ArrayList<>();
        newList.forEach(newAgileProduct -> {
            if (tobeUpdated.stream().noneMatch(toBeUpdatedAgileRateProduct -> toBeUpdatedAgileRateProduct.getName().trim().equals(newAgileProduct.getName().trim()))) {
                toBeCreated.add(newAgileProduct);
            }
        });
        return toBeCreated;
    }

    private String getConvertedExternalSystemValue(String externalSystem) {
        return ReservationSystem.HILSTAR.getConfigParameterValue().equalsIgnoreCase(externalSystem) ? HCRS : externalSystem;
    }

    public List<ProductHierarchy> findImpactedProductHierarchies(Integer productId) {
        List<ProductHierarchy> productHierarchies = tenantCrudService.findByNamedQuery(BY_TO_OR_FROM_PRODUCT_ID, QueryParameter.with("productId", productId).parameters());
        productHierarchies.forEach(h -> h.setMinimumDifference(CollectionUtils.isNotEmpty(h.getProductMinPriceDiffList()) ? h.getProductMinPriceDiffList().get(0).getSundayDiffWithTax() : BigDecimal.ZERO));
        return productHierarchies;
    }

    public List<ProductHierarchy> findImpactedProductHierarchies(Set<Integer> productIds) {
        List<ProductHierarchy> productHierarchies = tenantCrudService.findByNamedQuery(BY_TO_OR_FROM_PRODUCT_IDS, QueryParameter.with("productIds", productIds).parameters());
        productHierarchies.forEach(h -> h.setMinimumDifference(CollectionUtils.isNotEmpty(h.getProductMinPriceDiffList()) ? h.getProductMinPriceDiffList().get(0).getSundayDiffWithTax() : BigDecimal.ZERO));
        return productHierarchies;
    }

    public List<BrokenHierarchyDetails> handleBrokenProductHierarchies(Set<Integer> productIds,
                                                                       List<CeilingFloorDetails> overrides,
                                                                       List<java.time.LocalDate> selectedDates) {
        List<ProductHierarchy> impactedProductHierarchies = findImpactedProductHierarchies(productIds)
                .stream()
                .filter(productHierarchy -> productHierarchy.getToProduct().isSystemDefaultOrIndependentProduct() && productHierarchy.getFromProduct().isSystemDefaultOrIndependentProduct())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(impactedProductHierarchies)) {
            return Collections.emptyList();
        }
        Set<Integer> involvedProductIds = getAllProductIdsWithinHierarchies(impactedProductHierarchies);
        Map<Pair<Integer, AccomType>, TransientPricingBaseAccomType> ceilingFloorValuesByProduct = retrieveFloorCeilingMappingsForProducts(involvedProductIds)
                .stream()
                .collect(Collectors.toMap(ceilingFloorValue -> Pair.of(ceilingFloorValue.getProductID(), ceilingFloorValue.getAccomType()), value -> value));
        Map<java.time.LocalDate, Map<Pair<Integer, AccomType>, CeilingFloorDetails>> groupedOverrides = groupSpecificCeilingFloorDetails(overrides, selectedDates);
        Predicate<CeilingFloorDetails> shouldBeOverriddenBySpecificOverride =
                ceilingFloor -> Objects.isNull(ceilingFloor.getCeiling()) && Objects.isNull(ceilingFloor.getFloor());
        return groupedOverrides.entrySet()
                .stream()
                .map(entry -> {
                    java.time.LocalDate date = entry.getKey();
                    Map<Pair<Integer, AccomType>, CeilingFloorDetails> groupedOverridesForDate = entry.getValue();
                    return groupedOverridesForDate.entrySet()
                            .stream()
                            .map(groupedOverride -> impactedProductHierarchies.stream()
                                    .filter(productHierarchy -> {
                                        Product currentProduct;
                                        AccomType currentAccomType = groupedOverride.getKey().getValue();
                                        Integer currentProductId = groupedOverride.getKey().getKey();
                                        if (!isHierarcnhyContainProduct(productHierarchy, currentProductId)) {
                                            return false;
                                        }
                                        Product toProduct = productHierarchy.getToProduct();
                                        Product fromProduct = productHierarchy.getFromProduct();
                                        boolean isCurrentProductSelected = productHierarchy.getFromProduct().getId().equals(currentProductId);
                                        CeilingFloorDetails specificCeilingFloorOfAnotherProductInHierarchy;

                                        if (isCurrentProductSelected) {
                                            CPDecisionBAROutput override = getCPDecisionBAROutput(toProduct, LocalDateUtils.toJodaLocalDate(date), currentAccomType);
                                            CeilingFloorDetails initialCeilingFloorDetailsOverride = groupedOverridesForDate.getOrDefault(Pair.of(toProduct.getId(), currentAccomType), new CeilingFloorDetails());
                                            specificCeilingFloorOfAnotherProductInHierarchy = resolveCeilingFloorOfProduct(
                                                    initialCeilingFloorDetailsOverride, ceilingFloorValuesByProduct.get(Pair.of(toProduct.getId(), currentAccomType)),
                                                    override, LocalDateUtils.toJodaLocalDate(date));
                                            setSpecificOverrideAsCeilAndFloorValuesIfPriceWasOverridden(override, specificCeilingFloorOfAnotherProductInHierarchy,
                                                    shouldBeOverriddenBySpecificOverride.test(initialCeilingFloorDetailsOverride));
                                            currentProduct = fromProduct;
                                        } else {
                                            CPDecisionBAROutput override = getCPDecisionBAROutput(fromProduct, LocalDateUtils.toJodaLocalDate(date), currentAccomType);
                                            CeilingFloorDetails initialCeilingFloorDetailsOverride = groupedOverridesForDate.getOrDefault(Pair.of(fromProduct.getId(), currentAccomType), new CeilingFloorDetails());
                                            specificCeilingFloorOfAnotherProductInHierarchy = resolveCeilingFloorOfProduct(
                                                    initialCeilingFloorDetailsOverride, ceilingFloorValuesByProduct.get(Pair.of(fromProduct.getId(), currentAccomType)), override,
                                                    LocalDateUtils.toJodaLocalDate(date));
                                            setSpecificOverrideAsCeilAndFloorValuesIfPriceWasOverridden(override, specificCeilingFloorOfAnotherProductInHierarchy,
                                                    shouldBeOverriddenBySpecificOverride.test(initialCeilingFloorDetailsOverride));
                                            currentProduct = toProduct;
                                        }
                                        CPDecisionBAROutput overrideForCurrentProduct = getCPDecisionBAROutput(currentProduct, LocalDateUtils.toJodaLocalDate(date), currentAccomType);
                                        CeilingFloorDetails specificCeilingFloorOfCurrentProductInHierarchy = resolveCeilingFloorOfProduct(groupedOverride.getValue(),
                                                ceilingFloorValuesByProduct.get(Pair.of(currentProduct.getId(), currentAccomType)), overrideForCurrentProduct,
                                                LocalDateUtils.toJodaLocalDate(date));
                                        return !isCeilingFloorsValid(specificCeilingFloorOfCurrentProductInHierarchy, specificCeilingFloorOfAnotherProductInHierarchy, productHierarchy.getMinimumDifference(), isCurrentProductSelected);
                                    })
                                    .map(productHierarchy -> mapToBrokenHierarchyDetails(productHierarchy, groupedOverride.getKey().getValue(), date))
                                    .collect(Collectors.toList()))
                            .flatMap(List::stream)
                            .collect(Collectors.toList());
                })
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    private void setSpecificOverrideAsCeilAndFloorValuesIfPriceWasOverridden(CPDecisionBAROutput override,
                                                                             CeilingFloorDetails specificCeilingFloorOfAnotherProductInHierarchy,
                                                                             boolean shouldBeOverridden) {
        if (!shouldBeOverridden) {
            return;
        }
        Optional.of(override)
                .filter(ovr -> ovr.getOverrideType() == DecisionOverrideType.USER)
                .ifPresent(ovr -> {
                    specificCeilingFloorOfAnotherProductInHierarchy.setCeiling(ovr.getPrettyBAR());
                    specificCeilingFloorOfAnotherProductInHierarchy.setFloor(ovr.getPrettyBAR());
                });
    }

    private BrokenHierarchyDetails mapToBrokenHierarchyDetails(ProductHierarchy productHierarchy,
                                                               AccomType accomType,
                                                               java.time.LocalDate date) {
        BrokenHierarchyDetails brokenHierarchyDetails = new BrokenHierarchyDetails();
        brokenHierarchyDetails.setProductHierarchy(productHierarchy);
        brokenHierarchyDetails.setAccomType(accomType);
        brokenHierarchyDetails.setDate(date);
        return brokenHierarchyDetails;
    }

    private Map<java.time.LocalDate, Map<Pair<Integer, AccomType>, CeilingFloorDetails>> groupSpecificCeilingFloorDetails(
            List<CeilingFloorDetails> overrides, List<java.time.LocalDate> selectedDates) {
        return selectedDates.stream()
                .collect(Collectors.toMap(Function.identity(), date -> overrides.stream()
                        .collect(Collectors.toMap(override -> Pair.of(override.getProductId(), override.getAccomType()), override -> override))));
    }

    private CeilingFloorDetails resolveCeilingFloorOfProduct(CeilingFloorDetails specificCeilingFloorOverride,
                                                             TransientPricingBaseAccomType ceilingFloor, CPDecisionBAROutput override, LocalDate date) {
        BigDecimal floor = specificCeilingFloorOverride.getFloor();
        BigDecimal ceiling = specificCeilingFloorOverride.getCeiling();
        if (Objects.nonNull(floor) && Objects.nonNull(ceiling)) {
            return specificCeilingFloorOverride;
        }
        CeilingFloor resolvedCeilingFloor = retrieveCeilingFloorForParticularDate(ceilingFloor, date);
        if (Objects.isNull(floor)) {
            floor = override.getFloorOverride() != null ?
                    override.getFloorOverride() :
                    resolvedCeilingFloor.getFloor();
        }
        if (Objects.isNull(ceiling)) {
            ceiling = override.getCeilingOverride() != null ?
                    override.getCeilingOverride() :
                    resolvedCeilingFloor.getCeiling();
        }
        specificCeilingFloorOverride.setFloor(floor);
        specificCeilingFloorOverride.setCeiling(ceiling);

        return specificCeilingFloorOverride;
    }

    private boolean isHierarcnhyContainProduct(ProductHierarchy hierarchy, Integer productId) {
        return Objects.equals(hierarchy.getToProduct().getId(), productId) ||
                Objects.equals(hierarchy.getFromProduct().getId(), productId);
    }

    private boolean isCeilingFloorsValid(CeilingFloorDetails сeilingFloorOfCurrentProductInHierarchy,
                                         CeilingFloorDetails сeilingFloorOfAnotherProductInHierarchy,
                                         BigDecimal minDifference,
                                         boolean isCurrentProductSelected) {
        if (isCurrentProductSelected) {
            return (сeilingFloorOfCurrentProductInHierarchy.getCeiling() != null &&
                    сeilingFloorOfCurrentProductInHierarchy.getCeiling().add(minDifference).doubleValue()
                            <= сeilingFloorOfAnotherProductInHierarchy.getCeiling().doubleValue()) &&
                    (сeilingFloorOfCurrentProductInHierarchy.getFloor() != null &&
                            сeilingFloorOfCurrentProductInHierarchy.getFloor().add(minDifference).doubleValue()
                                    <= сeilingFloorOfAnotherProductInHierarchy.getFloor().doubleValue());
        }
        return (сeilingFloorOfAnotherProductInHierarchy.getCeiling() != null &&
                сeilingFloorOfAnotherProductInHierarchy.getCeiling().add(minDifference).doubleValue()
                        <= сeilingFloorOfCurrentProductInHierarchy.getCeiling().doubleValue()) &&
                (сeilingFloorOfAnotherProductInHierarchy.getFloor() != null &&
                        сeilingFloorOfAnotherProductInHierarchy.getFloor().add(minDifference).doubleValue()
                                <= сeilingFloorOfCurrentProductInHierarchy.getFloor().doubleValue());
    }

    public List<Product> getAllImpactedProductsWithinHierarchies(List<ProductHierarchy> allHierarchiesForProduct, Product editingProduct) {
        return allHierarchiesForProduct.stream()
                .map(hierarchy -> {
                    if (hierarchy.getToProduct().getId().equals(editingProduct.getId())) {
                        return hierarchy.getFromProduct();
                    }
                    return hierarchy.getToProduct();
                })
                .collect(Collectors.toList());
    }

    public CeilingFloor resolveCeilingFloorForNonEditingProduct(PricingBaseAccomType ceilingFloorValueForNonEditingProduct,
                                                                CPDecisionBAROutput override, LocalDate date) {
        if (ceilingFloorValueForNonEditingProduct == null) {
            return new CeilingFloor();
        }
        CeilingFloor ceilingFloorForParticularDay = retrieveCeilingFloorForParticularDate(ceilingFloorValueForNonEditingProduct, date);
        return Optional.of(override)
                .map(outputOverride -> constructCeilingFloor(ceilingFloorForParticularDay, override.getFloorOverride(), override.getCeilingOverride()))
                .get();
    }

    private CPDecisionBAROutput getCPDecisionBAROutput(Product product, LocalDate date, AccomType accomType) {
        List<CPDecisionBAROutput> overrides = tenantCrudService.findByNamedQuery(
                CPDecisionBAROutput.GET_DECISION_BY_ACCOM_TYPE_NAME_FOR_DATE,
                CPDecisionBAROutput.params(product, date, accomType.getName()));
        return overrides.stream()
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(new CPDecisionBAROutput());
    }

    public CeilingFloor retrieveCeilingFloorForParticularDate(PricingBaseAccomType ceilingFloorValueForNonEditingProduct,
                                                              LocalDate date) {
        CeilingFloor ceilingFloor = new CeilingFloor();
        DayOfWeek dayOfWeek = LocalDateUtils.getDayOfWeek(date);
        switch (dayOfWeek) {
            case MONDAY:
                ceilingFloor.setFloor(ceilingFloorValueForNonEditingProduct.getMondayFloorRateWithTax());
                ceilingFloor.setCeiling(ceilingFloorValueForNonEditingProduct.getMondayCeilingRateWithTax());
                break;
            case TUESDAY:
                ceilingFloor.setFloor(ceilingFloorValueForNonEditingProduct.getTuesdayFloorRateWithTax());
                ceilingFloor.setCeiling(ceilingFloorValueForNonEditingProduct.getTuesdayCeilingRateWithTax());
                break;
            case WEDNESDAY:
                ceilingFloor.setFloor(ceilingFloorValueForNonEditingProduct.getWednesdayFloorRateWithTax());
                ceilingFloor.setCeiling(ceilingFloorValueForNonEditingProduct.getWednesdayCeilingRateWithTax());
                break;
            case THURSDAY:
                ceilingFloor.setFloor(ceilingFloorValueForNonEditingProduct.getThursdayFloorRateWithTax());
                ceilingFloor.setCeiling(ceilingFloorValueForNonEditingProduct.getThursdayCeilingRateWithTax());
                break;
            case FRIDAY:
                ceilingFloor.setFloor(ceilingFloorValueForNonEditingProduct.getFridayFloorRateWithTax());
                ceilingFloor.setCeiling(ceilingFloorValueForNonEditingProduct.getFridayCeilingRateWithTax());
                break;
            case SATURDAY:
                ceilingFloor.setFloor(ceilingFloorValueForNonEditingProduct.getSaturdayFloorRateWithTax());
                ceilingFloor.setCeiling(ceilingFloorValueForNonEditingProduct.getSaturdayCeilingRateWithTax());
                break;
            case SUNDAY:
                ceilingFloor.setFloor(ceilingFloorValueForNonEditingProduct.getSundayFloorRateWithTax());
                ceilingFloor.setCeiling(ceilingFloorValueForNonEditingProduct.getSundayCeilingRateWithTax());
                break;
        }
        return ceilingFloor;
    }

    public List<TransientPricingBaseAccomType> retrieveFloorCeilingMappingsForProducts(Set<Integer> productIds) {
        return tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_DEFAULT_BY_PRODUCT_IDS,
                TransientPricingBaseAccomType.params(PacmanWorkContextHelper.getPropertyId(), productIds));
    }

    private CeilingFloor constructCeilingFloor(CeilingFloor ceilingFloorForParticularDay, BigDecimal floorOverride, BigDecimal ceilingOverride) {
        CeilingFloor ceilingFloor = new CeilingFloor();
        if (Objects.isNull(floorOverride)) {
            ceilingFloor.setFloor(ceilingFloorForParticularDay.getFloor());
        } else {
            ceilingFloor.setFloor(floorOverride);
        }
        if (Objects.isNull(ceilingOverride)) {
            ceilingFloor.setCeiling(ceilingFloorForParticularDay.getCeiling());
        } else {
            ceilingFloor.setCeiling(ceilingOverride);
        }
        return ceilingFloor;
    }

    /**
     * This method is only designed to support Older version of  Agile Rates Package UI  to forward compatible  Db changes
     * So not fit to use for  any other purpose
     */
    @Deprecated
    private AgileRatesPackageChargeType createAgileRatesPackageChargeTypeObject(AgileRatesPackage agileRatesPackage) {
        AgileRatesPackageChargeType agileRatesPackageChargeType = new AgileRatesPackageChargeType();
        agileRatesPackageChargeType.setId(agileRatesPackage.getAgileRatePackageChargeTypeId());
        agileRatesPackageChargeType.setOffsetMethod(agileRatesPackage.getOffsetMethod());
        agileRatesPackageChargeType.setAgileRatesPackage(agileRatesPackage);

        agileRatesPackageChargeType.setOccupancyType(AgileRatesChargeType.resolveOccupancyTypeFrom(agileRatesPackage.getChargeType()));

        agileRatesPackageChargeType.setSaturdayOffsetValue(agileRatesPackage.getOffsetValue());
        agileRatesPackageChargeType.setSundayOffsetValue(agileRatesPackage.getOffsetValue());
        agileRatesPackageChargeType.setMondayOffsetValue(agileRatesPackage.getOffsetValue());
        agileRatesPackageChargeType.setTuesdayOffsetValue(agileRatesPackage.getOffsetValue());
        agileRatesPackageChargeType.setWednesdayOffsetValue(agileRatesPackage.getOffsetValue());
        agileRatesPackageChargeType.setThursdayOffsetValue(agileRatesPackage.getOffsetValue());
        agileRatesPackageChargeType.setFridayOffsetValue(agileRatesPackage.getOffsetValue());
        agileRatesPackageChargeType.setSaturdayOffsetValue(agileRatesPackage.getOffsetValue());

        return agileRatesPackageChargeType;
    }

    public List<PricingBaseAccomType> retrieveAllFloorCeilingMappingsForProperty() {
        return tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<CPConfigOffsetAccomType> retrieveOffsetsForProducts(List<Integer> productIds) {
        return tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomType.FIND_BY_PRODUCT_IDS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(PRODUCT_IDs, productIds)
                        .parameters());
    }

    public Integer getNonEditingProductIdFromHierarchy(ProductHierarchy hierarchy, Integer editingProductId) {
        if (hierarchy.getToProduct().getId().equals(editingProductId)) {
            return hierarchy.getFromProduct().getId();
        }
        return hierarchy.getToProduct().getId();
    }

    public Set<Integer> getAllProductIdsWithinHierarchies(List<ProductHierarchy> hierarchies) {
        return hierarchies.stream()
                .map(hierarchy -> List.of(hierarchy.getToProduct().getId(), hierarchy.getFromProduct().getId()))
                .flatMap(List::stream)
                .collect(toSet());
    }

    private void saveDefaultAgileRatesPackage(AgileRatesPackage agileRatesPackage) {
        saveDefaultAgileRatesPackage(agileRatesPackage, new ArrayList<>());
    }

    public void saveDefaultAgileRatesPackage(AgileRatesPackage agileRatesPackage, List<AgileRatesPackageChargeType> packagesChargeTypesTobeAdded) {
        List<AgileRatesPackageChargeType> packageChargeTypeList = buildDefaultPackageChargeTypeItems(agileRatesPackage);

        // if package is getting created from older UI and if chargeType is perChild,copy offset value and set it for all child buckets, same for adult
        for (AgileRatesPackageChargeType item : packageChargeTypeList) {
            item.setOffsetMethod(agileRatesPackage.getOffsetMethod());
            if (agileRatesPackage.getChargeType().equals(AgileRatesChargeType.PER_CHILD)
                    && (OccupancyType.isChildBucket(item.getOccupancyType()) || item.getOccupancyType().equals(OccupancyType.EXTRA_CHILD))) {
                AgileRatesPackageChargeType.setOffsetValue(item, agileRatesPackage.getOffsetValue());
            } else if (agileRatesPackage.getChargeType().equals(AgileRatesChargeType.PER_ADULT)
                    && item.getOccupancyType().equals(OccupancyType.EXTRA_ADULT)) {
                AgileRatesPackageChargeType.setOffsetValue(item, agileRatesPackage.getOffsetValue());
            } else if (agileRatesPackage.getChargeType().equals(AgileRatesChargeType.SET_AMOUNT)
                    && item.getOccupancyType().equals(AgileRatesChargeType.resolveOccupancyTypeFrom(AgileRatesChargeType.SET_AMOUNT))) {
                AgileRatesPackageChargeType.setOffsetValue(item, agileRatesPackage.getOffsetValue());
            }

        }
        packageChargeTypeList.addAll(packagesChargeTypesTobeAdded);
        LOGGER.info("Creating  default AgileRatesPackageChargeTypes for Property: " + PacmanWorkContextHelper.getPropertyCode());
        tenantCrudService.save(packageChargeTypeList);
    }

    private List<AgileRatesPackageChargeType> addNewPackageInExistingSeasons(AgileRatesPackage agileRatesPackage) {
        List<AgileRatesPackageChargeType> seasons = findAllAgileRatesPackageChargeTypes().stream()
                .filter(item -> item.getStartDate() != null)
                .collect(Collectors.toList());

        Map<String, AgileRatesPackageChargeType> seasonMap = new HashMap<>();
        for (AgileRatesPackageChargeType season : seasons) {
            seasonMap.putIfAbsent(season.getName(), season);
        }

        List<AgileRatesPackageChargeType> packageSeasonsList = new ArrayList<>();
        for (Map.Entry<String, AgileRatesPackageChargeType> entry : seasonMap.entrySet()) {
            // create default entries for a new package  for existing seasons
            List<AgileRatesPackageChargeType> packageChargeTypeList = buildDefaultPackageChargeTypeItems(agileRatesPackage);
            for (AgileRatesPackageChargeType item : packageChargeTypeList) {
                item.setName(entry.getValue().getName());
                item.setStartDate(entry.getValue().getStartDate());
                item.setEndDate(entry.getValue().getEndDate());
                packageSeasonsList.add(item);
            }
        }
        return packageSeasonsList;
    }

    private List<AgileRatesPackageChargeType> buildDefaultPackageChargeTypeItems(AgileRatesPackage agileRatesPackage) {
        List<AgileRatesPackageChargeType> packageChargeTypeList = new ArrayList<>();
        if (agileRatesPackage.getChargeType().equals(AgileRatesChargeType.SET_AMOUNT)) {
            //OccupancyType will be saved as One_Child when ChargeType is Set_Amount
            packageChargeTypeList.add(AgileRatesPackageChargeType.createItem(agileRatesPackage,
                    AgileRatesChargeType.resolveOccupancyTypeFrom(AgileRatesChargeType.SET_AMOUNT)));
        } else {
            List<OccupancyType> OccupancyTypes = OccupancyType.getChildBucketsAndExtraPerson();

            List<OccupantBucketEntity> occupantBucketEntities = perPersonPricingService.getOccupantBuckets();
            for (OccupancyType occupancyType : OccupancyTypes) {
                if (!OccupancyType.isChildBucket(occupancyType) || (occupantBucketEntities != null
                        && occupantBucketEntities.stream().filter(o -> o.getOccupancyType().equals(occupancyType)).count() > 0)) {
                    packageChargeTypeList.add(AgileRatesPackageChargeType.createItem(agileRatesPackage, occupancyType));
                }
            }
        }

        return packageChargeTypeList;
    }

    public List<OccupantBucketEntity> getAllOccupantBuckets(){
        return perPersonPricingService.getOccupantBuckets();
    }

    public void invalidateOverlapMinMaxRooms(List<Product> smallGroupProducts, Integer changedProductId, Map<Integer, InvalidReason> invalidReasonMap) {
        //Need at least 2 products to verify
        if (smallGroupProducts.size() > 1) {
            for (int currentProdIndex = 0; currentProdIndex < smallGroupProducts.size(); currentProdIndex++) {
                Product currentProduct = smallGroupProducts.get(currentProdIndex);
                checkIfMinMaxRoomsOverlapWithOtherProducts(currentProdIndex, currentProduct, smallGroupProducts, changedProductId, invalidReasonMap);
            }
        }
    }

    //Check after saved product
    public void checkIfMinMaxRoomsOverlapWithOtherProducts(Integer currProdIndex, Product currProd, List<Product> smallGroupProducts,
                                                           Integer changedProductId, Map<Integer, InvalidReason> invalidReasonMap) {
        Integer currProdMinRooms = currProd.getMinRooms();
        Integer currProdMaxRooms = currProd.getMaxRooms();

        for (int i = currProdIndex + 1; i < smallGroupProducts.size(); i++) {
            Product otherProduct = smallGroupProducts.get(i);
            Integer otherProdMinRooms = otherProduct.getMinRooms();
            Integer otherProdMaxRooms = otherProduct.getMaxRooms();

            if (minMaxRoomsHasOverlap(currProdMinRooms, currProdMaxRooms, otherProdMinRooms, otherProdMaxRooms)) {
                setInvalidReasonForProductWithOverlapMinMaxRooms(currProd, otherProduct, changedProductId, invalidReasonMap);
            }
        }
    }

    //Check before saving the product to generate warning message
    public void checkIfMinMaxRoomsOverlapWithOtherProducts(Product currProd) {
        Integer currProdMinRooms = currProd.getMinRooms();
        Integer currProdMaxRooms = currProd.getMaxRooms();

        List<Product> otherSmallGroupProducts = findSmallGroupProducts();
        boolean didSetInvalidReason = false;

        for (int i = 0; i < otherSmallGroupProducts.size(); i++) {
            Product otherProduct = otherSmallGroupProducts.get(i);

            //make sure don't compare the same product
            if (!otherProduct.getId().equals(currProd.getId())) {
                Integer otherProdMinRooms = otherProduct.getMinRooms();
                Integer otherProdMaxRooms = otherProduct.getMaxRooms();

                if (minMaxRoomsHasOverlap(currProdMinRooms, currProdMaxRooms, otherProdMinRooms, otherProdMaxRooms)) {
                    currProd.setInvalidReason(InvalidReason.INVALID_OVERLAP_ROOM_SIZE);
                    didSetInvalidReason = true;
                }
            }
        }

        //did not have overlap, reset InvalidReason
        if (!didSetInvalidReason) {
            if ((currProd.getInvalidReason() != null) && (currProd.getInvalidReason().equals(InvalidReason.INVALID_OVERLAP_ROOM_SIZE))) {
                currProd.setInvalidReason(null);
            }
        }
    }

    @VisibleForTesting
	public
    boolean minMaxRoomsHasOverlap(Integer minProd1, Integer maxProd1, Integer minProd2, Integer maxProd2) {
        if (maxProd1 == null)
            maxProd1 = Integer.MAX_VALUE;
        else if (maxProd2 == null)
            maxProd2 = Integer.MAX_VALUE;
        return (minProd1 != null) && ((minProd1 <= maxProd2) && (maxProd1 >= minProd2));
    }

    private void setInvalidReasonForProductWithOverlapMinMaxRooms(Product currProd, Product otherProd, Integer changedProdId, Map<Integer, InvalidReason> invalidReasonMap) {
        //set invalid for product being edited
        if (currProd.getId().equals(changedProdId)) {
            currProd.setInvalidReason(InvalidReason.INVALID_OVERLAP_ROOM_SIZE);
        } else if (otherProd.getId().equals(changedProdId)) {
            otherProd.setInvalidReason(InvalidReason.INVALID_OVERLAP_ROOM_SIZE);
        } else {
            //when product is not the one being edited, use the hashmap to verify status
            InvalidReason currProdReason = invalidReasonMap.get(currProd.getId());
            if ((currProdReason != null) && (InvalidReason.INVALID_OVERLAP_ROOM_SIZE.equals(currProdReason))) {
                currProd.setInvalidReason(InvalidReason.INVALID_OVERLAP_ROOM_SIZE);
            } else {
                otherProd.setInvalidReason(InvalidReason.INVALID_OVERLAP_ROOM_SIZE);
            }
        }
    }

    public void invalidateSeqMinMaxRooms(List<Product> smallGroupProducts, Integer changedProductId) {
        if (!isSmallGroupPopUpEnabled() && smallGroupProducts.size() > 1) {
            smallGroupProducts.forEach(
                    currentProduct -> checkIfMinMaxRoomsSequenceBreakingWithOtherProductsPostSave(currentProduct, smallGroupProducts, changedProductId));
        }
    }

    private boolean isSmallGroupPopUpEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SMALL_GRP_MIN_MAX_ROOM_POPUP);
    }

    private void checkIfMinMaxRoomsSequenceBreakingWithOtherProductsPostSave(Product currProd, List<Product> smallGroupProducts, Integer changedProductId) {
        if (!isProdInSeqWithPrevProds(currProd, smallGroupProducts)) {
            if (currProd.getId().equals(changedProductId)) {
                currProd.setInvalidReason(InvalidReason.INVALID_SEQUENCE);
            }
        } else if ((currProd.getInvalidReason() != null) && (currProd.getInvalidReason().equals(InvalidReason.INVALID_SEQUENCE))) {
            currProd.setInvalidReason(null);
        }
    }

    public void checkIfMinMaxRoomsSequenceBreakingWithOtherProductsPreSave(Product currProd) {
        if (isSmallGroupPopUpEnabled())
            return;

        boolean isSeqWithPrevProds = isProdInSeqWithPrevProds(currProd, findSmallGroupProducts());

        if (!isSeqWithPrevProds) {
            currProd.setInvalidReason(InvalidReason.INVALID_SEQUENCE);
        }

        if (isSeqWithPrevProds) {
            if ((currProd.getInvalidReason() != null) && (currProd.getInvalidReason().equals(InvalidReason.INVALID_SEQUENCE))) {
                currProd.setInvalidReason(null);
            }
        }
    }

    @VisibleForTesting
	public
    boolean isProdInSeqWithPrevProds(Product currProd, List<Product> otherSmallGroupProducts) {
        if (otherSmallGroupProducts.isEmpty() || isCurrProdTheOnlySmallGroup(currProd, otherSmallGroupProducts) || isCurrProdMinMaxNotSet(currProd)) {
            return true;
        }
        return otherSmallGroupProducts.stream()
                .anyMatch((prevProd) ->
                        !prevProd.getId().equals(currProd.getId()) &&
                                (prevProd.getMaxRooms().equals(currProd.getMinRooms() - 1) || prevProd.getMinRooms().equals(currProd.getMaxRooms() + 1))
                );
    }

    private boolean isCurrProdTheOnlySmallGroup(Product currProd, List<Product> smallGroupProds) {
        return smallGroupProds.size() == 1 && smallGroupProds.get(0).getId().equals(currProd.getId());
    }

    private boolean isCurrProdMinMaxNotSet(Product currProd) {
        return currProd.getMinRooms() == null || currProd.getMaxRooms() == null;
    }

    public AgileRatesProductConfigurationDTO loadSmallGroupProductConfigurationByProductId(Integer productId) {
        Product product = tenantCrudService.find(Product.class, productId);
        if (product == null) {
            return null;
        }
        AgileRatesProductConfigurationDTO dto = new AgileRatesProductConfigurationDTO(product);
        dto.setProductType(AgileRatesProductTypeEnum.fromValue(product.getType()));
        Integer baseProductId = product.getDependentProductId();
        if (baseProductId != null) {
            dto.setBaseProduct(tenantCrudService.find(Product.class, baseProductId));
        }
        dto.setRateCodes(findRateCodesByProduct(product));
        List<AccomType> roomTypesByProduct = findRoomTypesByProduct(product);
        dto.setRoomTypes(roomTypesByProduct);
        dto.setPackageElements(findPackagesByProduct(product));
        List<ProductRateOffset> productRateOffsetsByProduct = findProductRateOffsetsByProduct(product);
        dto.setDefaultOffsets(productRateOffsetsByProduct.stream().filter(productRateOffset -> productRateOffset.getStartDate() == null).collect(Collectors.toList()));
        dto.setProductSeasons(loadAgileRatesSeasonByProductId(dto, productRateOffsetsByProduct));
        return dto;
    }

    public List<Product> filterProducts(List<Product> allProducts, Predicate<Product> filter) {
        return allProducts.stream()
                .filter(filter)
                .collect(Collectors.toList());
    }

    public WebrateType findWebrateTypeByRateTypeCode(String rateTypeCode) {
        return tenantCrudService.findByNamedQuerySingleResult(WebrateType.BY_TYPE_CODE, QueryParameter.with("code", rateTypeCode).parameters());
    }

    public WebrateType findWebrateTypeByWebrateTypeId(Integer webrateTypeId) {
        return tenantCrudService.findByNamedQuerySingleResult(WebrateType.BY_ID, QueryParameter.with("webrateTypeId", webrateTypeId).parameters());
    }

    public List<WebrateTypeProductDTO> findMinMaxLOSForEachPropertyIdByWebrateTypeId(Integer webrateTypeId) {
        Map<String, Object> parameters = QueryParameter.with("webrateTypeId", webrateTypeId).parameters();
        return tenantCrudService.findByNamedQuery(WebrateTypeProduct.GET_MIN_AND_MAX_LOS_FOR_EACH_PRODUCT_BY_WEBRATE_TYPE, parameters);
    }

    public WebrateTypeProductDTO findMinMaxLOSAndWebrateTypeByProductId(Integer productId) {
        Map<String, Object> parameters = QueryParameter.with("productId", productId).parameters();
        return tenantCrudService.findByNamedQuerySingleResult(WebrateTypeProduct.GET_MIN_AND_MAX_LOS_FOR_PRODUCT_BY_PRODUCT_ID, parameters);
    }

    public List<WebrateTypeProduct> findAllWebrateTypeProduct() {
        return tenantCrudService.findAll(WebrateTypeProduct.class);
    }

    public String findWebrateTypeNameByProductId(Integer productId) {
        Map<String, Object> parameters = QueryParameter.with("productId", productId).parameters();
        return tenantCrudService.findByNamedQuerySingleResult(WebrateType.GET_TYPE_NAME_BY_PRODUCT_ID, parameters);
    }

    public void deleteWebrateTypeProductForProductId(Integer productId) {
        Map<String, Object> parameters = QueryParameter.with("productId", productId).parameters();
        tenantCrudService.executeUpdateByNamedQuery(WebrateTypeProduct.DELETE_BY_PRODUCT_ID, parameters);
        tenantCrudService.flush();
    }

    public void saveWebrateTypeDataForIndependentProduct(IndependentProductConfigurationDTO dto) {
        String rateType = dto.getRateType();
        if (rateType != null && !rateType.equals(RATE_TYPE_NA_CODE) && getWebrateTypes().contains(rateType)) {
            WebrateType webrateType = findWebrateTypeByRateTypeCode(rateType);

            Integer LOSMin = dto.getRateTypeLOSMin();
            Integer LOSMax = dto.getRateTypeLOSMax();

            boolean changingFromCustomLOStoAllLOS = (LOSMin != null && LOSMax != null) && (LOSMin.equals(Integer.valueOf(-1)) && LOSMax.equals(Integer.valueOf(-1)));
            //When the All LOS option for Rate Type LOS is selected, we want to set LOS from 1 to 365
            if ((LOSMin == null && LOSMax == null) || changingFromCustomLOStoAllLOS) {
                LOSMin = DEFAULT_RATE_TYPE_MIN_LOS;
                LOSMax = DEFAULT_RATE_TYPE_MAX_LOS;
            }

            if (LOSMin != null && LOSMax != null) {
                int losRange = LOSMax.intValue() - LOSMin.intValue();
                if (losRange >= 0) {
                    //Since Rate Type LOS is a range, we need to insert separate entries
                    for (int los = LOSMin.intValue(); los <= LOSMax.intValue(); los++) {
                        WebrateTypeProduct webrateTypeDto = new WebrateTypeProduct();
                        webrateTypeDto.setProduct(dto.getProduct());
                        webrateTypeDto.setLos(los);
                        webrateTypeDto.setWebrateType(webrateType);
                        tenantCrudService.save(webrateTypeDto);
                    }
                } else {
                    LOGGER.error("The Rate Type LOS Range: " + losRange + " for Independent Product is invalid. Cannot save the WebrateType data.");
                    throw new TetrisException(ErrorCode.INVALID_RATE_TYPE_LOS_RANGE,
                            String.format("The Rate Type LOS Range: %s is invalid, cannot save WebrateType data for Independent Product", losRange));
                }
            }
        }
    }

    public void saveWebrateTypeDataForLinkedProduct(AgileRatesProductConfigurationDTO dto) {
        if (!dto.getRateType().equals(RATE_TYPE_NA_CODE)) {
            WebrateType webrateType = findWebrateTypeByRateTypeCode(dto.getRateType());

            Integer LosMin = dto.getRateTypeLOSMin();
            Integer LosMax = dto.getRateTypeLOSMax();

            boolean changingFromCustomLOStoAllLOS = (LosMin != null && LosMax != null) && (LosMin.equals(Integer.valueOf(-1)) && LosMax.equals(Integer.valueOf(-1)));
            //When the All LOS option for Rate Type LOS is selected, we want to set LOS from 1 to 365
            if ((LosMin == null && LosMax == null) || changingFromCustomLOStoAllLOS) {
                LosMin = DEFAULT_RATE_TYPE_MIN_LOS;
                LosMax = DEFAULT_RATE_TYPE_MAX_LOS;
            }

            if (LosMin != null && LosMax != null) {
                int losRange = LosMax.intValue() - LosMin.intValue();
                if (losRange >= 0) {
                    //Since Rate Type LOS is a range, we need to insert separate entries
                    for (int los = LosMin.intValue(); los <= LosMax.intValue(); los++) {
                        WebrateTypeProduct webrateTypeDto = new WebrateTypeProduct();
                        webrateTypeDto.setProduct(dto.getProduct());
                        webrateTypeDto.setLos(los);
                        webrateTypeDto.setWebrateType(webrateType);
                        tenantCrudService.save(webrateTypeDto);
                    }
                } else {
                    LOGGER.error("The Rate Type LOS Range: " + losRange + " for Linked Product is invalid. Cannot save the WebrateType data.");
                    throw new TetrisException(ErrorCode.INVALID_RATE_TYPE_LOS_RANGE,
                            String.format("The Rate Type LOS Range: %s is invalid, cannot save WebrateType data for Linked Product", losRange));
                }
            }
        }
    }

    private void setRateTypeDataForIndependentProduct(IndependentProductConfigurationDTO dto, Integer productId) {
        WebrateTypeProductDTO rateTypeAndMinMaxLOS = findMinMaxLOSAndWebrateTypeByProductId(productId);
        if (rateTypeAndMinMaxLOS != null) {
            Integer webrateTypeId = rateTypeAndMinMaxLOS.getWebrateTypeId();
            Integer minLOS = rateTypeAndMinMaxLOS.getMinLOS();
            Integer maxLOS = rateTypeAndMinMaxLOS.getMaxLOS();
            WebrateType webrateType = findWebrateTypeByWebrateTypeId(webrateTypeId);
            dto.setRateType(webrateType.getWebrateTypeCode());
            boolean isAllLOS = minLOS.equals(Integer.valueOf(1)) && maxLOS.equals(Integer.valueOf(365));
            if (!isAllLOS) {
                dto.setRateTypeLOSMin(minLOS);
                dto.setRateTypeLOSMax(maxLOS);
            }
        } else {
            dto.setRateType(RATE_TYPE_NA_CODE);
        }
    }

    private void setRateTypeDataForLinkedProduct(AgileRatesProductConfigurationDTO dto, Integer productId) {
        WebrateTypeProductDTO rateTypeAndMinMaxLOS = findMinMaxLOSAndWebrateTypeByProductId(productId);
        if (rateTypeAndMinMaxLOS != null) {
            Integer webrateTypeId = rateTypeAndMinMaxLOS.getWebrateTypeId();
            Integer minLOS = rateTypeAndMinMaxLOS.getMinLOS();
            Integer maxLOS = rateTypeAndMinMaxLOS.getMaxLOS();
            WebrateType webrateType = findWebrateTypeByWebrateTypeId(webrateTypeId);
            dto.setRateType(webrateType.getWebrateTypeCode());
            boolean isAllLOS = minLOS.equals(Integer.valueOf(1)) && maxLOS.equals(Integer.valueOf(365));
            if (!isAllLOS) {
                dto.setRateTypeLOSMin(minLOS);
                dto.setRateTypeLOSMax(maxLOS);
            }
        } else {
            dto.setRateType(RATE_TYPE_NA_CODE);
        }
    }

    public boolean isRDLEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
    }

    public void saveSeasonForSmallGroupProduct(AgileRatesSeason season) {
        java.time.LocalDate startDate = season.getOriginalJavaStartDate() != null ? season.getOriginalJavaStartDate() : season.getJavaStartDate();
        updateProductRateOffsets(season.getSeasonRateOffsets(), getPersistedSeasonOffsets(season.getProduct(), DateUtil.convertJavaToJodaLocalDate(startDate)));
        validateSmallGroupProductsWithProductId(season.getProduct().getId());
        registerSmallGroupProductChangedSyncEvent();
        pricingConfigurationLTBDEService.enabledLTBDEIfApplicable(season.getJavaEndDate(), dateService.getCaughtUpJavaLocalDate());
    }

    public void deleteProductSeasonsForSmallGroupProduct(AgileRatesSeason productSeason, Integer productId) {
        tenantCrudService.delete(productSeason.getSeasonRateOffsets());
        validateSmallGroupProductsWithProductId(productId);
        registerSmallGroupProductChangedSyncEvent();
        pricingConfigurationLTBDEService.enabledLTBDEIfApplicable(productSeason.getJavaEndDate(), dateService.getCaughtUpJavaLocalDate());
    }

    public List<WebrateType> getAllWebrateTypes() {
        return tenantCrudService
                .findByNamedQuery(WebrateType.BY_TYPE_CODES,
                        QueryParameter.with("webrateTypeCodes", WEB_RATE_TYPES_FOR_UI)
                                .parameters());
    }

    public List<String> getWebrateTypes() {
        return getAllWebrateTypes().stream().map(WebrateType::getWebrateTypeCode).collect(toList());
    }

    public int updateMinLOSAndMaxLOSByName(final int minLOS, final int maxLOS, final String name) {
        final Map<String, Object> parameters = QueryParameter
                .with(NAME, name)
                .and(MIN_LOS, minLOS)
                .and(MAX_LOS, maxLOS)
                .parameters();
        return tenantCrudService.executeUpdateByNativeQuery(UPDATE_PRODUCT_MIN_LOS_AND_MAX_LOS_BY_NAME, parameters);
    }

    public int updateRateShoppingLOSForIndependentProducts(final int rateShoppingMinLOS, final int maxLOS, final String name) {
        final Map<String, Object> parameters = QueryParameter
                .with(NAME, name)
                .and(MIN_LOS, rateShoppingMinLOS)
                .and(MAX_LOS, maxLOS)
                .parameters();
        return tenantCrudService.executeUpdateByNativeQuery(UPDATE_RATE_SHOPPING_MIN_LOS_AND_MAX_LOS_BY_NAME_FOR_INDEPENDENT_PRODUCTS, parameters);
    }

    @VisibleForTesting
	public
    List<RateCodeVendorMapping> getRateCodeVendorMappingsForProduct(Integer productId) {
        Product product = findProductById(productId);
        if (product == null) {
            return Collections.emptyList();
        }
        return rateCodeVendorMappingService.getForRateCodeName(product.getName());
    }

    public boolean isVendorCodeMappingPresentForProduct(Integer currentProductId) {
        return CollectionUtils.isNotEmpty(getRateCodeVendorMappingsForProduct(currentProductId));
    }

    public void deleteVendorMappingForProductIfPresent(Integer currentProductId) {
        List<RateCodeVendorMapping> rateCodeVendorMappings = getRateCodeVendorMappingsForProduct(currentProductId);
        if (CollectionUtils.isNotEmpty(rateCodeVendorMappings)) {
            rateCodeVendorMappingService.remove(rateCodeVendorMappings);
        }
    }

    public OccupancyType getBaseOccupancyType() {
        boolean isPerPersonEnabled = configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED);
        return isPerPersonEnabled ? OccupancyType.DOUBLE : OccupancyType.SINGLE;
    }

    public AgileRateProductResponseDTO deleteProducts(AgileRateProductDTO agileRateProducts) {

        List<String> nonDeletedProducts;
        List<String> deletedProducts = new ArrayList<>();

        List<Product> productList = getProductsByName(agileRateProducts.getProducts());
        if (CollectionUtils.sizeIsEmpty(productList)) {
            return createAgileRateProductResponseDTO(new ArrayList<>(), agileRateProducts.getProducts());
        }

        for (Product product : productList) {

            Product updatedDisplayOrderProduct = getProductByName(product.getName());

            if (isPrimaryPricedProduct(updatedDisplayOrderProduct.getName())) {
                continue;
            }
            Date caughtUpDate = dateService.getCaughtUpDate();
            List<Product> productsToUpdate = getAllProducts().stream()
                    .filter(p -> p.getDisplayOrder() > updatedDisplayOrderProduct.getDisplayOrder())
                    .collect(Collectors.toList());
            deleteIfIndependentProduct(updatedDisplayOrderProduct, caughtUpDate, deletedProducts);
            deleteIfSmallGroupProduct(updatedDisplayOrderProduct, caughtUpDate);
            saveAgileRatesProductHierarchies(Collections.emptyList(), getAllHierarchiesForProduct(updatedDisplayOrderProduct));
            deleteIfLinkedProduct(updatedDisplayOrderProduct, caughtUpDate);
            deletedProducts.add(updatedDisplayOrderProduct.getName());
            shiftProductDisplayOrder(productsToUpdate);
        }
        nonDeletedProducts = getNonDeletedProducts(agileRateProducts, deletedProducts);
        return createAgileRateProductResponseDTO(deletedProducts, nonDeletedProducts);
    }

    public Product getProductByName(String name) {
        return tenantCrudService.findByNamedQuerySingleResult(Product.GET_ALL_BY_NAME,
                QueryParameter.with("name", name).parameters());
    }

    private List<String> getNonDeletedProducts(AgileRateProductDTO agileRateProducts, List<String> deletedProducts) {
        List<String> allProducts = agileRateProducts.getProducts();
        allProducts.removeAll(deletedProducts);
        return allProducts;
    }

    private void deleteIfLinkedProduct(Product product, Date caughtUpDate) {
        deleteLinkedProduct(product, LocalDate.fromDateFields(caughtUpDate));
        deleteProductAssociatedRestrictions(product);
    }

    private void deleteIfSmallGroupProduct(Product product, Date caughtUpDate) {
        if (product.isGroupProduct()) {
            deleteSmallGroupProduct(product, LocalDate.fromDateFields(caughtUpDate));
            deleteProductAssociatedRestrictions(product);
        }
    }

    private void deleteIfIndependentProduct(Product product, Date caughtUpDate, List<String> deletedProducts) {
        //Find all products for the selected independent primary product
        if (product.isIndependentProduct()) {
            List<Product> relatedProducts = AgileRatesUtils.getChildren(product, getAllProducts());
            deleteIndependentProduct(product, relatedProducts, LocalDate.fromDateFields(caughtUpDate));
        }
    }

    private boolean isPrimaryPricedProduct(String productName) {
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        return product.getName().equalsIgnoreCase(productName);
    }

    private AgileRateProductResponseDTO createAgileRateProductResponseDTO(List<String> deletedProducts, List<String> nonDeletedProducts) {
        AgileRateProductResponseDTO response = new AgileRateProductResponseDTO();

        String messageForDeletedProducts = CollectionUtils.isEmpty(deletedProducts) ? "Failure" : "Successfully deleted products";
        String messageForNotDeletedProducts = CollectionUtils.isEmpty(nonDeletedProducts) ? "" : "Failed to delete products";

        response.setMessageForDeletedProducts(messageForDeletedProducts);
        response.setMessageForNotDeletedProducts(messageForNotDeletedProducts);
        response.setDeletedProducts(deletedProducts);
        response.setNotDeletedProducts(nonDeletedProducts);
        return response;
    }

    public Collection<Product> getAllProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_ALL);
    }

    public List<Product> getProductsByName(List<String> names) {
        if (CollectionUtils.isNotEmpty(names)) {
            return tenantCrudService.findByNamedQuery(Product.GET_ALL_BY_NAMES, QueryParameter.with("names", names).parameters());
        }
        return Collections.emptyList();
    }

    public boolean isIndependentProductsToggleEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    public List<Product> getAgileAndSystemDefaultProductsAndIndependentProducts() {
        List<Product> allProductsList = findAgileAndSystemDefaultProductsAndIndependentProducts();
        List<Product> products;
        if (isIndependentProductsToggleEnabled() || isRDLEnabled()) {
            products = allProductsList;
        } else {
            Product barProduct = allProductsList.stream().filter(Product::isSystemDefault).findFirst().orElse(null);
            products = new ArrayList<>(Collections.singletonList(barProduct));
        }
        return products;
    }

    public CeilingFloor resolveCeilingFloorForEditingProducts(PricingBaseAccomType ceilingFloorOfEditingProduct, BigDecimal floorOverride,
                                                              BigDecimal ceilingOverride, LocalDate date) {

        if (ceilingFloorOfEditingProduct == null) {
            return new CeilingFloor();
        }
        CeilingFloor ceilingFloorForParticularDay = retrieveCeilingFloorForParticularDate(ceilingFloorOfEditingProduct, date);
        return constructCeilingFloor(ceilingFloorForParticularDay, floorOverride, ceilingOverride);
    }

    public Tuple2<BigDecimal, OffsetMethod> retrieveOffsetForGivenDate(CPConfigOffsetAccomType offset, LocalDate comparedDate) {

        DayOfWeek dayofWeek = LocalDateUtils.getDayOfWeek(comparedDate);
        // Return the corresponding offset value for the day of the week
        switch (Objects.requireNonNull(dayofWeek)) {
            case SUNDAY:
                return Tuple.of(offset.getSundayOffsetValue(), offset.getOffsetMethod());
            case MONDAY:
                return Tuple.of(offset.getMondayOffsetValue(), offset.getOffsetMethod());
            case TUESDAY:
                return Tuple.of(offset.getTuesdayOffsetValue(), offset.getOffsetMethod());
            case WEDNESDAY:
                return Tuple.of(offset.getWednesdayOffsetValue(), offset.getOffsetMethod());
            case THURSDAY:
                return Tuple.of(offset.getThursdayOffsetValue(), offset.getOffsetMethod());
            case FRIDAY:
                return Tuple.of(offset.getFridayOffsetValue(), offset.getOffsetMethod());
            case SATURDAY:
                return Tuple.of(offset.getSaturdayOffsetValue(), offset.getOffsetMethod());
            default:
                return null;
        }
    }

    public BigDecimal calculateFloorRelativeToPrimaryProductWhenRoundingRuleChanges(String percentageOffValue, Product childProduct,
                                                                                    Integer baseProductID, RoundingRule roundingRule) {
        BigDecimal lowestFloorValue = getFloorOfBaseProduct(baseProductID);
        BigDecimal percentageOffPrimaryFloor = BigDecimal.ONE.add(new BigDecimal(percentageOffValue).divide(BigDecimal.valueOf(100), 5, RoundingMode.HALF_UP));
        Map<Integer, PricingRule> pricingRules = prettyPricingService.getPricingRules();
        if (lowestFloorValue == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal floor = lowestFloorValue.multiply(percentageOffPrimaryFloor).setScale(2, RoundingMode.HALF_UP);
        roundingRule = Objects.nonNull(roundingRule) ? roundingRule : childProduct.getRoundingRule();
        return applyProductRoundingRule(baseProductID, roundingRule, floor, pricingRules);
    }

    public List<Product> getApplicableProductsForPickUpAndChangeReport() {
        List<Product> products;
        boolean isGroupProductsEnabled = isSmallGroupProductsToggleEnabled();
        boolean isIndependentProductsEnabled = isIndependentProductsToggleEnabled();
        if (isGroupProductsEnabled && isIndependentProductsEnabled) {
            products = findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts();
        } else if (isIndependentProductsEnabled) {
            products = findAgileAndSystemDefaultProductsAndIndependentProducts();
        } else if (isGroupProductsEnabled) {
            products = findAgileAndSystemDefaultProductsAndSmallGroupProducts();
        } else {
            products = findAgileAndSystemDefaultProducts();
        }
        if (stopStoringPaceForNonOptimizedNonUploadableProductsEnabled()) {
            List<Product> filteredProducts = products.stream().
                    filter(p -> !(p.getCode().equals(Product.AGILE_RATES_PRODUCT_CODE) && !p.isUpload() && !p.isOptimized()))
                    .collect(Collectors.toList());
            products = filteredProducts;
        }
        products.sort(Comparator.comparing(Product::getDisplayOrder));
        return products;
    }

    public boolean isSmallGroupProductsToggleEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED);
    }

    public boolean stopStoringPaceForNonOptimizedNonUploadableProductsEnabled(){
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.STOP_STORING_PACE_FOR_NON_OPTIMIZED_NON_UPLOADABLE_PRODUCTS);
    }

    public List<String> validateAndCreateHierarchy(Set<HierarchyDTO> hierarchy) {

        List<String> hierarchyWarning = new ArrayList<>();
        Set<AgileRatesHierarchyDTO> hierarchyToSave = new HashSet<>();

        List<String> dtoValidationError = validateHierarchyDTO(hierarchy);
        if(isNotEmpty(dtoValidationError)) {
            return dtoValidationError;
        }

        List<String> nonExistingProducts = getNonExistingProducts(hierarchy);
        if(isNotEmpty(nonExistingProducts)){
            return List.of("Products do not exist: " + nonExistingProducts);
        }

        List<Product> allProducts = getEligibleProductsForHierarchy();
        List<String> invalidSelectedProduct = getInvalidSelectedProduct(hierarchy, allProducts);
        if (isNotEmpty(invalidSelectedProduct)) return invalidSelectedProduct;

        Map<String, String> invalidRelatedProduct = getInvalidRelatedProduct(hierarchy, allProducts);
        if (MapUtils.isNotEmpty(invalidRelatedProduct)) return getInvalidRelatedProductError(invalidRelatedProduct);

        validateHierarchy(hierarchy,hierarchyWarning, hierarchyToSave, allProducts);

        if (!hierarchyWarning.isEmpty()) {
            return hierarchyWarning;
        }else{
            saveHierarchy(hierarchyToSave);
            return emptyList();
        }
    }

    private List<String> validateHierarchyDTO(Set<HierarchyDTO> hierarchy) {
        List<String> errors = new ArrayList<>();
        for(HierarchyDTO dto : hierarchy) {
            String selectedProduct = dto.getSelectedProduct();
            String relatedProduct = dto.getRelatedProduct();
            BigDecimal minimumDiff = dto.getMinimumDifference();
            if(isNull(selectedProduct) || StringUtils.isEmpty(selectedProduct) || "BAR".equalsIgnoreCase(selectedProduct)){
                errors.add("Selected Product cannot be Empty,'NULL' or 'BAR'");
            }
            if(isNull(relatedProduct) || StringUtils.isEmpty(relatedProduct)){
                errors.add("Related Product cannot be Empty or 'NULL'");
            }
            if(minimumDiff == null || ((minimumDiff.compareTo(BigDecimal.ZERO) < 0))) {
                errors.add("Please enter a valid minimum difference that is greater than or equal to 0.");
            }
        }
        return errors;
    }

    private void saveHierarchy(Set<AgileRatesHierarchyDTO> hierarchyToSave) {
        List<ProductHierarchy> listToBeSaved = new ArrayList<>();
        hierarchyToSave.forEach(dto -> {
            listToBeSaved.add(getEntityToBeSaved(dto));
        });
        saveAgileRatesProductHierarchies(listToBeSaved, emptyList());
    }

    private void validateHierarchy(Set<HierarchyDTO> hierarchy, List<String> hierarchyWarning,
                                   Set<AgileRatesHierarchyDTO> hierarchyToSave, List<Product> allProducts) {
        Map<String, String> warningsMap = getAllHierarchyWarnings();
        int row =0;
        for(HierarchyDTO hierarchyDTO : hierarchy) {

            Product selectedProduct = getProductByName(hierarchyDTO.getSelectedProduct());
            Product relatedProduct = getRelatedProduct(hierarchyDTO, allProducts, selectedProduct);
            List<String> warnings = validateProductHierarchy(allProducts,selectedProduct, relatedProduct, hierarchyDTO.getMinimumDifference());

            if (CollectionUtils.isNotEmpty(warnings)) {
                warnings.forEach(w -> hierarchyWarning.add(warningsMap.getOrDefault(w,w)));
            }else{
                AgileRatesHierarchyDTO dto = new AgileRatesHierarchyDTO(row++,selectedProduct,relatedProduct,hierarchyDTO.getMinimumDifference(),true);
                hierarchyToSave.add(dto);
            }
        }
    }


    private Product getRelatedProduct(HierarchyDTO hierarchyDTO, List<Product> allProducts, Product selectedProduct) {
        List<Product> eligibleRelatedProducts = findEligibleRelationshipProducts(selectedProduct,allProducts);

        return eligibleRelatedProducts.stream()
                .filter(h -> h.getName().equalsIgnoreCase(hierarchyDTO.getRelatedProduct()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(
                        hierarchyDTO.getRelatedProduct() + "cannot form hierarchy with " + hierarchyDTO.getSelectedProduct())
                );
    }

    public ProductHierarchy getEntityToBeSaved(AgileRatesHierarchyDTO dto) {
        List<ProductHierarchy> productHierarchies = getProductHierarchies();
        ProductHierarchy productHierarchy = productHierarchies.stream()
                .filter(hierarchy -> hierarchy.getFromProduct().equals(dto.getSelectedProduct()))
                .filter(hierarchy -> hierarchy.getToProduct().equals(dto.getSelectedRelationshipProduct()))
                .findFirst()
                .orElse(null);
        if (productHierarchy != null) {
            productHierarchy.setMinimumDifference(dto.getMinimumDifference());
            return productHierarchy;
        } else {
            Optional<ProductHierarchy> optional = productHierarchies.stream()
                    .filter(hierarchy -> hierarchy.getFromProduct().equals(dto.getSelectedProduct()))
                    .filter(hierarchy -> hierarchy.getToProduct().equals(dto.getPreviousSelectedRelationshipProduct()))
                    .findFirst();
            if (optional.isPresent()) {
                ProductHierarchy productChangedHierarchy = optional.get();
                productChangedHierarchy.setToProduct(dto.getSelectedRelationshipProduct());
                productChangedHierarchy.setMinimumDifference(dto.getMinimumDifference());
                return productChangedHierarchy;
            } else {
                ProductHierarchy newHierarchy = new ProductHierarchy();
                newHierarchy.setFromProduct(dto.getSelectedProduct());
                newHierarchy.setToProduct(dto.getSelectedRelationshipProduct());
                newHierarchy.setMinimumDifference(dto.getMinimumDifference());
                return newHierarchy;
            }
        }
    }

    private Map<String, String> getAllHierarchyWarnings(){
        Map<String,String> error = new HashMap<>();
        error.put("invalid.hierarchy.max.adjustment.value.linked.product","Invalid hierarchy. Ensure that the Maximum Adjustment value of the Product is less than or equal to the Maximum Adjustment values of the product that it Relates to.");
        error.put("invalid.hierarchy.overlap.adjustment.value.linked.product","Invalid hierarchy. Ensure that there is an overlap in the Adjustment range of the two products.");
        error.put("invalid.hierarchy.mindiff.value.linked.product","Invalid hierarchy. Ensure that the Minimum Difference is less than the difference between the Minimum Adjustment of the Product and the Maximum Adjustment of the product that it Relates to.");
        error.put("invalid.hierarchy.excluded.value","Invalid hierarchy. Ensure that the value of the (lower priced) Product is less than or equal to the value of the (higher-priced) product that it Relates to.");
        error.put("invalid.hierarchy.floor.value", "Invalid hierarchy. Ensure that the Floor value of the (lower priced) Product is less than or equal to the Floor values of the (higher-priced) product that it Relates to.");
        error.put("invalid.hierarchy.ceiling.value", "Invalid hierarchy. Ensure that the Ceiling value of the Product is less than or equal to the Ceiling values of the product that it Relates to.");
        error.put("invalid.hierarchy.overlap.value", "Invalid hierarchy. Ensure that there is an overlap in the price range of the two products.");
        error.put("invalid.hierarchy.mindiff.value", "Invalid hierarchy. Ensure that the Minimum Difference is less than the difference between the Floor of the Product and the Ceiling of the product that it Relates to.");
        error.put("invalid.hierarchy.min.adjustment.value.linked.product","Invalid hierarchy. Ensure that the Minimum Adjustment value of the (lower priced) Product is less than or equal to the Minimum Adjustment values of the (higher-priced) product that it Relates to.");
        return error;
    }
    private List<String> getInvalidSelectedProduct(Set<HierarchyDTO> hierarchy, List<Product> allProducts) {
        List<String> eligibleSelectedProducts = getHierarchyProducts(allProducts).stream()
                .filter(p -> !p.isSystemDefault()).map(Product::getName).collect(toList());
        List<String> selectedProducts = hierarchy.stream().map(HierarchyDTO::getSelectedProduct).collect(toList());
        List<String> ineligibleSelectProducts = selectedProducts.stream().filter(product -> !eligibleSelectedProducts.contains(product)).collect(toList());
        if(isNotEmpty(ineligibleSelectProducts)){
            return List.of("Selected Products are not valid:" + ineligibleSelectProducts);
        }
        return emptyList();
    }

    private Map<String, String> getInvalidRelatedProduct(Set<HierarchyDTO> hierarchy, List<Product> allProducts) {
        Map<String, String> invalidProductHierarchy = new HashMap<>();
        for(HierarchyDTO hierarchyDTO : hierarchy) {
            Product product = getProductByName(hierarchyDTO.getSelectedProduct());
            List<Product> eligibleRelatedProducts = findEligibleRelationshipProducts(product, allProducts);
            List<String> eligibleRelatedProductNames = eligibleRelatedProducts.stream().map(Product::getName).collect(toList());
            if (!eligibleRelatedProductNames.contains(hierarchyDTO.getRelatedProduct())) {
                invalidProductHierarchy.put(hierarchyDTO.getSelectedProduct(),hierarchyDTO.getRelatedProduct());
            }
        }
        return invalidProductHierarchy;
    }

    private List<String> getInvalidRelatedProductError(Map<String, String> invalidProductHierarchy) {
        List<String> errors = new ArrayList<>();
        for(Map.Entry<String,String> hierarchy : invalidProductHierarchy.entrySet()) {
            errors.add(hierarchy.getValue() +" cannot form hierarchy with "+hierarchy.getKey());
        }
        return errors;
    }

    public List<Product> getEligibleProductsForHierarchy() {
        List<Product> allProducts;
        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        boolean isGroupProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED);

        if (isIndependentProductsEnabled && isGroupProductsEnabled) {
            allProducts = findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts();
        } else if (isIndependentProductsEnabled) {
            allProducts = findAgileAndSystemDefaultProductsAndIndependentProducts();
        } else if (isGroupProductsEnabled) {
            allProducts = findAgileAndSystemDefaultProductsAndSmallGroupProducts();
        } else {
            allProducts = findAgileAndSystemDefaultProducts();
        }
        return allProducts;
    }

    public List<Product> getHierarchyProducts(List<Product> allProducts) {
        // find all parent IDs
        List<Integer> parentProductsIds = allProducts.stream()
                .map(Product::getDependentProductId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // find parentIDs of FIXED AGILE RATE PRODUCTS
        List<Integer> agileRatesFixedParentProductIds = allProducts.stream()
                .filter(p -> parentProductsIds.contains(p.getId()) && AgileRatesOffsetMethod.FIXED.equals(p.getOffsetMethod()))
                .map(Product::getId)
                .collect(Collectors.toList());

        // find all children related to FIXED products
        List<Integer> invalidIds = findInvalidChildren(agileRatesFixedParentProductIds,allProducts);

        // ensure that products do not belong in a product group
        List<ProductGroup> allProductGroups = getProductGroups();

        List<Product> validHierarchyProducts = allProducts
                .stream()
                .filter(product -> product.isSystemDefault()
                        || product.isIndependentProduct()
                        || AgileRatesOffsetMethod.PERCENTAGE.equals(product.getOffsetMethod()))
                .filter(product -> !product.isGroupProduct()
                        && !product.isFreeNightEnabled())
                .filter(product -> !invalidIds.contains(product.getId()))
                .filter(Product::isActive)
                .filter(p -> allProductGroups.stream().noneMatch(pg -> pg.getProduct().equals(p)))
                .collect(Collectors.toList());

        if (isIndependentProductsOrLinkedProductHierarchyEnabled()) {
            return validHierarchyProducts;
        } else {
            return validHierarchyProducts.stream().filter(p -> p.isSystemDefaultOrIndependentProduct() || p.isOptimized()).collect(Collectors.toList());
        }
    }

    public List<Product> findEligibleRelationshipProducts(Product product, List<Product> productList) {
        List<Product> filteredProductList;
        if (isIndependentProductsOrLinkedProductHierarchyEnabled()) {
            if (product.isSystemDefaultOrIndependentProduct()) {
                filteredProductList = productList.stream()
                        .filter(p -> !p.equals(product))
                        .filter(Product::isSystemDefaultOrIndependentProduct)
                        .collect(Collectors.toList());
            } else {
                filteredProductList = findProductsWithSameBaseProduct(product,productList);
            }
        } else {
            filteredProductList = productList.stream().filter(p -> !p.equals(product)).collect(Collectors.toList());
        }

        if (!product.isSystemDefault()) {
            //Find all accom types for the selected product
            List<ProductAccomType> allProductAccomTypes = findAllProductAccomType();
            List<AccomType> accomTypesForSelectedProduct = allProductAccomTypes.stream().filter(productAccomType ->
                            productAccomType.getProduct().equals(product))
                    .map(ProductAccomType::getAccomType).collect(Collectors.toList());

            List<Product> finalFilteredProductList = filteredProductList;
            //Find all products with overlapping accom types
            Set<Product> productListWithOverlappingATs = allProductAccomTypes.stream().filter(productAccomType ->
                            finalFilteredProductList.contains(productAccomType.getProduct())
                                    && accomTypesForSelectedProduct.contains(productAccomType.getAccomType()))
                    .map(ProductAccomType::getProduct).collect(Collectors.toSet());

            //Filter product list to be system default or products with overlapping accom types
            filteredProductList = finalFilteredProductList.stream().filter(p -> p.isSystemDefault() || productListWithOverlappingATs.contains(p)).collect(Collectors.toList());
        }
        return filteredProductList;
    }

    public List<String> validateProductHierarchy(List<Product> allProducts, Product selectedProduct, Product relatedProduct, BigDecimal minimumDifference) {

        if(isNull(relatedProduct)){
            return List.of("Related Product is invalid for the selected Product "+selectedProduct.getName());
        }
        List<AgileRatesHierarchyDTO> agileRatesHierarchyDTOS = getAgileRatesHierarchyDTOList();
        List<ProductRateOffset> productRateOffsets = getProductRateOffset();
        if (SystemConfig.isComprehensiveProductHierarchyValidationEnabled()) {
            return validateProductHierarchy(allProducts,selectedProduct, relatedProduct, minimumDifference, agileRatesHierarchyDTOS);
        }

        List<String> warningMessages = new ArrayList<>();
        if (minimumDifference == null
                || ((minimumDifference.compareTo(BigDecimal.ZERO) < 0))) {
            return List.of("Please enter a valid minimum difference that is greater than or equal to 0.");
        } else {
            AgileRatesHierarchyDTO existingDTO = new AgileRatesHierarchyDTO(0,selectedProduct,relatedProduct,minimumDifference,true);
            if (isDuplicateHierarchy(selectedProduct, relatedProduct, existingDTO,agileRatesHierarchyDTOS)
                    || (!isHierarchyValid(selectedProduct, relatedProduct, productRateOffsets, minimumDifference, allProducts, warningMessages)
                    | (SystemConfig.isSeasonHierarchyValidationEnabled() &&
                    !isEditingHierarchyValidAccordingToSeasonalAdjustments(selectedProduct, relatedProduct, allProducts, minimumDifference, productRateOffsets, warningMessages)))) {
                return CollectionUtils.isNotEmpty(warningMessages)
                        ? warningMessages : List.of("Invalid Hierarchy exists. Please review your Hierarchy configuration.");
            }
        }
        return emptyList();
    }

    private List<Product> findProductsWithSameBaseProduct(Product product,List<Product> allProducts) {
        List<Product> productsWithSameBaseProduct = new ArrayList<>();
        Product lowestBaseProductLeftHandProduct = getLowestBaseProduct(product,allProducts);
        allProducts.stream()
                .filter(p -> !p.equals(product))
                .forEach(p -> {
                    if (getLowestBaseProduct(p,allProducts) == lowestBaseProductLeftHandProduct) {
                        productsWithSameBaseProduct.add(p);
                    }
                });
        if (!productsWithSameBaseProduct.contains(lowestBaseProductLeftHandProduct)) {
            productsWithSameBaseProduct.add(0, allProducts.stream().filter(p -> p.equals(lowestBaseProductLeftHandProduct)).findFirst().get());
        }
        return productsWithSameBaseProduct;
    }
    private List<AgileRatesHierarchyDTO> getAgileRatesHierarchyDTOList() {
        List<AgileRatesHierarchyDTO> agileRatesHierarchyDTOList = new ArrayList<>();
        getProductHierarchies().forEach(entity -> agileRatesHierarchyDTOList.add(createHierarchyDTOFromEntity(entity)));
        return agileRatesHierarchyDTOList;
    }

    public List<String> validateProductHierarchy(List<Product> allProducts, Product selectedProduct, Product relatedProduct, BigDecimal minimumDifference,
                                                 List<AgileRatesHierarchyDTO> agileRatesHierarchyDTOList) {

        if (minimumDifference == null || minimumDifference.compareTo(BigDecimal.ZERO) < 0) {
            return List.of("Please enter a valid minimum difference that is greater than or equal to 0.");
        }

        AgileRatesHierarchyDTO existingDTO = new AgileRatesHierarchyDTO(0,selectedProduct,relatedProduct,minimumDifference,true);
        if (isDuplicateHierarchy(selectedProduct, relatedProduct,existingDTO,agileRatesHierarchyDTOList)) {
            return List.of("Invalid Hierarchy exists. Please review your Hierarchy configuration.");
        }

        ProductHierarchyDto productHierarchyDto = new ProductHierarchyDto();
        productHierarchyDto.setSelectedProduct(selectedProduct);
        productHierarchyDto.setRelatedProduct(relatedProduct);
        productHierarchyDto.setMinimumDifference(minimumDifference);
        productHierarchyDto.setAllProducts(new HashSet<>(allProducts));
        productHierarchyDto.setAllLinkedProductOffsets(new HashSet<>(getProductRateOffset()));

        List<String> warnings = new ArrayList<>();
        productHierarchyValidationService.validateNewHierarchy(productHierarchyDto, warnings);
        return CollectionUtils.isNotEmpty(warnings) ? warnings: emptyList();
    }

    public boolean isDuplicateHierarchy(Product selectedProduct, Product relationshipProduct, AgileRatesHierarchyDTO existingDTO,
                                        List<AgileRatesHierarchyDTO> agileRatesHierarchyDTOList) {
        // prevents A < B if B < A exists
        List<AgileRatesHierarchyDTO> dtoList = agileRatesHierarchyDTOList;
        if (existingDTO != null) {
            dtoList = agileRatesHierarchyDTOList.stream()
                    .filter(dto -> !(dto.getSelectedProduct().equals(existingDTO.getSelectedProduct()) &&
                            dto.getSelectedRelationshipProduct().equals(existingDTO.getSelectedRelationshipProduct()) &&
                            dto.getMinimumDifference().equals(existingDTO.getMinimumDifference()))
                    )
                    .collect(Collectors.toList());
        }
        for (AgileRatesHierarchyDTO dto : dtoList) {
            if (dto.getSelectedRelationshipProduct() != null && (dto.getSelectedProduct().equals(selectedProduct)
                    && dto.getSelectedRelationshipProduct().equals(relationshipProduct)
                    || dto.getSelectedProduct().equals(relationshipProduct) && dto.getSelectedRelationshipProduct().equals(selectedProduct))) {
                return true;
            }
        }
        return false;
    }
    public boolean isIndependentProductsOrLinkedProductHierarchyEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED) ||
                configParamsService.getBooleanParameterValue(PreProductionConfigParamName.LINKED_PRODUCT_HIERARCHY_ENABLED);
    }

    private List<Integer> findInvalidChildren(List<Integer> parentIds,List<Product> allProducts) {
        // recursive function that finds all children products related to the given parents
        List<Integer> invalidChildrenIds = allProducts
                .stream()
                .filter(p -> parentIds.contains(p.getDependentProductId()))
                .map(Product::getId)
                .collect(Collectors.toList());

        invalidChildrenIds.removeAll(parentIds);
        if (invalidChildrenIds.isEmpty()) {
            return parentIds;
        }

        parentIds.addAll(invalidChildrenIds);
        return findInvalidChildren(parentIds,allProducts);
    }

    private List<String> getNonExistingProducts(Set<HierarchyDTO> hierarchy) {
        List<String> invalidProducts = new ArrayList<>();

        List<String> selectedProducts = hierarchy.stream().map(HierarchyDTO::getSelectedProduct).collect(toList());
        List<String> relatedProducts = hierarchy.stream().map(HierarchyDTO::getRelatedProduct).collect(toList());
        List<Product> existingSelectedProducts = getProductsByName(selectedProducts);
        List<Product> existingRelatedProducts = getProductsByName(relatedProducts);

        List<String> existingSelectedProductNames = existingSelectedProducts.stream().map(Product::getName).collect(toList());
        List<String> existingRelatedProductNames = existingRelatedProducts.stream().map(Product::getName).collect(toList());
        List<String> invalidSP = selectedProducts.stream().filter(product -> !existingSelectedProductNames.contains(product)).collect(toList());
        List<String> invalidRP = relatedProducts.stream().filter(product -> !existingRelatedProductNames.contains(product)).collect(toList());

        invalidProducts.addAll(invalidSP);
        invalidProducts.addAll(invalidRP);
        return invalidProducts;
    }

    public String deleteHierarchy(Set<HierarchyDTO> hierarchy) {
        List<ProductHierarchy> listToBeDeleted = new ArrayList<>();
        List<Product> allProducts = getEligibleProductsForHierarchy();
        List<AgileRatesHierarchyDTO> deleteHierarchy = new ArrayList<>();
        int row =0;

        for(HierarchyDTO hierarchyDTO : hierarchy) {

            if(hierarchyDTO.getMinimumDifference() != null && (hierarchyDTO.getMinimumDifference().compareTo(BigDecimal.ZERO) < 0)){
                return "Please enter a valid value that is greater than or equal to 0.";
            }

            Product selectedProduct = getProductByName(hierarchyDTO.getSelectedProduct());
            if(isNull(selectedProduct)){
                return "Product does not exist: "+hierarchyDTO.getSelectedProduct();
            }

            Product relatedProduct = getRelatedProduct(hierarchyDTO, allProducts, selectedProduct);
            if(isNull(relatedProduct)){
                return "Product does not exist: "+hierarchyDTO.getRelatedProduct();
            }
            if(isExistingHierarchy(selectedProduct,relatedProduct)) {
                AgileRatesHierarchyDTO dto = new AgileRatesHierarchyDTO(row++, selectedProduct, relatedProduct, hierarchyDTO.getMinimumDifference(), true);
                deleteHierarchy.add(dto);
            }else{
                return "Hierarchy for "+hierarchyDTO.getSelectedProduct()+" and "+hierarchyDTO.getRelatedProduct() +" does not exist";
            }
        }
        deleteHierarchy.forEach(dto -> {
            listToBeDeleted.add(getEntityToBeSaved(dto));
        });
        saveAgileRatesProductHierarchies(emptyList(), listToBeDeleted);
        return null;
    }

    private boolean isExistingHierarchy(Product selectedProduct, Product relatedProduct) {
        List<ProductHierarchy> productHierarchy = tenantCrudService.
                findByNamedQuery(BY_PRODUCT_ID, QueryParameter.with("productId", selectedProduct.getId()).parameters());
        return productHierarchy.stream().anyMatch(hierarchy->Objects.equals(hierarchy.getToProduct().getId(),relatedProduct.getId()));
    }

    public List<AgileRatesDTARangeResponseDTO> getAgileRatesDTARangeResponse() {
        List<AgileRatesDTARange> agileRatesDTARanges = findAllDTARanges();
        return agileRatesDTARanges.stream().map(this::mapToAgileRatesDTARangeResponseDTO).collect(toList());
    }

    private AgileRatesDTARangeResponseDTO mapToAgileRatesDTARangeResponseDTO(AgileRatesDTARange agileRatesDTARange) {
        AgileRatesDTARangeResponseDTO agileRatesDTARangeResponseDTO = new AgileRatesDTARangeResponseDTO();
        Integer minDTA = agileRatesDTARange.getMinDaysToArrival();
        Integer maxDTA = agileRatesDTARange.getMaxDaysToArrival();
        agileRatesDTARangeResponseDTO.setMinDTA(minDTA);
        agileRatesDTARangeResponseDTO.setMaxDTA(maxDTA);
        agileRatesDTARangeResponseDTO.setNumberOfDays(calculateNumberOfDays(minDTA, maxDTA));
        return agileRatesDTARangeResponseDTO;
    }

    private Integer calculateNumberOfDays(Integer minDTA, Integer maxDTA) {
        if (null == maxDTA) return null;
        return minDTA == 0 ? maxDTA : (maxDTA - minDTA + 1);
    }

    public void deleteAgileDtaRanges(List<AgileRatesDTARange> agileRatesDTARangesToDelete) {
        List<ProductRateOffsetOverride> allProductRateOffsetOverrides = findAllProductRateOffsetOverrides();
        List<ProductRateOffsetOverride> offsetOverridesToDelete = new ArrayList<>();
        if (!agileRatesDTARangesToDelete.isEmpty()) {
            deleteProductRateOffsets(getOffsetsToDelete(agileRatesDTARangesToDelete));
            offsetOverridesToDelete = getOffsetOverridesToDelete(allProductRateOffsetOverrides, agileRatesDTARangesToDelete);
            deleteProductRateOffsetOverrides(offsetOverridesToDelete);
            deleteDTARanges(agileRatesDTARangesToDelete);
        }
        if (CollectionUtils.isNotEmpty(allProductRateOffsetOverrides)) {
            inactivateProductRateOffsetOverrides(getOffsetOverridesToInactivate(allProductRateOffsetOverrides, offsetOverridesToDelete));
        }
    }

    public List<ProductRateOffset> getOffsetsToDelete(List<AgileRatesDTARange> agileRatesDTARangesToDelete) {
        return agileRatesDTARangesToDelete.stream().flatMap(range -> findAllProductRateOffsets().stream()
                .filter(offset -> offset.getAgileRatesDTARange() != null
                        && range.equals(offset.getAgileRatesDTARange()))).collect(Collectors.toList());
    }

    private List<ProductRateOffsetOverride> getOffsetOverridesToDelete(List<ProductRateOffsetOverride> allProductRateOffsetOverrides,
                                                                       List<AgileRatesDTARange> agileRatesDTARangesToDelete) {
        return agileRatesDTARangesToDelete.stream().flatMap(range -> allProductRateOffsetOverrides.stream()
                .filter(productRateOffsetOverride -> productRateOffsetOverride.getAgileRatesDTARange() != null
                        && range.equals(productRateOffsetOverride.getAgileRatesDTARange()))).collect(Collectors.toList());
    }

    private List<ProductRateOffsetOverride> getOffsetOverridesToInactivate(List<ProductRateOffsetOverride> allProductRateOffsetOverrides, List<ProductRateOffsetOverride> offsetOverridesToDelete) {
        java.time.LocalDate systemDateAsLocalDate = dateService.getCaughtUpJavaLocalDate();
        return allProductRateOffsetOverrides.stream()
                .filter(productRateOffsetOverride -> productRateOffsetOverride.getStatusId().equals(1)
                        && JavaLocalDateUtils.toJavaLocalDate(productRateOffsetOverride.getOccupancyDate())
                        .compareTo(systemDateAsLocalDate) >= 0
                        && !offsetOverridesToDelete.contains(productRateOffsetOverride))
                .collect(Collectors.toList());
    }

    public void saveAgileProductRestrictionAssociation(Map<Integer, Set<RateQualified>> srpCodesToAddInAssociation) {
        List<AgileProductRestrictionAssociation> associations = new ArrayList<>();
        srpCodesToAddInAssociation.forEach((productId, srpCode) -> {
            List<AgileProductRestrictionAssociation> agileProductRestrictionAssociations =
                    getAgileProductRestrictionAssociationsForRateQualifiedIds(srpCode.stream()
                            .map(RateQualified::getId).collect(toList()));
            Set<Integer> existingRateQualifiedIds = agileProductRestrictionAssociations.stream()
                    .map(AgileProductRestrictionAssociation::getRateQualified)
                    .map(RateQualified::getId)
                    .collect(toSet());
            Set<RateQualified> newSrpCodes = srpCode.stream()
                    .filter(rateQualified -> !existingRateQualifiedIds.contains(rateQualified.getId()))
                    .collect(toSet());

            Product product = tenantCrudService.find(Product.class, productId);
            agileProductRestrictionAssociations.forEach(association -> {
                if (association.getProduct().isAgileRatesProduct()) {
                    association.setProduct(product);
                    associations.add(association);
                }
            });

            newSrpCodes.forEach(rateQualified -> {
                    AgileProductRestrictionAssociation newAgileProductRestrictionAssociation =
                            newAgileProductRestrictionAssociation(product, rateQualified);
                associations.add(newAgileProductRestrictionAssociation);
            });
        });

        if (!associations.isEmpty())
            tenantCrudService.save(associations);
    }

    public void removeAgileProductRestrictionAssociation(Set<RateQualified> srpCodesToRemoveFromAssociation) {
        List<AgileProductRestrictionAssociation> associationsToRemove = new ArrayList<>();
        List<Integer> rateQualifiedIds = srpCodesToRemoveFromAssociation.stream().map(RateQualified::getId).collect(toList());
        if (!rateQualifiedIds.isEmpty()) {
            List<AgileProductRestrictionAssociation> existingAssociations =
                    getAgileProductRestrictionAssociationsForRateQualifiedIds(rateQualifiedIds)
                            .stream()
                            .filter(association -> association.getProduct().isAgileRatesProduct())
                            .collect(toList());


            tenantCrudService.delete(existingAssociations);
        }
    }

    public void cleanAgileRatesFeatures(int basePackageFeatureValue, boolean deleteLinkedProducts, boolean addOn){
        if(deleteLinkedProducts || (basePackageFeatureValue==0 && addOn)){
            List<Product> productsToDelete = tenantCrudService.findByNamedQuery(GET_ALL_PRODUCTS_EXCLUDING_DELETED_AND_SYSTEM_DEFAULT);
            Date caughtUpDate = dateService.getCaughtUpDate();
            boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
            boolean isGroupProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED);
            List<Product> allProducts = getAllProducts(isIndependentProductsEnabled, isGroupProductsEnabled);
            productsToDelete.forEach(ptd ->deleteProduct(caughtUpDate, isIndependentProductsEnabled, isGroupProductsEnabled, allProducts, ptd));
            return;
        }

        List<Product> products = getAgileProductToDeactivate(basePackageFeatureValue);

        for(Product product: products) {
            List<ProductGroup> productGroups = getAllProductsInProductGroup(product,new ArrayList<>());
            List<Product> rateProtectProducts = getRateProtectProducts(product);

                if (!productGroups.isEmpty()) {
                    deleteProductFromProductGroup(product, productGroups);
                }
                if (!rateProtectProducts.isEmpty()) {
                    rateProtectProducts.forEach(rateProtectProduct -> rateProtectProduct.setActive(false));
                    saveProducts(rateProtectProducts);
                }
            deactivateProduct(product);
            deleteFutureDecisionsWhenAgileRateUploadIsChangedToDisable(product);

        }
    }

    private List<Product> getAgileProductToDeactivate(int basePackageFeatureValue) {
        List<Product> products = gelAllActiveProduct();
        int numberOfProductToDeactivate = products.size()- basePackageFeatureValue;
        return (numberOfProductToDeactivate > 0)
                ? products.stream().limit(numberOfProductToDeactivate).collect(Collectors.toList())
                : Collections.emptyList();
    }

    private void deleteFutureDecisionsWhenAgileRateUploadIsChangedToDisable(Product product) {
        if (!configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)
                || product.isAgileRatesProduct()) {
            deleteFutureDecisionsForSmallGroupProductOrWhenAgileRateUploadIsChangedToDisabled(product);
        }
    }

    private void deactivateProduct(Product product) {
        product.setActive(false);
        product.setUpload(false);
        saveProduct(product, false);
    }

    private List<Product> getRateProtectProducts(Product product) {
        return fixedAboveBarConfigurationService.findFixedAboveBarProducts()
                .stream()
                .filter(prod -> AgileRatesProductTypeEnum.FIXED_ABOVE_BAR.getValue().equals(prod.getCode())
                                && product.getId().equals(prod.getDependentProductId()))
                .collect(Collectors.toList());
    }

    private List<Product> gelAllActiveProduct() {
        return tenantCrudService.findByNamedQuery(GET_ALL_ACTIVE_PRODUCT_WITH_LAST_UPDATED_DATE);
    }

    public void deleteProduct(Date caughtUpDate, boolean isIndependentProductsEnabled, boolean isGroupProductsEnabled, List<Product> allProducts, Product productsToDelete) {
        if (isIndependentProductsEnabled && productsToDelete.isIndependentProduct()) {
            List<Product> relatedProducts = AgileRatesUtils.getChildren(productsToDelete, allProducts);
            deleteIndependentProduct(productsToDelete, relatedProducts, LocalDate.fromDateFields(caughtUpDate));
        } else if (isGroupProductsEnabled && productsToDelete.isGroupProduct()) {
            deleteSmallGroupProduct(productsToDelete, LocalDate.fromDateFields(caughtUpDate));
            deleteProductAssociatedRestrictions(productsToDelete);
        } else {
            List<ProductGroup> productGroupList = getAllProductsInProductGroup(productsToDelete,new ArrayList<>());
            if (CollectionUtils.isNotEmpty(productGroupList)) {
                deleteProductFromProductGroup(productsToDelete, productGroupList);
            }
            saveAgileRatesProductHierarchies(Collections.emptyList(), getAllHierarchiesForProduct(productsToDelete));
            deleteLinkedProduct(productsToDelete, LocalDate.fromDateFields(caughtUpDate));
            deleteProductAssociatedRestrictions(productsToDelete);
        }

    }

    private List<Product> getAllProducts(boolean isIndependentProductsEnabled, boolean isGroupProductsEnabled) {
        List<Product> allProducts;
        if (isIndependentProductsEnabled && isGroupProductsEnabled) {
            allProducts = findAgileAndSystemDefaultProductsAndIndependentAndSmallGroupProducts();
        } else if (isIndependentProductsEnabled) {
            allProducts = findAgileAndSystemDefaultProductsAndIndependentProducts();
        } else if (isGroupProductsEnabled) {
            allProducts = findAgileAndSystemDefaultProductsAndSmallGroupProducts();
        } else {
            allProducts = findAgileAndSystemDefaultProducts();
        }
        return allProducts;
    }

}
