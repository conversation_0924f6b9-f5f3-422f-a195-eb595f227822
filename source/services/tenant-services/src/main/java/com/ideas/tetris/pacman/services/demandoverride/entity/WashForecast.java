package com.ideas.tetris.pacman.services.demandoverride.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.platform.common.entity.IdAwareEntity;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@NamedQueries({
        @NamedQuery(name = WashForecast.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID, query = "select wf " +
                "from WashForecast as wf  WHERE " +
                "wf.propertyID=:propertyId and wf.forecastGroupId=:forecastGroupId and " +
                "wf.occupancyDate =:occupancyDate "
        ),
        @NamedQuery(name = WashForecast.GET_BY_PROPERTY_ID, query = "select wf " + "from WashForecast as wf where wf.propertyID=:propertyId" ),
        @NamedQuery(name = WashForecast.DELETE_ALL_BY_FORECAST_GROUP_IDS, query = "delete from WashForecast as wf where wf.forecastGroupId IN (:forecastGroupIds)" )
})
@Table(name = "Wash_FCST")
public class WashForecast extends IdAwareEntity<Integer> {

    private static final long serialVersionUID = -3494155486458133862L;
    public static final String BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID = "com.ideas.tetris.pacman.services.demandoverride.entity.WashForecast.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID";
    public static final String GET_BY_PROPERTY_ID = "com.ideas.tetris.pacman.services.demandoverride.entity.WashForecast.GET_BY_PROPERTY_ID";
    public static final String DELETE_ALL_BY_FORECAST_GROUP_IDS = "com.ideas.tetris.pacman.services.demandoverride.entity.WashForecast.DELETE_BY_FORECAST_GROUP_ID";

    private Integer id;
    private Integer decisionID;
    private Integer propertyID;
    private Date occupancyDate;
    private Integer forecastGroupId;
    private Integer accomClassId;
    private BigDecimal userWash;
    private BigDecimal systemWash;


    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "Wash_FCST_ID", columnDefinition = "BIGINT")
    public Integer getId() {
        return id;
    }

    @Override
    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "Decision_ID", columnDefinition = "BIGINT")
    public Integer getDecisionID() {
        return decisionID;
    }

    public void setDecisionID(Integer decisionID) {
        this.decisionID = decisionID;
    }

    @Column(name = "Property_ID")
    public Integer getPropertyID() {
        return propertyID;
    }

    public void setPropertyID(Integer propertyID) {
        this.propertyID = propertyID;
    }

    @Column(name = "Occupancy_DT")
    @Temporal(TemporalType.DATE)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public void setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
    }

    @Column(name = "Forecast_Group_ID")
    public Integer getForecastGroupId() {
        return forecastGroupId;
    }

    public void setForecastGroupId(Integer forecastGroupId) {
        this.forecastGroupId = forecastGroupId;
    }

    @Column(name = "User_Wash")
    public BigDecimal getUserWash() {
        return userWash;
    }

    public void setUserWash(BigDecimal userWash) {
        this.userWash = userWash;
    }

    @Column(name = "System_Wash")
    public BigDecimal getSystemWash() {
        return systemWash;
    }

    public void setSystemWash(BigDecimal systemWash) {
        this.systemWash = systemWash;
    }

    @Column(name = "Accom_Class_ID")
    public Integer getAccomClassId() {
        return accomClassId;
    }

    public void setAccomClassId(Integer accomClassId) {
        this.accomClassId = accomClassId;
    }

}
