package com.ideas.tetris.pacman.services.bestavailablerate.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.pacman.services.individualtransactions.RoomStayRevenue;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.DELETE_RESERVATIONS_IN_RANGE_DELETE_INHOUSE;

@Entity
@SqlResultSetMappings({
        @SqlResultSetMapping(
                name = "ReservationNightMapping",
                columns = {
                        @ColumnResult(name = "Individual_Trans_ID"),
                        @ColumnResult(name = "File_Metadata_ID"),
                        @ColumnResult(name = "Property_ID"),
                        @ColumnResult(name = "Reservation_Identifier"),
                        @ColumnResult(name = "Individual_Status"),
                        @ColumnResult(name = "Arrival_DT"),
                        @ColumnResult(name = "Departure_DT"),
                        @ColumnResult(name = "Booking_DT"),
                        @ColumnResult(name = "Cancellation_DT"),
                        @ColumnResult(name = "Booked_Accom_Type_Code"),
                        @ColumnResult(name = "Accom_Type_ID"),
                        @ColumnResult(name = "Mkt_Seg_ID"),
                        @ColumnResult(name = "Room_Revenue"),
                        @ColumnResult(name = "Food_Revenue"),
                        @ColumnResult(name = "Beverage_Revenue"),
                        @ColumnResult(name = "Telecom_Revenue"),
                        @ColumnResult(name = "Other_Revenue"),
                        @ColumnResult(name = "Total_Revenue"),
                        @ColumnResult(name = "Source_Booking"),
                        @ColumnResult(name = "Nationality"),
                        @ColumnResult(name = "Rate_Code"),
                        @ColumnResult(name = "Rate_Value"),
                        @ColumnResult(name = "Room_Number"),
                        @ColumnResult(name = "Booking_type"),
                        @ColumnResult(name = "Number_Children"),
                        @ColumnResult(name = "Number_Adults"),
                        @ColumnResult(name = "CreateDate_DTTM"),
                        @ColumnResult(name = "Confirmation_No"),
                        @ColumnResult(name = "Channel"),
                        @ColumnResult(name = "Booking_TM"),
                        @ColumnResult(name = "Occupancy_DT"),
                        @ColumnResult(name = "Persistent_Key"),
                        @ColumnResult(name = "Analytics_Booking_Dt"),
                        @ColumnResult(name = "Inv_Block_Code"),
                        @ColumnResult(name = "Market_Code"),
                        @ColumnResult(name = "Gross_Rate_Value"),
                        @ColumnResult(name = "Gross_Room_Revenue"),
                        @ColumnResult(name = "Total_Acquisition_Cost"),
                        @ColumnResult(name = "Room_Revenue_Acquisition_Cost"),
                        @ColumnResult(name = "Net_Revenue"),
                        @ColumnResult(name = "Net_Rate")
                }
        )
})
@NamedQueries({
        @NamedQuery(name = ReservationNight.FIND_MIN_OCCUPANCY_DT, query = "select min(occupancyDate) from ReservationNight"),
        @NamedQuery(name = ReservationNight.FIND_MAX_OCCUPANCY_DT, query = "select max(occupancyDate) from ReservationNight "),
        @NamedQuery(name = ReservationNight.FIND_MIN_MAX_ARRIVAL_DT, query = "select MIN(arrivalDate),max(arrivalDate) from ReservationNight where fileMetadataId = :fileMetadataId "),
        @NamedQuery(name = ReservationNight.FIND_MIN_ARRIVAL_DT, query = "select MIN(arrivalDate) from ReservationNight where fileMetadataId = :fileMetadataId "),
        @NamedQuery(name = ReservationNight.FIND_MAX_ARRIVAL_DT, query = "select max(arrivalDate) from ReservationNight where fileMetadataId = :fileMetadataId "),
        @NamedQuery(name = ReservationNight.FIND_MAX_DEPARTURE_DT, query = "select max(departureDate) from ReservationNight "),
        @NamedQuery(name = ReservationNight.BY_MARKET_CODE, query = "select rn from ReservationNight as rn where marketCode =:marketCode"),
        @NamedQuery(name = ReservationNight.BY_MARKET_SEG_ID, query = "select rn from ReservationNight as rn where marketSegId = :marketSegId"),
        @NamedQuery(name = ReservationNight.COUNT_BY_MARKET_SEG_ID_IN, query = "select count(rn) from ReservationNight as rn where marketSegId in (:marketSegIds)"),
        @NamedQuery(name = ReservationNight.DISTINCT_RESERVATION_IDENTIFIERS_BY_MARKET_SEG_ID_IN, query = "select distinct rn.reservationIdentifier from ReservationNight as rn where marketSegId in (:marketSegIds)"),
        @NamedQuery(name = ReservationNight.DELETE_BY_RESERVATION_IDS, query = "delete from ReservationNight as rn where rn.reservationIdentifier in (:reservationIds)"),
        @NamedQuery(name = ReservationNight.DELETE_BY_RESERVATION_ID_LIKE, query = "delete from ReservationNight as rn where rn.reservationIdentifier like :reservationId"),
        @NamedQuery(name = ReservationNight.UPDATE_BOOKED_ACCOM_TYPE_CODE, query = "update ReservationNight as rn set rn.bookedAccomTypeCode = :newRTCode where rn.bookedAccomTypeCode = :oldRTCode"),

        @NamedQuery(name = ReservationNight.UPDATE_FILE_METADATA_ID_BY_START_DATE, query = "update ReservationNight as rn set rn.fileMetadataId = :fileMetadataId where rn.arrivalDate >= :startDate"),
        @NamedQuery(name = ReservationNight.UPDATE_FILE_METADATA_ID_BY_START_DATE_AND_DATE_RANGE, query = "update ReservationNight as rn set rn.fileMetadataId = :fileMetadataId where rn.occupancyDate between :startDate and :endDate"),
        @NamedQuery(name = ReservationNight.UPDATE_FILE_METADATA_ID_BY_DEPARTURE_DATE, query = "update ReservationNight as it set it.fileMetadataId = :fileMetadataId where it.propertyId=:propertyId and it.departureDate >= :minArrivalDate"),
        @NamedQuery(name = ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE, query = "select rn from ReservationNight as rn where rn.occupancyDate BETWEEN :startDate AND :endDate order by rn.occupancyDate, rn.arrivalDate, rn.departureDate, " +
                "rn.bookingDate, rn.cancellationDate, rn.bookedAccomTypeCode, rn.accomTypeId, rn.marketCode"),
        @NamedQuery(name = ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_DIFF, query = "select rn from ReservationNight as rn where rn.occupancyDate BETWEEN :startDate AND :endDate AND rn.lastUpdatedDate >= :lastUpdatedDate order by rn.occupancyDate, rn.arrivalDate, rn.departureDate, " +
                "rn.bookingDate, rn.cancellationDate, rn.bookedAccomTypeCode, rn.accomTypeId, rn.marketCode"),
        @NamedQuery(name = ReservationNight.GET_BY_OCCUPANCY_DT_RANGE, query = "select rn from ReservationNight as rn where rn.occupancyDate BETWEEN :startDate and :endDate AND rn.individualStatus NOT IN ('XX','NS','NO SHOW','NO_SHOW','CANCELLED') order by rn.occupancyDate"),
        @NamedQuery(name = ReservationNight.GET_BY_ARRIVAL_DT_RANGE, query = "select rn from ReservationNight as rn where rn.arrivalDate BETWEEN :startDate and :endDate order by rn.arrivalDate"),
        @NamedQuery(name = ReservationNight.GET_BY_DEPARTURE_DT_RANGE, query = "select rn from ReservationNight as rn where rn.departureDate BETWEEN :startDate and :endDate "),
        @NamedQuery(name = ReservationNight.GET_RECORDS, query = "select count(*) from ReservationNight rn"),
        @NamedQuery(name = ReservationNight.GET_RATE_CODES_FOR_MKT_SEG_IN, query = "select distinct(rateCode) from ReservationNight rn where marketSegId in (:marketSegIds)"),
        @NamedQuery(name = ReservationNight.GET_RATE_CODES_FOR_MKT_SEG_IN_WERE_RATE_CODE_LIKE, query = "select distinct(rateCode) from ReservationNight rn where marketSegId in (:marketSegIds) and rateCode like CONCAT(:code, '_%')")
})
@NamedNativeQueries({
        @NamedNativeQuery(name = ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_WITH_POST_DEPARTURE_REVENUE_DATA,
                query = "select Individual_Trans_ID,File_Metadata_ID,Property_ID,Reservation_Identifier,Individual_Status,Arrival_DT,Departure_DT,Booking_DT,Cancellation_DT, Booked_Accom_Type_Code, " +
                        " Accom_Type_ID,Mkt_Seg_ID,room_revenue,food_revenue,Beverage_Revenue,Telecom_Revenue,Other_Revenue,Total_Revenue,Source_Booking,Nationality,rate_code,rate_value," +
                        " Room_Number,Booking_type,Number_Children,Number_Adults,CreateDate_DTTM,Confirmation_No,Channel,Booking_TM,Occupancy_DT,Persistent_Key,Analytics_Booking_Dt,Inv_Block_Code," +
                        " market_code,Gross_Rate_Value,Gross_Room_Revenue,Total_Acquisition_Cost,Room_Revenue_Acquisition_Cost,Net_Revenue,Net_Rate " +
                        " from Reservation_Night as rn " +
                        " where rn.occupancy_Dt BETWEEN :startDate AND :endDate " +
                        " union " +
                        " SELECT null as individual_trans_id , rn.File_Metadata_ID, rn.Property_ID, post_dep.reservation_identifier, rn.Individual_Status, minMax.min_arrival_DT , minMax.max_departure_DT, rn.Booking_DT, rn.Cancellation_DT," +
                        "    rn.Booked_Accom_Type_Code, post_dep.Accom_Type_ID , post_dep.Mkt_Seg_ID , post_dep.room_revenue , post_dep.food_revenue , 0.00000 as beverrage_revenue, 0.00000 as telecom_revenue, " +
                        "    post_dep.other_revenue , post_dep.Total_Revenue , rn.Source_Booking, rn.Nationality, post_dep.rate_code, post_dep.rate_value, rn.Room_Number, rn.Booking_type, rn.Number_Children, " +
                        "    rn.Number_Adults, rn.CreateDate_DTTM, rn.Confirmation_No, rn.Channel, rn.Booking_TM, post_dep.occupancy_dt, null as Persistent_Key, rn.Analytics_Booking_Dt, rn.Inv_Block_Code, " +
                        "    post_dep.market_code, rn.Gross_Rate_Value, rn.Gross_Room_Revenue, rn.Total_Acquisition_Cost, rn.Room_Revenue_Acquisition_Cost, rn.Net_Revenue, rn.Net_Rate" +
                        " FROM dbo.reservation_night AS rn" +
                        "    JOIN (SELECT t.reservation_identifier,  min(arrival_DT) AS min_arrival_DT,  max(departure_DT) AS max_departure_DT  FROM dbo.reservation_night AS t  GROUP BY t.reservation_identifier) minMax " +
                        " ON rn.reservation_identifier = minMax.reservation_identifier" +
                        "    JOIN post_departure_revenue post_dep on rn.reservation_identifier = post_dep.reservation_identifier  and rn.occupancy_DT = minMax.min_arrival_DT" +
                        "    WHERE rn.Occupancy_DT BETWEEN :startDate AND :endDate" +
                        " order by rn.occupancy_Dt, rn.arrival_Dt, rn.departure_Dt, rn.booking_Dt, rn.cancellation_Dt, rn.Booked_Accom_Type_Code, rn.accom_Type_Id, rn.market_Code",
                resultSetMapping = "ReservationNightMapping"),
        @NamedNativeQuery(name = ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_WITH_POST_DEPARTURE_REVENUE_DATA_DIFF,
                query = "select Individual_Trans_ID,File_Metadata_ID,Property_ID,Reservation_Identifier,Individual_Status,Arrival_DT,Departure_DT,Booking_DT,Cancellation_DT, Booked_Accom_Type_Code, " +
                        " Accom_Type_ID,Mkt_Seg_ID,room_revenue,food_revenue,Beverage_Revenue,Telecom_Revenue,Other_Revenue,Total_Revenue,Source_Booking,Nationality,rate_code,rate_value," +
                        " Room_Number,Booking_type,Number_Children,Number_Adults,CreateDate_DTTM,Confirmation_No,Channel,Booking_TM,Occupancy_DT,Persistent_Key,Analytics_Booking_Dt,Inv_Block_Code," +
                        " market_code,Gross_Rate_Value,Gross_Room_Revenue,Total_Acquisition_Cost,Room_Revenue_Acquisition_Cost,Net_Revenue,Net_Rate " +
                        " from Reservation_Night as rn " +
                        " where rn.occupancy_Dt BETWEEN :startDate AND :endDate " +
                        " AND rn.Last_Updated_DTTM >= :lastUpdatedDate " +
                        " union " +
                        " SELECT null as individual_trans_id , rn.File_Metadata_ID, rn.Property_ID, post_dep.reservation_identifier, rn.Individual_Status, minMax.min_arrival_DT , minMax.max_departure_DT, rn.Booking_DT, rn.Cancellation_DT," +
                        "    rn.Booked_Accom_Type_Code, post_dep.Accom_Type_ID , post_dep.Mkt_Seg_ID , post_dep.room_revenue , post_dep.food_revenue , 0.00000 as beverrage_revenue, 0.00000 as telecom_revenue, " +
                        "    post_dep.other_revenue , post_dep.Total_Revenue , rn.Source_Booking, rn.Nationality, post_dep.rate_code, post_dep.rate_value, rn.Room_Number, rn.Booking_type, rn.Number_Children, " +
                        "    rn.Number_Adults, rn.CreateDate_DTTM, rn.Confirmation_No, rn.Channel, rn.Booking_TM, post_dep.occupancy_dt, null as Persistent_Key, rn.Analytics_Booking_Dt, rn.Inv_Block_Code, " +
                        "    post_dep.market_code, rn.Gross_Rate_Value, rn.Gross_Room_Revenue, rn.Total_Acquisition_Cost, rn.Room_Revenue_Acquisition_Cost, rn.Net_Revenue, rn.Net_Rate" +
                        " FROM dbo.reservation_night AS rn" +
                        "    JOIN (SELECT t.reservation_identifier,  min(arrival_DT) AS min_arrival_DT,  max(departure_DT) AS max_departure_DT  FROM dbo.reservation_night AS t  GROUP BY t.reservation_identifier) minMax " +
                        " ON rn.reservation_identifier = minMax.reservation_identifier" +
                        "    JOIN post_departure_revenue post_dep on rn.reservation_identifier = post_dep.reservation_identifier  and rn.occupancy_DT = minMax.min_arrival_DT" +
                        "    WHERE rn.Occupancy_DT BETWEEN :startDate AND :endDate" +
                        "    AND rn.Last_Updated_DTTM >= :lastUpdatedDate" +
                        " order by rn.occupancy_Dt, rn.arrival_Dt, rn.departure_Dt, rn.booking_Dt, rn.cancellation_Dt, rn.Booked_Accom_Type_Code, rn.accom_Type_Id, rn.market_Code",
                resultSetMapping = "ReservationNightMapping"),

        @NamedNativeQuery(name = ReservationNight.ADJUST_ARRIVAL_DT_AND_DEPARTURE_DT, query = "update reservation_night set arrival_DT =:arrivalDt , departure_DT =:departureDt where reservation_identifier =:reservationId and mkt_seg_id = :mktSegId and accom_type_id = :accomTypeId and occupancy_DT between :arrivalDt and :departureDt"),
        @NamedNativeQuery(name = ReservationNight.UPDATE_MARKET_CODE_IF_NULL, query = "UPDATE rn\n" +
                "SET market_code = coalesce(ams.market_code, ms.mkt_seg_code)\n" +
                "FROM reservation_night rn\n" +
                "JOIN mkt_seg ms ON rn.mkt_seg_id = ms.mkt_seg_id\n" +
                "LEFT JOIN analytical_mkt_seg ams ON ms.mkt_seg_code = ams.mapped_market_code\n" +
                "WHERE rn.market_code IS NULL"),
        @NamedNativeQuery(name = ReservationNight.UPDATE_CHANGE_MARKET_CODE_IF_NULL, query = "UPDATE rn\n" +
                "SET market_code = coalesce(ams.market_code, ms.mkt_seg_code)\n" +
                "FROM reservation_night_CHANGE rn\n" +
                "JOIN mkt_seg ms ON rn.mkt_seg_id = ms.mkt_seg_id\n" +
                "LEFT JOIN analytical_mkt_seg ams ON ms.mkt_seg_code = ams.mapped_market_code\n" +
                "WHERE rn.market_code IS NULL"),
        @NamedNativeQuery(name = ReservationNight.GET_TOTAL_ACTIVITY_BY_DATERANGE, query = "select dl.calendar_date, " +
                "coalesce(rooms_sold,0) rooms_sold,coalesce(room_revenue,0) room_revenue,coalesce(arrivals,0) " +
                "arrivals ,coalesce(departures,0) departures\n" +
                "from calendar_dim dl \n" +
                "left join     (select t.occupancy_DT,\n" +
                "                                        count(*)                                  rooms_sold,\n" +
                "                                        sum(room_revenue)                          room_revenue,\n" +
                "\t\t\t\t\t\t\t\t\t\tSUM(CASE WHEN T.OCCUPANCY_dt = T.ARRIVAL_dt THEN 1 ELSE 0 END) AS arrivals\n" +
                "                                 from reservation_night t \n" +
                "                                 where  t.individual_status NOT IN ('XX','CX','NS','NO_SHOW','NO " +
                "SHOW','CANCELLED')\n" +
                "                                   and occupancy_DT between :startDate and :endDate\n" +
                "                                 group by t.occupancy_DT) A on a.occupancy_DT = dl.calendar_date\n" +
                "                  left join (select t.departure_DT, count(*) departures\n" +
                "                             from reservation_night t \n" +
                "                             where  t.Individual_Status NOT IN ('XX','CX','NS','NO_SHOW','NO SHOW'," +
                "'CANCELLED')\n" +
                "                               and occupancy_DT between DATEADD(D,-1,:startDate) and :endDate\n" +
                "                             group by t.departure_DT) D on dl.calendar_date = D.departure_DT\n" +
                "where dl.calendar_date between :startDate and :endDate"),
        @NamedNativeQuery(name = ReservationNight.GET_NON_HONORS_TOTAL_ACTIVITY_BY_DATERANGE, query = " SELECT dl" +
                ".calendar_date,\n" +
                "       COALESCE(rooms_sold, 0)   rooms_sold,\n" +
                "       COALESCE(room_revenue, 0) room_revenue,\n" +
                "       COALESCE(arrivals, 0)     arrivals,\n" +
                "       COALESCE(departures, 0)   departures\n" +
                "FROM   calendar_dim dl\n" +
                "       LEFT JOIN (SELECT t.occupancy_dt,\n" +
                "                         Count(*)          rooms_sold,\n" +
                "                         Sum(room_revenue) room_revenue,\n" +
                "                         Sum(CASE\n" +
                "                               WHEN T.occupancy_dt = T.arrival_dt THEN 1\n" +
                "                               ELSE 0\n" +
                "                             END)          AS arrivals\n" +
                "                  FROM   reservation_night t\n" +
                "                  WHERE  t.individual_status NOT IN (\n" +
                "                         'XX', 'CX', 'NS', 'NO_SHOW',\n" +
                "                         'NO  SHOW', 'CANCELLED' )\n" +
                "                         AND occupancy_dt BETWEEN :startDate AND :endDate\n" +
                "                         AND rate_code NOT LIKE 'HHNSRR'\n" +
                "                         AND rate_code NOT LIKE 'L[A-Z][A-Z]RR[1-4]'\n" +
                "                         AND rate_code NOT LIKE 'HHPRR[1-4]'\n" +
                "                         AND rate_code NOT LIKE 'L[A-Z][A-Z]SRR'\n" +
                "                  GROUP  BY t.occupancy_dt) A\n" +
                "              ON a.occupancy_dt = dl.calendar_date\n" +
                "       LEFT JOIN (SELECT t.departure_dt,\n" +
                "                         Count(*) departures\n" +
                "                  FROM   reservation_night t\n" +
                "                  WHERE  t.individual_status NOT IN (\n" +
                "                         'XX', 'CX', 'NS', 'NO_SHOW',\n" +
                "                         'NO SHOW', 'CANCELLED' )\n" +
                "                         AND occupancy_dt BETWEEN Dateadd(d, -1, :startDate)\n" +
                "                                                  AND\n" +
                "                                                  :endDate\n" +
                "                         AND rate_code NOT LIKE 'HHNSRR'\n" +
                "                         AND rate_code NOT LIKE 'L[A-Z][A-Z]RR[1-4]'\n" +
                "                         AND rate_code NOT LIKE 'HHPRR[1-4]'\n" +
                "                         AND rate_code NOT LIKE 'L[A-Z][A-Z]SRR'\n" +
                "                  GROUP  BY t.departure_dt) D\n" +
                "              ON dl.calendar_date = D.departure_dt\n" +
                "WHERE  dl.calendar_date BETWEEN :startDate AND :endDate  "),
        @NamedNativeQuery(name = ReservationNight.GET_NON_HONORS_AVG_RATES_TOTAL_ACTIVITY_BY_DATERANGE, query =
                "SELECT ta.Occupancy_DT,\n" +
                        "  ISNULL(a.Rooms_Sold, 0) AS Rooms_Sold,\n" +
                        "  ISNULL(a.Room_Revenue, 0) AS Room_Revenue,\n" +
                        "  ISNULL(a.Arrivals, 0) AS Arrivals,\n" +
                        "  ISNULL(b.Departures, 0) AS Departures\n" +
                        "FROM (SELECT Occupancy_DT FROM total_activity) ta\n" +
                        "  LEFT OUTER JOIN (\n" +
                        "    SELECT Occupancy_DT,\n" +
                        "      SUM(Rooms_Sold) Rooms_Sold,\n" +
                        "      ROUND(SUM(Room_Revenue), 0) Room_Revenue,\n" +
                        "      SUM(Arrivals) Arrivals\n" +
                        "    FROM (\n" +
                        "        SELECT\n" +
                        "          Occupancy_DT,\n" +
                        "          1 as Rooms_Sold,\n" +
                        "          CASE WHEN DATEDIFF(d, rn.Occupancy_DT, rn.Departure_DT) > 0 THEN it.Room_Revenue / DATEDIFF(DAY, it.arrival_dt, it.departure_dt) ELSE 0 END AS Room_Revenue,\n" +
                        "          CASE WHEN DATEDIFF(d, rn.Arrival_DT, rn.Occupancy_DT) = 0 THEN 1 ELSE 0 END AS Arrivals\n" +
                        "        FROM\n" +
                        "          Reservation_Night rn INNER JOIN individual_trans it ON rn.reservation_identifier = it.reservation_identifier\n" +
                        "        WHERE\n" +
                        "          rn.rate_code NOT LIKE 'HHNSRR'\n" +
                        "          AND rn.rate_code NOT LIKE 'L[A-Z][A-Z]SRR'\n" +
                        "          AND rn.rate_code NOT LIKE 'L[A-Z][A-Z]RR[1-4]'\n" +
                        "          AND rn.rate_code NOT LIKE 'HHPRR[1-4]'\n" +
                        "          AND rn.rate_code NOT LIKE 'L[A-Z][A-Z]SRR'\n" +
                        "          AND rn.Individual_Status NOT IN ('XX', 'NS', 'NO_SHOW', 'CANCELLED')\n" +
                        "      ) rn1\n" +
                        "    GROUP BY Occupancy_DT\n" +
                        "  ) a ON ta.Occupancy_DT = a.Occupancy_DT\n" +
                        "  LEFT OUTER JOIN (\n" +
                        "    SELECT\n" +
                        "      d.occupancy_DT,\n" +
                        "      SUM(d.Departures) AS Departures\n" +
                        "    FROM (\n" +
                        "        SELECT\n" +
                        "          DATEADD(d, 1, Occupancy_DT) AS occupancy_DT,\n" +
                        "          CASE WHEN DATEDIFF(d, Occupancy_DT, Departure_DT) = 1 THEN 1 ELSE 0 END AS Departures\n" +
                        "        FROM\n" +
                        "          Reservation_Night\n" +
                        "        WHERE\n" +
                        "          rate_code NOT LIKE 'HHNSRR'\n" +
                        "          AND rate_code NOT LIKE 'L[A-Z][A-Z]SRR'\n" +
                        "          AND rate_code NOT LIKE 'L[A-Z][A-Z]RR[1-4]'\n" +
                        "          AND rate_code NOT LIKE 'HHPRR[1-4]'\n" +
                        "          AND rate_code NOT LIKE 'L[A-Z][A-Z]SRR'\n" +
                        "          AND Individual_Status NOT IN ('XX', 'NS', 'NO_SHOW', 'CANCELLED')\n" +
                        "      ) d\n" +
                        "    GROUP BY d.Occupancy_DT\n" +
                        "  ) b ON a.Occupancy_DT = b.occupancy_DT\n" +
                        " WHERE ta.Occupancy_DT BETWEEN :startDate AND :endDate "),
        @NamedNativeQuery(name = DELETE_RESERVATIONS_IN_RANGE_DELETE_INHOUSE, query = "DELETE rn\n" +
                "FROM   reservation_night rn\n" +
                "       JOIN (SELECT reservation_identifier,\n" +
                "                    Min(occupancy_dt) minStayDate,\n" +
                "                    Max(occupancy_dt) maxStayDate\n" +
                "             FROM   reservation_night\n" +
                "             GROUP  BY reservation_identifier\n" +
                "             HAVING MAX(occupancy_dt) >= :migrationDate ) A\n" +
                "         ON rn.reservation_identifier = A.reservation_identifier\n" +
                "            AND rn.individual_status NOT IN ( 'XX', 'NS', 'NO SHOW', 'NO_SHOW',\n" +
                "                                              'CANCELLED' );  "),
        @NamedNativeQuery(name = ReservationNight.DELETE_RESERVATIONS_IN_RANGE_EXCLUDE_INHOUSE, query = "DELETE rn\n" +
                "FROM   reservation_night rn\n" +
                "       JOIN (SELECT reservation_identifier,\n" +
                "                    Min(occupancy_dt) minStayDate,\n" +
                "                    Max(occupancy_dt) maxStayDate\n" +
                "             FROM   reservation_night\n" +
                "             GROUP  BY reservation_identifier\n" +
                "             HAVING MIN(occupancy_dt) >= :migrationDate ) A\n" +
                "         ON rn.reservation_identifier = A.reservation_identifier\n" +
                "            AND rn.individual_status NOT IN ( 'XX', 'NS', 'NO SHOW', 'NO_SHOW',\n" +
                "                                              'CANCELLED' );  "),
        @NamedNativeQuery(name = ReservationNight.DELETE_BY_OCCUPANCY_DT,
                query = "DELETE FROM Reservation_Night WHERE Occupancy_DT >= :minimumTransactionDateFromExtSysReservations"),
        @NamedNativeQuery(name = ReservationNight.TRIM_INHOUSE_RESERVATIONS_WITH_FUTURE_DEPARTURE_DT,
                query = "UPDATE Reservation_Night SET Departure_DT = :minimumTransactionDateFromExtSysReservations " +
                        "WHERE Departure_DT > :minimumTransactionDateFromExtSysReservations AND Arrival_Dt < :minimumTransactionDateFromExtSysReservations"),
        @NamedNativeQuery(name = ReservationNight.TRIM_NEW_RESERVATIONS_POST_MIGRATION_FOR_OPERA,
                query = "UPDATE Reservation_Night SET Arrival_DT = :cutoffDt " +
                        "WHERE reservation_identifier IN (SELECT DISTINCT Reservation_Identifier FROM Reservation_Night " +
                        "WHERE Departure_DT>:cutoffDt AND Arrival_Dt < :cutoffDt " +
                        "GROUP BY reservation_identifier, Arrival_DT, Departure_DT " +
                        "HAVING COUNT(DISTINCT Occupancy_DT) <> DATEDIFF(DAY,Arrival_DT,Departure_DT))"),
        @NamedNativeQuery(name = ReservationNight.GET_STRAIGHT_BAR_MS_ANALYTICAL_RESERVATIONS,
                query = "select rn.Occupancy_DT, ms.Mkt_Seg_Code, ac.Accom_Class_Code, rn.Rate_Code, rn.Arrival_DT, " +
                        "       rn.Departure_DT, rn.Booking_DT, rn.Room_Revenue, rn.Total_Revenue, rn.Rate_Value, " +
                        "       rn.Individual_Status, ms.Mkt_Seg_ID,rn.Number_Adults,rn.Number_Children " +
                        "   from Reservation_Night rn " +
                        "         join Mkt_Seg_Details msd on rn.Mkt_Seg_ID = msd.Mkt_Seg_ID and msd.Priced_By_BAR = 1 " +
                        "         join Mkt_Seg ms on ms.Mkt_Seg_ID = msd.Mkt_Seg_ID " +
                        "         join Accom_Type at on rn.Accom_Type_ID = at.Accom_Type_ID " +
                        "         join Accom_Class ac on at.Accom_Class_ID = ac.Accom_Class_ID " +
                        "   where rn.Occupancy_DT >= :startDate " +
                        "  and rn.Occupancy_DT <= :endDate " +
                        "  and DATEDIFF(DAY, Booking_DT, Arrival_DT) <= :maxDta " +
                        "  and DATEDIFF(DAY, Arrival_DT, Departure_DT) <= :maxLos " +
                        "  and rn.Individual_Status not in ('XX', 'NS', 'NO SHOW', 'NO_SHOW', 'CANCELLED')" +
                        "  AND rn.Number_Adults < 5 " +
                        "  AND rn.Room_Revenue > 0;"
        ),
        @NamedNativeQuery(name = ReservationNight.GET_MARKET_CODE_RATE_CODE_VOLUME_BY_PERCENTAGE_FOR_RATE_CODES,
                query = "select t.Market_Code, t.Rate_Code,t.volume,  ROUND(100.0 * t.volume / SUM(t.volume) over (partition by t.Rate_Code), 2 ) as percentage " +
                        "from " +
                        "( " +
                        "   select Market_Code, Rate_Code, sum(reserVolume) as volume  from " +
                        "   ( " +
                        "       select Market_Code, Rate_Code, count(*) as reserVolume from Reservation_Night where Rate_Code in (:rateCodes) group by Market_Code, Rate_Code, Reservation_Identifier " +
                        "   ) as t group by Market_Code, Rate_Code " +
                        ") as t "),
        @NamedNativeQuery(name = ReservationNight.GET_STANDARD_HONORS_RATE_CODE, query = "SELECT DISTINCT RATE_CODE FROM reservation_night WHERE rate_code = 'HHNSRR' or rate_code LIKE 'L__SRR'"),
        @NamedNativeQuery(name = ReservationNight.GET_LATEST_INHOUSE_CHECKOUT_TRXNS_FOR_IPP, query = "SELECT RN.* " +
                "FROM Reservation_Night RN " +
                "JOIN (SELECT Reservation_Identifier, MAX(Occupancy_DT) AS Max_Occ_DT FROM Reservation_Night " +
                "       WHERE Occupancy_DT <= :caughtUpDate " +
                "       GROUP BY Reservation_Identifier) AS ORN " +
                "   ON RN.Reservation_Identifier = ORN.Reservation_Identifier " +
                "       AND RN.Occupancy_DT = ORN.Max_Occ_DT " +
                "WHERE RN.Reservation_Identifier IN (:txnIds) " +
                "   AND RN.Mkt_Seg_ID IN (:msIds) " +
                "   AND RN.Occupancy_DT <= :caughtUpDate ",
                resultClass = ReservationNight.class),
        @NamedNativeQuery(name = ReservationNight.GET_DISTINCT_RATE_CODES_BY_MKT_SEG_CODE,
                query = "select distinct Rate_Code, Market_Code from Reservation_Night where Market_Code in (:marketCodes)"),
        @NamedNativeQuery(name = ReservationNight.GET_ADR_BAR_BY_OCCUPANCY_FROM_DATE,
                query = "SELECT RN.Occupancy_DT AS occupancyDate, sum(RN.Room_Revenue)/count(RN.Room_Revenue) AS barAdr FROM Reservation_Night RN " +
                        "INNER JOIN Mkt_Seg MS ON RN.MKT_SEG_ID = MS.Mkt_Seg_ID " +
                        "INNER JOIN Accom_Type ACCT ON RN.Accom_Type_ID = ACCT.Accom_Type_ID " +
                        "WHERE RN.Individual_Status NOT IN('CX','CANCELLED') AND MS.Mkt_Seg_Code = 'BAR' AND RN.Occupancy_DT >= :startDate " +
                        "GROUP BY RN.Occupancy_DT ORDER BY RN.Occupancy_DT"),
        @NamedNativeQuery(name = ReservationNight.GET_ROOM_REVENUE_BY_OCCUPANCY_FROM_DATE_EXCLUDE_RATE_CODE_LIST,
                query = "SELECT RN.Occupancy_DT AS occupancyDate, sum(RN.Room_Revenue) AS roomRevenue FROM Reservation_Night RN " +
                        "WHERE RN.Individual_Status IN('RESERVED','SS', 'IN_HOUSE', 'CI') AND RN.Occupancy_DT >= :startDate AND RN.Rate_Code NOT IN :rateCodeList " +
                        "GROUP BY RN.Occupancy_DT ORDER BY RN.Occupancy_DT"),
        @NamedNativeQuery(name = ReservationNight.GET_ROOMS_SOLD_BY_OCCUPANCY_EXCLUDE_RATE_CODE_LIST,
                query = "SELECT RN.Occupancy_DT AS occupancyDate, COUNT(*) AS roomSoldDifference FROM Reservation_Night RN " +
                        "WHERE RN.Individual_Status IN('RESERVED','SS', 'IN_HOUSE', 'CI') AND RN.Occupancy_DT >= :startDate AND RN.Rate_Code NOT IN :rateCodeList " +
                        "GROUP BY RN.Occupancy_DT ORDER BY RN.Occupancy_DT")
}
)
@Table(name = "Reservation_Night")
@EntityListeners({ReservationNight.ReservationNightFoodRevenueListener.class,
        ReservationNight.ReservationNightBeverageRevenueListener.class,
        ReservationNight.ReservationNightOtherRevenueListener.class,
        ReservationNight.ReservationNightTotalRevenueListener.class,
        ReservationNight.ReservationNightRoomRevenueListener.class,
        ReservationNight.ReservationNightTelecomRevenueListener.class,
        ReservationNight.ReservationNightRateValueListener.class})
public class ReservationNight extends ActivityLastUpdatedDateAwareEntity implements
        Serializable {

    public static final String TABLE_NAME = "RESERVATION_NIGHT";
    public static final String GET_ADR_BAR_BY_OCCUPANCY_FROM_DATE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_ADR_BAR_BY_OCCUPANCY_FROM_DATE";
    public static final String GET_ROOM_REVENUE_BY_OCCUPANCY_FROM_DATE_EXCLUDE_RATE_CODE_LIST = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_ROOM_REVENUE_BY_OCCUPANCY_FROM_DATE_EXCLUDE_RATE_CODE_LIST";
    public static final String GET_ROOMS_SOLD_BY_OCCUPANCY_EXCLUDE_RATE_CODE_LIST = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_ROOMS_SOLD_BY_OCCUPANCY_EXCLUDE_RATE_CODE_LIST";
    public static final String GET_RECORDS = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_RECORDS";
    public static final String FIND_MIN_OCCUPANCY_DT = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.FIND_MIN_OCCUPANCY_DT";
    public static final String FIND_MAX_OCCUPANCY_DT = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.FIND_MAX_OCCUPANCY_DT";
    public static final String BY_MARKET_CODE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.BY_MARKET_CODE";
    public static final String BY_MARKET_SEG_ID = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.BY_MARKET_SEG_ID";
    public static final String COUNT_BY_MARKET_SEG_ID_IN = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.BY_MARKET_SEG_ID_IN";
    public static final String DISTINCT_RESERVATION_IDENTIFIERS_BY_MARKET_SEG_ID_IN = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.DISTINCT_RESERVATION_IDENTIFIERS_BY_MARKET_SEG_ID_IN";
    public static final String DELETE_BY_RESERVATION_IDS = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.DELETE_BY_RESERVATION_IDS";
    public static final String DELETE_BY_RESERVATION_ID_LIKE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.DELETE_BY_RESERVATION_ID_LIKE";
    public static final String UPDATE_BOOKED_ACCOM_TYPE_CODE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.UPDATE_BOOKED_ACCOM_TYPE_CODE";
    public static final String UPDATE_FILE_METADATA_ID_BY_START_DATE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.UPDATE_FILE_METADATA_ID_BY_START_DATE";
    public static final String GET_BY_OCCUPANCY_DATE_RANGE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE";
    public static final String GET_BY_OCCUPANCY_DATE_RANGE_DIFF = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_DIFF";
    public static final String GET_BY_OCCUPANCY_DATE_RANGE_WITH_POST_DEPARTURE_REVENUE_DATA = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_WITH_POST_DEPARTURE_REVENUE_DATA";
    public static final String GET_BY_OCCUPANCY_DATE_RANGE_WITH_POST_DEPARTURE_REVENUE_DATA_DIFF = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_BY_OCCUPANCY_DATE_RANGE_WITH_POST_DEPARTURE_REVENUE_DATA_DIFF";
    public static final String GET_RATE_CODES_FOR_MKT_SEG_IN = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_RATE_CODES_FOR_MKT_SEG_IN";
    public static final String GET_RATE_CODES_FOR_MKT_SEG_IN_WERE_RATE_CODE_LIKE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_RATE_CODES_FOR_MKT_SEG_IN_WERE_RATE_CODE_LIKE";

    public static final String GET_DISTINCT_RATE_CODES_BY_MKT_SEG_CODE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_DISTINCT_RATE_CODES_BY_MKT_SEG_CODE";

    public static final String UPDATE_FILE_METADATA_ID_BY_DEPARTURE_DATE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.UPDATE_FILE_METADATA_ID_BY_DEPARTURE_DATE";
    public static final String UPDATE_FILE_METADATA_ID_BY_START_DATE_AND_DATE_RANGE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".UPDATE_FILE_METADATA_ID_BY_START_DATE_AND_DATE_RANGE";
    public static final String ADJUST_ARRIVAL_DT_AND_DEPARTURE_DT =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".ADJUST_ARRIVAL_DT_AND_DEPARTURE_DT";
    public static final String DELETE_BY_OCCUPANCY_DT = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.DELETE_BY_OCCUPANCY_DT";
    public static final String TRIM_INHOUSE_RESERVATIONS_WITH_FUTURE_DEPARTURE_DT = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.TRIM_INHOUSE_RESERVATIONS_WITH_FUTURE_DEPARTURE_DT";
    public static final String TRIM_NEW_RESERVATIONS_POST_MIGRATION_FOR_OPERA = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.TRIM_NEW_RESERVATIONS_POST_MIGRATION_FOR_OPERA";
    public static final String GET_STRAIGHT_BAR_MS_ANALYTICAL_RESERVATIONS = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_STRAIGHT_BAR_MS_ANALYTICAL_RESERVATIONS";
    public static final String UPDATE_MARKET_CODE_IF_NULL =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.UPDATE_MARKET_CODE_IF_NULL";
    public static final String UPDATE_CHANGE_MARKET_CODE_IF_NULL =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".UPDATE_CHANGE_MARKET_CODE_IF_NULL";
    public static final String FIND_MIN_MAX_ARRIVAL_DT =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.FIND_MIN_MAX_ARRIVAL_DT";
    public static final String FIND_MIN_ARRIVAL_DT =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.FIND_MIN_ARRIVAL_DT";
    public static final String FIND_MAX_ARRIVAL_DT =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.FIND_MAX_ARRIVAL_DT";
    public static final String FIND_MAX_DEPARTURE_DT =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.FIND_MAX_DEPARTURE_DT";
    public static final String GET_BY_OCCUPANCY_DT_RANGE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_BY_BOOKING_DATE_RANGE";
    public static final String GET_BY_ARRIVAL_DT_RANGE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_BY_ARRIVAL_DT_RANGE";
    public static final String DELETE_RESERVATIONS_IN_RANGE_DELETE_INHOUSE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".DELETE_RESERVATIONS_IN_RANGE_DELETE_INHOUSE";
    public static final String DELETE_RESERVATIONS_IN_RANGE_EXCLUDE_INHOUSE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".DELETE_RESERVATIONS_IN_RANGE_EXCLUDE_INHOUSE";
    public static final String GET_BY_DEPARTURE_DT_RANGE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_BY_DEPARTURE_DT_RANGE";
    public static final String GET_TOTAL_ACTIVITY_BY_DATERANGE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".GET_TOTAL_ACTIVITY_BY_DATERANGE";
    public static final String GET_NON_HONORS_TOTAL_ACTIVITY_BY_DATERANGE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".GET_NON_HONORS_TOTAL_ACTIVITY_BY_DATERANGE";
    public static final String GET_NON_HONORS_AVG_RATES_TOTAL_ACTIVITY_BY_DATERANGE =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".GET_NON_HONORS_AVG_RATES_TOTAL_ACTIVITY_BY_DATERANGE";

    public static final String GET_MARKET_CODE_RATE_CODE_VOLUME_BY_PERCENTAGE_FOR_RATE_CODES =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".GET_MARKET_CODE_RATE_CODE_VOLUME_BY_PERCENTAGE_FOR_RATE_CODES";

    public static final String GET_LATEST_INHOUSE_CHECKOUT_TRXNS_FOR_IPP =
            "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight" +
                    ".GET_LATEST_INHOUSE_CHECKOUT_TRXNS_FOR_IPP";


    public static final String GET_MARKET_CODE_RATE_CODE_BY_MARKET_CODES =
            "select  " +
                    "    distinct isnull(rn.Market_Code, originalMktCodeFromID.Market_Code) as Market_Code, rn" +
                    ".Rate_Code, ams.Attribute " +
                    "from  " +
                    "    Reservation_Night rn " +
                    "    inner join " +
                    "    ( " +
                    "        SELECT " +
                    "            ana_mkt_id as Mkt_Seg_ID, ori_mkt_code as Market_Code " +
                    "        FROM " +
                    "            dbo.ufn_get_generic_analyticalmarketsegid_originalmarketsegment_mapping() " +
                    "    ) as originalMktCodeFromID " +
                    "    on rn.Mkt_Seg_ID = originalMktCodeFromID.Mkt_Seg_ID " +
                    "    inner join " +
                    "    Analytical_Mkt_Seg ams " +
                    "    on ams.Market_Code = originalMktCodeFromID.Market_Code " +
                    "where originalMktCodeFromID.Market_Code in (:marketCodes) ";


    public static final String GET_DISTINCT_RATE_CODES = new StringBuilder()
            .append(" SELECT distinct rate_code as Rate_Code FROM Reservation_Night RN ")
            .append(" where Rate_Code is not NULL").toString();

    public static final String GET_DISTINCT_MARKET_CODES = " SELECT distinct Market_Code FROM Reservation_Night where Market_Code is not NULL";

    public static final String GET_NEW_AMS_TO_BE_CREATED = "select distinct(ycr.Analytical_Market_Code) from " +
            "reservation_night rn \n" +
            "    join opera.yield_category_rule ycr on rn.market_code = ycr.Market_Code and coalesce(rn.rate_code,'') =" +
            " ycr.Rate_Code\n" +
            "    left join mkt_seg ms on ycr.Analytical_Market_Code = ms.Mkt_Seg_Code\n" +
            "    where ycr.market_code in (:marketCodes) and ms.Mkt_Seg_Code is null";
    // LNR, CNR, MKT, DISC, IT

    public static final String GET_NEW_AMS_TO_BE_CREATED_INCLUDE_GROUP = "select distinct(ycr" +
            ".Analytical_Market_Code) from \n" +
            "(select market_Code, rate_code from reservation_night rn union( select  market_code,group_code as " +
            "rate_code  from group_master gm join mkt_seg ms on gm.mkt_seg_id = ms.mkt_seg_id join analytical_mkt_seg_aud amsa on ms.mkt_seg_code = amsa.mapped_market_code) )\n" +
            " rn \n" +
            "    join opera.yield_category_rule ycr on rn.market_code = ycr.Market_Code and coalesce(rn.rate_code,'') =\n" +
            " ycr.Rate_Code\n" +
            "    left join mkt_seg ms on ycr.Analytical_Market_Code = ms.Mkt_Seg_Code\n" +
            "    where ycr.market_code in (:marketCodes) and ms.Mkt_Seg_Code is null";

    public static final String GET_NEW_STRAIGHT_DEFAULT_MS_TO_BE_CREATED = "SELECT distinct(ams.Mapped_Market_Code)\n" +
            "FROM analytical_mkt_seg ams\n" +
            "JOIN\n" +
            "  (SELECT DISTINCT market_code,\n" +
            "                   rate_code\n" +
            "   FROM reservation_night\n" +
            "   UNION SELECT DISTINCT market_code,\n" +
            "                         rate_code\n" +
            "   FROM reservation_night_change) rn ON ams.market_code = rn.market_code\n" +
            "LEFT JOIN opera.Yield_Category_Rule ycr ON rn.Market_Code = ycr.Market_Code\n" +
            "AND rn.Rate_Code = ycr.Rate_Code\n" +
            "LEFT JOIN mkt_seg ms ON ams.Mapped_Market_Code = ms.Mkt_Seg_Code\n" +
            "WHERE ycr.Yield_Category_Rule_ID IS NULL\n" +
            "  AND (ams.market_code IN (:marketCodes))\n" +
            "  AND (ams.Rate_Code_Type IN (:rateCodeTypes))\n" +
            "  AND ms.mkt_seg_id IS NULL";

    public static final String GET_NEW_STRAIGHT_DEFAULT_MS_TO_BE_CREATED_INCLUDE_GROUPS = "SELECT distinct(ams\n" +
            ".Mapped_Market_Code)\n" +
            "FROM analytical_mkt_seg ams\n" +
            "JOIN\n" +
            "  (SELECT DISTINCT market_code,\n" +
            "                   rate_code\n" +
            "   FROM reservation_night\n" +
            "   UNION SELECT DISTINCT market_code,\n" +
            "                         rate_code\n" +
            "   FROM reservation_night_change\n" +
            "   union\n" +
            "   select  market_code,group_code as    \n" +
            "             rate_code  from group_master gm join mkt_seg ms on gm.mkt_seg_id = ms.mkt_seg_id join analytical_mkt_seg_aud amsa on ms.mkt_seg_code = amsa.mapped_market_code\n" +
            "   ) rn ON ams.market_code = rn.market_code\n" +
            "LEFT JOIN opera.Yield_Category_Rule ycr ON rn.Market_Code = ycr.Market_Code\n" +
            "AND rn.Rate_Code = ycr.Rate_Code\n" +
            "LEFT JOIN mkt_seg ms ON ams.Mapped_Market_Code = ms.Mkt_Seg_Code\n" +
            "WHERE ycr.Yield_Category_Rule_ID IS NULL\n" +
            "  AND (ams.market_code IN (:marketCodes))\n" +
            "  AND (ams.Rate_Code_Type IN (:rateCodeTypes))\n" +
            "  AND ms.mkt_seg_id IS NULL";

    public static final String GET_RATE_CODES_AND_FORECAST_GROUP_IDS_USING_RESERVATION_NIGHT = new StringBuilder()
            .append("SELECT  rate_code as Rate_Code ,msfg.Forecast_Group_ID,MAX(booking_dt) as booking_dt ")
            .append("FROM Reservation_Night RN ")
            .append("INNER JOIN Mkt_Seg_Forecast_Group msfg on RN.Mkt_Seg_ID = msfg.Mkt_Seg_ID ")
            .append("WHERE ")
            .append("    RN.Property_ID = :propertyId ")
            .append("    and Individual_Status not in ('XX', 'NS') ")
            .append("    and Arrival_DT<> Departure_DT ")
            .append("    and Rate_Code is not NULL ")
            .append("    and LTRIM(RTRIM(rate_code)) != '' ")
            .append("    AND msfg.Status_ID = 1 ")
            .append("    group by rate_code ,msfg.Forecast_Group_ID ")
            .append("  ORDER BY Rate_Code asc, booking_dt desc")
            .toString();
    public static final String GET_RATE_CODE_NAMES_USING_RESERVATION_NIGHT = new StringBuilder()
            .append("SELECT distinct rate_code as Rate_Code ")
            .append("FROM Reservation_Night RN ")
            .append("INNER JOIN Mkt_Seg_Forecast_Group msfg on RN.Mkt_Seg_ID = msfg.Mkt_Seg_ID ")
            .append("WHERE ")
            .append("    RN.Property_ID = :propertyId ")
            .append("    and Individual_Status not in ('XX', 'NS') ")
            .append("    and Arrival_DT<> Departure_DT ")
            .append("    and Rate_Code is not NULL ")
            .append("    and LTRIM(RTRIM(rate_code)) != '' ")
            .append("    AND msfg.Status_ID = 1 ")
            .append("  ORDER BY Rate_Code")
            .toString();

    public static final String AND_OCCUPANCY_DATE_BETWEEN_CLAUSE = " AND rn.occupancy_dt between :startDate and :endDate";
    public static final String MAPPING_FOR_MKT_SEG_ID_AND_AMS_MARKET_CODE = "SELECT distinct ms.mkt_seg_id AS ana_mkt_id,coalesce(ams.Market_Code,ms.mkt_seg_code) AS ori_mkt_code FROM mkt_seg ms " +
            " LEFT JOIN (select distinct market_code, mapped_market_code from Analytical_Mkt_Seg where Preserved = 0) ams " +
            " ON ms.Mkt_Seg_Code COLLATE SQL_Latin1_General_CP1_CS_AS = ams.Mapped_Market_Code COLLATE SQL_Latin1_General_CP1_CS_AS";

    public static final String MAXIMUM_OCCUPANCY_DATE = "select max(occupancy_dt) from %s";

    public static final String MINIMUM_OCCUPANCY_DATE = "select min(occupancy_dt) from %s";

    public static final String DELETE_DISCONTINUED_OLD_PMS_MAPPINGS = "delete rn from %s rn where (" +
            " rn.accom_type_id in (select accom_type_id from accom_type at inner join PMS_Migration_Mapping pmm on at.Accom_Type_Code = pmm.Current_Code and pmm.Code_Type = :roomCodeType and pmm.Discontinued = 1)" +
            " or" +
            " rn.Market_Code in (select pmm.current_code from PMS_Migration_Mapping pmm where pmm.Code_Type in  (:marketCodeTypes) and pmm.Discontinued = 1))"
            + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_ACCOM_TYPES_WITH_NEW_PMS_MAPPINGS =
            "update rn set rn.Accom_Type_ID = new_at.Accom_Type_ID " +
                    "from %s rn " +
                    "join Accom_Type old_at on old_at.Accom_Type_ID = rn.Accom_Type_ID " +
                    "join PMS_Migration_Mapping pmm on pmm.Current_Code = old_at.Accom_Type_Code and pmm.Code_Type = :roomCodeType and COALESCE(pmm.Is_Primary_Code_For_One_To_Many_Splits,1) <> 0 " +
                    "join Accom_Type new_at on new_at.Accom_Type_Code = pmm.New_Equivalent_Code and new_at.Status_ID = 1"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_BOOKED_ACCOM_TYPE_CODES_WITH_NEW_PMS_MAPPINGS =
            "update rn set rn.Booked_Accom_Type_Code = (case when pmm.Discontinued = 0 then pmm.New_Equivalent_Code else new_at.Accom_Type_Code end) " +
                    "from %s rn " +
                    "join Accom_Type new_at on new_at.Accom_Type_ID = rn.Accom_Type_ID " +
                    "join PMS_Migration_Mapping pmm on pmm.Current_Code = rn.Booked_Accom_Type_Code and pmm.Code_Type = :roomCodeType and COALESCE(pmm.Is_Primary_Code_For_One_To_Many_Splits,1) <> 0 "
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MARKET_CODES_AGAINST_BLANK_RATES_WITH_NEW_PMS_MAPPINGS =
            "update rn set rn.Market_Code = (case when pmm.Discontinued = 0 then pmm.New_Equivalent_Code else pmm.Current_Code end) " +
                    "from %s rn " +
                    "join (" + MAPPING_FOR_MKT_SEG_ID_AND_AMS_MARKET_CODE + ") mapping on mapping.ana_mkt_id = rn.Mkt_Seg_ID " +
                    "join PMS_Migration_Mapping pmm on (pmm.Current_Code COLLATE SQL_Latin1_General_CP1_CS_AS = mapping.ori_mkt_code COLLATE SQL_Latin1_General_CP1_CS_AS) " +
                    "and pmm.Code_Type in (:marketCodeTypes) and COALESCE(pmm.Is_Primary_Code_For_One_To_Many_Splits,1) <> 0 " +
                    "where TRIM(rn.Rate_Code) = ''"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    /**
     * This query will update reservation table records having market code and blank rate code combination using new mapping.
     * For split market segment scenario we consider primary market segment marked by client in PMS mapping configuration sheet if
     * we can't figure out the new market code based on rate code from AMS table.
     */
    public static final String UPDATE_RESERVATION_NIGHT_MARKET_CODES_AGAINST_BLANK_RATES_WITH_NEW_PMS_MAPPINGS_FOR_MKT_SEG_RECODING =
            "UPDATE rn SET rn.Market_Code = mapping.New_Mkt_Code FROM %s rn INNER JOIN " +
                    "( SELECT pmm.Current_Code AS Current_Mkt_Code, pmm.New_Equivalent_Code AS New_Mkt_Code " +
                    "FROM PMS_Migration_Mapping AS pmm JOIN  opera.Yield_Category_Rule AS ycr ON " +
                    "(pmm.New_Equivalent_Code = ycr.Market_Code AND pmm.Code_Type IN(:marketCodeTypes) AND " +
                    "pmm.Discontinued = 0) AND pmm.New_Equivalent_Code <> ''  AND COALESCE(pmm.Is_Primary_Code_For_One_To_Many_Splits,1) <> 0 " +
                    "AND (pmm.Current_Code COLLATE SQL_Latin1_General_CP1_CS_AS <> pmm.New_Equivalent_Code COLLATE SQL_Latin1_General_CP1_CS_AS) " +
                    "AND ycr.Rate_Code IS NULL) AS mapping ON rn.Market_Code= mapping.Current_Mkt_Code WHERE TRIM(rn.Rate_Code) = ''"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MARKET_CODES_AND_RATE_CODES_AGAINST_NON_BLANK_RATES_WITH_NEW_PMS_MAPPINGS =
            "update rn set rn.Market_Code = pmm_rc_ms.New_Mkt_Seg, rn.Rate_Code = (case when (pmm_rc_ms.New_Rate_Code is null or pmm_rc_ms.New_Rate_Code = '') then rn.Rate_Code else pmm_rc_ms.New_Rate_Code end) " +
                    "from %s rn " +
                    "join PMS_Migration_Rate_code_mkt_seg_Mapping pmm_rc_ms " +
                    "on (pmm_rc_ms.Current_Rate_Code COLLATE SQL_Latin1_General_CP1_CS_AS = rn.Rate_Code COLLATE SQL_Latin1_General_CP1_CS_AS) " +
                    "where TRIM(rn.Rate_Code) <> ''"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_GROUP_BLOCK_CODES_WITH_NEW_PMS_MAPPINGS =
            "update rn set rn.Inv_Block_Code = pmm.New_Equivalent_Code " +
                    "from %s rn " +
                    "join PMS_Migration_Mapping pmm on pmm.Current_Code = rn.Inv_Block_Code and pmm.Code_Type = :groupCodeType " +
                    "where rn.Inv_Block_Code is not null"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_WITH_ALL_AMS_RULES_BY_PROPERTY_ID =
            "update rn set rn.Mkt_Seg_ID = new_ams.Mkt_Seg_ID " +
                    "from %s rn " +
                    "join Mkt_Seg ms on ms.Mkt_Seg_ID = rn.Mkt_Seg_ID and ms.Property_ID = :dummyPropertyId " +
                    "join opera.Yield_Category_Rule ycr on ycr.Market_Code = rn.Market_Code and ycr.Rate_Code = rn.Rate_Code " +
                    "join Mkt_Seg new_ams on new_ams.Mkt_Seg_Code = ycr.Analytical_Market_Code and new_ams.Property_ID = :propertyId"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_BY_MARKET_CODES =
            "update rn set rn.Mkt_Seg_ID = new_ams.Mkt_Seg_ID " +
                    "from %s rn " +
                    "join Mkt_Seg ms on ms.Mkt_Seg_ID = rn.Mkt_Seg_ID " +
                    "join opera.Yield_Category_Rule ycr on UPPER(ycr.Market_Code) = UPPER(rn.Market_Code) and UPPER(ycr.Rate_Code) = UPPER(rn.Rate_Code) " +
                    "join Mkt_Seg new_ams on UPPER(new_ams.Mkt_Seg_Code) = UPPER(ycr.Analytical_Market_Code) and ycr.Market_code in (:marketCodes)";

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_BY_MARKET_CODES_BY_DATES =
            "update rn set rn.Mkt_Seg_ID = new_ams.Mkt_Seg_ID " +
                    "from %s rn " +
                    "join Mkt_Seg ms on ms.Mkt_Seg_ID = rn.Mkt_Seg_ID " +
                    "join opera.Yield_Category_Rule ycr on UPPER(ycr.Market_Code) = UPPER(rn.Market_Code) and UPPER(ycr.Rate_Code) = UPPER(rn.Rate_Code) " +
                    "join Mkt_Seg new_ams on UPPER(new_ams.Mkt_Seg_Code) = UPPER(ycr.Analytical_Market_Code) and ycr.Market_code in (:marketCodes)"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_WITH_ALL_AMS_RULES =
            "update rn set rn.Mkt_Seg_ID = new_ams.Mkt_Seg_ID " +
                    "from %s rn " +
                    "join Mkt_Seg ms on ms.Mkt_Seg_ID = rn.Mkt_Seg_ID " +
                    "join opera.Yield_Category_Rule ycr on UPPER(ycr.Market_Code) = UPPER(rn.Market_Code) and UPPER(ycr.Rate_Code) = UPPER(rn.Rate_Code) " +
                    "join Mkt_Seg new_ams on UPPER(new_ams.Mkt_Seg_Code) = UPPER(ycr.Analytical_Market_Code) and new_ams.Property_ID = :propertyId"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_WITH_DEFAULT_AND_STRAIGHT_AMS_RULES =
            "update rn set rn.Mkt_Seg_ID = new_ams.Mkt_Seg_ID " +
                    "from %s rn " +
                    "join Analytical_Mkt_Seg ams on ams.Market_Code = rn.Market_Code and ams.Rate_Code_Type in (:rateCodeTypes) " +
                    "join Mkt_Seg new_ams on new_ams.Mkt_Seg_Code = ams.Mapped_Market_Code and new_ams.Property_ID = :propertyId " +
                    "where not exists (select 1 from opera.Yield_Category_Rule ycr where ycr.Market_Code = rn.Market_Code and ycr.Rate_Code = rn.Rate_Code)"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_WITH_DEFAULT_AND_STRAIGHT_AMS_RULES_BY_MARKET_CODES =
            "update rn set rn.Mkt_Seg_ID = new_ams.Mkt_Seg_ID " +
                    "from %s rn " +
                    "join Analytical_Mkt_Seg ams on UPPER(ams.Market_Code) = UPPER(rn.Market_Code) and ams.Rate_Code_Type in (:rateCodeTypes) " +
                    "join Mkt_Seg new_ams on UPPER(new_ams.Mkt_Seg_Code) = UPPER(ams.Mapped_Market_Code) and new_ams.Property_ID = :propertyId " +
                    "where ams.market_Code in (:marketCodes) and not exists (select 1 from opera.Yield_Category_Rule ycr where UPPER(ycr.Market_Code) = UPPER(rn.Market_Code) and UPPER(ycr.Rate_Code) = UPPER(rn.Rate_Code))";

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_WITH_DEFAULT_AND_STRAIGHT_AMS_RULES_BY_MARKET_CODES_BY_DATES =
            "update rn set rn.Mkt_Seg_ID = new_ams.Mkt_Seg_ID " +
                    "from %s rn " +
                    "join Analytical_Mkt_Seg ams on UPPER(ams.Market_Code) = UPPER(rn.Market_Code) and ams.Rate_Code_Type in (:rateCodeTypes) " +
                    "join Mkt_Seg new_ams on UPPER(new_ams.Mkt_Seg_Code) = UPPER(ams.Mapped_Market_Code) and new_ams.Property_ID = :propertyId " +
                    "where ams.market_Code in (:marketCodes) and not exists (select 1 from opera.Yield_Category_Rule ycr where UPPER(ycr.Market_Code) = UPPER(rn.Market_Code) and UPPER(ycr.Rate_Code) = UPPER(rn.Rate_Code))"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    /**
     * This query will find out the old market code using Mkt_Seg_ID and update the corresponding record in reservation table.
     */
    public static final String UPDATE_UNINITIALIZED_RESERVATION_NIGHT_MARKET_CODES_WITH_AMS_MARKET_CODES =
            "UPDATE rn SET rn.Market_Code = mapping.ori_mkt_code FROM %s rn " +
                    "join (SELECT * FROM dbo.ufn_get_generic_analyticalmarketsegid_originalmarketsegment_mapping()) mapping ON mapping.ana_mkt_id = rn.Mkt_Seg_ID " +
                    "WHERE rn.Market_Code IS NULL"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    /**
     * Update reservation table records having market codes and rate codes. This query will consider only non split market segments related records to update rate codes.
     */
    public static final String UPDATE_RESERVATION_NIGHT_RATE_CODES_WITH_NEW_PMS_RATE_CODES =
            "update rn set rn.Rate_Code = rateCodeMapping.New_Equivalent_Code from %s rn " +
                    "join PMS_Migration_Mapping mktSegMapping on mktSegMapping.Current_Code = rn.Market_Code and mktSegMapping.Code_Type IN(:marketCodeTypes) " +
                    "join PMS_Migration_Mapping rateCodeMapping on rateCodeMapping.Current_Code = rn.Rate_Code and rateCodeMapping.Code_Type IN(:rateCodeTypes) " +
                    "AND (mktSegMapping.Discontinued = 0) AND (rateCodeMapping.Discontinued = 0) " +
                    "AND (rateCodeMapping.Current_Code COLLATE SQL_Latin1_General_CP1_CS_AS <> rateCodeMapping.New_Equivalent_Code COLLATE SQL_Latin1_General_CP1_CS_AS) " +
                    "AND rateCodeMapping.New_Equivalent_Code <> '' AND mktSegMapping.Is_Primary_Code_For_One_To_Many_Splits IS NULL AND COALESCE(rateCodeMapping.Is_Primary_Code_For_One_To_Many_Splits,1) <> 0"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    /**
     * Update reservation table records having market codes and rate codes. This query will consider only non split market segments related records to update market codes.
     */
    public static final String UPDATE_RESERVATION_NIGHT_MARKET_CODES_WITH_NEW_PMS_MARKET_CODES =
            "update rn set rn.Market_Code = mktSegMapping.New_Equivalent_Code from %s rn " +
                    "join PMS_Migration_Mapping mktSegMapping on mktSegMapping.Current_Code = rn.Market_Code AND mktSegMapping.Code_Type IN(:marketCodeTypes) " +
                    "AND (mktSegMapping.Discontinued = 0) " +
                    "AND (mktSegMapping.Current_Code COLLATE SQL_Latin1_General_CP1_CS_AS <> mktSegMapping.New_Equivalent_Code COLLATE SQL_Latin1_General_CP1_CS_AS) " +
                    "AND mktSegMapping.New_Equivalent_Code <> '' AND mktSegMapping.Is_Primary_Code_For_One_To_Many_Splits IS NULL"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    /**
     * Update reservation table records having market codes and rate codes. This query will consider only split market segments related records to update market codes and rate codes.
     */
    public static final String UPDATE_RESERVATION_NIGHT_MARKET_CODES_AND_RATE_CODES_AGAINST_NON_BLANK_MARKET_CODES_AND_RATES_WITH_NEW_PMS_MAPPINGS =
            "UPDATE rn SET rn.Market_Code = mapping.New_Mkt_Code, rn.Rate_Code = mapping.New_rate_Code " +
                    "FROM %s rn INNER JOIN ( SELECT t.Current_Mkt_Code, t.Current_Rate_Code, t.New_Mkt_Code, t.New_Rate_Code " +
                    "FROM ( SELECT mktSegMapping.Current_Code AS Current_Mkt_Code,rateCodeMapping.Current_Code AS Current_Rate_Code, " +
                    "mktSegMapping.New_Equivalent_Code AS New_Mkt_Code, ycr.Rate_Code AS New_Rate_Code, " +
                    "ROW_NUMBER() OVER (PARTITION BY mktSegMapping.Current_Code, rateCodeMapping.Current_Code ORDER BY mktSegMapping.Is_Primary_Code_For_One_To_Many_Splits DESC) AS rowNum " +
                    " FROM PMS_Migration_Mapping AS mktSegMapping JOIN opera.Yield_Category_Rule AS ycr ON (mktSegMapping.New_Equivalent_Code = ycr.Market_Code " +
                    "AND mktSegMapping.Code_Type IN(:marketCodeTypes) AND mktSegMapping.Is_Primary_Code_For_One_To_Many_Splits is not null " +
                    "AND mktSegMapping.Discontinued = 0 " +
                    "AND mktSegMapping.Current_Code COLLATE SQL_Latin1_General_CP1_CS_AS <> mktSegMapping.New_Equivalent_Code COLLATE SQL_Latin1_General_CP1_CS_AS) " +
                    "JOIN PMS_Migration_Mapping AS rateCodeMapping ON (rateCodeMapping.New_Equivalent_Code = ycr.Rate_Code " +
                    "AND rateCodeMapping.Code_Type IN(:rateCodeTypes) AND rateCodeMapping.Discontinued = 0)) AS t WHERE t.rowNum = 1) AS mapping " +
                    "ON rn.Market_Code = mapping.Current_Mkt_Code AND rn.Rate_Code = mapping.Current_Rate_Code AND TRIM(rn.Rate_Code) <> ''"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    /**
     * This query will update market segment ids from reservation table with AMS table rules with rate code type = EQUALS. The 'DEFAULT' rate code type related records will
     * be stored in AMS as 'EQUALS' only.
     */
    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_WITH_EQUALS_AMS_RULES_FOR_MS_RECODING =
            "UPDATE rn SET rn.Mkt_Seg_ID = ams_mapping.Mkt_Seg_ID FROM %s rn JOIN" +
                    " (SELECT DISTINCT ams.Mapped_Market_Code, ams.Market_Code,ams.Rate_Code, ms.Mkt_Seg_ID" +
                    " FROM Analytical_Mkt_Seg ams" +
                    " JOIN Mkt_Seg ms ON (ams.Mapped_Market_Code COLLATE SQL_Latin1_General_CP1_CS_AS = ms.Mkt_Seg_Code COLLATE SQL_Latin1_General_CP1_CS_AS)" +
                    " AND ams.Rate_Code_Type = :amsRateCodeType and ms.Property_Id <> 1)" +
                    " AS ams_mapping ON rn.Market_Code = ams_mapping.Market_Code and rn.Rate_Code = ams_mapping.Rate_Code"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    /* This query will update market segment ids from reservation table with AMS table rules with rate code type = ALL. */

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_WITH_ALL_AMS_RULES_FOR_MS_RECODING =
            "UPDATE rn SET rn.Mkt_Seg_ID = ms.Mkt_Seg_ID " +
                    " FROM %s rn " +
                    " JOIN Mkt_Seg ms ON rn.Market_Code = ms.Mkt_Seg_Code" +
                    " JOIN PMS_Migration_Mapping mkt_code_mapping ON mkt_code_mapping.New_Equivalent_Code = ms.Mkt_Seg_Code AND mkt_code_mapping.Code_Type IN(:marketCodeTypes)" +
                    " AND ms.Property_ID <> 1" +
                    AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MKT_SEG_IDS_WITH_TRANSIENT_TO_GROUP_SHIFT_FOR_MS_RECODING =
            "UPDATE rn SET rn.Mkt_Seg_ID = ms.Mkt_Seg_ID " +
                    " FROM %s rn " +
                    " JOIN MS_Recoding_Business_Type_Shift bts ON bts.Market_Segment_Name = rn.Market_Code AND bts.Previous_Business_Type = 'Transient' " +
                    " JOIN Mkt_seg ms ON ms.Mkt_Seg_Code = bts.Market_Segment_Name " +
                    " AND ms.Property_ID <> 1 AND rn.Mkt_Seg_ID <> ms.Mkt_Seg_ID"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MARKET_CODES_FOR_SPLIT_STRAIGHT_MARKET_SEGMENTS_WITH_NEW_PMS_MAPPINGS =
            "UPDATE rn SET rn.Market_Code = mktSegMapping.New_Equivalent_Code " +
                    " FROM %s rn " +
                    " JOIN PMS_Migration_Mapping mktSegMapping on mktSegMapping.Current_Code = rn.Market_Code AND mktSegMapping.Code_Type IN(:marketCodeTypes) " +
                    " AND (mktSegMapping.Discontinued = 0) AND mktSegMapping.New_Equivalent_Code <> '' AND COALESCE(mktSegMapping.Is_Primary_Code_For_One_To_Many_Splits,1) <> 0" +
                    " AND (mktSegMapping.Current_Code COLLATE SQL_Latin1_General_CP1_CS_AS <> mktSegMapping.New_Equivalent_Code COLLATE SQL_Latin1_General_CP1_CS_AS) "
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_RATE_CODES_FOR_SPLIT_STRAIGHT_MARKET_SEGMENTS_WITH_NEW_PMS_MAPPINGS =
            "UPDATE rn SET rn.Rate_Code = rateCodeMapping.New_Equivalent_Code " +
                    " FROM %s rn " +
                    " JOIN PMS_Migration_Mapping mktSegMapping on mktSegMapping.Current_Code = rn.Market_Code and mktSegMapping.Code_Type IN(:marketCodeTypes) " +
                    " JOIN PMS_Migration_Mapping rateCodeMapping on rateCodeMapping.Current_Code = rn.Rate_Code and rateCodeMapping.Code_Type IN(:rateCodeTypes) " +
                    " AND (mktSegMapping.Discontinued = 0) AND (rateCodeMapping.Discontinued = 0) AND rateCodeMapping.New_Equivalent_Code <> '' " +
                    " AND (rateCodeMapping.Current_Code COLLATE SQL_Latin1_General_CP1_CS_AS <> rateCodeMapping.New_Equivalent_Code COLLATE SQL_Latin1_General_CP1_CS_AS) " +
                    " AND COALESCE(mktSegMapping.Is_Primary_Code_For_One_To_Many_Splits,1) <> 0 AND COALESCE(rateCodeMapping.Is_Primary_Code_For_One_To_Many_Splits,1) <> 0"
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_RESERVATION_NIGHT_MARKET_CODES_FOR_RATE_CODE_SHIFT_ASSOCIATIONS =
            "UPDATE " +
                    "   rn SET rn.Market_Code = rcShift.New_Market_Segment " +
                    "FROM " +
                    "   %s rn " +
                    "INNER JOIN " +
                    "   MS_Recoding_Rate_Code_Shift as rcShift " +
                    "ON " +
                    "   rcShift.Rate_Code = rn.Rate_Code " +
                    "   and rn.Market_Code = rcShift.Previous_Market_Segment "
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_MARKET_CODE_BY_NEW_FOR_GIVEN_MARKET_CODE =
            "UPDATE " +
                    "   rn SET Market_Code = mapping.New_Equivalent_Code " +
                    "FROM " +
                    "   %s rn " +
                    "INNER JOIN " +
                    "   Tier_Market_Segment_Mapping mapping " +
                    "ON " +
                    "   rn.Market_Code = mapping.Current_Code " +
                    "   and mapping.Code_Type in ('MARKET_SEGMENT_NON_GROUP','MARKET_SEGMENT') " +
                    "WHERE " +
                    "   rn.Market_Code in (:marketCodes) "
                    + AND_OCCUPANCY_DATE_BETWEEN_CLAUSE;

    public static final String UPDATE_MARKET_SEGMENT_ID_FOR_SHARED_RATE_CODE_BETWEEN_TIER_MS =
            "UPDATE " +
                    "   rn SET Mkt_Seg_ID = ms.Mkt_Seg_ID " +
                    "FROM " +
                    "   %s rn " +
                    "INNER JOIN " +
                    "   MS_Recoding_Rate_Code_Shift_Tier as tier " +
                    "ON " +
                    "   rn.Market_Code = tier.Market_Code and rn.Rate_Code = tier.Rate_Code " +
                    "INNER JOIN " +
                    "   Mkt_Seg as ms " +
                    "ON " +
                    "   tier.Preferred_Mapped_Market_Code = ms.Mkt_Seg_Code " +
                    "WHERE " +
                    "rn.occupancy_dt between :startDate and :endDate ";

    public static final String ADD_DUMMY_RECORD_FOR_EMPTY_DATABASE = "INSERT [dbo].[Reservation_Night] ( " +
            "[File_Metadata_ID], [Property_ID], [Reservation_Identifier], [Individual_Status], [Arrival_DT], " +
            "[Departure_DT], [Booking_DT], [Cancellation_DT], [Booked_Accom_Type_Code], [Accom_Type_ID], " +
            "[Mkt_Seg_ID], [Room_Revenue], [Food_Revenue], [Beverage_Revenue], [Telecom_Revenue], [Other_Revenue]" +
            ", [Total_Revenue], [Source_Booking], [Nationality], [Rate_Code], [Rate_Value], [Room_Number]," +
            " [Booking_type], [Number_Children], [Number_Adults], [CreateDate_DTTM], [Confirmation_No], [Channel], " +
            "[Booking_TM], [Occupancy_DT], [Persistent_Key], [Analytics_Booking_Dt], [Inv_Block_Code], [Market_Code]," +
            " [Gross_Rate_Value], [Gross_Room_Revenue], [Total_Acquisition_Cost], [Room_Revenue_Acquisition_Cost], " +
            "[Net_Revenue], [Net_Rate]) VALUES (:fileMetadataId, :propertyId , N'123456789', N'SS', CAST" +
            "(N'2022-01-01' AS Date)," +
            " CAST(N'2022-01-02' AS Date), CAST(N'2022-01-01' AS Date), CAST(N'1900-01-01' AS Date), :accomTypeCode," +
            " :accomTypeID, 2, CAST(0.00000 AS Numeric(19, 5)), CAST(0.00000 AS Numeric(19, 5)), CAST(0.00000 AS " +
            "Numeric(19, 5))," +
            " CAST(0.00000 AS Numeric(19, 5)), CAST(0.00000 AS Numeric(19, 5)), CAST(0.00000 AS Numeric(19, 5)), " +
            "N'', N'', N'SHHQO1', CAST(0.00000 AS Numeric(19, 5)), N'127', N'IN', CAST(20 AS Numeric(18, 0)), " +
            "CAST(40 AS Numeric(18, 0)), CAST(N'2019-06-25T07:59:09.920' AS DateTime), NULL, NULL, NULL, " +
            "CAST(N'2022-01-01' AS Date), N'7264078401,2011-08-11', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, " +
            "NULL)";

    public static final String ADD_DUMMY_RECORD_FOR_MKT_SEG_CODE_RATE_CODE = "INSERT INTO [dbo].[Reservation_Night]\n" +
            "VALUES \n" +
            "    (:fileMetadataId, :propertyId, :resId, N'SS', \n" +
            "    CAST(N'2022-01-01' AS Date), CAST(N'2022-01-02' AS Date), CAST(N'2022-01-01' AS Date), CAST(N'1900-01-01' AS Date),\n" +
            "    :accomTypeCode, :accomTypeID, (select mkt_seg_id from mkt_seg where Mkt_Seg_Code = :mktSegCode), \n" +
            "    CAST(0.00000 AS Numeric(19, 5)), CAST(0.00000 AS Numeric(19, 5)), CAST(0.00000 AS Numeric(19, 5)),\n" +
            "    CAST(0.00000 AS Numeric(19, 5)), CAST(0.00000 AS Numeric(19, 5)), CAST(0.00000 AS Numeric(19, 5)),\n" +
            "    Null, Null, :rateCode, \n" +
            "    CAST(0.00000 AS Numeric(19, 5)), N'127', Null, \n" +
            "    CAST(0 AS Numeric(18, 0)), CAST(0 AS Numeric(18, 0)), CAST(N'2019-06-25T07:59:09.920' AS DateTime), \n" +
            "    NULL, NULL, NULL, CAST(N'2022-01-01' AS Date), NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, CAST(N'2019-06-25T07:59:09.920' AS DateTime));";


    public static final String DELETE_FUTURE_ARRIVAL_RESERVATIONS = "DELETE\n" +
            "FROM reservation_night\n" +
            "WHERE arrival_DT > :migrationDate\n" +
            "  AND individual_status NOT IN ('XX',\n" +
            "                                'NS',\n" +
            "                                'NO SHOW',\n" +
            "                                'NO_SHOW',\n" +
            "                                'CANCELLED');\n";
    public static final String UPDATE_ONGOING_RESERVATION_DEPARTURE_DT =
            "UPDATE reservation_night\n" +
                    "SET departure_DT = :migrationDate\n" +
                    "WHERE departure_DT > :migrationDate\n" +
                    "  AND individual_status NOT IN ('XX',\n" +
                    "                                'NS',\n" +
                    "                                'NO SHOW',\n" +
                    "                                'NO_SHOW',\n" +
                    "                                'CANCELLED');\n";
    public static final String DELETE_FUTURE_OCCUPANCY_INHOUSE_RESERVATIONS =
            "DELETE\n" +
                    "FROM reservation_night\n" +
                    "WHERE occupancy_DT >= :migrationDate " +
                    "  AND individual_status NOT IN ('XX',\n" +
                    "                                'NS',\n" +
                    "                                'NO SHOW',\n" +
                    "                                'NO_SHOW',\n" +
                    "                                'CANCELLED');";

    public static final String UPDATE_IPP_TIER_MARKET_SEGMENT_FOR_VIRTUAL_PROPERTY =
            " UPDATE rn\n" +
            " SET rn.Mkt_Seg_ID = ms_new.Mkt_Seg_ID\n" +
            " FROM %s rn\n" +
            " JOIN Mkt_Seg ms_old ON rn.Mkt_Seg_ID = ms_old.Mkt_Seg_ID\n" +
            " JOIN Mkt_Seg ms_new ON ms_new.Mkt_Seg_Code = CONCAT(SUBSTRING(rn.Rate_Code, 1, CHARINDEX('_', rn.Rate_Code)), ms_old.Mkt_Seg_Code)\n" +
            " WHERE ms_old.Mkt_Seg_Code in ('Tier1', 'Tier2', 'Tier3')";

    private static final Logger LOGGER = Logger.getLogger(ReservationNight.class);
    static final BigDecimal MAX_REVENUE_LIMIT = BigDecimal.valueOf(1e14 - 1);
    static final BigDecimal MIN_REVENUE_LIMIT = BigDecimal.valueOf(-1e14 + 1);
    private static final int GROUP_CODE_LENGTH_LIMIT = 50;
    public static final String GET_STANDARD_HONORS_RATE_CODE = "com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.GET_STANDARD_HONORS_RATE_CODE";
    private Integer id;
    private String reservationIdentifier;
    private String individualStatus;
    private Date arrivalDate;
    private Date departureDate;
    private Date bookingDate;
    private Date cancellationDate;
    private Date occupancyDate;
    private String bookedAccomTypeCode;
    private Integer accomTypeId;
    private Integer marketSegId;
    private BigDecimal roomRevenue;
    private BigDecimal foodRevenue;
    private BigDecimal beverageRevenue;
    private BigDecimal telecomRevenue;
    private BigDecimal otherRevenue;
    private BigDecimal totalRevenue;
    private String sourceBooking;
    private String nationality;
    private String rateCode;
    private BigDecimal rateValue;
    private String roomNumber;
    private String bookingType;
    private Integer numberChildren;
    private Integer numberAdults;
    private String channel;
    private String confirmationNo;
    private Date bookingTm;
    private Date createDate;
    private List<String> sharers;
    private List<String> previousSharers;
    private String persistentKey;
    private String invBlockCode;
    private String marketCode;
    private Date analyticsBookingDt;
    private BigDecimal grossRateValue;
    private BigDecimal grossRoomRevenue;
    private BigDecimal totalAcquisitionCost;
    private BigDecimal roomRevenueAcquisitionCost;
    private BigDecimal netRevenue;
    private BigDecimal netRate;
    private transient String invTypeCode;
    private transient List<Map<String, Object>> rates;
    private transient String analyticalMarketSegmentCode;
    private transient Map<Date, RoomStayRevenue> revenueByOccupancyDates;
    private transient Map<Date, RoomStayRevenue> stayRevenueByOccupancyDates;
    public static final String GET_RESERVATION_ID_MKT_SEG_ACCOM_ARRIVAL_DT_DEPARTURE_DT = "select distinct " +
            "reservation_identifier, mkt_seg_id, accom_type_id, " +
            "arrival_DT, departure_DT from reservation_night where file_metadata_id = :fileMetadataId\n" +
            "order by 1,2,3,4";

    public ReservationNight() {

    }

    public ReservationNight(IndividualTransactions transaction, Date occupancyDate) {
        super();
        int los = getLOS(transaction);
        this.occupancyDate = occupancyDate;
        setFileMetadataId(transaction.getFileMetadataId());
        setPropertyId(transaction.getPropertyId());
        this.reservationIdentifier = transaction.getReservationIdentifier();
        this.individualStatus = transaction.getIndividualStatus();
        this.arrivalDate = transaction.getArrivalDate();
        this.departureDate = transaction.getDepartureDate();
        this.bookingDate = transaction.getBookingDate();
        this.cancellationDate = transaction.getCancellationDate();
        this.bookedAccomTypeCode = transaction.getBookedAccomTypeCode();
        this.accomTypeId = transaction.getAccomTypeId();
        this.marketSegId = transaction.getMarketSegId();
        this.roomRevenue = allocateRevenue(transaction.getRoomRevenue(), los);
        this.foodRevenue = allocateRevenue(transaction.getFoodRevenue(), los);
        this.beverageRevenue = allocateRevenue(transaction.getBeverageRevenue(), los);
        this.telecomRevenue = allocateRevenue(transaction.getTelecomRevenue(), los);
        this.otherRevenue = allocateRevenue(transaction.getOtherRevenue(), los);
        this.totalRevenue = allocateRevenue(transaction.getTotalRevenue(), los);
        this.sourceBooking = transaction.getSourceBooking();
        this.nationality = transaction.getNationality();
        this.rateCode = transaction.getRateCode();
        this.rateValue = transaction.getRateValue();
        this.roomNumber = transaction.getRoomNumber();
        this.bookingType = transaction.getBookingType();
        this.numberChildren = transaction.getNumberChildren();
        this.numberAdults = transaction.getNumberAdults();
        this.createDate = Optional.ofNullable(transaction.getCreateDate()).orElse(LocalDateTime.now().toDate());
        this.confirmationNo = transaction.getConfirmationNo();
        this.channel = transaction.getChannel();
        this.bookingTm = transaction.getBookingTm();
        this.sharers = transaction.getSharers();
        this.previousSharers = transaction.getPreviousSharers();
        this.persistentKey = transaction.getReservationIdentifier() + "," + DateUtil.formatDate(occupancyDate, "yyyy-MM-dd");
        this.invBlockCode = transaction.getInvBlockCode();
        this.marketCode = transaction.getMarketCode();
    }

    public ReservationNight(ReservationNight reservationNight, Date occupancyDate) {
        super();
        int los = getLOS(reservationNight.getArrivalDate(), reservationNight.getDepartureDate());
        this.occupancyDate = occupancyDate;
        setFileMetadataId(reservationNight.getFileMetadataId());
        setPropertyId(reservationNight.getPropertyId());
        this.reservationIdentifier = reservationNight.getReservationIdentifier();
        this.individualStatus = reservationNight.getIndividualStatus();
        this.arrivalDate = reservationNight.getArrivalDate();
        this.departureDate = reservationNight.getDepartureDate();
        this.bookingDate = reservationNight.getBookingDate();
        this.cancellationDate = reservationNight.getCancellationDate();
        this.bookedAccomTypeCode = reservationNight.getBookedAccomTypeCode();
        this.accomTypeId = reservationNight.getAccomTypeId();
        this.marketSegId = reservationNight.getMarketSegId();
        this.roomRevenue = allocateRevenue(reservationNight.getRoomRevenue(), los);
        this.foodRevenue = allocateRevenue(reservationNight.getFoodRevenue(), los);
        this.beverageRevenue = allocateRevenue(reservationNight.getBeverageRevenue(), los);
        this.telecomRevenue = allocateRevenue(reservationNight.getTelecomRevenue(), los);
        this.otherRevenue = allocateRevenue(reservationNight.getOtherRevenue(), los);
        this.totalRevenue = allocateRevenue(reservationNight.getTotalRevenue(), los);
        this.sourceBooking = reservationNight.getSourceBooking();
        this.nationality = reservationNight.getNationality();
        this.rateCode = reservationNight.getRateCode();
        this.rateValue = reservationNight.getRateValue();
        this.roomNumber = reservationNight.getRoomNumber();
        this.bookingType = reservationNight.getBookingType();
        this.numberChildren = reservationNight.getNumberChildren();
        this.numberAdults = reservationNight.getNumberAdults();
        this.createDate = Optional.ofNullable(reservationNight.getCreateDate()).orElse(LocalDateTime.now().toDate());
        this.confirmationNo = reservationNight.getConfirmationNo();
        this.channel = reservationNight.getChannel();
        this.bookingTm = reservationNight.getBookingTm();
        this.sharers = reservationNight.getSharers();
        this.previousSharers = reservationNight.getPreviousSharers();
        this.persistentKey = reservationNight.getReservationIdentifier() + "," + DateUtil.formatDate(occupancyDate, "yyyy-MM-dd");
        this.invBlockCode = reservationNight.getInvBlockCode();
        this.marketCode = reservationNight.getMarketCode();
        this.invTypeCode = reservationNight.getInvTypeCode();
        this.analyticalMarketSegmentCode = reservationNight.getAnalyticalMarketSegmentCode();
    }

    protected int getLOS(IndividualTransactions transaction) {
        return getLOS(transaction.getArrivalDate(), transaction.getDepartureDate());
    }

    private int getLOS(Date arrivalDate, Date departureDate) {
        LocalDate arrDate = new LocalDate(arrivalDate);
        LocalDate depDate = new LocalDate(departureDate);
        int los = Days.daysBetween(arrDate, depDate).getDays();
        return Math.max(los, 1);
    }

    protected BigDecimal allocateRevenue(BigDecimal revenue, int los) {
        if (los <= 1) {
            return revenue;
        }
        return revenue == null ? null : revenue.divide(BigDecimal.valueOf(los), 4, RoundingMode.HALF_EVEN);
    }

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "Individual_Trans_ID")
    public Integer getId() {
        return id;
    }

    @Override
    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "Reservation_Identifier")
    public String getReservationIdentifier() {
        return reservationIdentifier;
    }

    public ReservationNight setReservationIdentifier(String reservationIdentifier) {
        this.reservationIdentifier = reservationIdentifier;
        return this;
    }

    @Column(name = "Persistent_Key")
    public String getPersistentKey() {
        return persistentKey;
    }

    public ReservationNight setPersistentKey(String persistentKey) {
        this.persistentKey = persistentKey;
        return this;
    }

    @Column(name = "Individual_Status")
    public String getIndividualStatus() {
        return individualStatus;
    }

    public ReservationNight setIndividualStatus(String individualStatus) {
        this.individualStatus = individualStatus;
        return this;
    }

    @Column(name = "Arrival_DT")
    @Temporal(TemporalType.DATE)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getArrivalDate() {
        return arrivalDate;
    }

    public ReservationNight setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
        return this;
    }

    @Column(name = "Departure_DT")
    @Temporal(TemporalType.DATE)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getDepartureDate() {
        return departureDate;
    }

    public ReservationNight setDepartureDate(Date departureDate) {
        this.departureDate = departureDate;
        return this;
    }

    @Column(name = "Booking_DT")
    @Temporal(TemporalType.DATE)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getBookingDate() {
        return bookingDate;
    }

    public ReservationNight setBookingDate(Date bookingDate) {
        this.bookingDate = bookingDate;
        return this;
    }

    @Column(name = "Occupancy_DT")
    @Temporal(TemporalType.DATE)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getOccupancyDate() {
        return occupancyDate;
    }

    public ReservationNight setOccupancyDate(Date occupancyDate) {
        this.occupancyDate = occupancyDate;
        return this;
    }

    @Column(name = "Cancellation_DT")
    @Temporal(TemporalType.DATE)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getCancellationDate() {
        return cancellationDate;
    }

    public ReservationNight setCancellationDate(Date cancellationDate) {
        this.cancellationDate = cancellationDate;
        return this;
    }

    @Column(name = "Booked_Accom_Type_Code")
    public String getBookedAccomTypeCode() {
        return bookedAccomTypeCode;
    }

    public ReservationNight setBookedAccomTypeCode(String bookedAccomTypeCode) {
        this.bookedAccomTypeCode = bookedAccomTypeCode;
        return this;
    }

    @Column(name = "Accom_Type_ID")
    public Integer getAccomTypeId() {
        return accomTypeId;
    }

    public ReservationNight setAccomTypeId(Integer accomTypeId) {
        this.accomTypeId = accomTypeId;
        return this;
    }

    @Column(name = "Mkt_Seg_ID")
    public Integer getMarketSegId() {
        return marketSegId;
    }

    public ReservationNight setMarketSegId(Integer marketSegId) {
        this.marketSegId = marketSegId;
        return this;
    }

    @Column(name = "Room_Revenue", length = 19, precision = 5)
    public BigDecimal getRoomRevenue() {
        return roomRevenue;
    }

    public ReservationNight setRoomRevenue(BigDecimal roomRevenue) {
        this.roomRevenue = roomRevenue;
        return this;
    }

    @Column(name = "Food_Revenue")
    public BigDecimal getFoodRevenue() {
        return foodRevenue;
    }

    public ReservationNight setFoodRevenue(BigDecimal foodRevenue) {
        this.foodRevenue = foodRevenue;
        return this;
    }

    @Column(name = "Beverage_Revenue")
    public BigDecimal getBeverageRevenue() {
        return beverageRevenue;
    }

    public ReservationNight setBeverageRevenue(BigDecimal beverageRevenue) {
        this.beverageRevenue = beverageRevenue;
        return this;
    }

    @Column(name = "Telecom_Revenue")
    public BigDecimal getTelecomRevenue() {
        return telecomRevenue;
    }

    public ReservationNight setTelecomRevenue(BigDecimal telecomRevenue) {
        this.telecomRevenue = telecomRevenue;
        return this;
    }

    @Column(name = "Other_Revenue")
    public BigDecimal getOtherRevenue() {
        return otherRevenue;
    }

    public ReservationNight setOtherRevenue(BigDecimal otherRevenue) {
        this.otherRevenue = otherRevenue;
        return this;
    }

    @Column(name = "Total_Revenue", length = 19, precision = 5)
    public BigDecimal getTotalRevenue() {
        return totalRevenue;
    }

    public ReservationNight setTotalRevenue(BigDecimal totalRevenue) {
        this.totalRevenue = totalRevenue;
        return this;
    }

    @Column(name = "Source_Booking")
    public String getSourceBooking() {
        return sourceBooking;
    }

    public ReservationNight setSourceBooking(String sourceBooking) {
        this.sourceBooking = sourceBooking;
        return this;
    }

    @Column(name = "Nationality")
    public String getNationality() {
        return nationality;
    }

    public ReservationNight setNationality(String nationality) {
        this.nationality = nationality;
        return this;
    }

    @Column(name = "Rate_Code")
    public String getRateCode() {
        return rateCode;
    }

    public ReservationNight setRateCode(String rateCode) {
        this.rateCode = rateCode;
        return this;
    }

    @Column(name = "Rate_Value")
    public BigDecimal getRateValue() {
        return rateValue;
    }

    public ReservationNight setRateValue(BigDecimal rateValue) {
        this.rateValue = rateValue;
        return this;
    }

    @Column(name = "Room_Number")
    public String getRoomNumber() {
        return roomNumber;
    }

    public ReservationNight setRoomNumber(String roomNumber) {
        this.roomNumber = roomNumber;
        return this;
    }

    @Column(name = "Booking_type")
    public String getBookingType() {
        return bookingType;
    }

    public ReservationNight setBookingType(String bookingType) {
        this.bookingType = bookingType;
        return this;
    }

    @Column(name = "Number_Children", columnDefinition = "numeric")
    public Integer getNumberChildren() {
        return numberChildren;
    }

    public ReservationNight setNumberChildren(Integer numberChildren) {
        this.numberChildren = numberChildren;
        return this;
    }

    @Column(name = "Number_Adults", columnDefinition = "numeric")
    public Integer getNumberAdults() {
        return numberAdults;
    }

    public ReservationNight setNumberAdults(Integer numberAdults) {
        this.numberAdults = numberAdults;
        return this;
    }

    @Column(name = "Channel")
    public String getChannel() {
        return channel;
    }

    public ReservationNight setChannel(String channel) {
        this.channel = channel;
        return this;
    }

    @Column(name = "Confirmation_No")
    public String getConfirmationNo() {
        return confirmationNo;
    }

    public ReservationNight setConfirmationNo(String confirmationNo) {
        this.confirmationNo = confirmationNo;
        return this;
    }

    @Column(name = "Booking_TM")
    public Date getBookingTm() {
        return bookingTm;
    }

    public ReservationNight setBookingTm(Date bookingTm) {
        this.bookingTm = bookingTm;
        return this;
    }

    @Column(name = "CreateDate_DTTM")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getCreateDate() {
        return createDate;
    }

    public ReservationNight setCreateDate(Date createDate) {
        this.createDate = createDate == null ? LocalDateTime.now().toDate() : createDate;
        return this;
    }

    @Transient
    public List<String> getSharers() {
        return sharers;
    }

    public ReservationNight setSharers(List<String> sharers) {
        this.sharers = sharers;
        return this;
    }

    @Transient
    public List<String> getPreviousSharers() {
        return previousSharers;
    }

    public ReservationNight setPreviousSharers(List<String> previousSharers) {
        this.previousSharers = previousSharers;
        return this;
    }

    @Column(name = "Inv_Block_Code")
    public String getInvBlockCode() {
        return invBlockCode;
    }

    public ReservationNight setInvBlockCode(String invBlockCode) {
        if (StringUtils.length(invBlockCode) > GROUP_CODE_LENGTH_LIMIT) {
            LOGGER.warn("Very long InvBlockCode string : " + invBlockCode);
        }
        this.invBlockCode = StringUtils.left(invBlockCode, GROUP_CODE_LENGTH_LIMIT);
        return this;
    }

    @Column(name = "Market_Code")
    public String getMarketCode() {
        return marketCode;
    }

    public ReservationNight setMarketCode(String marketCode) {
        this.marketCode = marketCode;
        return this;
    }

    @Column(name = "Analytics_Booking_Dt")
    public Date getAnalyticsBookingDt() {
        return analyticsBookingDt;
    }

    public ReservationNight setAnalyticsBookingDt(final Date analyticsBookingDt) {
        this.analyticsBookingDt = analyticsBookingDt;
        return this;
    }

    @Column(name = "Gross_Rate_Value")
    public BigDecimal getGrossRateValue() {
        return grossRateValue;
    }

    public void setGrossRateValue(BigDecimal grossRateValue) {
        this.grossRateValue = grossRateValue;
    }

    @Column(name = "Gross_Room_Revenue")
    public BigDecimal getGrossRoomRevenue() {
        return grossRoomRevenue;
    }

    public void setGrossRoomRevenue(BigDecimal grossRoomRevenue) {
        this.grossRoomRevenue = grossRoomRevenue;
    }

    @Column(name = "Total_Acquisition_Cost")
    public BigDecimal getTotalAcquisitionCost() {
        return totalAcquisitionCost;
    }

    public void setTotalAcquisitionCost(BigDecimal totalAcquisitionCost) {
        this.totalAcquisitionCost = totalAcquisitionCost;
    }

    @Column(name = "Room_Revenue_Acquisition_Cost")
    public BigDecimal getRoomRevenueAcquisitionCost() {
        return roomRevenueAcquisitionCost;
    }

    public void setRoomRevenueAcquisitionCost(BigDecimal roomRevenueAcquisitionCost) {
        this.roomRevenueAcquisitionCost = roomRevenueAcquisitionCost;
    }

    @Column(name = "Net_Revenue")
    public BigDecimal getNetRevenue() {
        return netRevenue;
    }

    public void setNetRevenue(BigDecimal netRevenue) {
        this.netRevenue = netRevenue;
    }

    @Column(name = "Net_Rate")
    public BigDecimal getNetRate() {
        return netRate;
    }

    public void setNetRate(BigDecimal netRate) {
        this.netRate = netRate;
    }

    public ReservationNight setInvTypeCode(String invTypeCode) {
        this.invTypeCode = invTypeCode;
        return this;
    }

    @Transient
    public String getInvTypeCode() {
        return invTypeCode;
    }

    @Transient
    public List<Map<String, Object>> getRates() {
        return rates;
    }

    public ReservationNight setRates(List<Map<String, Object>> rates) {
        this.rates = rates;
        return this;
    }

    public ReservationNight addRates(List<Map<String, Object>> rates) {
        if (CollectionUtils.isEmpty(rates)) {
            return this;
        }
        if (this.rates == null) {
            this.rates = new ArrayList<>();
        }
        this.rates.addAll(rates);
        return this;
    }

    @Transient
    public RoomStayRevenue getRevenue(final Date occupancyDate) {
        return Stream.of(stayRevenueByOccupancyDates, revenueByOccupancyDates)
                .filter(MapUtils::isNotEmpty)
                .map(m -> m.get(occupancyDate))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    }

    public ReservationNight setRevenue(RoomStayRevenue revenue) {
        if (revenue == null) {
            return this;
        }
        long los = DateUtil.daysBetween(arrivalDate, departureDate);
        final RoomStayRevenue splitRoomRevenue = revenue.divide(los);
        this.revenueByOccupancyDates = DateUtil.datesBetweenInclusive(arrivalDate,
                DateUtil.addDaysToDate(departureDate, -1)).stream().collect(Collectors.toMap(Function.identity(),
                (d) -> splitRoomRevenue));
        return this;
    }

    @Transient
    public Map<Date, RoomStayRevenue> getRevenueByOccupancyDates() {
        return revenueByOccupancyDates;
    }

    public void setRevenueByOccupancyDates(final Map<Date, RoomStayRevenue> revenueByOccupancyDates) {
        this.revenueByOccupancyDates = revenueByOccupancyDates;
    }

    public ReservationNight addRevenueByOccupancyDates(final Map<Date, RoomStayRevenue> revenueByOccupancyDates) {
        if (revenueByOccupancyDates == null) {
            return this;
        }
        if (this.revenueByOccupancyDates == null) {
            this.revenueByOccupancyDates = new HashMap<>();
        }
        this.revenueByOccupancyDates.putAll(revenueByOccupancyDates);
        return this;
    }

    @Transient
    public String getAnalyticalMarketSegmentCode() {
        return analyticalMarketSegmentCode;
    }

    public ReservationNight setAnalyticalMarketSegmentCode(String analyticalMarketSegmentCode) {
        this.analyticalMarketSegmentCode = analyticalMarketSegmentCode;
        return this;
    }

    @Transient
    public Map<Date, RoomStayRevenue> getStayRevenueByOccupancyDates() {
        return stayRevenueByOccupancyDates;
    }

    public ReservationNight setStayRevenueByOccupancyDates(final Map<Date, RoomStayRevenue> stayRevenueByOccupancyDates) {
        this.stayRevenueByOccupancyDates = stayRevenueByOccupancyDates;
        return this;
    }

    public static final class ReservationNightFoodRevenueListener {
        @PreUpdate
        @PrePersist
        public void setFoodRevenue(ReservationNight reservationNight) {
            BigDecimal limitedRev = reservationNight.getFoodRevenue() == null ?
                    BigDecimal.ZERO :
                    BigDecimalUtil.max(MIN_REVENUE_LIMIT, BigDecimalUtil.min(reservationNight.getFoodRevenue(), MAX_REVENUE_LIMIT));
            reservationNight.setFoodRevenue(limitedRev);
        }
    }

    public static final class ReservationNightBeverageRevenueListener {
        @PreUpdate
        @PrePersist
        public void setBeverageRevenue(ReservationNight reservationNight) {
            BigDecimal limitedRev = reservationNight.getBeverageRevenue() == null ?
                    BigDecimal.ZERO :
                    BigDecimalUtil.max(MIN_REVENUE_LIMIT, BigDecimalUtil.min(reservationNight.getBeverageRevenue(), MAX_REVENUE_LIMIT));
            reservationNight.setBeverageRevenue(limitedRev);
        }
    }

    public static final class ReservationNightOtherRevenueListener {
        @PreUpdate
        @PrePersist
        public void setOtherRevenue(ReservationNight reservationNight) {
            BigDecimal limitedRev = reservationNight.getOtherRevenue() == null ?
                    BigDecimal.ZERO :
                    BigDecimalUtil.max(MIN_REVENUE_LIMIT, BigDecimalUtil.min(reservationNight.getOtherRevenue(), MAX_REVENUE_LIMIT));
            reservationNight.setOtherRevenue(limitedRev);
        }
    }

    public static final class ReservationNightTotalRevenueListener {
        @PreUpdate
        @PrePersist
        public void setTotalRevenue(ReservationNight reservationNight) {
            if (reservationNight.getTotalRevenue() == null) {
                reservationNight.setTotalRevenue(BigDecimal.ZERO);
            } else {
                var limitedRev = BigDecimalUtil.max(MIN_REVENUE_LIMIT, BigDecimalUtil.min(reservationNight.getTotalRevenue(), MAX_REVENUE_LIMIT));
                reservationNight.setTotalRevenue(limitedRev);
            }
        }
    }

    public static final class ReservationNightRoomRevenueListener {
        @PreUpdate
        @PrePersist
        public void setRoomRevenue(ReservationNight reservationNight) {
            if (reservationNight.getRoomRevenue() == null) {
                reservationNight.setRoomRevenue(BigDecimal.ZERO);
            } else {
                var limitedRev = BigDecimalUtil.max(MIN_REVENUE_LIMIT, BigDecimalUtil.min(reservationNight.getRoomRevenue(), MAX_REVENUE_LIMIT));
                reservationNight.setRoomRevenue(limitedRev);
            }
        }
    }

    public static final class ReservationNightTelecomRevenueListener {
        @PreUpdate
        @PrePersist
        public void setTelecomRevenue(ReservationNight reservationNight) {
            if (reservationNight.getTelecomRevenue() == null) {
                reservationNight.setTelecomRevenue(BigDecimal.ZERO);
            }
        }
    }

    public static final class ReservationNightRateValueListener {
        @PreUpdate
        @PrePersist
        public void setRateValue(ReservationNight reservationNight) {
            if (reservationNight.getRateValue() != null) {
                var limitedRev = BigDecimalUtil.max(MIN_REVENUE_LIMIT, BigDecimalUtil.min(reservationNight.getRateValue(), MAX_REVENUE_LIMIT));
                reservationNight.setRateValue(limitedRev);
            }
        }
    }
}
