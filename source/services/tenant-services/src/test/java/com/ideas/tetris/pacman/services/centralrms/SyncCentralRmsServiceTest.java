package com.ideas.tetris.pacman.services.centralrms;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.centralrms.dto.CentralRMSJobStatusDto;
import com.ideas.tetris.pacman.services.centralrms.dto.CentralRMSJobStatusDtoV2;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.retryservice.RetryService;
import com.ideas.tetris.pacman.services.specialevent.entity.SpecialEventType;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SyncCentralRmsServiceTest {

    @Mock
    @TenantCrudServiceBean.Qualifier
    CrudService tenCrudService;

    @Mock
    private RetryService retryService;

    @Mock
    private RestTemplate centralRMSPricingConfigRestTemplate;

    @Mock
    private RestTemplate centralRMSCommonAlertsRestTemplate;

    @InjectMocks
    private SyncCentralRmsService service;

    @BeforeEach
    void setup() {
        PacmanWorkContextHelper.setWorkContext(new WorkContextType());
        PacmanWorkContextHelper.setClientCode("CLIENT");
        PacmanWorkContextHelper.setPropertyCode("PROPERTY");
        PacmanWorkContextHelper.setUserId("11403");
        PacmanWorkContextHelper.setPropertyId(1000);
    }

    @AfterEach
    void teardown() {
        PacmanThreadLocalContextHolder.cleanupThread();
    }

    @Test
    void syncTransientPricingConfigurationData_AddOrUpdate() {
        List<TransientPricingBaseAccomType> pricingBaseAccomTypes = List.of(new TransientPricingBaseAccomType());
        pricingBaseAccomTypes.forEach(config -> config.setProductID(1));
        doNothing().when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.syncTransientPricingConfigurationData(pricingBaseAccomTypes.get(0), false);

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).exchange(eq(SystemConfig.getCentralRMSPricingConfigBaseUri() + "/v2/sync"), eq(HttpMethod.POST), httpEntityCaptor.capture(), eq(Void.class));

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<Map<String, PricingBaseAccomType>> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
        assertEquals(pricingBaseAccomTypes, entity.getBody());
    }

    @Test
    void syncTransientPricingConfigurationData_forPriceExcluded() {
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomClass accomClass = new AccomClass();
        accomClass.setId(2);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);

        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setPriceExcluded(true);

        when(tenCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PRICE_EXCLUDED)).thenReturn(List.of(pricingAccomClass));

        List<PricingBaseAccomType> pricingBaseAccomTypes = List.of(new TransientPricingBaseAccomType());
        pricingBaseAccomTypes.forEach(config -> config.setAccomType(accomType));
        pricingBaseAccomTypes.forEach(config -> config.setProductID(1));

        service.syncTransientPricingConfigurationData(pricingBaseAccomTypes.get(0), false);

        verifyNoInteractions(retryService);
    }

    @Test
    void syncTransientPricingConfigurationData_nonCpProperty() {
        service.syncTransientPricingConfigurationData(new ArrayList<>(), false);
        verifyNoInteractions(retryService);
    }

    @Test
    void syncTransientPricingConfigurationData_Delete() {
        PacmanWorkContextHelper.setClientCode("CLIENT");
        PacmanWorkContextHelper.setPropertyCode("123");

        List<TransientPricingBaseAccomType> pricingBaseAccomTypes = List.of(new TransientPricingBaseAccomType());
        pricingBaseAccomTypes.forEach(config -> config.setProductID(1));
        doNothing().when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.syncTransientPricingConfigurationData(pricingBaseAccomTypes.get(0), false);

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).exchange(eq(SystemConfig.getCentralRMSPricingConfigBaseUri() + "/v2/sync"), eq(HttpMethod.POST), httpEntityCaptor.capture(), eq(Void.class));

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<Map<String, PricingBaseAccomType>> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
        assertEquals(pricingBaseAccomTypes, entity.getBody());
    }

    @Test
    void syncPricingConfigurationData_ExhaustRetries() {
        PacmanWorkContextHelper.setClientCode("CLIENT");
        PacmanWorkContextHelper.setPropertyCode("123");

        List<PricingBaseAccomType> pricingBaseAccomTypes = List.of(new TransientPricingBaseAccomType());
        pricingBaseAccomTypes.forEach(config -> config.setProductID(1));
        doThrow(new TetrisException("anything")).when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.syncTransientPricingConfigurationData(pricingBaseAccomTypes, false);

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).exchange(eq(SystemConfig.getCentralRMSPricingConfigBaseUri() + "/v2/sync"), eq(HttpMethod.POST), httpEntityCaptor.capture(), eq(Void.class));

        // Assert the request
        HttpEntity<Map<String, List<PricingBaseAccomType>>> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientcode"));
        assertEquals(pricingBaseAccomTypes, entity.getBody());

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));
    }

    @Test
    void syncTransientPricingConfigurationData_Truncate() {
        PacmanWorkContextHelper.setClientCode("CLIENT");
        PacmanWorkContextHelper.setPropertyCode("123");

        doNothing().when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.truncateTransientPricingConfigurationData();

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).exchange(eq(SystemConfig.getCentralRMSPricingConfigBaseUri() + "/v1/truncate"), eq(HttpMethod.DELETE), httpEntityCaptor.capture(), eq(Void.class));

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<Map<String, PricingBaseAccomType>> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
        assertNull(entity.getBody());
    }

    @Test
    void syncPricingConfigurationData_Truncate_ExhaustRetries() {
        PacmanWorkContextHelper.setClientCode("CLIENT");
        PacmanWorkContextHelper.setPropertyCode("123");

        doThrow(new TetrisException("anything")).when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.truncateTransientPricingConfigurationData();

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).exchange(eq(SystemConfig.getCentralRMSPricingConfigBaseUri() + "/v1/truncate"), eq(HttpMethod.DELETE), httpEntityCaptor.capture(), eq(Void.class));

        // Assert the request
        HttpEntity<Map<String, Void>> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientcode"));
        assertNull(entity.getBody());

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));
    }

    @Test
    void syncPricingConfigurationData_Ignore_IndependentProducts() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = List.of(
                new TransientPricingBaseAccomType(),
                new TransientPricingBaseAccomType(),
                new TransientPricingBaseAccomType(),
                new TransientPricingBaseAccomType()
        );
        pricingBaseAccomTypes.get(0).setProductID(1);
        pricingBaseAccomTypes.get(1).setProductID(2);
        pricingBaseAccomTypes.get(2).setProductID(3);
        pricingBaseAccomTypes.get(3).setProductID(1);
        List<PricingBaseAccomType> barProducts = pricingBaseAccomTypes.stream()
                .filter(config -> config.getProductID() == 1)
                .collect(Collectors.toList());

        doNothing().when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.syncTransientPricingConfigurationData(pricingBaseAccomTypes, false);

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).exchange(eq(SystemConfig.getCentralRMSPricingConfigBaseUri() + "/v2/sync"), eq(HttpMethod.POST), httpEntityCaptor.capture(), eq(Void.class));

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<Map<String, PricingBaseAccomType>> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
        assertEquals(barProducts, entity.getBody());
    }

    @Test
    void updateFloorCeilingJobStatus() {
        doNothing().when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.updateCentralRMSJobStatus("CLIENT", "PROPERTY", "BATCH", 1L, "STATUS", "MESSAGE");

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).postForObject(
                eq(SystemConfig.getCentralRMSBaseUri() + "/job/v1/status/{batch-id}/{property-code}"),
                httpEntityCaptor.capture(),
                eq(Void.class),
                eq("BATCH"),
                eq("PROPERTY")
        );

        // Assert the request
        CentralRMSJobStatusDto expected = new CentralRMSJobStatusDto("BATCH", 1L, "CLIENT", "PROPERTY", "STATUS", "MESSAGE");
        assertEquals(expected, httpEntityCaptor.getValue().getBody());

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<CentralRMSJobStatusDto> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
    }

    @Test
    void updateFloorCeilingJobStatus_ExhaustRetries() {
        doThrow(new TetrisException("anything")).when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.updateCentralRMSJobStatus("CLIENT", "PROPERTY", "BATCH", 1L, "STATUS", "MESSAGE");

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).postForObject(
                eq(SystemConfig.getCentralRMSBaseUri() + "/job/v1/status/{batch-id}/{property-code}"),
                httpEntityCaptor.capture(),
                eq(Void.class),
                eq("BATCH"),
                eq("PROPERTY")
        );

        // Assert the request
        CentralRMSJobStatusDto expected = new CentralRMSJobStatusDto("BATCH", 1L, "CLIENT", "PROPERTY", "STATUS", "MESSAGE");
        assertEquals(expected, httpEntityCaptor.getValue().getBody());

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<CentralRMSJobStatusDto> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
    }

    @Test
    void updateCentralRMSJobStatusV2() {
        doNothing().when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.updateCentralRMSJobStatusV2("CLIENT", "PROPERTY", "PRODUCT", "BATCH", 1L, "STATUS", "MESSAGE");

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).postForObject(
                eq(SystemConfig.getCentralRMSBaseUri() + "/job/v2/status/{batch-id}/{property-code}/{product-name}"),
                httpEntityCaptor.capture(),
                eq(Void.class),
                eq("BATCH"),
                eq("PROPERTY"),
                eq("PRODUCT")
        );

        CentralRMSJobStatusDtoV2 expected = new CentralRMSJobStatusDtoV2("BATCH", 1L, "CLIENT", "PROPERTY", "PRODUCT", "STATUS", "MESSAGE");
        assertEquals(expected, httpEntityCaptor.getValue().getBody());

        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<CentralRMSJobStatusDto> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
    }

    @Test
    void updateCentralRMSJobStatusV2_ExhaustRetries() {
        doThrow(new TetrisException("anything")).when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.updateCentralRMSJobStatusV2("CLIENT", "PROPERTY", "PRODUCT", "BATCH", 1L, "STATUS", "MESSAGE");

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSPricingConfigRestTemplate).postForObject(
                eq(SystemConfig.getCentralRMSBaseUri() + "/job/v2/status/{batch-id}/{property-code}/{product-name}"),
                httpEntityCaptor.capture(),
                eq(Void.class),
                eq("BATCH"),
                eq("PROPERTY"),
                eq("PRODUCT")
        );

        CentralRMSJobStatusDtoV2 expected = new CentralRMSJobStatusDtoV2("BATCH", 1L, "CLIENT", "PROPERTY", "PRODUCT", "STATUS", "MESSAGE");
        assertEquals(expected, httpEntityCaptor.getValue().getBody());

        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<CentralRMSJobStatusDto> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
    }

    @Test
    void truncateAlertData() {
        PacmanWorkContextHelper.setClientCode("CLIENT");
        PacmanWorkContextHelper.setPropertyCode("123");

        doNothing().when(retryService).attempt(any(Runnable.class), eq(3), eq(Duration.ofMillis(100)), any(Function.class));

        service.truncateAlertsData();

        ArgumentCaptor<HttpEntity> httpEntityCaptor = ArgumentCaptor.forClass(HttpEntity.class);
        ArgumentCaptor<Runnable> restCallCaptor = ArgumentCaptor.forClass(Runnable.class);
        ArgumentCaptor<Function> canRetryCaptor = ArgumentCaptor.forClass(Function.class);

        verify(retryService).attempt(restCallCaptor.capture(), eq(3), eq(Duration.ofMillis(100)), canRetryCaptor.capture());

        // Assert the call
        Runnable restCallRunnable = restCallCaptor.getValue();
        restCallRunnable.run();
        verify(centralRMSCommonAlertsRestTemplate).exchange(eq(SystemConfig.getCentralRMSCommonAlertsBaseUri() + "/v1/truncate"), eq(HttpMethod.DELETE), httpEntityCaptor.capture(), eq(Void.class));

        // Assert the retry mechanism
        Function<Exception, Boolean> canRetry = canRetryCaptor.getValue();
        assertTrue(canRetry.apply(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR)));
        assertFalse(canRetry.apply(new HttpClientErrorException(HttpStatus.BAD_REQUEST)));
        assertFalse(canRetry.apply(new Exception("Not an Http Exception")));

        HttpEntity<Map<String, Void>> entity = httpEntityCaptor.getValue();
        assertTrue(entity.getHeaders().containsKey("clientCode"));
        assertEquals(1, entity.getHeaders().getOrEmpty("clientCode").size());
        assertEquals("CLIENT", entity.getHeaders().getFirst("clientCode"));
        assertNull(entity.getBody());
    }

    @Test
    void filterOutPriceExcludedRoomTypes() {
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomClass accomClass = new AccomClass();
        accomClass.setId(2);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);

        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setPriceExcluded(true);

        when(tenCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PRICE_EXCLUDED)).thenReturn(List.of(pricingAccomClass));

        List<PricingBaseAccomType> pricingBaseAccomTypes = List.of(new TransientPricingBaseAccomType());
        pricingBaseAccomTypes.forEach(config -> config.setAccomType(accomType));

        List<PricingBaseAccomType> retPricingBaseAccomTypes = service.filterOutPriceExcludedRoomTypes(pricingBaseAccomTypes);

        assertTrue(retPricingBaseAccomTypes.isEmpty());
    }

    @Test
    void filterOutPriceExcludedRoomTypes_multipleRoomTypesOneExcluded() {
        PricingAccomClass pricingAccomClass1 = new PricingAccomClass();
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        AccomType accomType1 = new AccomType();
        accomType1.setAccomClass(accomClass1);

        pricingAccomClass1.setAccomClass(accomClass1);
        pricingAccomClass1.setAccomType(accomType1);
        pricingAccomClass1.setPriceExcluded(true);

        PricingAccomClass pricingAccomClass2 = new PricingAccomClass();
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        AccomType accomType2 = new AccomType();
        accomType2.setAccomClass(accomClass2);

        pricingAccomClass2.setAccomClass(accomClass2);
        pricingAccomClass2.setAccomType(accomType2);
        pricingAccomClass2.setPriceExcluded(false);

        when(tenCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PRICE_EXCLUDED)).thenReturn(List.of(pricingAccomClass1));

        TransientPricingBaseAccomType transientPricingBaseAccomType1 = new TransientPricingBaseAccomType();
        transientPricingBaseAccomType1.setAccomType(accomType1);

        TransientPricingBaseAccomType transientPricingBaseAccomType2 = new TransientPricingBaseAccomType();
        transientPricingBaseAccomType2.setAccomType(accomType2);

        List<PricingBaseAccomType> retPricingBaseAccomTypes = service.filterOutPriceExcludedRoomTypes(List.of(transientPricingBaseAccomType1, transientPricingBaseAccomType2));

        assertEquals(1, retPricingBaseAccomTypes.size());
        assertSame(retPricingBaseAccomTypes.get(0).getAccomType(), accomType2);
    }

    @Test
    void filterOutPriceExcludedRoomTypes_noPriceExcludedRoomClasses() {
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        AccomClass accomClass = new AccomClass();
        accomClass.setId(2);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);

        pricingAccomClass.setAccomClass(accomClass);
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setPriceExcluded(false);

        when(tenCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PRICE_EXCLUDED)).thenReturn(new ArrayList<>());

        List<PricingBaseAccomType> pricingBaseAccomTypes = List.of(new TransientPricingBaseAccomType());
        pricingBaseAccomTypes.forEach(config -> config.setAccomType(accomType));

        List<PricingBaseAccomType> retPricingBaseAccomTypes = service.filterOutPriceExcludedRoomTypes(pricingBaseAccomTypes);

        assertEquals(1, retPricingBaseAccomTypes.size());
    }

    @Nested
    class SpecialEventTypes {

        @Test
        void postSpecialEventTypeChanges() {
            SpecialEventType specialEventType = new SpecialEventType();

            ArgumentCaptor<Runnable> runnable = ArgumentCaptor.forClass(Runnable.class);

            service.postSpecialEventTypeChanges(List.of(specialEventType));

            verify(retryService).attempt(
                    runnable.capture(),
                    eq(3),
                    eq(Duration.ofMillis(100)),
                    any(Function.class)
            );
        }

        @Test
        void postSpecialEventTypes() {
            SpecialEventType specialEventType = new SpecialEventType();

            when(tenCrudService.findByNamedQuery(anyString())).thenReturn(List.of(specialEventType));
            ArgumentCaptor<Runnable> runnable = ArgumentCaptor.forClass(Runnable.class);

            service.postSpecialEventTypes();

            verify(tenCrudService, times(1)).findByNamedQuery(SpecialEventType.ALL);
            verify(retryService).attempt(
                    runnable.capture(),
                    eq(3),
                    eq(Duration.ofMillis(100)),
                    any(Function.class)
            );
        }
    }
}
