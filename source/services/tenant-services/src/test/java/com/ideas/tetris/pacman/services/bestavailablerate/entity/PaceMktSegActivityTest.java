package com.ideas.tetris.pacman.services.bestavailablerate.entity;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

public class PaceMktSegActivityTest extends AbstractG3JupiterTest {

    private static final int GROUP = 1;
    private final int TRANSIENT = 2;
    private final LocalDate today = LocalDate.now();
    private static final String GET_INDEX_INFO = "SELECT\n" +
            " lower(a.name)  AS Index_Name,\n" +
            " OBJECT_NAME(a.object_id),\n" +
            " LOWER(COL_NAME(b.object_id,b.column_id)) AS Column_Name,\n" +
            " b.index_column_id,\n" +
            " b.key_ordinal,\n" +
            " b.is_included_column\n" +
            "FROM\n" +
            " sys.indexes AS a\n" +
            "INNER JOIN\n" +
            " sys.index_columns AS b\n" +
            "       ON a.object_id = b.object_id AND a.index_id = b.index_id\n" +
            "WHERE\n" +
            "        a.is_hypothetical = 0 AND\n" +
            " a.object_id = OBJECT_ID('dbo.pace_mkt_activity');";

    @BeforeEach
    public void setUp() {
        cleanPaceMktSegActivity(today.toString());
        cleanPaceMktSegActivity(today.minusDays(1).toString());
        createMarketSegmentDetails(createMarketSegment("MKT_SEG_1"), TRANSIENT);
        createMarketSegmentDetails(createMarketSegment("MKT_SEG_2"), TRANSIENT);
        createMarketSegmentDetails(createMarketSegment("MKT_SEG_3"), TRANSIENT);
        createMarketSegmentDetails(createMarketSegment("MKT_SEG_4"), GROUP);
        createMarketSegmentDetails(createMarketSegment("MKT_SEG_5"), GROUP);
        createMarketSegmentDetails(createMarketSegment("MKT_SEG_6"), GROUP);
    }

    @Test
    void verifyRebuildPaceMktActivtyRecreatesTheSameTable() {
        // assert columns are same
        final Map<String, Object[]> prevColumns = getColumnInfo();
        final Map<String, Map<String, List<Object[]>>> prevIndexInfo = getIndexInfo();

        final Integer prevCount = countPaceMktActivityRows();

        final List<Integer> marketSegIdsToDelete = tenantCrudService().findByNamedQuery(MktSeg.GET_MKT_SEG_IDS_FOR_DELETION);

        // Invoke the reconstruct
        String splitMsIds = marketSegIdsToDelete.stream()
                .map(Objects::toString).collect(Collectors.joining(","));

        tenantCrudService().executeUpdateByNativeQuery("exec dbo.usp_delete_discontinued_pace_mkt_activity :splitMsIds ",
                QueryParameter.with("splitMsIds", splitMsIds).parameters());

        final Integer newCount = countPaceMktActivityRows();
        assertEquals(prevCount, newCount, "count mismatch after reconstruction");
        assertEquals(prevColumns.keySet(), getColumnInfo().keySet());
        assertEquals(prevIndexInfo.keySet(), getIndexInfo().keySet());

    }

    private Integer countPaceMktActivityRows() {
        return tenantCrudService().findByNativeQuerySingleResult("Select count(*) from Pace_mkt_activity;", null);
    }

    private Map<String, Map<String, List<Object[]>>> getIndexInfo() {
        // assert indexes are same
        final List<Object> indexInfos = tenantCrudService().findByNativeQuery(GET_INDEX_INFO);
        return indexInfos.stream().map(obj -> (Object[]) obj).collect(groupingBy(objs -> objs[0].toString(),
                groupingBy(objects -> objects[2].toString())));
    }

    private Map<String, Object[]> getColumnInfo() {
        final List<Object> columnsData = tenantCrudService().findByNativeQuery("exec sp_columns" +
                " pace_mkt_activity");
        return columnsData.stream().map(obj -> (Object[]) obj).collect(Collectors.toMap(objs -> objs[3].toString().toLowerCase(), Function.identity()));
    }

    public void cleanPaceMktSegActivity(String occupancyDate) {
        int result = tenantCrudService().executeUpdateByNativeQuery("delete from PACE_Mkt_Activity where Occupancy_Dt = '" + occupancyDate + "'");
        assertTrue(result > 0);
    }

    public void createMarketSegmentDetails(Integer mkt_seg_1, final int businessType) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg_Details values(" + mkt_seg_1 + ", " + businessType + ", 3, 2, 1, 0, 0, 0, 0, null, 0, 2, 2, 0, 1, '2010-07-19 14:55:55.067', 0, 1, '2010-07-19 14:55:55.067', 1, 1.0)");
    }

    public Integer createMarketSegment(String marketSegmentName) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg ([Property_ID] ,[Mkt_Seg_Code]  ,[Mkt_Seg_Name]  ,[Mkt_Seg_Description] ,[Status_ID],[Last_Updated_DTTM],[Is_Editable],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID]) values(5,'" + marketSegmentName + "', '" + marketSegmentName + "', '" + marketSegmentName + "', 1, '2010-07-19 12:48:56.587', 1, 1, '2010-07-19 12:48:56.587', 1)");
        return getMarketSegmentIdByName(marketSegmentName);
    }

    public Integer getMarketSegmentIdByName(String marketSegmentName) {
        return (Integer) tenantCrudService().findByNativeQuerySingleResult("select Mkt_Seg_ID from Mkt_Seg where Mkt_Seg_Code = :marketSegmentName",
                QueryParameter.with("marketSegmentName", marketSegmentName).parameters());
    }

    @Test
    void countAllEntries() {
        assertTrue((Long) tenantCrudService().findByNamedQuerySingleResult(PaceMktSegActivity.GET_ALL_COUNT) > 0L);
    }

    @Test
    void countGetEntriesByMktSegIds() {
        assertTrue((Long) tenantCrudService().findByNamedQuerySingleResult(PaceMktSegActivity.GET_COUNT_BY_MKT_SEG_IDS, QueryParameter.with("mktSegIds", Arrays.asList(2, 12)).parameters()) > 0);
    }

    @Test
    public void shouldGiveOccupancyChangeForForecastGroupLevelWhenCurrentRoomSoldIsGreaterThanLastRoomSold() {
        createFileMetadataForBDE();
        //GIVEN
        Integer forecastGroupId = initializeMarketSegmentAndForecastGroup();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        //Last Room Sold
        createLastRoomSold(1);

        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_FORECAST_GROUP_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("forecastGroupId", forecastGroupId).parameters());
        Object[] row = results.get(0);

        //THEN
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("180"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("150"), row[6]);
        assertEquals(new BigDecimal("30"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForForecastGroupLevelWhenCurrentRoomSoldIsLessThanLastRoomSold() {
        createFileMetadataForBDE();
        //GIVEN
        Integer forecastGroupId = initializeMarketSegmentAndForecastGroup();
        //Current Room Sold
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_1", 50, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_2", 50, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_3", 50, 2);
        //Last Room Sold
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_1", 60, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_2", 60, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_3", 60, 1);

        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_FORECAST_GROUP_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("forecastGroupId", forecastGroupId).parameters());
        Object[] row = results.get(0);

        //THEN
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("150"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("180"), row[6]);
        assertEquals(new BigDecimal("-30"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForForecastGroupLevelWhenCurrentRoomSoldNotPresent() {
        createFileMetadataForBDE();
        //GIVEN
        Integer forecastGroupId = initializeMarketSegmentAndForecastGroup();
        //Last Room Sold
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_1", 60, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_2", 60, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_3", 60, 1);

        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_FORECAST_GROUP_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("forecastGroupId", forecastGroupId).parameters());
        Object[] row = results.get(0);

        //THEN
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("0"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("180"), row[6]);
        assertEquals(new BigDecimal("-180"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForForecastGroupLevelWhenLastRoomSoldNotPresent() {
        createFileMetadataForBDE();
        //GIVEN
        Integer forecastGroupId = initializeMarketSegmentAndForecastGroup();
        //Current Room Sold
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_1", 50, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_2", 50, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_3", 50, 2);

        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_FORECAST_GROUP_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("forecastGroupId", forecastGroupId).parameters());
        Object[] row = results.get(0);

        //THEN
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("150"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("0"), row[6]);
        assertEquals(new BigDecimal("150"), row[7]);
    }

    @Test
    public void mergePace() {
        FileMetadata fileMetadata = tenantCrudService().find(FileMetadata.class, 3);
        FileMetadata updatedFileMeta = getNewFileMetaData(fileMetadata);

        tenantCrudService().save(updatedFileMeta);
        assertFalse(fileMetadata.getId().equals(updatedFileMeta.getId()));

        Map<String, Object> params = QueryParameter.with("startDate", DateUtil.addDaysToDate(updatedFileMeta.getSnapshotDt(), -3))
                .and("endDate", DateUtil.addDaysToDate(updatedFileMeta.getSnapshotDt(), 3))
                .and("propertyId", PacmanWorkContextHelper.getPropertyId())
                .and("snapshotDt", updatedFileMeta.getSnapshotDt())
                .and("snapshotDtTm", updatedFileMeta.getSnapshotDtTm())
                .and("fileMetaDataId", updatedFileMeta.getId())
                .parameters();
        int resultCount = tenantCrudService().executeUpdateByNamedQuery(PaceMktSegActivity.MERGE_PACE, params);
        PaceMktSegActivity template = new PaceMktSegActivity();
        template.setFileMetadataId(updatedFileMeta.getId());
        Collection<PaceMktSegActivity> activitiesAfter = tenantCrudService().findByExample(template);
        assertEquals(18, activitiesAfter.size());
        for (PaceMktSegActivity paceEntry : activitiesAfter) {
            verifyAggregateValue(paceEntry);
        }
    }


    @Test
    public void shouldConsiderOnlySuccessfulProcessWhileGettingOccupancyChangeForForecastGroupLevel() {
        tenantCrudService().executeUpdateByNativeQuery("update File_Metadata set Process_Status_ID = 2, IsBDE = 1, Record_Type_ID = 3");
        //GIVEN
        Integer forecastGroupId = initializeMarketSegmentAndForecastGroup();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        //Last Room Sold
        createLastRoomSold(2);

        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_FORECAST_GROUP_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("forecastGroupId", forecastGroupId).parameters());

        //THEN
        assertEquals(0, results.size());
    }

    @Test
    public void shouldNotConsiderOtherThanBdeProcessWhileGettingOccupancyChangeForForecastGroupLevel() {
        tenantCrudService().executeUpdateByNativeQuery("update File_Metadata set Process_Status_ID = 13, IsBDE = 0, Record_Type_ID = 3");
        //GIVEN
        Integer forecastGroupId = initializeMarketSegmentAndForecastGroup();
        createCurrentRoomsSoldMarketActivity();

        //Last Room Sold
        createLastRoomSold(2);

        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_FORECAST_GROUP_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("forecastGroupId", forecastGroupId).parameters());

        //THEN
        assertEquals(0, results.size());
    }


    @Test
    public void insertZeroFillPaceMarketSegmentActivity() {
        tenantCrudService().executeUpdateByNativeQuery("update File_Metadata set Process_Status_ID = 13, IsBDE = 0, Record_Type_ID = 3");
        //GIVEN
        initializeMarketSegmentAndForecastGroup();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        //Last Room Sold
        createLastRoomSold(2);

        //WHEN
        int countOfPaceMktSegActivity = tenantCrudService().executeUpdateByNamedQuery(PaceMktSegActivity.INSERT_ZERO_FILL_PACE_MKT_SEG_ACTIVITY,
                QueryParameter.with("startDate", today.minusDays(1).toString(DateTimeFormat.forPattern("yyyy-MM-dd")))
                        .and("endDate", today.plusDays(1).toString(DateTimeFormat.forPattern("yyyy-MM-dd")))
                        .and("fileMetaDataId", 3)
                        .parameters());
        //THEN
        assertEquals(51, countOfPaceMktSegActivity);
    }

    @Test
    public void shouldNotConsiderOtherThanT2SnapProcessWhileGettingOccupancyChangeForForecastGroupLevel() {
        tenantCrudService().executeUpdateByNativeQuery("update File_Metadata set Process_Status_ID = 13, IsBDE = 1, Record_Type_ID = 2");
        //GIVEN
        Integer forecastGroupId = initializeMarketSegmentAndForecastGroup();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        //Last Room Sold
        createLastRoomSold(2);

        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_FORECAST_GROUP_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("forecastGroupId", forecastGroupId).parameters());

        //THEN
        assertEquals(0, results.size());
    }

    @Test
    public void shouldNotConsiderInactiveForecastGroupWhileGettingOccupancyChangeForForecastGroupLevel() {
        tenantCrudService().executeUpdateByNativeQuery("update File_Metadata set Process_Status_ID = 13, IsBDE = 1, Record_Type_ID = 3");
        //GIVEN
        Integer forecastGroupId = initializeMarketSegmentAndForecastGroup();
        tenantCrudService().executeUpdateByNativeQuery("update Mkt_Seg_Forecast_Group set Status_ID = 2 where Forecast_Group_ID = " + forecastGroupId);
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        //Last Room Sold
        createLastRoomSold(2);

        //WHEN
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_FORECAST_GROUP_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("forecastGroupId", forecastGroupId).parameters());

        //THEN
        assertEquals(0, results.size());
    }

    public Integer createForecastGroupFor(String forecastGroupName, Integer... mktSeg) {
        tenantCrudService().executeUpdateByNativeQuery("insert into Forecast_Group values(5, '" + forecastGroupName + "', '" + forecastGroupName + "', '" + forecastGroupName + "', 2, 9, 1, 0, 0, 1, '2010-07-19 13:26:00.000', 1, '2010-07-19 13:26:00.000', 1, 1)");
        Integer forecastGroupId = tenantCrudService().findByNativeQuerySingleResult("select Forecast_Group_ID from Forecast_Group where Forecast_Group_Name = :forecastGroupName", QueryParameter.with("forecastGroupName", forecastGroupName).parameters());
        for (Integer mktSegment : mktSeg) {
            tenantCrudService().executeUpdateByNativeQuery("insert into Mkt_Seg_Forecast_Group values(" + mktSegment + ", " + forecastGroupId + ", 1, 1, '2010-07-19 15:21:00.000', 1, '2010-07-19 15:21:00.000')");
        }
        return forecastGroupId;
    }

    @Test
    public void shouldGiveOccupancyChangeForBusinessTypeLevelWhenCurrentRoomSoldsIsGreaterThanLastRoomSold() {
        createFileMetadataForBDE();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        //Last Room Sold
        createLastRoomSold(1);

        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("180"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("150"), row[6]);
        assertEquals(new BigDecimal("30"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForBusinessTypeLevelWhenCurrentRoomSoldsIsLessThanLastRoomSold() {
        createFileMetadataForBDE();
        //Current Room Sold
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_1", 50, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_2", 50, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_3", 50, 2);
        //Last Room Sold
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_1", 60, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_2", 60, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_3", 60, 1);

        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("150"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("180"), row[6]);
        assertEquals(new BigDecimal("-30"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForTransientBusinessTypeLevel() {
        createFileMetadataForBDE();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_4", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_5", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_6", 60, 2);
        //Last Room Sold
        createLastRoomSold(1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_4", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_5", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_6", 50, 1);
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("180"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("150"), row[6]);
        assertEquals(new BigDecimal("30"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForTransientBusinessTypeLevel_NoCurrentData() {
        createFileMetadataForBDE();
        //Current Room Sold
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_4", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_5", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_6", 60, 2);
        //Last Room Sold
        createLastRoomSold(1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_4", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_5", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_6", 50, 1);
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("0"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("150"), row[6]);
        assertEquals(new BigDecimal("-150"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForTransientBusinessTypeLevel_NoLastData() {
        createFileMetadataForBDE();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_4", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_5", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_6", 60, 2);
        //Last Room Sold
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_4", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_5", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_6", 50, 1);
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("180"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("0"), row[6]);
        assertEquals(new BigDecimal("180"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForTransientBusinessTypeLevel_NoCurrentNorLastData() {
        createFileMetadataForBDE();
        //Current Room Sold
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_4", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_5", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_6", 60, 2);
        //Last Room Sold
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_4", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_5", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_6", 50, 1);
        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        assertTrue(results.isEmpty());
    }

    private void createFileMetadataForBDE() {
        tenantCrudService().executeUpdateByNativeQuery("update File_Metadata set Record_Type_ID=3, IsBDE=1, Process_Status_ID=13, SnapShot_DT=DATEADD(DAY,-2,GETDATE())\n" +
                "where File_Metadata_ID=1");
    }

    @Test
    public void shouldGiveOccupancyChangeForGroupBusinessTypeLevel() {
        createFileMetadataForBDE();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_4", 70, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_5", 70, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_6", 70, 2);
        //Last Room Sold
        createLastRoomSold(1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_4", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_5", 50, 1);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_6", 50, 1);

        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("businessTypeId", GROUP).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("210"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("150"), row[6]);
        assertEquals(new BigDecimal("60"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForBusinessTypeLevelByDatesWhenCurrentRoomSoldsIsGreaterThanLastRoomSold() {
        createFileMetadataForBDE();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();
        //Last Room Sold
        createLastRoomSold(1);

        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL_BY_DATES,
                QueryParameter.with("dates", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("180"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("150"), row[6]);
        assertEquals(new BigDecimal("30"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForBusinessTypeLevelByDatesWhenLastRoomSoldIsMissing() {
        createFileMetadataForBDE();
        //Current Room Sold
        createCurrentRoomsSoldMarketActivity();

        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL_BY_DATES,
                QueryParameter.with("dates", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("180"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("0"), row[6]);
        assertEquals(new BigDecimal("180"), row[7]);
    }

    @Test
    public void shouldGiveOccupancyChangeForBusinessTypeLevelByDatesWhenCurrentRoomSoldIsMissing() {
        createFileMetadataForBDE();
        //Last Room Sold
        createLastRoomSold(1);

        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL_BY_DATES,
                QueryParameter.with("dates", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        Object[] row = results.get(0);
        assertEquals(5, row[0]);
        assertEquals("NA", row[1]);
        assertEquals(today.toString(), new LocalDate(row[2]).toString());
        assertNull(row[3]);
        assertEquals(new BigDecimal("0"), row[4]);
        assertNull(row[5]);
        assertEquals(new BigDecimal("150"), row[6]);
        assertEquals(new BigDecimal("-150"), row[7]);
    }

    @Test
    public void shouldConsiderOnlyForSuccessfulProcessBdeWhileGettingOccupancyChange() {
        tenantCrudService().executeUpdateByNativeQuery("update File_Metadata set Process_Status_ID = 2");
        //Current Room Sold
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_1", 50, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_2", 50, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_3", 50, 2);
        //Last Room Sold
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_1", 60, 2);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_2", 60, 2);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_3", 60, 2);

        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("businessTypeId", TRANSIENT).parameters());
        assertEquals(0, results.size());
    }


    @Test
    public void shouldCreate_OCCUPANCY_CHANGE_NotificationAtMarketSegmentLevel() {
        createFileMetadataForBDE();
        final Integer mkt_seg_5 = getMarketSegmentIdByName("MKT_SEG_5");

        List<Object[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_MARKET_SEGMENT_LEVEL,
                QueryParameter.with("startDate", today.toString())
                        .and("endDate", today.toString())
                        .and("mktSegId", mkt_seg_5).parameters());
        assertEquals(0, results.size());

        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_5", 70, 2);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_5", 50, 1);

        createPaceMktActivity(today.minusDays(1).toString(), today.minusDays(1).toString(), "MKT_SEG_5", 70, 2);
        createPaceMktActivity(today.minusDays(1).toString(), today.minusDays(2).toString(), "MKT_SEG_5", 90, 1);

        createPaceMktActivity(today.minusDays(1).toString(), today.minusDays(1).toString(), "MKT_SEG_4", 70, 2);
        createPaceMktActivity(today.minusDays(1).toString(), today.minusDays(2).toString(), "MKT_SEG_4", 90, 1);

        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_4", 70, 2);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_4", 70, 2);

        results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_MARKET_SEGMENT_LEVEL,
                QueryParameter.with("startDate", today.minusDays(2).toString())
                        .and("endDate", today.toString())
                        .and("mktSegId", mkt_seg_5).parameters());

        assertEquals(2, results.size());

        Object[] row0 = results.get(0);
        assertEquals(5, row0[0]);
        assertEquals("NA", row0[1]);
        assertEquals(today.minusDays(1).toString(), new LocalDate(row0[2]).toString());
        assertNull(row0[3]);
        assertEquals(new BigDecimal("70"), row0[4]);
        assertNull(row0[5]);
        assertEquals(new BigDecimal("90"), row0[6]);
        assertEquals(new BigDecimal("-20"), row0[7]);

        Object[] row1 = results.get(1);
        assertEquals(5, row1[0]);
        assertEquals("NA", row1[1]);
        assertEquals(today.toString(), new LocalDate(row1[2]).toString());
        assertNull(row1[3]);
        assertEquals(new BigDecimal("70"), row1[4]);
        assertNull(row1[5]);
        assertEquals(new BigDecimal("50"), row1[6]);
        assertEquals(new BigDecimal("20"), row1[7]);

        final Integer mkt_seg_4 = getMarketSegmentIdByName("MKT_SEG_4");

        results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_MARKET_SEGMENT_LEVEL,
                QueryParameter.with("startDate", today.minusDays(2).toString())
                        .and("endDate", today.toString())
                        .and("mktSegId", mkt_seg_4).parameters());

        assertEquals(1, results.size());
    }

    @Test
    public void shouldCreate_OCCUPANCY_CHANGE_NotificationAtMarketSegmentLevelVariousScenarios() {
        createFileMetadataForBDE();
        final Integer mkt_seg_5 = getMarketSegmentIdByName("MKT_SEG_5");
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_5", 50, 1);

        createPaceMktActivity(today.minusDays(1).toString(), today.minusDays(1).toString(), "MKT_SEG_5", 70, 2);

        List<Objects[]> results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_MARKET_SEGMENT_LEVEL,
                QueryParameter.with("startDate", today.minusDays(2).toString())
                        .and("endDate", today.toString())
                        .and("mktSegId", mkt_seg_5).parameters());

        assertEquals(2, results.size());

        Object[] row0 = results.get(0);
        assertEquals(5, row0[0]);
        assertEquals("NA", row0[1]);
        assertEquals(today.minusDays(1).toString(), new LocalDate(row0[2]).toString());
        assertNull(row0[3]);
        assertEquals(new BigDecimal("70"), row0[4]);
        assertNull(row0[5]);
        assertEquals(new BigDecimal("0"), row0[6]);
        assertEquals(new BigDecimal("70"), row0[7]);

        Object[] row1 = results.get(1);
        assertEquals(5, row1[0]);
        assertEquals("NA", row1[1]);
        assertEquals(today.toString(), new LocalDate(row1[2]).toString());
        assertNull(row1[3]);
        assertEquals(new BigDecimal("0"), row1[4]);
        assertNull(row1[5]);
        assertEquals(new BigDecimal("50"), row1[6]);
        assertEquals(new BigDecimal("-50"), row1[7]);

        final Integer mkt_seg_4 = getMarketSegmentIdByName("MKT_SEG_4");

        results = tenantCrudService().findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_MARKET_SEGMENT_LEVEL,
                QueryParameter.with("startDate", today.minusDays(2).toString())
                        .and("endDate", today.toString())
                        .and("mktSegId", mkt_seg_4).parameters());

        assertEquals(0, results.size());
    }

    private void createCurrentRoomsSoldMarketActivity() {
        //Current Room Sold
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_1", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_2", 60, 2);
        createPaceMktActivity(today.toString(), today.toString(), "MKT_SEG_3", 60, 2);
    }

    private void createLastRoomSold(int fileMetadataId) {
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_1", 50, fileMetadataId);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_2", 50, fileMetadataId);
        createPaceMktActivity(today.toString(), today.minusDays(1).toString(), "MKT_SEG_3", 50, fileMetadataId);
    }

    private Integer initializeMarketSegmentAndForecastGroup() {
        Integer mkt_seg_1 = getMarketSegmentIdByName("MKT_SEG_1");
        Integer mkt_seg_2 = getMarketSegmentIdByName("MKT_SEG_2");
        Integer mkt_seg_3 = getMarketSegmentIdByName("MKT_SEG_3");
        return createForecastGroupFor("FG_TRANS_TEST1", mkt_seg_1, mkt_seg_2, mkt_seg_3);
    }


    private void verifyAggregateValue(PaceMktSegActivity paceEntry) {
        Map<String, Object> params = QueryParameter.with("occDate", paceEntry.getOccupancyDate())
                .and("marketSegId", paceEntry.getMktSegId())
                .parameters();
        List<MktSegAccomActivity> rolledUpValue = tenantCrudService().findByNativeQuery(ROLLUP_QUERY, params, MktSegAccomActivity.class);
        assertEquals(paceEntry.getArrivals().intValue(), rolledUpValue.stream().mapToInt(i -> i.getArrivals().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
        assertEquals(paceEntry.getDepartures().intValue(), rolledUpValue.stream().mapToInt(i -> i.getDepartures().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
        assertEquals(paceEntry.getNoShows().intValue(), rolledUpValue.stream().mapToInt(i -> i.getNoShows().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
        assertEquals(paceEntry.getCancellations().intValue(), rolledUpValue.stream().mapToInt(i -> i.getCancellations().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
        assertEquals(paceEntry.getCancellations().intValue(), rolledUpValue.stream().mapToInt(i -> i.getCancellations().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
        assertEquals(paceEntry.getFoodRevenue().intValue(), rolledUpValue.stream().mapToInt(i -> i.getFoodRevenue().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
        assertEquals(paceEntry.getRoomRevenue().intValue(), rolledUpValue.stream().mapToInt(i -> i.getRoomRevenue().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
        assertEquals(paceEntry.getRoomsSold().intValue(), rolledUpValue.stream().mapToInt(i -> i.getRoomsSold().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
        assertEquals(paceEntry.getTotalRevenue().intValue(), rolledUpValue.stream().mapToInt(i -> i.getTotalRevenue().intValue()).sum(),
                "there was an issue for MS " + paceEntry.getMktSegId() + " and " + paceEntry.getOccupancyDate());
    }

    private static final String ROLLUP_QUERY = "select * from Mkt_Accom_Activity \n" +
            "   where Mkt_Seg_ID = :marketSegId and Occupancy_DT = :occDate ";

    private FileMetadata getNewFileMetaData(FileMetadata template) {
        FileMetadata metadata = new FileMetadata();

        try {
            BeanUtils.copyProperties(metadata, template);
        } catch (IllegalAccessException | InvocationTargetException e) {
            fail("Couldn't copy the object " + e.getMessage());
        }

        metadata.setId(null);
        metadata.setFileName(metadata.getFileName() + "_new");
        return metadata;

    }

    public void createPaceMktActivity(String occupancyDate, String snapshotDate, String marketSegmentName, final int roomSold, int fileMetadataId) {
        tenantCrudService().executeUpdateByNativeQuery("insert into PACE_Mkt_Activity values(5, '" + occupancyDate + "', '" + snapshotDate + "', '" + snapshotDate + "', "
                + getMarketSegmentIdByName(marketSegmentName) + ", " + roomSold + ", 40, 30, 0, 0, 2000.0, 1000.0, 3000.0, " + fileMetadataId + ", 1, 1, '2015-09-28 12:35:50.783', 0, 0)");
    }

}
