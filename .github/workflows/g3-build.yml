name: "Build and Test G3"

defaults:
  run:
    shell: bash

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref && github.ref || github.run_id }}
  cancel-in-progress: true

on:
  pull_request:
  push:
    branches:
      - "main"
      - "release-*"

jobs:
  integration-tests:
    name: "G3 Integration Run Tests"
    needs: [build]
    strategy:
      matrix:
        test_group:
          [
            ESP_MEMPR,
            OPERA,
            OPER2_LAXAG,
            CTYHOCN_RATCHT_OPER1,
            TLHTL_RATVDW_RATHIL_H1,
          ]
      fail-fast: false
    uses: ./.github/workflows/g3-integration-test.yml
    with:
      test_group: ${{ matrix.test_group }}
      image_version: ${{ needs.build.outputs.TAG }}
      sas_image_version: ${{ needs.build.outputs.SAS_VERSION }}
    secrets: inherit

  unit-test:
    name: "Unit Test And Code Analysis G3"
    runs-on: ["self-hosted", "medium"]
    env:
      TZ: "America/Chicago"
    timeout-minutes: 60
    steps:
      - name: "Checkout [${{ github.repository }}]"
        uses: actions/checkout@v4
        with:
          # Disabling shallow clone is recommended for improving relevancy of reporting
          fetch-depth: 0
          show-progress: false

      - name: "Cache Maven Dependencies"
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository/*/*
            !~/.m2/repository/com/ideas
          key: ${{ runner.os }}-unit-cache-${{ hashFiles('pom.xml', 'source/**/*pom.xml', 'ear/**/*pom.xml', 'app-test/**/*pom.xml', 'docker/pom.xml', 'wildfly/**/*pom.xml', 'modules/**/*pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-unit-cache-

      - name: "Docker Login"
        uses: docker/login-action@v3
        with:
          username: "ideastechops"
          password: ${{ secrets.IDEAS_DOCKER_PASSWORD }}

      - name: "Maven Test"
        env:
          DD_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          WORKFLOW_UID: 1001
        run: |
          mkdir -p ~/.m2/repository
          chmod -R 777 ~/.m2
          chmod -R 777 ${GITHUB_WORKSPACE}
          docker compose -f docker-compose.ci.yml run unit

      - name: "Publish Unit Test Results"
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          check_name: "Unit Test Results"
          files: |
            **/target/surefire-reports/*.xml
            !source/services/war-job/job-web/target/surefire-reports/testng-results.xml

  db-test:
    name: "DB Test And Code Analysis G3"
    runs-on: ["self-hosted", "medium"]
    env:
      TZ: "America/Chicago"
    timeout-minutes: 60
    steps:
      - name: "Checkout [${{ github.repository }}]"
        uses: actions/checkout@v4
        with:
          # Disabling shallow clone is recommended for improving relevancy of reporting
          fetch-depth: 0
          show-progress: false

      - name: "Cache Maven Dependencies"
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository/*/*
            !~/.m2/repository/com/ideas
          key: ${{ runner.os }}-db-test-cache-${{ hashFiles('pom.xml', 'source/**/*pom.xml', 'ear/**/*pom.xml', 'app-test/**/*pom.xml', 'docker/pom.xml', 'wildfly/**/*pom.xml', 'modules/**/*pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-db-test-cache-

      - name: "Docker Login"
        uses: docker/login-action@v3
        with:
          username: "ideastechops"
          password: ${{ secrets.IDEAS_DOCKER_PASSWORD }}

      - name: "Maven Test"
        env:
          DD_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          WORKFLOW_UID: 1001
        run: |
          mkdir -p ~/.m2/repository
          chmod -R 777 ~/.m2
          chmod -R 777 ${GITHUB_WORKSPACE}
          docker compose -f docker-compose.ci.yml run db_test

      - name: "Publish DB Test Results"
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          check_name: "DB Test Results"
          files: |
            **/target/surefire-reports/*.xml
            !source/services/war-job/job-web/target/surefire-reports/testng-results.xml

  slow-db-test:
    name: "Slow DB Test And Code Analysis G3"
    runs-on: ["self-hosted", "medium"]
    env:
      TZ: "America/Chicago"
    timeout-minutes: 60
    steps:
      - name: "Checkout [${{ github.repository }}]"
        uses: actions/checkout@v4
        with:
          # Disabling shallow clone is recommended for improving relevancy of reporting
          fetch-depth: 0
          show-progress: false

      - name: "Cache Maven Dependencies"
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository/*/*
            !~/.m2/repository/com/ideas
          key: ${{ runner.os }}-slow-db-test-cache-${{ hashFiles('pom.xml', 'source/**/*pom.xml', 'ear/**/*pom.xml', 'app-test/**/*pom.xml', 'docker/pom.xml', 'wildfly/**/*pom.xml', 'modules/**/*pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-slow-db-test-cache-

      - name: "Docker Login"
        uses: docker/login-action@v3
        with:
          username: "ideastechops"
          password: ${{ secrets.IDEAS_DOCKER_PASSWORD }}

      - name: "Maven Test"
        env:
          DD_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          WORKFLOW_UID: 1001
        run: |
          mkdir -p ~/.m2/repository
          chmod -R 777 ~/.m2
          chmod -R 777 ${GITHUB_WORKSPACE}
          docker compose -f docker-compose.ci.yml run slow_db_test

      - name: "Publish Slow DB Test Results"
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          check_name: "Slow DB Test Results"
          files: |
            **/target/surefire-reports/*.xml
            !source/services/war-job/job-web/target/surefire-reports/testng-results.xml

  ui-test:
    name: "UI Test And Code Analysis G3"
    runs-on: ["self-hosted", "medium"]
    env:
      TZ: "America/Chicago"
    timeout-minutes: 60
    steps:
      - name: "Checkout [${{ github.repository }}]"
        uses: actions/checkout@v4
        with:
          # Disabling shallow clone is recommended for improving relevancy of reporting
          fetch-depth: 0
          show-progress: false

      - name: "Cache Maven Dependencies"
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository/*/*
            !~/.m2/repository/com/ideas
          key: ${{ runner.os }}-ui-test-cache-${{ hashFiles('pom.xml', 'source/**/*pom.xml', 'ear/**/*pom.xml', 'app-test/**/*pom.xml', 'docker/pom.xml', 'wildfly/**/*pom.xml', 'modules/**/*pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-ui-test-cache-

      - name: "Docker Login"
        uses: docker/login-action@v3
        with:
          username: "ideastechops"
          password: ${{ secrets.IDEAS_DOCKER_PASSWORD }}

      - name: "Maven Test"
        env:
          DD_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          WORKFLOW_UID: 1001
        run: |
          mkdir -p ~/.m2/repository
          chmod -R 777 ~/.m2
          chmod -R 777 ${GITHUB_WORKSPACE}
          docker compose -f docker-compose.ci.yml run ui_test

      - name: "UI Test Results"
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          check_name: "UI Test Results"
          files: |
            **/target/surefire-reports/*.xml
            !source/services/war-job/job-web/target/surefire-reports/testng-results.xml

  build:
    name: "Build G3"
    runs-on: ["self-hosted", "medium"]
    env:
      TZ: "America/Chicago"
    timeout-minutes: 40
    steps:
      - name: "Checkout [${{ github.repository }}]"
        uses: actions/checkout@v4

      - name: "Cache Maven Dependencies"
        uses: actions/cache@v4
        with:
          path: |
            ~/.m2/repository/*/*
            !~/.m2/repository/com/ideas
          key: ${{ runner.os }}-build-cache-${{ hashFiles('pom.xml', 'source/**/*pom.xml', 'ear/**/*pom.xml', 'app-test/**/*pom.xml', 'docker/pom.xml', 'wildfly/**/*pom.xml', 'modules/**/*pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-build-cache-

      - name: "GHCR Login"
        uses: docker/login-action@v3
        with:
          registry: "ghcr.io"
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: "Docker Login"
        uses: docker/login-action@v3
        with:
          username: "ideastechops"
          password: ${{ secrets.IDEAS_DOCKER_PASSWORD }}

      - name: "Parameters Set"
        id: set-parameters
        run: |
          mkdir -p ~/.m2/repository 
          chmod -R 777 ~/.m2 
          chmod -R 777 ${GITHUB_WORKSPACE}
          SHORT_SHA=$(git rev-parse --short=11 ${{ github.sha }})
          TAG=10.0.${SHORT_SHA}
          if [[ "${{ github.ref }}" == "refs/pull/"${{ github.event.pull_request.number }}"/merge" ]]; then
            TAG=${TAG}-Branch      
          fi
          PROJECT_VERSION=$(docker compose -f docker-compose.ci.yml run mvncmd mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
          echo "PROJECT_VERSION=${PROJECT_VERSION}" >> $GITHUB_OUTPUT
          echo "SHORT_SHA=${SHORT_SHA}" >> $GITHUB_OUTPUT
          echo "TAG=${TAG}" >> $GITHUB_OUTPUT
          
          SAS_VERSION=$(cat modules/G3/Properties/SAS-Version.txt | tr -d '\n')
          echo "Extracted version: ${SAS_VERSION}"
          echo "SAS_VERSION=${SAS_VERSION}" >> $GITHUB_OUTPUT


      - name: "Parameters Print"
        run: echo "IMAGE TAG - ${{ steps.set-parameters.outputs.TAG }}"

      - name: "Updating version in g3 pom.xml to ${{ steps.set-parameters.outputs.TAG }}"
        run: |
          docker compose -f docker-compose.ci.yml run mvncmd mvn versions:set -DnewVersion=${{ steps.set-parameters.outputs.TAG }}

      - name: "Maven Build"
        env:
          DD_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          G3Version: ${{ steps.set-parameters.outputs.SHORT_SHA }}
          WORKFLOW_UID: 1001
        run: |
          docker compose -f docker-compose.ci.yml run build

      - name: "Upload Integration Test Artifacts"
        uses: actions/upload-artifact@v4
        with:
          name: integration-test-dependencies-${{ github.run_id }}
          retention-days: 1
          path: |
            ~/.m2/repository/com/ideas/g3/services
            ~/.m2/repository/com/ideas/g3/client
            ~/.m2/repository/com/ideas/g3/ui
            ~/.m2/repository/com/ideas/g3/g3

      - name: "Docker Push [g3_db]"
        uses: docker/build-push-action@v5
        with:
          context: docker/db
          push: true
          tags: |
            ideasinc/g3_db:${{ steps.set-parameters.outputs.TAG }}

      - name: "Docker Push [g3_db_migrate]"
        uses: docker/build-push-action@v5
        with:
          context: docker/dbmigrate
          push: true
          tags: |
            ideasinc/g3_db_migrate:${{ steps.set-parameters.outputs.TAG }}

      - name: "Docker Push [g3_app]"
        uses: docker/build-push-action@v5
        with:
          context: docker/app
          push: true
          tags: |
            ideasinc/g3_app:${{ steps.set-parameters.outputs.TAG }}

      - name: "Docker Push [g3_apache]"
        uses: docker/build-push-action@v5
        with:
          context: docker/apache
          push: true
          tags: |
            ideasinc/g3_apache:${{ steps.set-parameters.outputs.TAG }}

      - name: "Updating version in g3 pom.xml to ${{ steps.set-parameters.outputs.PROJECT_VERSION }}"
        run: |
          docker compose -f docker-compose.ci.yml run mvncmd mvn versions:set -DnewVersion=${{ steps.set-parameters.outputs.PROJECT_VERSION }}

    outputs:
      TAG: ${{ steps.set-parameters.outputs.TAG }}
      SAS_VERSION: ${{ steps.set-parameters.outputs.SAS_VERSION }}
