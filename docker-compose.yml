#version: "2.4" #silence the warning
name: g3
services:
  build:
    build: docker/build
    environment:
      - TZ=America/Chicago
      - dbHost=localhost
    volumes:
      - ${HOST_PROJECT_PATH:-.}:/home/<USER>/repo
      - ${HOST_HOME_PATH:-~}/.m2/repository:/home/<USER>/.m2/repository
    command: clean install -DskipTests -DdbClean=true -Duser.timezone=America/Chicago
    depends_on:
      db:
        condition: service_healthy
    network_mode: host

  app:
    build: docker/app
    container_name: g3_app
    environment:
      - DEPLOY_EXPLODED_LOCAL=true
      - TZ=America/Chicago
      - JAVA_OPTS=${JAVA_OPTS:--Xmx4g -Xms4g -Djava.net.preferIPv4Stack=true -agentpath:/libjrebel64.so -Drebel.remoting_plugin=true -Drebel.log.stdout=false -Dspring.profiles.active=LocalDevelopment -XX:-OmitStackTraceInFastThrow} ${INCLUDE_DATADOG:+-javaagent:/opt/datadog/dd-java-agent.jar -Ddd.profiling.enabled=false -Ddd.logs.injection=true -Ddd.integration.jdbc-datasource.enabled=false }
      - ENV=LocalDevelopment
      - DB_HOST=localhost
      - SAS_HOST=localhost
      - DB_USER=sa
      - DB_PASSWORD=IDeaS123
      - S3_ACCESS_KEY=${S3_ACCESS_KEY:-}
      - S3_SECRET_KEY=${S3_SECRET_KEY:-}
      - OAUTH2_CLIENT_SECRET=${OAUTH2_CLIENT_SECRET:-}
      - AWS_REGION=us-east-2
    volumes:
      - ${HOST_PROJECT_PATH:-.}/ear/platform-ear/target/platform-ear-4.1.0-SNAPSHOT/lib:/opt/wildfly/standalone/deployments/platform-ear.ear/lib
      - ${HOST_PROJECT_PATH:-.}/ear/platform-ear/target/platform-ear-4.1.0-SNAPSHOT/META-INF:/opt/wildfly/standalone/deployments/platform-ear.ear/META-INF
      - ${HOST_PROJECT_PATH:-.}/source/services/global-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/global-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/tenant-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/tenant-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/ratchet-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/ratchet-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/job-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/job-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/ejb-htng-integration/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/ejb-htng-integration.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/ejb-pacman/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/ejb-pacman.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/reports-g3/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/reports-g3.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/war-g3/target/pacman-platformsecurity:/opt/wildfly/standalone/deployments/platform-ear.ear/pacman-platformsecurity.war
      - ${HOST_PROJECT_PATH:-.}/source/ui/vaadin/vaadin-web/target/vaadin-web:/opt/wildfly/standalone/deployments/platform-ear.ear/vaadin-web.war
      - ${HOST_PROJECT_PATH:-.}/source/ui/angular/target/war-angular:/opt/wildfly/standalone/deployments/platform-ear.ear/war-angular.war
      - ${HOST_PROJECT_PATH:-.}/source/db/tenant-runnable/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/tenant-runnable.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/war-job/job-web/target/job-web:/opt/wildfly/standalone/deployments/job-web.war
      - ${HOST_PROJECT_PATH:-.}/source/services/war-api/target/api:/opt/wildfly/standalone/deployments/api.war
      - ~/.jrebel:/.jrebel
      - /opt/jrebel/jrebel.jar:/jrebel.jar
      - /opt/jrebel/lib/libjrebel64.so:/libjrebel64.so
      - ${HOST_PROJECT_PATH:-.}/.data/G3:/opt/G3
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Logs:/opt/SAS/Logs
      - ${HOST_PROJECT_PATH:-.}/.data/wildfly/logs:/opt/wildfly/standalone/log
    entrypoint:
      [
        "/bin/bash",
        "-c",
        "/entrypoint.sh && /opt/wildfly/bin/standalone.sh -b=0.0.0.0 -bmanagement=0.0.0.0 --debug *:8787",
      ]
    depends_on:
      db:
        condition: service_healthy
      apache:
        condition: service_healthy
      sas:
        condition: service_healthy
      localstack:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 5
      start_period: 2m
      test: >
        curl -s --fail 'http://localhost:8080/job-web/health/g3Application' | jq -r '.status' | grep -q "UP" &&
        curl -s --fail 'http://localhost:8080/api/helloworld/v1/' || exit 1
    network_mode: host
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "10"

  db:
    build: docker/db
    environment:
      - TZ=America/Chicago
      - MSSQL_AGENT_ENABLED=False
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/mssql:/var/opt/mssql
      - ${HOST_PROJECT_PATH:-.}/.data/G3/Temp/ftp:/opt/G3/Temp/ftp
    network_mode: host

  apache:
    build: docker/apache
    environment:
      - APP_HOST=localhost
      - TZ=America/Chicago
      - DD_SERVICE=g3_apache
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/apache/logs:/usr/local/apache2/logs
    network_mode: host

  db_migrate:
    build: docker/dbmigrate
    environment:
      - TZ=America/Chicago
      - DB_HOST=localhost
      - SAS_HOST=localhost
    command: migrate
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data:/data
      - ${HOME}/.aws:/root/.aws
      - ${HOST_PROJECT_PATH:-.}/source/db/global/target/global.jar:/flyway/jars/global/global.jar
      - ${HOST_PROJECT_PATH:-.}/source/db/dbautils/target/dbautils.jar:/flyway/jars/dbautils/dbautils.jar
      - ${HOST_PROJECT_PATH:-.}/source/db/job/target/job.jar:/flyway/jars/job/job.jar
      - ${HOST_PROJECT_PATH:-.}/source/db/profile/target/profile.jar:/flyway/jars/profile/profile.jar
      - ${HOST_PROJECT_PATH:-.}/source/db/quartz/target/quartz.jar:/flyway/jars/quartz/quartz.jar
      - ${HOST_PROJECT_PATH:-.}/source/db/ratchet/target/ratchet.jar:/flyway/jars/ratchet/ratchet.jar
      - ${HOST_PROJECT_PATH:-.}/source/db/serveragentjobs/target/serveragentjobs.jar:/flyway/jars/serveragentjobs/serveragentjobs.jar
      - ${HOST_PROJECT_PATH:-.}/source/db/tenant-runnable/target/tenant-runnable.jar:/flyway/jars/tenant/tenant.jar
    depends_on:
      db:
        condition: service_healthy
    network_mode: host

  mock_sftp:
    image: atmoz/sftp
    ports:
      - "2222:22"
    environment:
      - SFTP_USERS=deployer:IDeaS123:1001
      - TZ=America/Chicago
    command: /bin/sh -c "mkdir -p /home/<USER>/crsdata &&  chmod -R 777 /home/<USER>/crsdata && exec /entrypoint"
    restart: always
    network_mode: host

  mock_ftp:
    image: fauria/vsftpd
    environment:
      - FTP_PASS=password
      - FTP_USER=qa03ftpuser1
      - PASV_ADDRESS=localhost
      - PASV_ADDR_RESOLVE=YES
      - TZ=America/Chicago
    ports:
      - "20-21:20-21/tcp"
      - "40000-40009:40000-40009/tcp"
    restart: always
    network_mode: host

  anymocker:
    image: ghcr.io/ideasorg/ideasanymocker:latest
    environment:
      - TZ=America/Chicago
    volumes:
      - ./app-test/processors:/processors
      - ./app-test/requests:/requests
    ports:
      - "9191:9191"
    network_mode: host

  sas:
    image: ideasinc/g3_sas:main
    environment:
      TZ: America/Chicago
      AWS_REGION: us-east-2
      SPRING_APPLICATION_JSON: '{
        "spring.profiles.active": "local",
        "aws.s3.endpoint": "http://localhost:4566"
        }'

    working_dir: /opt/SAS
    entrypoint: /entrypoint.sh encode
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Data/Properties:/opt/SAS/Data/Properties
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Data/Ratchet/Properties:/opt/SAS/Data/Ratchet/Properties
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Logs:/opt/SAS/Logs
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Temp:/opt/SAS/Temp
      - ${HOST_PROJECT_PATH:-.}/.data/G3/Data:/opt/G3/Data
      - ${HOST_PROJECT_PATH:-.}/.data/G3/Temp:/opt/G3/Temp
    ports:
      - "7070:7070"
      - "7072:7072"
    network_mode: host

  # Need to run this service if running SAS locally
  localstack:
    image: localstack/localstack:latest
    environment:
      - TMPDIR=/tmp
      - AWS_REGION=us-east-2
      - DEBUG=1
      - USE_SSL=0
      - SERVICES=s3,sqs
      - AWS_DEFAULT_REGION=us-east-2
    ports:
      - "4566:4566"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
      - ${HOST_PROJECT_PATH:-.}/.data/tmp:/tmp
    network_mode: host

volumes:
  m2:
