name: g3
services:
  app:
    image: ideasinc/g3_app:${IMAGE_VERSION}
    environment:
      - TZ=America/Chicago
      - JAVA_OPTS=${JAVA_OPTS:--Xmx4g -Xms4g -Djava.net.preferIPv4Stack=true} ${INCLUDE_DATADOG:+-javaagent:/opt/datadog/dd-java-agent.jar -Ddd.profiling.enabled=true -Ddd.logs.injection=true}
      - ENV=QALocalDevelopment
      - DB_HOST=localhost
      - SAS_HOST=localhost
      - DB_USER=sa
      - DB_PASSWORD=IDeaS123
      - S3_ACCESS_KEY=${S3_ACCESS_KEY:-}
      - S3_SECRET_KEY=${S3_SECRET_KEY:-}
      - OAUTH2_CLIENT_SECRET=${OAUTH2_CLIENT_SECRET:-}
      - DD_RUM_ENABLE=${DD_RUM_ENABLE:-false}
      - DD_RUM_APP_ID=${DD_RUM_APP_ID:-}
      - DD_RUM_CLIENT_TOKEN=${DD_RUM_CLIENT_TOKEN:-}
      - DD_DBM_PROPAGATION_MODE=full
      - DD_INTEGRATION_JDBC_DATASOURCE_ENABLED=true
      - DD_DBM_TRACE_PREPARED_STATEMENTS=true
      - DD_LOGS_INJECTION=true
      - AWS_REGION=${AWS_REGION:-us-east-1}
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/G3:/opt/G3
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Logs:/opt/SAS/Logs
    depends_on:
      db:
        condition: service_healthy
      apache:
        condition: service_healthy
      sas:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 5
      start_period: 2m
      test: >
        curl -s --fail 'http://localhost:8080/job-web/health/g3Application' | jq -r '.status' | grep -q "UP" &&
        curl -s --fail 'http://localhost:8080/api/helloworld/v1/' || exit 1
    network_mode: host

  db:
    image: ideasinc/g3_db:${IMAGE_VERSION}
    environment:
      - TZ=America/Chicago
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/mssql:/var/opt/mssql
    network_mode: host

  apache:
    image: ideasinc/g3_apache:${IMAGE_VERSION}
    environment:
      - APP_HOST=localhost
      - TZ=America/Chicago
      - DD_SERVICE=g3_apache
      - DD_VERSION=${IMAGE_VERSION}
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/apache/logs:/usr/local/apache2/logs
    network_mode: host

  db_migrate:
    image: ideasinc/g3_db_migrate:${IMAGE_VERSION}
    environment:
      - TZ=America/Chicago
      - DB_HOST=localhost
      - SAS_HOST=localhost
    command: migrate
    volumes:
      - .data:/data
      - ${HOME}/.aws:/root/.aws

    depends_on:
      db:
        condition: service_healthy
    network_mode: host

  mock_sftp:
    image: atmoz/sftp
    ports:
      - "2222:22"
    environment:
      - SFTP_USERS=deployer:IDeaS123:1001
      - TZ=America/Chicago
    command: /bin/sh -c "mkdir -p /home/<USER>/crsdata &&  chmod -R 777 /home/<USER>/crsdata && exec /entrypoint"
    restart: always
    network_mode: host

  mock_ftp:
    image: fauria/vsftpd
    environment:
      - FTP_PASS=password
      - FTP_USER=qa03ftpuser1
      - PASV_ADDRESS=localhost
      - PASV_ADDR_RESOLVE=YES
      - TZ=America/Chicago
    ports:
      - "20-21:20-21/tcp"
      - "40000-40009:40000-40009/tcp"
    restart: always
    network_mode: host

  anymocker:
    image: ghcr.io/ideasorg/ideasanymocker:latest
    environment:
      - TZ=America/Chicago
    volumes:
      - ./app-test/processors:/processors
      - ./app-test/requests:/requests
    ports:
      - "9191:9191"
    network_mode: host

  sas:
    image: ideasinc/g3_sas:main
    environment:
      TZ: America/Chicago
      AWS_REGION: ${AWS_REGION:-us-east-1}
      SPRING_APPLICATION_JSON: '{
        "spring.profiles.active": "local",
        "aws.s3.endpoint": "http://localhost:4566"
        }'

    working_dir: /opt/SAS
    entrypoint: /entrypoint.sh encode
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Data/Properties:/opt/SAS/Data/Properties
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Data/Ratchet/Properties:/opt/SAS/Data/Ratchet/Properties
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Logs:/opt/SAS/Logs
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Temp:/opt/SAS/Temp
      - ${HOST_PROJECT_PATH:-.}/.data/G3/Data:/opt/G3/Data
    network_mode: host
