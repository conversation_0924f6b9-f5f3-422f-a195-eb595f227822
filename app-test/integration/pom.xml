<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ideas.tetris.platform.deploy.test</groupId>
    <artifactId>integration-test</artifactId>
    <packaging>jar</packaging>
    <version>4.1.0-SNAPSHOT</version>
    <name>integration-test</name>

    <properties>
        <gmavenplus.version>1.6.2</gmavenplus.version>
        <groovy.version>2.5.5</groovy.version>
        <spring-integration-groovy.version>5.4.6</spring-integration-groovy.version>
        <buildDirectory>${project.basedir}/target</buildDirectory>
        <appVersion>4.1.0-SNAPSHOT</appVersion>
        <cucumber.version>4.8.1</cucumber.version>
        <extent.version>4.1.5</extent.version>
        <log4j.version>2.17.0</log4j.version>
        <testFailureIgnore>true</testFailureIgnore>
    </properties>

    <build>
        <directory>${buildDirectory}</directory>
        <testResources>
            <testResource>
                <filtering>true</filtering>
                <directory>src/test/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.txt</include>
                </includes>
                <excludes>
                    <exclude>**/binary/**</exclude>
                    <exclude>**/*.xlsx</exclude>
                    <exclude>**/*.xls</exclude>
                    <exclude>**/*.pdf</exclude>
                    <exclude>**/*.jar</exclude>
                    <exclude>**/*.bin</exclude>
                    <exclude>**/*.gz</exclude>
                    <exclude>**/*.zip</exclude>
                    <exclude>**/*.psv</exclude>
                </excludes>
            </testResource>
            <testResource>
                <filtering>false</filtering>
                <directory>src/test/resources</directory>
                <includes>
                    <include>**/binary/**</include>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                    <include>**/*.pdf</include>
                    <include>**/*.jar</include>
                    <include>**/*.bin</include>
                    <include>**/*.gz</include>
                    <include>**/*.zip</include>
                    <include>**/*.psv</include>
                </includes>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>${gmavenplus.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <invokeDynamic>true</invokeDynamic>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>zip</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jar</nonFilteredFileExtension>
                        <nonFilteredFileExtension>bin</nonFilteredFileExtension>
                        <nonFilteredFileExtension>gz</nonFilteredFileExtension>
                        <nonFilteredFileExtension>psv</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <testFailureIgnore>${testFailureIgnore}</testFailureIgnore>
                    <useFile>true</useFile>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.surefire</groupId>
                        <artifactId>surefire-junit47</artifactId>
                        <version>2.19.1</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself. -->
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>
                                            org.codehaus.gmaven
                                        </groupId>
                                        <artifactId>
                                            gmaven-plugin
                                        </artifactId>
                                        <versionRange>
                                            [1.3,)
                                        </versionRange>
                                        <goals>
                                            <goal>testCompile</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-client</artifactId>
            <version>1.9.1</version>
        </dependency>
        <dependency>
            <groupId>net.sf</groupId>
            <artifactId>sevenzipjbinding</artifactId>
            <version>4.65-1.06</version>
        </dependency>
        <dependency>
            <groupId>net.sf</groupId>
            <artifactId>sevenzipjbinding-AllPlatforms</artifactId>
            <version>4.65-1.06</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy.version}</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-dateutil</artifactId>
            <version>${groovy.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.10.0</version>
        </dependency>

        <!-- Spring Integration -->
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-groovy</artifactId>
            <version>${spring-integration-groovy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-ftp</artifactId>
            <version>${spring-integration-groovy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-sftp</artifactId>
            <version>${spring-integration-groovy.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>8.4.0.jre8</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy.modules.http-builder</groupId>
            <artifactId>http-builder</artifactId>
            <version>0.5.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.14</version>
        </dependency>
        <dependency>
            <groupId>sas.libraries</groupId>
            <artifactId>sas.core</artifactId>
            <version>902200.8.0.20090602190000_v920m2</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>sas.libraries</groupId>
            <artifactId>sas.svc.connection</artifactId>
            <version>902200.10.0.20090526113105_v920m2_pp</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>sas.libraries</groupId>
            <artifactId>sas.security.sspi</artifactId>
            <version>902200.1.0.20090602190000_v920m2</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.googlecode.totallylazy</groupId>
            <artifactId>totallylazy</artifactId>
            <version>991</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
            <version>2.3.3.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-multipart-provider</artifactId>
            <version>2.3.3.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.ideas.g3.client</groupId>
            <artifactId>client</artifactId>
            <version>${appVersion}</version>
        </dependency>
        <dependency>
            <groupId>com.ideas.g3.services</groupId>
            <artifactId>common-g3</artifactId>
            <version>${appVersion}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <scope>compile</scope>
            <version>1.10</version>
        </dependency>
        <dependency>
            <groupId>xmlunit</groupId>
            <artifactId>xmlunit</artifactId>
            <version>1.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.9</version>
        </dependency>
        <dependency>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
            <version>0.9.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.github.tomakehurst</groupId>
            <artifactId>wiremock-standalone</artifactId>
            <version>2.18.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.aventstack</groupId>
            <artifactId>extentreports</artifactId>
            <version>${extent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.rallydev.rest</groupId>
            <artifactId>rally-rest-api</artifactId>
            <version>2.2.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.2.4</version>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-groovy</artifactId>
            <version>4.7.1</version>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-junit</artifactId>
            <version>${cucumber.version}</version>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-core</artifactId>
            <version>${cucumber.version}</version>
        </dependency>
        <dependency>
            <groupId>net.masterthought</groupId>
            <artifactId>cucumber-reporting</artifactId>
            <version>4.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.aventstack</groupId>
            <artifactId>extentreports-cucumber4-adapter</artifactId>
            <version>1.0.12</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.openjdk-orb</groupId>
            <artifactId>openjdk-orb</artifactId>
            <version>8.1.4.Final</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.6.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.6.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.6.3</version>
        </dependency>
<!--         https://mvnrepository.com/artifact/io.rest-assured/rest-assured-->
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>5.4.0</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy-xml</artifactId>
                </exclusion>
            </exclusions>
            <!-- https://mvnrepository.com/artifact/io.rest-assured/json-schema-validator -->
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>json-schema-validator</artifactId>
            <version>5.4.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy-xml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.skyscreamer/jsonassert -->
        <dependency>
            <groupId>org.skyscreamer</groupId>
            <artifactId>jsonassert</artifactId>
            <version>1.5.0</version>
            <scope>test</scope>
        </dependency>

    </dependencies>
    <profiles>
        <profile>
            <id>bdd</id>
            <dependencies>
                <dependency>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                    <version>4.12</version>
                    <scope>test</scope>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>CucumberFast</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>8</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/ContinuousPricingTestRunner</include>
                                <include>**/GroupPricingTestRunner</include>
                                <include>**/DecisionDailyRunTestRunner</include>
                                <include>**/OperaRoomTypeRecodeTestRunner</include>
                                <include>**/MultiPropertyGroupPricingTestRunner</include>
                                <include>**/ReportsTestRunner</include>
                                <include>**/DiscoverTestRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>CucumberSlow</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>8</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/OperaDailyRunTestRunner</include>
                                <include>**/OperaEndToEndTestRunner</include>
                                <include>**/ComponentRoomWithNewSASFlowTestRunner</include>
                                <include>**/MultiPropertyRatePlanProductionPostmanRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>IntegrationSlow</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!--<forkCount>4</forkCount>
                            <reuseForks>false</reuseForks>-->
                            <parallel>classes</parallel>
                            <threadCount>8</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <testFailureIgnore>true</testFailureIgnore>
                            <runOrder>alphabetical</runOrder>
                            <includes>
                                <include>integration.g3.configure.forecasts.limiteddatabuild.LDBJobWithGenericFlowTest</include>
                                <include>integration.g3.configure.forecasts.limiteddatabuild.LDBJobWithCPTest</include>
                                <!--<include>com.ideas.tetris.integration.runners.LDBRunner</include>
                                 <include>integration.g3.jobs.purge.PurgePropertyJobTest</include>
                                 <include>integration.g3.jobs.purge.PurgeGlobalDataJobTest</include>-->
                                <!--<include>integration.g3.jobs.purge.DecisionPurgeTest</include>
                                <include>integration.g3.jobs.purge.MarketSegmentPurgeTest</include>-->
                                <!--<include>integration.g3.configure.forecasts.limiteddatabuild.BudgetSheetTest</include>-->
                            </includes>
                            <!--<properties>
                                <property>
                                    <name>listener</name>
                                    <value>common.TestListener</value>
                                </property>
                            </properties>-->
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>CucumberSmoke</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>8</threadCount>
                            <argLine>-Xmx512m</argLine>
                            <includes>
                                <include>**/PerPersonPricingTestRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Prod1Sanity</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>8</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/ProductionSanityStoppedJobsRunner</include>
                                <include>**/Prod1SanitySchedulerTestRunner</include>
                                <include>**/Prod1SanityProcessCRSFileBDERunner</include>
                                <include>**/Prod1SanityNGIGenerateDatafeedFileRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Prod2Sanity</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>10</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/ProductionSanityStoppedJobsRunner</include>
                                <include>**/Prod2SanitySchedulerTestRunner</include>
                                <include>**/Prod2SanityNGIDeferredDeliveryJobRunner</include>
                                <include>**/Prod2SanityNGIGenerateDatafeedFileRunner</include>
                                <include>**/Prod2SanityProcessCRSFileBDERunner</include>
                                <include>**/Prod2SanityOperaDataLoadRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Prod3Sanity</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>10</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/ProductionSanityStoppedJobsRunner</include>
                                <include>**/Prod3SanitySchedulerTestRunner</include>
                                <include>**/Prod2SanityNGIDeferredDeliveryJobRunner</include>
                                <include>**/Prod2SanityOperaDataLoadRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>PostmanOXIRates</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>5</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/OXIUnqualifiedPostmanRunner</include>
                                <include>**/OXIQualifiedPostmanRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>PostmanOXIReservation</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>5</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/OXIReservationPostmanRunner</include>
                                <include>**/OXIMultiUnitReservationPostmanRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>PostmanOXIGroupSubstitution</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>5</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/OXIGroupSubstitutionPostmanRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>PostmanOXIAMS</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>5</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/OXIAMSPostmanRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>PostmanOXIShare</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>5</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/OXISharedReservationPostmanRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>PostmanOXICurrencyExchange</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>5</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/OXIReservationCurrencyExchangePostmanRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>PostmanOXIPackageRates</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <parallel>classes</parallel>
                            <threadCount>5</threadCount>
                            <argLine>-Xmx1024m</argLine>
                            <includes>
                                <include>**/OXIReservationPackageRatesPostmanRunner</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
