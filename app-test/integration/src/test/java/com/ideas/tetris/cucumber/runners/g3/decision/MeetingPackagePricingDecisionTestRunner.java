package com.ideas.tetris.cucumber.runners.g3.decision;


import com.ideas.tetris.cucumber.runners.CustomRunner;
import io.cucumber.junit.CucumberOptions;
import org.junit.runner.RunWith;

@RunWith(CustomRunner.class)
@CucumberOptions(plugin = {"com.ideas.tetris.cucumber.runners.CustomReportListener"},
        tags = {"@MeetingPackagePricingOptimizationTest","not @incubator", "not @Pending", "not @UI","not @DailyRun"},
        features = {"src/test/resources/com/ideas/tetris/cucumber/features/g3/meetingpackagepricing"},
        glue={"com.ideas.tetris.cucumber.stepdefinition.g3.meetingpackagepricing"},strict = true,monochrome = true)
public class MeetingPackagePricingDecisionTestRunner {
}
