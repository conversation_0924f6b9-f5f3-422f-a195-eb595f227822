package com.ideas.tetris.cucumber.runners.g3.datafeed;

import com.ideas.tetris.cucumber.runners.CustomRunner;
import io.cucumber.junit.CucumberOptions;
import org.junit.runner.RunWith;

@RunWith(CustomRunner.class)
@CucumberOptions(plugin = {"com.ideas.tetris.cucumber.runners.CustomReportListener","pretty","html:target/cucumber-html-report","json:target/cucumber-json-report.json"},
        tags = {"@CoreDatafeed"},
        features = {"src/test/resources/com/ideas/tetris/cucumber/features/g3/support/datafeed/coreDatafeed.feature"},
        glue={"com.ideas.tetris.cucumber.stepdefinition.g3.support.datafeed"}, strict = true, monochrome = true)
public class CoreDatafeedRunner {
}