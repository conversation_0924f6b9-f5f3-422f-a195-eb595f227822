[{"businessViewName": "BAR", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_SB_1", "forecastGroupName": "UNQUAL_BAR", "forecastGroupDescription": "FG_UNQUALIFIED_SB_1", "mktSegCode": "BAR", "mktSegName": "BAR", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Unfenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "Yes", "baseProduct": null}, {"businessViewName": "CMP", "businessViewDescription": "", "forecastGroupCode": "FG_NOFCST_NSB_1", "forecastGroupName": "CMP", "forecastGroupDescription": "FG_NOFCST_NSB_1", "mktSegCode": "CMP", "mktSegName": "CMP", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "None", "control": "Non-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CMTG", "businessViewDescription": "", "forecastGroupCode": "FG_GROUP_NSB_1", "forecastGroupName": "GROUP_CMTG SMRF", "forecastGroupDescription": "FG_GROUP_NSB_1", "mktSegCode": "CMTG", "mktSegName": "CMTG", "mktSegDescription": null, "businessType": "Group", "contract": "System Decision", "booking": "System Decision", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_SEMIYLD_NSB_1", "forecastGroupName": "QUAL_SEMIYLD_CNR LNR", "forecastGroupDescription": "FG_QUALIFIED_SEMIYLD_NSB_1", "mktSegCode": "CNR_DEF_FUT", "mktSegName": "CNR_DEF_FUT", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Semi-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_SEMIYLD_NSB_1", "forecastGroupName": "QUAL_SEMIYLD_CNR LNR", "forecastGroupDescription": "FG_QUALIFIED_SEMIYLD_NSB_1", "mktSegCode": "CNR_DEF_HIST", "mktSegName": "CNR_DEF_HIST", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Semi-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_NONYLD_NSB_1", "forecastGroupName": "QUAL_LINKED_NONYLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_NONYLD_NSB_1", "mktSegCode": "CNR_QNL", "mktSegName": "CNR_QNL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Non-Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_SEMIYLD_NSB_1", "forecastGroupName": "QUAL_SEMIYLD_CNR LNR", "forecastGroupDescription": "FG_QUALIFIED_SEMIYLD_NSB_1", "mktSegCode": "CNR_QS", "mktSegName": "CNR_QS", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Semi-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_SEMIYLD_NSB_1", "forecastGroupName": "QUAL_LINKED_SEMIYLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_SEMIYLD_NSB_1", "mktSegCode": "CNR_QSL", "mktSegName": "CNR_QSL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Semi-Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_YLD_NSB_1", "forecastGroupName": "QUAL_YLD_CNR LNR", "forecastGroupDescription": "FG_QUALIFIED_YLD_NSB_1", "mktSegCode": "CNR_QY", "mktSegName": "CNR_QY", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_YLD_NSB_1", "forecastGroupName": "QUAL_LINKED_YLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_YLD_NSB_1", "mktSegCode": "CNR_QYL", "mktSegName": "CNR_QYL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "CONS", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_SB_1", "forecastGroupName": "UNQUAL_BAR", "forecastGroupDescription": "FG_UNQUALIFIED_SB_1", "mktSegCode": "CONS", "mktSegName": "CONS", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Unfenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "Yes", "baseProduct": null}, {"businessViewName": "CONV", "businessViewDescription": "", "forecastGroupCode": "FG_GROUP_NSB_2", "forecastGroupName": "GROUP_GT CONV", "forecastGroupDescription": "FG_GROUP_NSB_2", "mktSegCode": "CONV", "mktSegName": "CONV", "mktSegDescription": null, "businessType": "Group", "contract": "System Decision", "booking": "System Decision", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_2", "forecastGroupName": "UNQUAL_DISC_2", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_2", "mktSegCode": "DISC_DEF_FUT", "mktSegName": "DISC_DEF_FUT", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Fenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_2", "forecastGroupName": "UNQUAL_DISC_2", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_2", "mktSegCode": "DISC_DEF_HIST", "mktSegName": "DISC_DEF_HIST", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Fenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_TRANSIENT_BLOCK_NSB_1", "forecastGroupName": "PERM", "forecastGroupDescription": "FG_TRANSIENT_BLOCK_NSB_1", "mktSegCode": "DISC_QB", "mktSegName": "DISC_QB", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Non-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_NONYLD_NSB_1", "forecastGroupName": "QUAL_NONYLD_DISC", "forecastGroupDescription": "FG_QUALIFIED_NONYLD_NSB_1", "mktSegCode": "DISC_QN", "mktSegName": "DISC_QN", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Non-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_NONYLD_NSB_1", "forecastGroupName": "QUAL_LINKED_NONYLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_NONYLD_NSB_1", "mktSegCode": "DISC_QNL", "mktSegName": "DISC_QNL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Non-Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_SEMIYLD_NSB_2", "forecastGroupName": "QUAL_SEMIYLD_2", "forecastGroupDescription": "FG_QUALIFIED_SEMIYLD_NSB_2", "mktSegCode": "DISC_QS", "mktSegName": "DISC_QS", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Semi-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_SEMIYLD_NSB_1", "forecastGroupName": "QUAL_LINKED_SEMIYLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_SEMIYLD_NSB_1", "mktSegCode": "DISC_QSL", "mktSegName": "DISC_QSL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Semi-Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_YLD_NSB_2", "forecastGroupName": "QUAL_YLD_DISC_2", "forecastGroupDescription": "FG_QUALIFIED_YLD_NSB_2", "mktSegCode": "DISC_QY", "mktSegName": "DISC_QY", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_YLD_NSB_1", "forecastGroupName": "QUAL_LINKED_YLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_YLD_NSB_1", "mktSegCode": "DISC_QYL", "mktSegName": "DISC_QYL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_1", "forecastGroupName": "UNQUAL_DISC MKT", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_1", "mktSegCode": "DISC_U", "mktSegName": "DISC_U", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Unfenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_1", "forecastGroupName": "UNQUAL_DISC MKT", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_1", "mktSegCode": "DISC_UF", "mktSegName": "DISC_UF", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Fenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "DISC", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_3", "forecastGroupName": "UNQUAL_DISC MKT_3", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_3", "mktSegCode": "DISC_UFP", "mktSegName": "DISC_UFP", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Fenced Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "GOV", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_YLD_NSB_3", "forecastGroupName": "QUAL_YLD_GOV", "forecastGroupDescription": "FG_QUALIFIED_YLD_NSB_3", "mktSegCode": "GOV", "mktSegName": "GOV", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "GT ", "businessViewDescription": "", "forecastGroupCode": "FG_GROUP_NSB_2", "forecastGroupName": "GROUP_GT CONV", "forecastGroupDescription": "FG_GROUP_NSB_2", "mktSegCode": "GT", "mktSegName": "GT", "mktSegDescription": null, "businessType": "Group", "contract": "System Decision", "booking": "System Decision", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "IT", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_YLD_NSB_1", "forecastGroupName": "QUAL_LINKED_YLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_YLD_NSB_1", "mktSegCode": "IT_DEF_HIST", "mktSegName": "IT_DEF_HIST", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "IT", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_YLD_NSB_1", "forecastGroupName": "QUAL_LINKED_YLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_YLD_NSB_1", "mktSegCode": "IT_QYL", "mktSegName": "IT_QYL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "LNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_YLD_NSB_1", "forecastGroupName": "QUAL_YLD_CNR LNR", "forecastGroupDescription": "FG_QUALIFIED_YLD_NSB_1", "mktSegCode": "LNR_DEF_FUT", "mktSegName": "LNR_DEF_FUT", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "LNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_YLD_NSB_1", "forecastGroupName": "QUAL_YLD_CNR LNR", "forecastGroupDescription": "FG_QUALIFIED_YLD_NSB_1", "mktSegCode": "LNR_DEF_HIST", "mktSegName": "LNR_DEF_HIST", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "LNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_SEMIYLD_NSB_1", "forecastGroupName": "QUAL_SEMIYLD_CNR LNR", "forecastGroupDescription": "FG_QUALIFIED_SEMIYLD_NSB_1", "mktSegCode": "LNR_QS", "mktSegName": "LNR_QS", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Semi-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "LNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_SEMIYLD_NSB_1", "forecastGroupName": "QUAL_LINKED_SEMIYLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_SEMIYLD_NSB_1", "mktSegCode": "LNR_QSL", "mktSegName": "LNR_QSL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Semi-Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "LNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_YLD_NSB_1", "forecastGroupName": "QUAL_YLD_CNR LNR", "forecastGroupDescription": "FG_QUALIFIED_YLD_NSB_1", "mktSegCode": "LNR_QY", "mktSegName": "LNR_QY", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "LNR", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_YLD_NSB_1", "forecastGroupName": "QUAL_LINKED_YLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_YLD_NSB_1", "mktSegCode": "LNR_QYL", "mktSegName": "LNR_QYL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "MKT", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_1", "forecastGroupName": "UNQUAL_DISC MKT", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_1", "mktSegCode": "MKT_DEF_FUT", "mktSegName": "MKT_DEF_FUT", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Fenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "MKT", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_1", "forecastGroupName": "UNQUAL_DISC MKT", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_1", "mktSegCode": "MKT_DEF_HIST", "mktSegName": "MKT_DEF_HIST", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Fenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "MKT", "businessViewDescription": "", "forecastGroupCode": "FG_QUALIFIED_LINKED_NONYLD_NSB_1", "forecastGroupName": "QUAL_LINKED_NONYLD", "forecastGroupDescription": "FG_QUALIFIED_LINKED_NONYLD_NSB_1", "mktSegCode": "MKT_QNL", "mktSegName": "MKT_QNL", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Non-Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Non-Yieldable", "linked": "Yes", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "MKT", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_3", "forecastGroupName": "UNQUAL_DISC MKT_3", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_3", "mktSegCode": "MKT_U", "mktSegName": "MKT_U", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Unfenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "MKT", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_3", "forecastGroupName": "UNQUAL_DISC MKT_3", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_3", "mktSegCode": "MKT_UF", "mktSegName": "MKT_UF", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Fenced Non-Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "MKT", "businessViewDescription": "", "forecastGroupCode": "FG_UNQUALIFIED_NSB_3", "forecastGroupName": "UNQUAL_DISC MKT_3", "forecastGroupDescription": "FG_UNQUALIFIED_NSB_3", "mktSegCode": "MKT_UFP", "mktSegName": "MKT_UFP", "mktSegDescription": null, "businessType": "Transient", "contract": "Unqualified", "booking": "System Decision", "selling": "Fenced Packaged", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "PERM", "businessViewDescription": "", "forecastGroupCode": "FG_TRANSIENT_BLOCK_NSB_1", "forecastGroupName": "PERM", "forecastGroupDescription": "FG_TRANSIENT_BLOCK_NSB_1", "mktSegCode": "PERM", "mktSegName": "PERM", "mktSegDescription": null, "businessType": "Transient", "contract": "Qualified", "booking": "Block", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Non-Yieldable", "linked": "No", "pricedByBar": "No", "baseProduct": null}, {"businessViewName": "SMERF", "businessViewDescription": "", "forecastGroupCode": "FG_GROUP_NSB_1", "forecastGroupName": "GROUP_CMTG SMRF", "forecastGroupDescription": "FG_GROUP_NSB_1", "mktSegCode": "SMRF", "mktSegName": "SMRF", "mktSegDescription": null, "businessType": "Group", "contract": "System Decision", "booking": "System Decision", "selling": "System Decision", "forecastType": "De<PERSON> and Wash", "control": "Yieldable", "linked": "System Decision", "pricedByBar": "No", "baseProduct": null}]