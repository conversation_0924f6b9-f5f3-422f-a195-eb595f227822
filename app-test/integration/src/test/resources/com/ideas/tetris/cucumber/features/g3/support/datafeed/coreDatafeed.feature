@CoreDatafeed
Feature: Verify CORE bucket DataFeeds api

  Scenario: Verify CHANNEL_FORECAST Datafeed Endpoint with file ChannelForecast
    Given I select property "CPGP02" with propertyId "011032" for Core bucket apis
    Then Trigger "CHANNEL_FORECAST" api with dto "ChannelForecastDTO" and validate output with "ChannelForecast.json"


  Scenario: Verify MARKET_SEGMENT_CONFIG Datafeed Endpoint with file MarketSegmentConfig
    Given I select property "XNAES" with propertyId "010027" for Core bucket apis
    Then Trigger "MARKET_SEGMENT_CONFIG" api with dto "MarketSegmentConfig" and validate output with "MarketSegmentConfig.json"


  Scenario: Verify PROPERTY_BASIC_INFORMATION_ENHANCED_BOOKED_STATUS Datafeed Endpoint with file Informational
    Given I select property "XNAES" with propertyId "010027" for Core bucket apis
    And Set Toggle "pacman.feature.RatePlanConfigurationEnabled" to "false" for property "XNAES"
    And Set Toggle "pacman.preProduction.isForecastAndDecisionWindowEnabledInInformationalDatafeed" to "false" for property "XNAES"
    Then Trigger "PROPERTY_BASIC_INFORMATION_ENHANCED_BOOKED_STATUS" api with dto "PropertyBasicInformationEnhancedBookedStatus" and validate output with "Informational_WithBookedStatus.json"


  Scenario: Verify PROPERTY_BASIC_INFORMATION_ENHANCED_LAST_LIMITED_DATA_BUILD Datafeed Endpoint with file Informational
    Given I select property "XNAES" with propertyId "010027" for Core bucket apis
    And Set Toggle "pacman.feature.RatePlanConfigurationEnabled" to "true" for property "XNAES"
    And Set Toggle "pacman.preProduction.isForecastAndDecisionWindowEnabledInInformationalDatafeed" to "false" for property "XNAES"
    Then Trigger "PROPERTY_BASIC_INFORMATION_ENHANCED_LAST_LIMITED_DATA_BUILD" api with dto "PropertyBasicInformationEnhancedLimitedBuildDate" and validate output with "Informational_WithLimitedDataBuild.json"


  Scenario: Verify PROPERTY_BASIC_INFORMATION_ENHANCED_WITH_BOOKED_STATUS_AND_WINDOW_SETTINGS Datafeed Endpoint with file Informational
    Given I select property "XNAES" with propertyId "010027" for Core bucket apis
    And Set Toggle "pacman.feature.RatePlanConfigurationEnabled" to "false" for property "XNAES"
    And Set Toggle "pacman.preProduction.isForecastAndDecisionWindowEnabledInInformationalDatafeed" to "true" for property "XNAES"
    Then Trigger "PROPERTY_BASIC_INFORMATION_ENHANCED_WITH_BOOKED_STATUS_AND_WINDOW_SETTINGS" api with dto "PropertyInformationEnhancedBookedStatusAndLimitedBuildDateAndWindowSettings" and validate output with "Informational_WithBookedStatusAndWindowSettings.json"


  Scenario: Verify PROPERTY_BASIC_INFORMATION_ENHANCED_WITH_LAST_LIMITED_DATA_BUILD_AND_WINDOW_SETTINGS Datafeed Endpoint with file Informational
    Given I select property "XNAES" with propertyId "010027" for Core bucket apis
    And Set Toggle "pacman.feature.RatePlanConfigurationEnabled" to "true" for property "XNAES"
    And Set Toggle "pacman.preProduction.isForecastAndDecisionWindowEnabledInInformationalDatafeed" to "true" for property "XNAES"
    Then Trigger "PROPERTY_BASIC_INFORMATION_ENHANCED_WITH_LAST_LIMITED_DATA_BUILD_AND_WINDOW_SETTINGS" api with dto "PropertyInformationEnhancedBookedStatusAndLimitedBuildDateAndWindowSettings" and validate output with "Informational_WithBookedStatusAndLimitedBuildDateAndWindowSettings.json"


  Scenario: Verify ROOM_CLASS_CONFIGURATION Datafeed Endpoint with file RoomClassConfiguration
    Given I select property "XNAES" with propertyId "010027" for Core bucket apis
    Then Trigger "ROOM_CLASS_CONFIGURATION" api with dto "RoomClassConfiguration" and validate output with "RoomClassConfiguration.json"


  @RoomClassConfigEnhanced
  Scenario: Verify ROOM_CLASS_CONFIGURATION_WITH_DISCONTINUED_ROOM_TYPE_ENHANCED Datafeed Endpoint with file RoomClassConfiguration
    Given I select property "XNAES" with propertyId "010027" for Core bucket apis
    And Set isDiscontinued as "2" for accomType
    Then Trigger "ROOM_CLASS_CONFIGURATION_WITH_DISCONTINUED_ROOM_TYPE_ENHANCED" api with dto "RoomClassConfiguration" and validate output with "RoomClassConfigurationEnhanced.json"
