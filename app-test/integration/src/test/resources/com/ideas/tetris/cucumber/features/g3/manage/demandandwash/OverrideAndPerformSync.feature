@optimiseandOverride

Feature: Validate optimization on overrides

  Scenario: Perform CP overrides and verify the status of optimization job
    When Set OPT_OUTPUT_VERSION to 0 for "011033"
    When Set CP propertyID and Clean overrides
    When perform single day occupancy override for CP
    When perform multi day occupancy override for CP
    When perform single day wash override for CP
    Then verify that wash override data is populated in Wash_Forecast_Group_FCST_OVR table for CP
    When performing agile override
    When performing override for pricing management bar
    When Optimisation job is run for the test
    Then Verify the status of the optimisation job

  Scenario: Perform Non CP overrides and verify the status of optimization job
    When Set OPT_OUTPUT_VERSION to 0 for "010027"
    When Set NonCP propertyID and Clean overrides
    When perform single day occupancy override for Non CP
    When perform multi day occupancy override for Non CP
    When perform single day wash override for Non CP
    Then verify that wash override data is populated in Wash_Forecast_Group_FCST_OVR table for Non CP
    When Optimisation job is run for the test
    Then Verify the status of the optimisation job