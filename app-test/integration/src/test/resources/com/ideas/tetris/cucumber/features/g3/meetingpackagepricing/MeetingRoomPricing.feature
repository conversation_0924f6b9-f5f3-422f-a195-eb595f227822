@MeetingRoomPricing
Feature: Retrieve Meeting Room Pricing for Products

  As an API consumer
  I want to retrieve meeting room pricing details for given products and date range
  So that I can validate the pricing logic and data structure

  Scenario: Successfully retrieve meeting room pricing for given input
    Given the API endpoint is available
    And the request contains the following input:
      | Start Date       | 2022-09-06         |
      | End Date         | 2022-09-07         |
      | Product Ids      | 1,2,3,4,5,6        |
      | Meeting Room Ids | 9,10,11,12         |
    When the API is called with the above input
    Then the response status code should be 200
    And the response should contain product details with:
      | Field             |
      | productId         |
      | productName       |
      | linkedProduct     |
      | adjustment        |
      | meetingRoomPrices |

    And the response should contain meeting room prices with:
      | Field                          |
      | meetingRoomId                  |
      | meetingRoomName                |
      | meetingRoomPrice               |
      | totalPackagePrice              |
      | previousBar                    |
      | baseMeetingRoom                |
      | priceHigh                      |
      | specificOverride               |
      | floorOverride                  |
      | ceilingOverride                |
      | oldBar                         |
      | adjustmentValue                |
      | baseProduct                    |
      | baseProductPrice               |
      | baseProductSpecificOverride    |
      | baseProductFloorOverride       |
      | baseProductCeilingOverride     |
