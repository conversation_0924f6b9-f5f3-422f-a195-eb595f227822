package com.ideas.tetris.cucumber.stepdefinition.g3.manage.demandandwash

import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.TestUtil
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import com.ideas.tetris.util.testsuite.RestCallUtil
import common.JemsTest
import cucumber.api.groovy.EN
import cucumber.api.groovy.Hooks
import groovyx.net.http.ContentType

this.metaClass.mixin(Hooks)
this.metaClass.mixin(EN)

def propertyId
def client

def hashmap = new HashMap()
String currentDirectory = System.getProperty("user.dir")

Before('@optimiseandOverride'){
    cleaningOverrides(hashmap, 10027)
    cleaningOverrides(hashmap, 11033)
    def params = [username: "<EMAIL>", password: "password"]
    client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
}

When(~/perform single day occupancy override for Non CP$/) { ->
    String rateSeasonRequestBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/SingleDayOccupancyOverride1.json").text
    RestCallUtil.postCall("demandOverride/saveOccDemandOverride/v1", propertyId, hashmap, rateSeasonRequestBody, ContentType.JSON)
}

When(~/perform single day occupancy override for CP$/) { ->
    String rateSeasonRequestBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/SingleDayOccupancyOverride2.json").text
    RestCallUtil.postCall("demandOverride/saveOccDemandOverride/v1", propertyId, hashmap, rateSeasonRequestBody, ContentType.JSON)
}

Then(~/verify that occupancy override data is populated in Occupancy_Demand_FCST_OVR table for Non CP$/) { ->
    def queryStandard = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = 1 and Accom_Class_ID=3"
    def querySuite = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = 1 and Accom_Class_ID=2"
    def expectedValueForStandard = ["3", "2016-03-23", "0.28", "0.28", "0"]
    def expectedValueForSuite = ["3", "2016-03-23", "0.05", "0.05", "0"]
    dbverificationForOccupancyOverride(queryStandard, expectedValueForStandard, propertyId)
    dbverificationForOccupancyOverride(querySuite, expectedValueForSuite, propertyId)
}

Then(~/verify that occupancy override data is populated in Occupancy_Demand_FCST_OVR table for CP$/) { ->
    def queryStandard = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = 1 and Accom_Class_ID=3"
    def querySuite = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = 1 and Accom_Class_ID=2"
    def expectedValueForStandard = ["3", "2017-10-10", "0.28", "0.28", "0"]
    def expectedValueForSuite = ["3", "2017-10-10", "0.05", "0.05", "0"]
    dbverificationForOccupancyOverride(queryStandard, expectedValueForStandard, propertyId)
    dbverificationForOccupancyOverride(querySuite, expectedValueForSuite, propertyId)
}

When(~/perform single day arrival override for Non CP$/) { ->
    String rateSeasonRequestBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/SingleDayArrivalOverride1.json").text
    RestCallUtil.postCall("demandOverride/saveArrDemandOverride/v1", propertyId, hashmap, rateSeasonRequestBody, ContentType.JSON)
}

When(~/perform single day arrival override for CP$/) { ->
    String rateSeasonRequestBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/SingleDayArrivalOverride2.json").text
    RestCallUtil.postCall("demandOverride/saveArrDemandOverride/v1", propertyId, hashmap, rateSeasonRequestBody, ContentType.JSON)
}

Then(~/verify that arrival override data is populated in Arrival_Demand_FCST_OVR table$/) { ->
    verifySingleDayArrivalOverride(1, propertyId)
}

When(~/perform multi day occupancy override for CP$/) { ->
    String rateSeasonRequestBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/MultiDayOccupancyOverride2.json").text
    RestCallUtil.postCall("demandOverride/saveOccDemandOverride/v1", propertyId, hashmap, rateSeasonRequestBody, ContentType.JSON)
}

When(~/perform multi day occupancy override for Non CP$/) { ->
    String rateSeasonRequestBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/MultiDayOccupancyOverride1.json").text
    RestCallUtil.postCall("demandOverride/saveOccDemandOverride/v1", propertyId, hashmap, rateSeasonRequestBody, ContentType.JSON)
}

Then(~/verify that occupancy override data is populated in Occupancy_Demand_FCST_OVR table for multiday$/) { ->
    verifyMultiDayOccupancyOverride(1, propertyId)
}

When(~/perform multi day arrival override for CP$/) { ->
    String rateSeasonRequestBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/MultiDayArrivalOverride2.json").text
    RestCallUtil.postCall("demandOverride/saveArrDemandOverride/v1", propertyId, hashmap, rateSeasonRequestBody, ContentType.JSON)
}

When(~/perform multi day arrival override for Non CP$/) { ->
    String rateSeasonRequestBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/MultiDayArrivalOverride1.json").text
    RestCallUtil.postCall("demandOverride/saveArrDemandOverride/v1", propertyId, hashmap, rateSeasonRequestBody, ContentType.JSON)
}

Then(~/verify that arrival override data is populated in Arrival_Demand_FCST_OVR table for multiday$/) { ->
    verifyMultidayArrivalOverride(1,propertyId)
}

When(~/perform single day wash override for Non CP$/) { ->
    String washOverrideBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/SingleDayWashOverride1.json").text
    RestCallUtil.postCall("demandOverride/saveWashOverride/v1", propertyId, hashmap, washOverrideBody, ContentType.JSON)
}

When(~/perform single day wash override for CP$/) { ->
    String washOverrideBody = new File(currentDirectory + "/src/test/resources/DemandAndWashJson/SingleDayWashOverride2.json").text
    RestCallUtil.postCall("demandOverride/saveWashOverride/v1", propertyId, hashmap, washOverrideBody, ContentType.JSON)
}

Then(~/verify that wash override data is populated in Wash_Forecast_Group_FCST_OVR table for Non CP$/) { ->
    def query = "select Forecast_Group_ID, Occupancy_DT, System_Wash, User_Wash_OVR, Expiration_DT from Wash_Forecast_Group_FCST_OVR where Status_ID =1"
    def expectedValue = ["1", TestUtil.getDateFromEpoch(1458864000000), "0.00", "1.00", TestUtil.getDateFromEpoch(1458864000000)]
    dbverificationForWashOverride(query, expectedValue, propertyId)
}

Then(~/verify that wash override data is populated in Wash_Forecast_Group_FCST_OVR table for CP$/) { ->
    def query = "select Forecast_Group_ID, Occupancy_DT, System_Wash, User_Wash_OVR, Expiration_DT from Wash_Forecast_Group_FCST_OVR where Status_ID =1"
    def expectedValue = ["2", TestUtil.getDateFromEpoch(1509308000000), "0.00", "1.00",TestUtil.getDateFromEpoch(1509308000000) ]
    dbverificationForWashOverride(query, expectedValue, propertyId)
}

When(~/^Set NonCP propertyID and Clean overrides$/) { ->
    propertyId = 10027
    client.headers.propertyId = propertyId
    cleaningOverrides(hashmap, propertyId)
}

When(~/^Set OPT_OUTPUT_VERSION to 0 for "([^"]*)"$/) { String Db->
    def  connection = DbConnection.getTenantDatabaseConnection(Db)
    connection.execute("update IP_Cfg_Property_Attribute set Value=0 where IP_Cfg_Property_Attribute_ID=307")
    connection.close()
}

When(~/^Set CP propertyID and Clean overrides$/) { ->
    propertyId = 11033
    client.headers.propertyId = propertyId
    cleaningOverrides(hashmap, propertyId)
}
When(~/^performing override for pricing management bar$/) { ->
    def cpDecisionContextJSON = new File(currentDirectory + "/src/test/resources/PricingManagementJSON/CPBarOverrideJSON.json").text
    RestCallUtil.postCall("agentCPDecision/override/v1", propertyId, hashmap, cpDecisionContextJSON, ContentType.JSON)
}
When(~/^performing agile override$/) { ->
    PropertyRolloutRESTUtil.post(client,"productManagementService/saveOptimizedAgileProductOverrides/v1/17/2/2017-11-01/2017-11-10/17/10", null, null, false)
}

When(~/Optimisation job is run for the test$/) { ->
    hashmap.put("clientId","6")
    PropertyRolloutRESTUtil.post(client,"sync/syncAll/v1", null, hashmap, false)
}

Then(~/Verify the status of the optimisation job$/) { ->
    def query = "Select max(JOB_INSTANCE_ID) as jobid from [Job].[dbo].JOB_STATE"
    verifyJobStatus(query, "COMPLETED", propertyId)
}

static def getActiveOverrides(String tableName) {
    def connection = DbConnection.getDatabaseConnection_XNAES()
    def query = "select " + tableName + "_ID from " + tableName + " where Status_ID=1"
    return connection.rows(query).collect {d-> d.values().collect()[0]}
}

static def cleaningOverrides(HashMap hashmap, Integer propertyId) {
    hashmap.put("propertyId", propertyId)
    RestCallUtil.deleteCall("demandOverride/arrivalDemandOverride/v1", propertyId, hashmap)
    RestCallUtil.deleteCall("demandOverride/occupancyDemandOverride/v1", propertyId, hashmap)
    RestCallUtil.deleteCall("demandOverride/washOverride/v1", propertyId, hashmap)
}

static def dbverificationForOccupancyOverride(query, expectedvalue, propertyId) {
    def connection = getConnection(propertyId)
    def result = connection.rows(query)
    assert result.Forecast_Group_ID[0].toString().equalsIgnoreCase(expectedvalue[0]): "Actual Forecast_Group_ID value is - " + result.Forecast_Group_ID[0].toString() + " and Expected Start_Date_DT value is - " + expectedvalue[0]
    assert result.Occupancy_DT[0].toString().equalsIgnoreCase(expectedvalue[1]): "Actual Occupancy_DT value is - " + result.Occupancy_DT[0].toString() + " and Expected End_Date_DT value is - " + expectedvalue[1]
    assert result.Remaining_Demand[0].toString().equalsIgnoreCase(expectedvalue[2]): "Actual Remaining_Demand value is - " + result.Remaining_Demand[0].toString() + " and Expected Notes value is - " + expectedvalue[2]
    assert result.User_Demand_Override_Value[0].toString().equalsIgnoreCase(expectedvalue[3]): "Actual User_Demand_Override_Value value is - " + result.User_Demand_Override_Value[0].toString() + " and Expected Notes value is - " + expectedvalue[3]
    assert result.Rooms_sold[0].toString().equalsIgnoreCase(expectedvalue[4]): "Actual Rooms_sold value is - " + result.Rooms_sold[0].toString() + " and Expected Notes value is - " + expectedvalue[4]
    connection.close()
}

static def dbverificationForWashOverride(query, expectedvalue, propertyId) {
    def connection = getConnection(propertyId)
    def result = connection.rows(query)
    assert result.Forecast_Group_ID[0].toString().equalsIgnoreCase(expectedvalue[0]): "Actual Forecast_Group_ID value is - " + result.Forecast_Group_ID[0].toString() + " and Expected Start_Date_DT value is - " + expectedvalue[0]
    assert result.Occupancy_DT[0].toString().equalsIgnoreCase(expectedvalue[1]): "Actual Occupancy_DT value is - " + result.Occupancy_DT[0].toString() + " and Expected End_Date_DT value is - " + expectedvalue[1]
    assert result.System_Wash[0].toString().equalsIgnoreCase(expectedvalue[2]): "Actual Remaining_Demand value is - " + result.Remaining_Demand[0].toString() + " and Expected Notes value is - " + expectedvalue[2]
    assert result.User_Wash_OVR[0].toString().equalsIgnoreCase(expectedvalue[3]): "Actual User_Demand_Override_Value value is - " + result.User_Demand_Override_Value[0].toString() + " and Expected Notes value is - " + expectedvalue[3]
    assert result.Expiration_DT[0].toString().equalsIgnoreCase(expectedvalue[4]): "Actual Rooms_sold value is - " + result.Rooms_sold[0].toString() + " and Expected Notes value is - " + expectedvalue[4]
    connection.close()
}

static def dbverificationForArrivalOverride(query, expectedvalue, propertyId) {
    def connection = getConnection(propertyId)
    def result = connection.rows(query)
    assert result.Forecast_Group_ID[0].toString().equalsIgnoreCase(expectedvalue[0]): "Actual Forecast_Group_ID value is - " + result.Forecast_Group_ID[0].toString() + " and Expected Start_Date_DT value is - " + expectedvalue[0]
    assert result.Arrival_DT[0].toString().equalsIgnoreCase(expectedvalue[1]): "Actual Arrival_DT value is - " + result.Arrival_DT[0].toString() + " and Expected End_Date_DT value is - " + expectedvalue[1]
    assert result.Remaining_Demand[0].toString().equalsIgnoreCase(expectedvalue[2]): "Actual Remaining_Demand value is - " + result.Remaining_Demand[0].toString() + " and Expected Notes value is - " + expectedvalue[2]
    assert result.User_Demand_Override_Value[0].toString().equalsIgnoreCase(expectedvalue[3]): "Actual User_Demand_Override_Value value is - " + result.User_Demand_Override_Value[0].toString() + " and Expected Notes value is - " + expectedvalue[3]
    connection.close()
}

static def verifyMultidayArrivalOverride(statusId, propertyId) {
    def queryStandardLOS1
    def expectedValueForStandardLOS1
    def querySuiteLOS1
    def expectedValueForSuiteLOS1
    def queryStandardLOS9
    def expectedValueForStandardLOS9
    def querySuiteLOS9
    def expectedValueForSuiteLOS9
    def queryStandardLOS17
    def expectedValueForStandardLOS17
    def querySuiteLOS17
    def expectedValueForSuiteLOS17
    def queryStandardLOS25
    def expectedValueForStandardLOS25
    def querySuiteLOS25
    def expectedValueForSuiteLOS25
    def queryStandardLOS33
    def expectedValueForStandardLOS33
    def querySuiteLOS33
    def expectedValueForSuiteLOS33
    def queryStandardLOS41
    def expectedValueForStandardLOS41
    def querySuiteLOS41
    def expectedValueForSuiteLOS41
    def queryStandardLOS49
    def expectedValueForStandardLOS49
    def querySuiteLOS49
    def expectedValueForSuiteLOS49
    if(propertyId == 10027)
    {
        queryStandardLOS1 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2016-04-24'"
        expectedValueForStandardLOS1 = ["3", "2016-04-24", "0.68", "0.69"]

        querySuiteLOS1 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2016-04-24'"
        expectedValueForSuiteLOS1 = ["3", "2016-04-24", "0.15", "0.15"]

        queryStandardLOS9 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2016-04-25'"
        expectedValueForStandardLOS9 = ["3", "2016-04-25", "0.53", "0.54"]

        querySuiteLOS9 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2016-04-25'"
        expectedValueForSuiteLOS9 = ["3", "2016-04-25", "0.02", "0.02"]

        queryStandardLOS17 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2016-04-26'"
        expectedValueForStandardLOS17 = ["3", "2016-04-26", "1.44", "1.45"]

        querySuiteLOS17 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2016-04-26'"
        expectedValueForSuiteLOS17 = ["3", "2016-04-26", "0.36", "0.36"]

        queryStandardLOS25 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2016-04-27'"
        expectedValueForStandardLOS25 = ["3", "2016-04-27", "0.65", "0.66"]

        querySuiteLOS25 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2016-04-27'"
        expectedValueForSuiteLOS25 = ["3", "2016-04-27", "0.10", "0.10"]

        queryStandardLOS33 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2016-04-28'"
        expectedValueForStandardLOS33 = ["3", "2016-04-28", "0.65", "0.66"]

        querySuiteLOS33 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2016-04-28'"
        expectedValueForSuiteLOS33 = ["3", "2016-04-28", "0.04", "0.04"]

        queryStandardLOS41 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2016-04-29'"
        expectedValueForStandardLOS41 = ["3", "2016-04-29", "0.76", "0.77"]

        querySuiteLOS41 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2016-04-29'"
        expectedValueForSuiteLOS41 = ["3", "2016-04-29", "0.00", "0.00"]

        queryStandardLOS49 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2016-04-30'"
        expectedValueForStandardLOS49 = ["3", "2016-04-30", "1.30", "1.31"]

        querySuiteLOS49 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2016-04-30'"
        expectedValueForSuiteLOS49 = ["3", "2016-04-30", "0.29", "0.29"]
    }
    else
    {
        queryStandardLOS1 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2017-11-24'"
        expectedValueForStandardLOS1 = ["3", "2017-11-24", "0.68", "0.69"]

        querySuiteLOS1 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2017-11-24'"
        expectedValueForSuiteLOS1 = ["3", "2017-11-24", "0.15", "0.15"]

        queryStandardLOS9 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2017-11-25'"
        expectedValueForStandardLOS9 = ["3", "2017-11-25", "0.53", "0.54"]

        querySuiteLOS9 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2017-11-25'"
        expectedValueForSuiteLOS9 = ["3", "2017-11-25", "0.02", "0.02"]

        queryStandardLOS17 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2017-11-26'"
        expectedValueForStandardLOS17 = ["3", "2017-11-26", "1.44", "1.45"]

        querySuiteLOS17 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2017-11-26'"
        expectedValueForSuiteLOS17 = ["3", "2017-11-26", "0.36", "0.36"]

        queryStandardLOS25 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2017-11-27'"
        expectedValueForStandardLOS25 = ["3", "2017-11-27", "0.65", "0.66"]

        querySuiteLOS25 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2017-11-27'"
        expectedValueForSuiteLOS25 = ["3", "2017-11-27", "0.10", "0.10"]

        queryStandardLOS33 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2017-11-28'"
        expectedValueForStandardLOS33 = ["3", "2017-11-28", "0.65", "0.66"]

        querySuiteLOS33 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2017-11-28'"
        expectedValueForSuiteLOS33 = ["3", "2017-11-28", "0.04", "0.04"]

        queryStandardLOS41 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2017-11-29'"
        expectedValueForStandardLOS41 = ["3", "2017-11-29", "0.76", "0.77"]

        querySuiteLOS41 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2017-11-29'"
        expectedValueForSuiteLOS41 = ["3", "2017-11-29", "0.00", "0.00"]

        queryStandardLOS49 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1 and Arrival_DT = '2017-11-30'"
        expectedValueForStandardLOS49 = ["3", "2017-11-30", "1.30", "1.31"]

        querySuiteLOS49 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1 and Arrival_DT = '2017-11-30'"
        expectedValueForSuiteLOS49 = ["3", "2017-11-30", "0.29", "0.29"]
    }


    dbverificationForArrivalOverride(querySuiteLOS49, expectedValueForSuiteLOS49, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS49, expectedValueForStandardLOS49, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS41, expectedValueForSuiteLOS41, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS41, expectedValueForStandardLOS41, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS33, expectedValueForSuiteLOS33, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS33, expectedValueForStandardLOS33, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS25, expectedValueForSuiteLOS25, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS25, expectedValueForStandardLOS25, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS17, expectedValueForSuiteLOS17, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS17, expectedValueForStandardLOS17, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS9, expectedValueForSuiteLOS9, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS9, expectedValueForStandardLOS9, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS1, expectedValueForSuiteLOS1, propertyId )
    dbverificationForArrivalOverride(queryStandardLOS1, expectedValueForStandardLOS1, propertyId)
}

static def verifyMultiDayOccupancyOverride(statusId, propertyId) {
    def queryStandard12
    def queryStandard13
    def queryStandard14
    def queryStandard15
    def queryStandard16
    def queryStandard17
    def queryStandard18
    def querySuite12
    def querySuite13
    def querySuite14
    def querySuite15
    def querySuite16
    def querySuite17
    def querySuite18
    def expectedValueForStandard12
    def expectedValueForStandard13
    def expectedValueForStandard14
    def expectedValueForStandard15
    def expectedValueForStandard16
    def expectedValueForStandard17
    def expectedValueForStandard18
    def expectedValueForSuite12
    def expectedValueForSuite13
    def expectedValueForSuite14
    def expectedValueForSuite15
    def expectedValueForSuite16
    def expectedValueForSuite17
    def expectedValueForSuite18
    if(propertyId == 11033)
    {
        queryStandard12 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2017-10-15'"
        queryStandard13 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2017-10-16'"
        queryStandard14 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2017-10-17'"
        queryStandard15 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2017-10-18'"
        queryStandard16 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2017-10-19'"
        queryStandard17 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2017-10-20'"
        queryStandard18 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2017-10-21'"

        querySuite12 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2017-10-15'"
        querySuite13 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2017-10-16'"
        querySuite14 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2017-10-17'"
        querySuite15 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2017-10-18'"
        querySuite16 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2017-10-19'"
        querySuite17 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2017-10-20'"
        querySuite18 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2017-10-21'"

        expectedValueForStandard12 = ["3", "2017-10-15", "0.00", "0.00", "0"]
        expectedValueForStandard13 = ["3", "2017-10-16", "0.06", "0.06", "0"]
        expectedValueForStandard14 = ["3", "2017-10-17", "0.00", "0.00", "0"]
        expectedValueForStandard15 = ["3", "2017-10-18", "0.00", "0.00", "0"]
        expectedValueForStandard16 = ["3", "2017-10-19", "0.00", "0.00", "0"]
        expectedValueForStandard17 = ["3", "2017-10-20", "0.00", "0.00", "0"]
        expectedValueForStandard18 = ["3", "2017-10-21", "0.04", "0.04", "0"]

        expectedValueForSuite12 = ["3", "2017-10-15", "0.05", "0.05", "0"]
        expectedValueForSuite13 = ["3", "2017-10-16", "0.02", "0.02", "0"]
        expectedValueForSuite14 = ["3", "2017-10-17", "0.05", "0.05", "0"]
        expectedValueForSuite15 = ["3", "2017-10-18", "0.00", "0.00", "0"]
        expectedValueForSuite16 = ["3", "2017-10-19", "0.04", "0.04", "0"]
        expectedValueForSuite17 = ["3", "2017-10-20", "0.00", "0.00", "0"]
        expectedValueForSuite18 = ["3", "2017-10-21", "0.07", "0.07", "0"]
    }
    else
    {
        queryStandard12 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2016-04-12'"
        queryStandard13 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2016-04-13'"
        queryStandard14 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2016-04-14'"
        queryStandard15 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2016-04-15'"
        queryStandard16 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2016-04-16'"
        queryStandard17 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2016-04-17'"
        queryStandard18 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and Occupancy_DT = '2016-04-18'"

        querySuite12 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2016-04-12'"
        querySuite13 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2016-04-13'"
        querySuite14 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2016-04-14'"
        querySuite15 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2016-04-15'"
        querySuite16 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2016-04-16'"
        querySuite17 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2016-04-17'"
        querySuite18 = "select Forecast_Group_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value, Rooms_sold from Occupancy_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and Occupancy_DT = '2016-04-18'"

        expectedValueForStandard12 = ["3", "2016-04-12", "0.00", "0.00", "0"]
        expectedValueForStandard13 = ["3", "2016-04-13", "0.00", "0.00", "0"]
        expectedValueForStandard14 = ["3", "2016-04-14", "0.06", "0.06", "0"]
        expectedValueForStandard15 = ["3", "2016-04-15", "0.00", "0.00", "0"]
        expectedValueForStandard16 = ["3", "2016-04-16", "0.00", "0.00", "0"]
        expectedValueForStandard17 = ["3", "2016-04-17", "0.00", "0.00", "0"]
        expectedValueForStandard18 = ["3", "2016-04-18", "0.04", "0.04", "0"]

        expectedValueForSuite12 = ["3", "2016-04-12", "0.00", "0.00", "0"]
        expectedValueForSuite13 = ["3", "2016-04-13", "0.00", "0.00", "0"]
        expectedValueForSuite14 = ["3", "2016-04-14", "0.02", "0.02", "0"]
        expectedValueForSuite15 = ["3", "2016-04-15", "0.04", "0.04", "0"]
        expectedValueForSuite16 = ["3", "2016-04-16", "0.05", "0.05", "0"]
        expectedValueForSuite17 = ["3", "2016-04-17", "0.05", "0.05", "0"]
        expectedValueForSuite18 = ["3", "2016-04-18", "0.07", "0.07", "0"]
    }
    dbverificationForOccupancyOverride(queryStandard12, expectedValueForStandard12, propertyId)
    dbverificationForOccupancyOverride(queryStandard13, expectedValueForStandard13, propertyId)
    dbverificationForOccupancyOverride(queryStandard14, expectedValueForStandard14, propertyId)
    dbverificationForOccupancyOverride(queryStandard15, expectedValueForStandard15, propertyId)
    dbverificationForOccupancyOverride(queryStandard16, expectedValueForStandard16, propertyId)
    dbverificationForOccupancyOverride(queryStandard17, expectedValueForStandard17, propertyId)
    dbverificationForOccupancyOverride(queryStandard18, expectedValueForStandard18, propertyId)

    dbverificationForOccupancyOverride(querySuite12, expectedValueForSuite12, propertyId)
    dbverificationForOccupancyOverride(querySuite13, expectedValueForSuite13, propertyId)
    dbverificationForOccupancyOverride(querySuite14, expectedValueForSuite14, propertyId)
    dbverificationForOccupancyOverride(querySuite15, expectedValueForSuite15, propertyId)
    dbverificationForOccupancyOverride(querySuite16, expectedValueForSuite16, propertyId)
    dbverificationForOccupancyOverride(querySuite17, expectedValueForSuite17, propertyId)
    dbverificationForOccupancyOverride(querySuite18, expectedValueForSuite18, propertyId)
}

static def verifySingleDayArrivalOverride(statusId, propertyId) {
    def queryStandardLOS1 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=1"
    def queryStandardLOS2 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=2"
    def queryStandardLOS3 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=3"
    def queryStandardLOS4 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=4"
    def queryStandardLOS5 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=5"
    def queryStandardLOS6 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=6"
    def queryStandardLOS7 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=7"
    def queryStandardLOS8 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=2 and LOS=8"

    def querySuiteLOS1 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=1"
    def querySuiteLOS2 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=2"
    def querySuiteLOS3 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=3"
    def querySuiteLOS4 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=4"
    def querySuiteLOS5 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=5"
    def querySuiteLOS6 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=6"
    def querySuiteLOS7 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=7"
    def querySuiteLOS8 = "select Forecast_Group_ID, Arrival_DT, Remaining_Demand, User_Demand_Override_Value from Arrival_Demand_FCST_OVR where Status_ID = "+statusId+" and Accom_Class_ID=3 and LOS=8"

    def expectedValueForStandardLOS1
    def expectedValueForStandardLOS2
    def expectedValueForStandardLOS3
    def expectedValueForStandardLOS4
    def expectedValueForStandardLOS5
    def expectedValueForStandardLOS6
    def expectedValueForStandardLOS7
    def expectedValueForStandardLOS8

    def expectedValueForSuiteLOS1
    def expectedValueForSuiteLOS2
    def expectedValueForSuiteLOS3
    def expectedValueForSuiteLOS4
    def expectedValueForSuiteLOS5
    def expectedValueForSuiteLOS6
    def expectedValueForSuiteLOS7
    def expectedValueForSuiteLOS8
    if(propertyId == 10027){
         expectedValueForStandardLOS1 = ["3", "2016-03-27", "0.09", "0.09"]
         expectedValueForStandardLOS2 = ["3", "2016-03-27", "0.04", "0.04"]
         expectedValueForStandardLOS3 = ["3", "2016-03-27", "0.02", "0.02"]
         expectedValueForStandardLOS4 = ["3", "2016-03-27", "0.01", "0.01"]
         expectedValueForStandardLOS5 = ["3", "2016-03-27", "0.00", "0.00"]
         expectedValueForStandardLOS6 = ["3", "2016-03-27", "0.00", "0.00"]
         expectedValueForStandardLOS7 = ["3", "2016-03-27", "0.00", "0.00"]
         expectedValueForStandardLOS8 = ["3", "2016-03-27", "0.00", "0.00"]

         expectedValueForSuiteLOS1 = ["3", "2016-03-27", "0.07", "0.07"]
         expectedValueForSuiteLOS2 = ["3", "2016-03-27", "0.01", "0.01"]
         expectedValueForSuiteLOS3 = ["3", "2016-03-27", "0.01", "0.01"]
         expectedValueForSuiteLOS4 = ["3", "2016-03-27", "0.01", "0.01"]
         expectedValueForSuiteLOS5 = ["3", "2016-03-27", "0.00", "0.00"]
         expectedValueForSuiteLOS6 = ["3", "2016-03-27", "0.00", "0.00"]
         expectedValueForSuiteLOS7 = ["3", "2016-03-27", "0.00", "0.00"]
         expectedValueForSuiteLOS8 = ["3", "2016-03-27", "0.00", "0.00"]
    }else{
         expectedValueForStandardLOS1 = ["3", "2017-12-01", "0.09", "0.09"]
         expectedValueForStandardLOS2 = ["3", "2017-12-01", "0.04", "0.04"]
         expectedValueForStandardLOS3 = ["3", "2017-12-01", "0.02", "0.02"]
         expectedValueForStandardLOS4 = ["3", "2017-12-01", "0.01", "0.01"]
         expectedValueForStandardLOS5 = ["3", "2017-12-01", "0.00", "0.00"]
         expectedValueForStandardLOS6 = ["3", "2017-12-01", "0.00", "0.00"]
         expectedValueForStandardLOS7 = ["3", "2017-12-01", "0.00", "0.00"]
         expectedValueForStandardLOS8 = ["3", "2017-12-01", "0.00", "0.00"]

         expectedValueForSuiteLOS1 = ["3", "2017-12-01", "0.07", "0.07"]
         expectedValueForSuiteLOS2 = ["3", "2017-12-01", "0.01", "0.01"]
         expectedValueForSuiteLOS3 = ["3", "2017-12-01", "0.01", "0.01"]
         expectedValueForSuiteLOS4 = ["3", "2017-12-01", "0.01", "0.01"]
         expectedValueForSuiteLOS5 = ["3", "2017-12-01", "0.00", "0.00"]
         expectedValueForSuiteLOS6 = ["3", "2017-12-01", "0.00", "0.00"]
         expectedValueForSuiteLOS7 = ["3", "2017-12-01", "0.00", "0.00"]
         expectedValueForSuiteLOS8 = ["3", "2017-12-01", "0.00", "0.00"]
    }

    dbverificationForArrivalOverride(queryStandardLOS1, expectedValueForStandardLOS1, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS2, expectedValueForStandardLOS2, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS3, expectedValueForStandardLOS3, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS4, expectedValueForStandardLOS4, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS5, expectedValueForStandardLOS5, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS6, expectedValueForStandardLOS6, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS7, expectedValueForStandardLOS7, propertyId)
    dbverificationForArrivalOverride(queryStandardLOS8, expectedValueForStandardLOS8, propertyId)

    dbverificationForArrivalOverride(querySuiteLOS1, expectedValueForSuiteLOS1, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS2, expectedValueForSuiteLOS2, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS3, expectedValueForSuiteLOS3, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS4, expectedValueForSuiteLOS4, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS5, expectedValueForSuiteLOS5, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS6, expectedValueForSuiteLOS6, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS7, expectedValueForSuiteLOS7, propertyId)
    dbverificationForArrivalOverride(querySuiteLOS8, expectedValueForSuiteLOS8, propertyId)
}

static def verifyJobStatus( query, expectedValue, propertyId) {
    def connection = getConnection(propertyId)
    def jobid
    def result = connection.rows(query)
    jobid = result.jobid[0].toString()
    def status = "Running"
    while(status.equalsIgnoreCase("Running"))
    {
        result = connection.rows("Select EXECUTION_STATUS from [Job].[dbo].JOB_STATE where JOB_INSTANCE_ID="+ jobid)
        if(result.EXECUTION_STATUS[0].toString().equalsIgnoreCase("COMPLETED"))
        {
            status = "COMPLETED"
        }
        if(result.EXECUTION_STATUS[0].toString().equalsIgnoreCase("FAILED"))
        {
            status = "FAILED"
        }
    }
    assert status.equalsIgnoreCase(expectedValue): "JOB STATUS Expected Value is: "+ expectedValue + " Actual Value is: "+ status
    print "Status Matched"
}

static def getConnection(propertyId) {
    def connection
    if(propertyId == 10027){
        connection = DbConnection.getDatabaseConnection_XNAES()
    }else{
        connection = DbConnection.getDatabaseConnection_CPGP02()
    }
    return connection
}