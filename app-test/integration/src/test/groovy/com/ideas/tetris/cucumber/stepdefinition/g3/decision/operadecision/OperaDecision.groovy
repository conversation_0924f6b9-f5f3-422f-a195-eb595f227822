package com.ideas.tetris.cucumber.stepdefinition.g3.decision.operadecision

import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.ClientLocator
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.PropertiesTestHelper
import com.ideas.tetris.util.StreamUtil
import com.ideas.tetris.util.opera.OperaGlobalFunctions
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import com.ideas.tetris.util.propertyrollout.PropertyRolloutTestHelper
import common.InsertAnalyticalMarketSegmentQuery
import common.JemsTest
import common.OperaUtil
import groovy.json.JsonSlurper
import groovy.transform.Field
import groovyx.net.http.ContentType
import groovyx.net.http.HttpResponseException
import cucumber.api.groovy.EN
import cucumber.api.groovy.Hooks

import static org.junit.Assert.assertEquals
import static org.junit.Assert.assertTrue

this.metaClass.mixin(Hooks)
this.metaClass.mixin(EN)

@Field String DatabaseName = "011022"
@Field String propertyId = "11022"
@Field rdsDecFolder
@Field String rdsPMSId = "123"
@Field rdsDecisionFileName
@Field String vendorPartner = "opera"
@Field TetrisRESTClient tetrisRESTClient
@Field String UPDATTE_OPT_VERSION = "update IP_Cfg_Property_Attribute set Value=0 where IP_Cfg_Property_Attribute_ID=307"

Before('@OperaDecision') {
    clearDecisionTables(DatabaseName);
    setupRDSDecisionFolder();
    setGlobalParameters(propertyId);
    fixGroupDataIssueForTheProperty(DatabaseName);
    insertRateQualified(DatabaseName);
    insertRateQualifiedDetails(DatabaseName)
    insertDailyBarConfiguration(DatabaseName);
    insertBasicDataForDataLoadJob(DatabaseName);
}


public void setupRDSDecisionFolder() {
    def props = new java.util.Properties()
    props.load(StreamUtil.class.getResourceAsStream("/env.properties"))
    Map<String, Integer> additionalProperties = new HashMap<String, Integer>();
    additionalProperties.put("SandBox.ClientID", ClientLocator.getSandboxClientID());
    props.putAll(additionalProperties);
    String tetrisDataDir = props.getProperty("tetrisDataDir")

    rdsDecFolder = tetrisDataDir + "/RDSDecisions"

    println("********** Deleting RDS Decision File Starts **********")
    def rdsDecFilePath = new File(rdsDecFolder)
    if (rdsDecFilePath.exists()) {
        rdsDecFilePath.deleteDir()
    }
    println("********** Deleting RDS Decision File Ends **********")
    println("********** Creating RDS Decision Folder Starts **********")
    if (!rdsDecFilePath.exists()) {
        rdsDecFilePath.mkdir();
    }
    println("********** Creating RDS Decision Folder Ends **********")


}

void fixGroupDataIssueForTheProperty(String DatabaseName) {
    def sqlStr = "update gm set gm.Group_Name = left(hgm.Block_Code +'_' + hgm.Group_Name,50) ,gm.Group_code= hgm.Group_ID " +
            "from Group_Master as gm inner join opera.G3_Group_Master_Link as ggml on gm.Group_ID=ggml.G3_Group_ID inner join " +
            "(select distinct Group_ID, Block_Code,Group_Name from opera.History_Group_Master ) " +
            "as hgm on ggml.Opera_Group_ID =hgm.Group_ID"

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName);
    connection.execute(sqlStr);
    connection.close();
}

void clearDecisionTables(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("delete from Decision_Bar_Output");
    connection.execute("delete from Decision_Dailybar_Output");
    connection.execute("delete from Decision_LRV");
    connection.execute("delete from Decision_MINLOS");
    connection.execute("delete from Decision_Ovrbk_Accom");
    connection.execute("delete from Decision_Ovrbk_Property");
    connection.execute("delete from Decision_Qualified_FPLOS");
    connection.execute("delete from Decision_FPLOS_By_Hierarchy");
    connection.execute("delete from Decision_FPLOS_By_RoomType");

    connection.execute("delete from Pace_FPLOS_By_RoomType");
    connection.execute("delete from Pace_FPLOS_By_Hierarchy");
    connection.execute("delete from PACE_MINLOS");
    connection.execute("delete from PACE_Qualified_FPLOS");
    connection.execute("delete from PACE_LRV");
    connection.execute("delete from pace_Bar_Output");
    connection.execute("delete from PACE_Ovrbk_Accom");
    connection.execute("delete from pace_Ovrbk_Property");
    connection.execute("delete from Pace_Dailybar_Output");
    connection.execute("delete from Wash_Ind_Group_Fcst");
    connection.execute("delete from LRA_Restriction_Mapping");
    connection.execute("delete from Rate_Qualified_Details");
    connection.execute("delete from Rate_Qualified");
    //Upload Window Parameters
    connection.execute("delete from PACE_LRV_NOTIFICATION");
    connection.execute("delete from PACE_Bar_Output_NOTIFICATION");
    connection.execute("delete from Pace_Ovrbk_Accom_NOTIFICATION");
    connection.execute("delete from pace_Ovrbk_Property_NOTIFICATION");
    connection.execute("delete from PACE_Mkt_Occupancy_FCST_NOTIFICATION");
    connection.execute("delete from PACE_Accom_Occupancy_FCST_NOTIFICATION");
    connection.execute("delete from PACE_Ovrbk_property_Upload");
    connection.execute("delete from PACE_Ovrbk_Accom_Upload");
    connection.execute("delete from PACE_Bar_Output_Upload");
    connection.close();
}

void insertRateQualified(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("insert into Rate_Qualified (File_Metadata_ID,Property_ID,Rate_Code_Name,Rate_Code_Description,Rate_Code_Currency,Start_Date_DT,End_Date_DT,Yieldable,Price_Relative,Reference_Rate_Code,Includes_Package,Last_Updated_DTTM,Status_ID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID, managed_in_g3 ) values (3,11022,'RQ1','RQ1','USD','2010-07-14','2073-07-14',1,0,'None',1,'2013-06-26 18:34:29.837',1,1,'2013-06-26 18:34:29.837',1,1)");
    connection.execute("insert into Rate_Qualified (File_Metadata_ID,Property_ID,Rate_Code_Name,Rate_Code_Description,Rate_Code_Currency,Start_Date_DT,End_Date_DT,Yieldable,Price_Relative,Reference_Rate_Code,Includes_Package,Last_Updated_DTTM,Status_ID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID, managed_in_g3 ) values (3,11022,'RQ2','RQ2','USD','2010-07-14','2073-07-14',1,0,'None',1,'2013-06-26 18:34:29.837',1,1,'2013-06-26 18:34:29.837',1,1)");
    connection.close();
}

void insertRateQualifiedDetails(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)

    def rateID = connection.firstRow("select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name='RQ1'")
    def rateIDRQ1 = rateID[0];
    rateID = connection.firstRow("select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name='RQ2'")
    def rateIDRQ2 = rateID[0];

    for (int accomTypeid = 1; accomTypeid <= 8; accomTypeid++) {
        connection.execute("insert into [Rate_Qualified_Details] (Rate_Qualified_ID,Accom_Type_ID,Start_Date_DT,End_Date_DT,Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM) values(" + rateIDRQ1 + "," + accomTypeid + ",'2010-07-14','2073-07-14',55,55,55,55,55,55,55,1,GETDATE(),1,GETDATE())");
        connection.execute("insert into [Rate_Qualified_Details] (Rate_Qualified_ID,Accom_Type_ID,Start_Date_DT,End_Date_DT,Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM) values(" + rateIDRQ2 + "," + accomTypeid + ",'2010-07-14','2073-07-14',55,55,55,55,55,55,55,1,GETDATE(),1,GETDATE())");
    }
    connection.close();
}

void insertDailyBarConfiguration(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("delete Daily_Bar_Config");
    connection.execute("delete Daily_Bar_Rate_Chart");

    connection.execute("insert into  Daily_Bar_Rate_Chart values('FIXED',100,100,100,100,100,100,100,100,NULL,1,GETDATE(),1,GETDATE())");
    def rateID = connection.firstRow("select max(Daily_Bar_Rate_chart_id) from Daily_Bar_Rate_Chart")
    def rateChartID = rateID[0];

    connection.execute("insert into Daily_Bar_Config values('LV0','QR','2010-10-30','2073-10-30'," + rateChartID + "," + rateChartID + "," + rateChartID + "," + rateChartID + ", NULL , NULL , NULL,1,GETDATE(),1,GETDATE())");
    connection.execute("insert into Daily_Bar_Config values('LV0','QS','2010-10-30','2073-10-30'," + rateChartID + "," + rateChartID + "," + rateChartID + "," + rateChartID + ", NULL, NULL, NULL,1,GETDATE(),1,GETDATE())");
    connection.execute("insert into Daily_Bar_Config values('LV0','TS','2010-10-30','2073-10-30'," + rateChartID + "," + rateChartID + "," + rateChartID + "," + rateChartID + ", NULL, NULL, NULL,1,GETDATE(),1,GETDATE())");
    connection.execute("insert into Daily_Bar_Config values(NULL,'QR','2010-10-30','2073-10-30'," + rateChartID + "," + rateChartID + "," + rateChartID + "," + rateChartID + ", NULL, NULL, NULL,1,GETDATE(),1,GETDATE())");
    connection.execute("insert into Daily_Bar_Config values(NULL,'QS','2010-10-30','2073-10-30'," + rateChartID + "," + rateChartID + "," + rateChartID + "," + rateChartID + ", NULL, NULL, NULL,1,GETDATE(),1,GETDATE())");
    connection.execute("insert into Daily_Bar_Config values(NULL,'TS','2010-10-30','2073-10-30'," + rateChartID + "," + rateChartID + "," + rateChartID + "," + rateChartID + ", NULL, NULL, NULL,1,GETDATE(),1,GETDATE())");
    connection.close();
}

Given(~/I change Property Stage to TwoWay and set AutoDetectSufficientBookedRTPace to false$/) { ->
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def context = 'pacman.SandBox.OPER2';
    PropertiesTestHelper.setPropertyStage(client, propertyId, PropertyRolloutTestHelper.STAGE_TWO_WAY)
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.ams.autoDetectSufficientBookedRTPace', context, 'false');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.RoomTypeRecodingEnabled', context, 'false')
}

Given(~/LRA Restriction Mapping is set for Rate Code Level$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def rateID = connection.firstRow("select Rate_Qualified_ID from Rate_Qualified where Rate_Code_Name='RQ1'")
    def rateIDRQ1 = rateID[0];
    connection.call("INSERT [dbo].[LRA_Restriction_Mapping] ([Restriction_Type], [Rate_Qualified_ID], [Rate_Category], [Created_DTTM], [Created_By_User_ID], [Last_Updated_DTTM], [Last_Updated_By_User_ID]) VALUES ('RATE_CODE_LEVEL'," + rateIDRQ1 + ", NULL, GETDATE(), 11403, GETDATE(), 11403)");
    connection.close();
}

void setGlobalParameters(String propertyId) {
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def context = 'pacman.SandBox.OPER2';
    def contextSynxix = 'SynXis.SandBox.OPER2';
    client.headers.propertyId = propertyId
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.core.property.externalSystem', context, 'OPERA');
    PropertyRolloutRESTUtil.clearSyncEvent(client, 'ACCOMMODATION_CONFIG_CHANGED', context)

    //Opera
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.receivingsystems', context, 'opera');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.dailybarPMSRateCode', context, 'TestCode');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.DailyBAR.dailybarRateCode', contextSynxix, 'TestCodeSynexis');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.dailyBAR.useBaseRateAsSingleRate', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.dailyBAR.useExtraAdultConfig', context, 'true');
    //BARDecision Level
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.bar.barDecision', context, 'BARByLOS');

    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.population.analyticalMarketSegmentMappingComplete', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.AnalyticalMarketSegmentEnabled', context, 'true');


    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.dailyBAR.useExtraChildConfig', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.MinimumLengthOfStayByRateCode.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.DailyBar.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.LastRoomValuebyRoomClass.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.RoomTypeOverbooking.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.HotelOverbooking.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.BarFPLOSByHierarchy.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.FplosByRateCodeByRoomType.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.BarByLOS.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.LRAControlFPLOS.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.LRAControlMinLOS.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.BarFplosByRoomType.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.core.HistoricalDataAvailabilityDays', context, '730');
    //Reserve
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.RESERVE.HotelOverbooking.uploadtype', context, 'none');
    //ORS
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.ORS.DailyBAR.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.ORS.DBAR.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.ORS.LastRoomValuebyRoomClass.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.ORS.HotelOverbooking.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.ORS.RoomTypeOverbooking.uploadtype', context, 'full');
    //MYFIDELIO
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.MYFIDELIO.LastRoomValuebyRoomClass.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.MYFIDELIO.HotelOverbooking.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.MYFIDELIO.RoomTypeOverbooking.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.MYFIDELIO.DailyBAR.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.MYFIDELIO.BarFplosByHierarchy.uploadtype', context, 'full');
    //RDS
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.RDS.decisionFilePath', context, rdsDecFolder);
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.RDS.pmsSideId', context, '123');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.RDS.BarByLOSatRoomClass.uploadtype', context, 'full');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.RDS.BarByLOSatRoomType.uploadtype', context, 'none');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.optimization.optimizationWindowCDP', context, '45');


    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.PreProduction.ExtendedLOSDataEnabled', context, 'TRUE');
    //Decision Upload Window Parameter
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.optimization.optimizationWindowBDE', context, '365');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.upload.DecisionUploadWindowBDEDays', context, '365');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.ZeroCapacityRoomTypesToInclude', context, 'TS');

    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.DeleteDecisionsForZeroCapacityAccomTypesEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.SendRoomTypesAsRoomClassLRV', context, 'true');

    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.SufficientBookedDataAlertEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.AlertUserForCanceledGroupsHavingDemandWashOverrideEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.isGroupMasterPaceEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.usePaceGroupMasterForBackFill', context, 'true');

}

void insertBasicDataForDataLoadJob(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    String correlationID = OperaGlobalFunctions.getCorrelationID()
    connection.execute("update opera.History_Incoming_Metadata set Business_Time=(select convert(varchar(8),(DATEADD(MINUTE, 20, (SELECT convert(varchar(8), (select Business_Time from opera.History_Incoming_Metadata where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)), 108)))),108)    ) where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)")
    connection.execute("update opera.History_Incoming_Metadata set Prepared_Time=(select convert(varchar(8),(DATEADD(MINUTE, 20, (SELECT convert(varchar(8), (select Business_Time from opera.History_Incoming_Metadata where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)), 108)))),108)    ) where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)")
    connection.execute("update opera.Data_Load_Metadata   set Correlation_ID = '" + correlationID + "' where Correlation_ID = (select correlation_ID from opera.Data_Load_Metadata where \n" +
            "Data_Load_Metadata_ID = (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata))")
    connection.execute("update opera.History_Transaction set Market_Code='FI' where Market_Code='CDP' and Data_Load_Metadata_ID=1602")
    //connection.execute("update opera.History_Occupancy_Summary set Room_Type='CON', Physical_Rooms=10 where Room_Type='PM' and Raw_Occupancy_Summary_ID=10140286")
    connection.execute("INSERT INTO [opera].[History_Occupancy_Summary] ([Resort],[Occupancy_DT]\n" +
            "           ,[Market_Code],[Physical_Rooms],[Out_Of_Order_Rooms],[Out_Of_Service_Rooms]\n" +
            "           ,[Rooms_Sold],[Room_Revenue],[Room_Arrivals],[Room_Departures],[Total_Revenue]\n" +
            "           ,[Food_Revenue],[Cancelled_Rooms],[No_Show_Rooms],[Reservation_Type]\n" +
            "           ,[Room_Type],[Data_Load_Metadata_ID])\n" +
            "     VALUES ('OPERATEST','2013-07-28',NULL,10,1,0,0,'796.296296296296',0\n" +
            "           ,0,'796.296296296296',0,0,0,'','CON',1607)")

    connection.execute(UPDATTE_OPT_VERSION)

    connection.close();
}

void introduceNewMarketSegment(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    String correlationID = OperaGlobalFunctions.getCorrelationID()
    connection.execute("update opera.History_Incoming_Metadata set Business_Time=(select convert(varchar(8),(DATEADD(MINUTE, 20, (SELECT convert(varchar(8), (select Business_Time from opera.History_Incoming_Metadata where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)), 108)))),108)    ) where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)")
    connection.execute("update opera.History_Incoming_Metadata set Prepared_Time=(select convert(varchar(8),(DATEADD(MINUTE, 20, (SELECT convert(varchar(8), (select Business_Time from opera.History_Incoming_Metadata where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)), 108)))),108)    ) where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)")
    connection.execute("update opera.Data_Load_Metadata   set Correlation_ID = '" + correlationID + "' where Correlation_ID = (select correlation_ID from opera.Data_Load_Metadata where \n" +
            "Data_Load_Metadata_ID = (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata))")
    connection.execute("update opera.History_Transaction set Market_Code='CDP' where Market_Code='FI' and Data_Load_Metadata_ID=1602")
    connection.execute("update opera.History_Occupancy_Summary set Physical_Rooms=205,Out_Of_Order_Rooms=1,Out_Of_Service_Rooms=1,Rooms_Sold=4,Room_Revenue='0.129529518519',Room_Arrivals=3,Room_Departures=2,Food_Revenue='0.029529518519'\n" +
            " where Market_Code='HU' and Data_Load_Metadata_ID='1600' and Occupancy_DT='2013-10-11' and Room_Type='QR'")
    connection.execute("update opera.History_Occupancy_Summary set Physical_Rooms=205,Out_Of_Order_Rooms=1,Out_Of_Service_Rooms=1,Rooms_Sold=9,Room_Revenue='0.129529518519',Room_Arrivals=3,Room_Departures=2,Food_Revenue='0.029529518519'\n" +
            " where Market_Code='CG' and Data_Load_Metadata_ID='1600' and Occupancy_DT='2013-10-11' and Room_Type='QR'")
    connection.execute("update opera.History_Occupancy_Summary set Physical_Rooms=205,Out_Of_Order_Rooms=1,Out_Of_Service_Rooms=1,Rooms_Sold=31,Room_Revenue='0.129529518519',Room_Arrivals=3,Room_Departures=2,Food_Revenue='0.029529518519'\n" +
            "where Data_Load_Metadata_ID='1601' and Occupancy_DT='2013-10-11' and Room_Type='QR'")
    connection.close();
}

void makeOneGroupDefinite(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update opera.History_Group_Master set Status='DEF' where Data_Load_Metadata_ID=1599 and Block_Code='ARC190913'")
    connection.close();
}

void makeOneGroupCancelled(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update opera.History_Group_Master set Status='CXL' where Data_Load_Metadata_ID=1599 and Block_Code='ARC190913'")
    connection.close();
}

void deleteOccupancyDayOverride(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update Occupancy_Demand_FCST_OVR set Status_ID=2")
    connection.close();
}

void reActivateOccupancyDayOverride(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update Occupancy_Demand_FCST_OVR set Status_ID=1")
    connection.close();
}

void updateDataDuringSecondBDE(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update opera.History_Occupancy_Summary set Physical_Rooms=205,Out_Of_Order_Rooms=1,Out_Of_Service_Rooms=1,Rooms_Sold=10,Room_Revenue='0.129529518519',Room_Arrivals=3,Room_Departures=2,Food_Revenue='0.029529518519'\n" +
            " where Market_Code='CG' and Data_Load_Metadata_ID='1600' and Occupancy_DT='2013-10-12' and Room_Type='QR'")
    connection.execute("update opera.History_Occupancy_Summary set Physical_Rooms=205,Out_Of_Order_Rooms=1,Out_Of_Service_Rooms=1,Rooms_Sold=32,Room_Revenue='0.129529518519',Room_Arrivals=3,Room_Departures=2,Food_Revenue='0.029529518519'\n" +
            "where Data_Load_Metadata_ID='1601' and Occupancy_DT='2013-10-12' and Room_Type='QR'")
    connection.close();
}


When(~/Analytical Market Segments are configured$/) { ->
    insertAnalyticalMarketSegmentData(DatabaseName)
}

static void insertAnalyticalMarketSegmentData(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute(InsertAnalyticalMarketSegmentQuery.QUERY1)
}

When(~/I execute operaDataLoad job For Decision Generation$/) { ->
    OperaUtil.executeOperaDataLoadJob(DatabaseName, propertyId, Boolean.TRUE)
}

When(~/Property has 365 days or more of booked data$/) { ->
    def params = [username: "<EMAIL>", password: "password"];
    tetrisRESTClient = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params);
    tetrisRESTClient.headers.propertyId = propertyId.toInteger();
    tetrisRESTClient.post(
            path: RestServiceUtil.getPacmanRestURL()+"informationManager/insertFileMetaDataForBookedDataIMAlertsForTestBench/v1/",
            query: ['propertyId': 11022, 'extracts': 250],
            body: [],
            requestContentType: ContentType.JSON
    )
}

Given(~/I change Property Stage to OneWay and run TurnOnBookedDataToggleStep$/) { ->
    def params = [username: "<EMAIL>", password: "password"]
    tetrisRESTClient = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    PropertiesTestHelper.setPropertyStage(tetrisRESTClient, propertyId, PropertyRolloutTestHelper.STAGE_ONE_WAY)
    tetrisRESTClient.headers.propertyId = propertyId.toInteger();
    tetrisRESTClient.post(
            path: RestServiceUtil.getPacmanRestURL()+"useBookedData/movePropertyToTwoWayBasedOnUserSelectionForBookedDataUsageStartedAlert/v1/",
            query: ['propertyId': 11022],
            body: [],
            requestContentType: ContentType.JSON
    )
}

Then(~/System should remain in OneWay$/) { ->
    checkSystemStageIsOneWay(propertyId)
}


private void checkSystemStageIsOneWay(String propertyId) {
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def stage = PropertiesTestHelper.getPropertyStage(client, propertyId)
    println "Stage= ${stage}"
    JemsTest.logm("Stage= ${stage}")
    assertTrue(stage == "OneWay")
}

When(~/I resolve the booked data usage started alert$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update Info_Mgr_Instance set Info_Mgr_Status_ID = 4 where Info_Mgr_Type_ID=55");
    connection.execute("update Info_Mgr_Instance_Step_state set Actioned = 1 where Info_Mgr_Steps_ID = 463");
    connection.close();
}
When(~/I resolve the sufficient booked data alert$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update Info_Mgr_Instance set Info_Mgr_Status_ID = 4 where Info_Mgr_Type_ID=56");
    connection.execute("update Info_Mgr_Instance_Step_state set Actioned = 1 where Info_Mgr_Steps_ID = 455");
    connection.close();
}

When(~/I resolve the Demand And Wash override may be invalid due to cancellations of Groups alert$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update Info_Mgr_Instance set Info_Mgr_Status_ID = 4 where Info_Mgr_Type_ID=81");
    connection.execute("update Info_Mgr_Instance_Step_state set Actioned = 1 where Info_Mgr_Steps_ID = 523");
    connection.close();
}


Then(~/New alert is generated for Demand And Wash override may be invalid due to cancellations$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    rows1 = connection.rows("select COUNT(*) alertStatusCount from Info_Mgr_Instance where " +
            "Info_Mgr_Type_ID=(select Info_Mgr_Type_ID from info_mgr_type where Name='DemandAndWashOverrideInvalidDueToCanceledGroups') and Info_Mgr_Status_ID=1")
    assertTrue("Demand And Wash override may be invalid due to cancellations alert status count - Expected is 1 but in actual it is " + rows1.get(0).alertStatusCount + " ", rows1.get(0).alertStatusCount == 1)
    connection.close();
}


Given(~/I make capacity of accom type 6 as zero$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update opera.History_Occupancy_Summary set Physical_Rooms=0 where Room_Type='TS' and Data_Load_Metadata_ID in ('1601','1607')");
    connection.close();
}

Given(~/I make Group Cancelled where occupancy Day overrides are present$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("delete from Occupancy_Demand_FCST_OVR\n" +
            "delete from Occupancy_Demand_FCST where Decision_id in (28,29);\n" +
            "delete from Decision where Decision_id in (28,29);\n" +
            " SET IDENTITY_INSERT [Decision] ON\n" +
            "INSERT INTO [dbo].[Decision] (Decision_ID,[Property_ID],[Business_DT],[Caught_up_DTTM],[Rate_Unqualified_DTTM],[WebRate_DTTM],[Decision_Type_ID],[Start_DTTM]\n" +
            "           ,[End_DTTM],[Process_Status_ID],[CreateDate_DTTM])\n" +
            "     VALUES (28,11022,'2013-09-14','2013-09-15',GETDATE(),GETDATE(),6,GETDATE(),GETDATE(),2,GETDATE()),\n" +
            "\t\t   (29,11022,'2013-09-14','2013-09-15',GETDATE(),GETDATE(),6,GETDATE(),GETDATE(),2,GETDATE())\n" +
            "SET IDENTITY_INSERT [Decision] OFF\n" +
            "SET IDENTITY_INSERT [dbo].[Occupancy_Demand_FCST_OVR] ON \n" +
            "INSERT [dbo].[Occupancy_Demand_FCST_OVR] ([Occupancy_Demand_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Remaining_Demand], [User_Demand_Override_Value], [Rate_Unqualified_ID], [Status_ID], [Rooms_Sold], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Value], [Product_ID]) VALUES (4, 28, 11022, 1, 2, CAST(N'2013-09-21' AS Date), CAST(0.03 AS Numeric(18, 2)), CAST(0.03 AS Numeric(18, 2)), 13, 1, CAST(0 AS Numeric(18, 0)), 11403, CAST(N'2020-09-18T12:35:13.947' AS DateTime), 11403, CAST(N'2020-09-18T12:35:13.947' AS DateTime), NULL, 1)\n" +
            "INSERT [dbo].[Occupancy_Demand_FCST_OVR] ([Occupancy_Demand_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Remaining_Demand], [User_Demand_Override_Value], [Rate_Unqualified_ID], [Status_ID], [Rooms_Sold], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Value], [Product_ID]) VALUES (6, 29, 11022, 1, 2, CAST(N'2013-09-22' AS Date), CAST(0.01 AS Numeric(18, 2)), CAST(0.01 AS Numeric(18, 2)), 13, 1, CAST(0 AS Numeric(18, 0)), 11403, CAST(N'2020-09-18T12:35:39.950' AS DateTime), 11403, CAST(N'2020-09-18T12:35:39.953' AS DateTime), NULL, 1)\n" +
            "INSERT [dbo].[Occupancy_Demand_FCST_OVR] ([Occupancy_Demand_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Remaining_Demand], [User_Demand_Override_Value], [Rate_Unqualified_ID], [Status_ID], [Rooms_Sold], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Value], [Product_ID]) VALUES (2, 28, 11022, 1, 3, CAST(N'2013-09-21' AS Date), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), 13, 1, CAST(0 AS Numeric(18, 0)), 11403, CAST(N'2020-09-18T12:35:13.940' AS DateTime), 11403, CAST(N'2020-09-18T12:35:13.940' AS DateTime), NULL, 1)\n" +
            "INSERT [dbo].[Occupancy_Demand_FCST_OVR] ([Occupancy_Demand_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Remaining_Demand], [User_Demand_Override_Value], [Rate_Unqualified_ID], [Status_ID], [Rooms_Sold], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Value], [Product_ID]) VALUES (5, 29, 11022, 1, 3, CAST(N'2013-09-22' AS Date), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), 13, 1, CAST(0 AS Numeric(18, 0)), 11403, CAST(N'2020-09-18T12:35:39.950' AS DateTime), 11403, CAST(N'2020-09-18T12:35:39.950' AS DateTime), NULL, 1)\n" +
            "INSERT [dbo].[Occupancy_Demand_FCST_OVR] ([Occupancy_Demand_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Remaining_Demand], [User_Demand_Override_Value], [Rate_Unqualified_ID], [Status_ID], [Rooms_Sold], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Value], [Product_ID]) VALUES (1, 28, 11022, 1, 4, CAST(N'2013-09-21' AS Date), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), 13, 1, CAST(0 AS Numeric(18, 0)), 11403, CAST(N'2020-09-18T12:35:13.917' AS DateTime), 11403, CAST(N'2020-09-18T12:35:13.920' AS DateTime), NULL, 1)\n" +
            "INSERT [dbo].[Occupancy_Demand_FCST_OVR] ([Occupancy_Demand_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Remaining_Demand], [User_Demand_Override_Value], [Rate_Unqualified_ID], [Status_ID], [Rooms_Sold], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Value], [Product_ID]) VALUES (8, 29, 11022, 1, 4, CAST(N'2013-09-22' AS Date), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), 13, 1, CAST(0 AS Numeric(18, 0)), 11403, CAST(N'2020-09-18T12:35:39.957' AS DateTime), 11403, CAST(N'2020-09-18T12:35:39.957' AS DateTime), NULL, 1)\n" +
            "INSERT [dbo].[Occupancy_Demand_FCST_OVR] ([Occupancy_Demand_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Remaining_Demand], [User_Demand_Override_Value], [Rate_Unqualified_ID], [Status_ID], [Rooms_Sold], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Value], [Product_ID]) VALUES (3, 28, 11022, 1, 5, CAST(N'2013-09-21' AS Date), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), 13, 1, CAST(0 AS Numeric(18, 0)), 11403, CAST(N'2020-09-18T12:35:13.943' AS DateTime), 11403, CAST(N'2020-09-18T12:35:13.943' AS DateTime), NULL, 1)\n" +
            "INSERT [dbo].[Occupancy_Demand_FCST_OVR] ([Occupancy_Demand_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Remaining_Demand], [User_Demand_Override_Value], [Rate_Unqualified_ID], [Status_ID], [Rooms_Sold], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Rate_Value], [Product_ID]) VALUES (7, 29, 11022, 1, 5, CAST(N'2013-09-22' AS Date), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), 13, 1, CAST(0 AS Numeric(18, 0)), 11403, CAST(N'2020-09-18T12:35:39.953' AS DateTime), 11403, CAST(N'2020-09-18T12:35:39.953' AS DateTime), NULL, 1)\n" +
            "SET IDENTITY_INSERT [dbo].[Occupancy_Demand_FCST_OVR] OFF\n" +
            "update opera.History_Group_Master set Status='CXL' where Data_Load_Metadata_ID=1599 and Block_Code='ARC190913';")
    connection.close();
}

Then(~/BAR Decisions Are Generated$/) { ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select count(*) as numberOfRows   from Decision_Bar_Output");
    println("Decision_Bar_Output Row Count :" + result.numberOfRows);
    assert (0 < result.numberOfRows)
    connection.close();
}

Then(~/Daily Bar Decisions Are Generated$/) { ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select count(*) as numberOfRows from Decision_Dailybar_Output");
    println("Daily Bar Decision Row Count :" + result.numberOfRows);
    assert (0 < result.numberOfRows)

    /* verify decision Daily Bar Decisions are getting generated for room types mentioned in ZeroCapacityRoomTypesToInclude */
    def result1 = connection.firstRow("select count(*) as numberOfRows from Decision_Dailybar_Output where accom_type_id=6");
    println("Daily Bar Decision Row Count for room types mentioned in ZeroCapacityRoomTypesToInclude:" + result1.numberOfRows);
    assert (0 < result1.numberOfRows)

    connection.close();
}

Then(~/Last Room Value By Room Class Decisions Are Generated$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select count(*) as numberOfRows from Decision_LRV");
    println("Last Room Value By Room Class Decisions Row Count :" + result.numberOfRows);
    assert (0 < result.numberOfRows)
    connection.close();
}

Then(~/Hotel Overbooking Decisions Are Generated$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select count(*) as numberOfRows from Decision_Ovrbk_Property");
    println("Hotel Overbooking Decisions Row Count :" + result.numberOfRows);
    assert (0 < result.numberOfRows)
    connection.close();
}

Then(~/Room Type Overbooking Decisions Are Generated$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select count(*) as numberOfRows from Decision_Ovrbk_Accom");
    println("Room Type Overbooking Decisions Row Count :" + result.numberOfRows);
    assert (0 < result.numberOfRows)

    /* verify room type overbooking Decisions are not getting generated even if room types mentioned in ZeroCapacityRoomTypesToInclude */
    def result1 = connection.firstRow("select count(*) as numberOfRows from Decision_Ovrbk_Accom where accom_type_id=6");
    println("Room Type Overbooking Decisions Row Count for room types mentioned in ZeroCapacityRoomTypesToInclude :" + result1.numberOfRows);
    assert (0 == result1.numberOfRows)

    connection.close();
}

Then(~/FPLOS Decisions Are Generated$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select count(*) as numberOfRows from Decision_Qualified_FPLOS");
    println("FPLOS Decisions Row Count :" + result.numberOfRows);
    assert (0 < result.numberOfRows)

    /* verify FPLOS Decisions are not getting generated for room types mentioned in ZeroCapacityRoomTypesToInclude */
    def result1 = connection.firstRow("select count(*) as numberOfRows from Decision_Qualified_FPLOS where accom_type_id=6");
    println("FPLOS Decisions Row Count for room types mentioned in ZeroCapacityRoomTypesToInclude :" + result1.numberOfRows);
    assert (0 == result1.numberOfRows)


    connection.close();
}

Then(~/MINLOS Decisions Are Generated$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select count(*) as numberOfRows from Decision_MINLOS");
    println("MINLOS Decisions Row Count :" + result.numberOfRows);
    assert (0 < result.numberOfRows)

    /* verify MINLOS Decisions are not getting generated for room types mentioned in ZeroCapacityRoomTypesToInclude */
    def result1 = connection.firstRow("select count(*) as numberOfRows from Decision_MINLOS where accom_type_id=6");
    println("MINLOS Decisions Row Count for room types mentioned in ZeroCapacityRoomTypesToInclude :" + result1.numberOfRows);
    assert (0 == result1.numberOfRows)

    connection.close();
}

Then(~/RDS Decision File Is Created For Upload$/) { ->

    def props = new java.util.Properties()
    props.load(StreamUtil.class.getResourceAsStream("/env.properties"))
    Map<String, Integer> additionalProperties = new HashMap<String, Integer>();
    additionalProperties.put("SandBox.ClientID", ClientLocator.getSandboxClientID());
    props.putAll(additionalProperties);
    String tetrisDataDir = props.getProperty("tetrisDataDir")
    rdsDecFolder = tetrisDataDir + "/RDSDecisions"
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName);
    def result = connection.firstRow("select top 1 file_metadata_id,SnapShot_DT,Snapshot_TM from File_Metadata order by 1 desc");
    String snapShotDt = result[1].toString()
    String snapShotTm = result[2].toString()
    snapShotDt = snapShotDt.replaceAll("-", "")
    snapShotTm = snapShotTm.replaceAll(":", "")[0..3]
    rdsDecisionFileName = rdsPMSId + "_" + snapShotDt + "_" + snapShotTm + "_RateDistribution_decision.zip"
    connection.close();

    def decFile = new File(rdsDecFolder + "/" + rdsDecisionFileName)
    assert decFile.exists()

}

Then(~/ValidateDecisions for enabled UseDecisionUploadWindow$/) { ->
    def context = 'pacman.SandBox.OPER2';
    def params = [username: "<EMAIL>", password: "password"];
    String vendor = "opera";
    String partnerSynxis = ".SynXis"
    propertyId = "11022";
    tetrisRESTClient = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params);
    tetrisRESTClient.headers.propertyId = propertyId.toInteger();
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.core.property.externalSystem', context, 'OPERA');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.core.property.YieldCurrencyCode', context, 'USD');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.optimization.optimizationWindowCDP', context, '2');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.optimization.optimizationWindowBDE', context, '10');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.upload.DecisionUploadWindowBDEDays', context, '5');

    setSynXisDecisionParameters('full', propertyId)

    try {
        tetrisRESTClient.headers.propertyId = propertyId.toInteger()
        HashMap parameterMap = new HashMap();
        parameterMap.put("externalSystem", vendor);
        parameterMap.put("propertyId", propertyId);

        HashMap parameterPartnerMap = new HashMap();
        parameterPartnerMap.put("propertyId", propertyId);
        parameterPartnerMap.put("partner", partnerSynxis);


        def response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/BarFpLOSByHierarchy/v1", query: parameterMap, requestContentType: ContentType.JSON)
        def slurper = new JsonSlurper()
        def response1 = slurper.parseText(response.getData().toString())
        println("BarFPLOSByHierarchyDecisions for enabled UseDecisionUploadWindow " + response1.size())
        assertTrue(300 == response1.size());
/**/
        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/BarFplosByRoomType/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("BarFplosByRoomType for enabled UseDecisionUploadWindow " + response1.size())
        assertTrue(300 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/LRAControlFPLOSByRateCode/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("LRAControlFPLOSByRateCode enabled UseDecisionUploadWindow " + response1.size())
        //assertTrue(35 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/LRAControlMinLOSByRateCode/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("LRAControlMinLOSByRateCode enabled UseDecisionUploadWindow " + response1.size())
        //assertTrue(35 == response1.size());

/**/
        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/BarByLOS/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("BarByLOS for enabled UseDecisionUploadWindow " + response1.size())
        assertTrue(35 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/LastRoomValuebyRoomClass/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("LastRoomValuebyRoomClass for enabled UseDecisionUploadWindow " + response1.size())
        assertTrue(20 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/DailyBAR/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("DailyBAR for enabled UseDecisionUploadWindow " + response1.size())
        assertTrue(15 == response1.size());

        parameterMap.put("externalSystem", "ORS");
        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/DBAR/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("DBAR for enabled UseDecisionUploadWindow " + response1.size())
        assertTrue(35 == response1.size());

        parameterMap.put("externalSystem", "OPERA");
        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/HotelOverbooking/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("Hotel Overbooking for enabled UseDecisionUploadWindow " + response1.size())
        assertTrue(5 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/RoomTypeOverbooking/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("RoomType Overbooking for enabled UseDecisionUploadWindow " + response1.size())
        assertTrue(20 == response1.size());

        //SynXis
        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomTypeLrvDecisionMessage/v1", query: parameterPartnerMap)
        println "Response object for Room Type LRV Decisions For Synxis for enabled UseDecisionUploadWindow:" + response.getData().toString()
        println("Room Type LRV Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(25 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomClassLrvDecisionMessage/v1", query: parameterPartnerMap)
        println("Room Type LRV Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(20 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/propertyOverbookingDecisionMessage/v1", query: parameterPartnerMap)
        println("Property  Overbooking Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(5 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomTypeOverbookingDecisionMessage/v1", query: parameterPartnerMap)
        println("Room Type Overbooking Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(20 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/fplosDecisionMessage/v1", query: parameterPartnerMap)
        println("Qualified FPLOS Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(40 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/losBarDecisionMessage/v1", query: parameterPartnerMap)
        println("Bar By LOS Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(5 == response.getData()[0].children.children[0].size);

        //response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/barFplosDecisionMessage", query:parameterPartnerMap)
        //println("Bar FPLOS Decisions For Synxis for enabled UseDecisionUploadWindow "+response.getData()[0].children.children[0].size)
        //assertTrue(5==response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/dailyBarDecisionMessage/v1", query: parameterPartnerMap)
        println("Daily Bar Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].children[0][0].children.size)
        assertTrue(15 == response.getData()[0].children.children[0].children[0][0].children.size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/MinLOSDecisionMessage/v1", query: parameterPartnerMap)
        println("MINLOS Decisions By Rate Code By Room Type For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(40 == response.getData()[0].children.children[0].size);

    }
    catch (HttpResponseException ex) {
        EvaluationException = ex
        println ex.response.data
        JemsTest.logm(ex.response.data)
    }

    setSynXisDecisionParameters('none', propertyId);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.optimization.optimizationWindowCDP', context, '45');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.optimization.optimizationWindowBDE', context, '365');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.upload.DecisionUploadWindowBDEDays', context, '365');


}

Then(~/ValidateDecisions for disabled UseDecisionUploadWindow$/) { ->
    def context = 'pacman.SandBox.OPER2';
    def params = [username: "<EMAIL>", password: "password"];
    String vendor = "opera";
    String partnerSynxis = ".SynXis"
    propertyId = "11022";
    tetrisRESTClient = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params);
    tetrisRESTClient.headers.propertyId = propertyId.toInteger();
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.core.property.externalSystem', context, 'OPERA');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.optimization.optimizationWindowCDP', context, '2');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.optimization.optimizationWindowBDE', context, '10');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.upload.DecisionUploadWindowBDEDays', context, '5');

    setSynXisDecisionParameters('full', propertyId)

    try {
        tetrisRESTClient.headers.propertyId = propertyId.toInteger()
        HashMap parameterMap = new HashMap();
        parameterMap.put("externalSystem", vendor);
        parameterMap.put("propertyId", propertyId);

        HashMap parameterPartnerMap = new HashMap();
        parameterPartnerMap.put("propertyId", propertyId);
        parameterPartnerMap.put("partner", partnerSynxis);

        def response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/BarFplosByHierarchy/v1", query: parameterMap, requestContentType: ContentType.JSON)
        def slurper = new JsonSlurper()
        def response1 = slurper.parseText(response.getData().toString())
        println("BarFPLOSByHierarchyDecisions for disabled UseDecisionUploadWindow " + response1.size())
        assertTrue(600 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/BarByLOS/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("BarByLOS for disabled UseDecisionUploadWindow " + response1.size())
        assertTrue(70 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/LastRoomValuebyRoomClass/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("LastRoomValuebyRoomClass for disabled UseDecisionUploadWindow " + response1.size())
        assertTrue(40 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/DailyBAR/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("DailyBAR for disabled UseDecisionUploadWindow " + response1.size())
        assertTrue(20 == response1.size());

        parameterMap.put("externalSystem", "ORS");
        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/DBAR/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("DBAR for disabled UseDecisionUploadWindow " + response1.size())
        assertTrue(70 == response1.size());

        parameterMap.put("externalSystem", "OPERA");
        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/HotelOverbooking/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("Hotel Overbooking for disabled UseDecisionUploadWindow " + response1.size())
        assertTrue(10 == response1.size());

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/RoomTypeOverbooking/v1", query: parameterMap, requestContentType: ContentType.JSON)
        slurper = new JsonSlurper()
        response1 = slurper.parseText(response.getData().toString())
        println("Room Type Overbooking for disabled UseDecisionUploadWindow " + response1.size())
        assertTrue(50 == response1.size());

        //SynXis
        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomTypeLrvDecisionMessage/v1", query: parameterPartnerMap)
        println("Room Type LRV Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(50 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomClassLrvDecisionMessage/v1", query: parameterPartnerMap)
        println("Room Type LRV Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(40 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/propertyOverbookingDecisionMessage/v1", query: parameterPartnerMap)
        println("Property  Overbooking Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(10 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomTypeOverbookingDecisionMessage/v1", query: parameterPartnerMap)
        println("Room Type Overbooking Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(50 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/fplosDecisionMessage/v1", query: parameterPartnerMap)
        println("Qualified FPLOS Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(100 == response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/losBarDecisionMessage/v1", query: parameterPartnerMap)
        println("Bar By LOS Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(10 == response.getData()[0].children.children[0].size);

        //response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/barFplosDecisionMessage", query:parameterPartnerMap)
        //println("Bar FPLOS Decisions For Synxis for enabled UseDecisionUploadWindow "+response.getData()[0].children.children[0].size)
        //assertTrue(5==response.getData()[0].children.children[0].size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/dailyBarDecisionMessage/v1", query: parameterPartnerMap)
        println("Daily Bar Decisions For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].children[0][0].children.size)
        assertTrue(20 == response.getData()[0].children.children[0].children[0][0].children.size);

        response = tetrisRESTClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/MinLOSDecisionMessage/v1", query: parameterPartnerMap)
        println("MINLOS Decisions By Rate Code By Room Type For Synxis for enabled UseDecisionUploadWindow " + response.getData()[0].children.children[0].size)
        assertTrue(100 == response.getData()[0].children.children[0].size);


        setSynXisDecisionParameters('none', propertyId)
    }
    catch (HttpResponseException ex) {
        EvaluationException = ex
        println ex.response.data
        JemsTest.logm(ex.response.data)
    }
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.optimization.optimizationWindowCDP', context, '45');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.optimization.optimizationWindowBDE', context, '365');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.upload.DecisionUploadWindowBDEDays', context, '365');
}

Given(~/I execute OperaDataLoad job for CDP$/) { ->
//    clearMainAndPaceTablesSAS();
    OperaUtil.executeOperaDataLoadJob(DatabaseName, propertyId, Boolean.FALSE)
}

Then(~/Main pacman activity tables are populated$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def cntTotalActivity = connection.firstRow("select COUNT(distinct(occupancy_dt)) numberOfRows from Total_Activity where File_Metadata_ID >= (select max(File_Metadata_ID) File_Metadata_ID from File_Metadata where IsBDE=0 and Record_Type_ID = 3)")
    assert (0 < cntTotalActivity.numberOfRows)
    def cntAccomActivity = connection.firstRow("select COUNT(distinct(occupancy_dt)) numberOfRows from accom_activity where File_Metadata_ID >= (select max(File_Metadata_ID) File_Metadata_ID from File_Metadata where IsBDE=0 and Record_Type_ID = 3)")
    assert (0 < cntAccomActivity.numberOfRows)
    def cntMktAccomActivity = connection.firstRow("select COUNT(distinct(occupancy_dt)) numberOfRows from mkt_Accom_Activity where File_Metadata_ID >= (select max(File_Metadata_ID) File_Metadata_ID from File_Metadata where IsBDE=0 and Record_Type_ID = 3)")
    assert (0 < cntMktAccomActivity.numberOfRows)
    def cntIndividualTrans = connection.firstRow("select COUNT(distinct(arrival_dt)) numberOfRows from individual_trans where File_Metadata_ID >= (select max(File_Metadata_ID) File_Metadata_ID from File_Metadata where IsBDE=0 and Record_Type_ID = 3)")
    assert (0 < cntIndividualTrans.numberOfRows)
    connection.close();
}

Then(~/Unassigned MS alert is generated on IM$/) { ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def cntInfoMgrInstance = connection.firstRow("select * from dbo.Info_Mgr_Instance where Info_Mgr_Type_ID=3")
    assertEquals("CDP", cntInfoMgrInstance.Alert_Detail)
    assertEquals(1, cntInfoMgrInstance.Info_Mgr_Status_ID)
    assertEquals(100, cntInfoMgrInstance.Score)
    connection.close();
}

Then(~/Pace pacman activity tables are not populated$/) { ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def cntPaceTotalActivity = connection.firstRow("select COUNT(distinct(occupancy_dt)) numberOfRows from Pace_Total_Activity where File_Metadata_ID >= (select max(File_Metadata_ID) File_Metadata_ID from File_Metadata where IsBDE=0 and Record_Type_ID = 3)")
    assert (0 == cntPaceTotalActivity.numberOfRows)
    def cntPaceAccomActivity = connection.firstRow("select COUNT(distinct(occupancy_dt)) numberOfRows from pace_accom_activity where File_Metadata_ID >= (select max(File_Metadata_ID) File_Metadata_ID from File_Metadata where IsBDE=0 and Record_Type_ID = 3)")
    assert (0 == cntPaceAccomActivity.numberOfRows)
    def cntPaceMktActivity = connection.firstRow("select COUNT(distinct(occupancy_dt)) numberOfRows from PACE_Mkt_Activity where File_Metadata_ID >= (select max(File_Metadata_ID) File_Metadata_ID from File_Metadata where IsBDE=0 and Record_Type_ID = 3)")
    assert (0 == cntPaceMktActivity.numberOfRows)
    connection.close();
}

Then(~/Main SAS activity tables are populated$/) { ->
    def result;
    def statement;
    def sasDatasetConnection = DbConnection.getSasDatasetConnection(propertyId)
    statement = sasDatasetConnection.createStatement();
    def resultMktAccomLos = statement.executeQuery("select count(*) from mySasLib.mkt_accom_los_inventory where mkt_seg_ID= 16 ")
    resultMktAccomLos.next()
    assert resultMktAccomLos.getInt(1).toInteger() > 0: "mkt_accom_los_inventory table is not populated with CDP"
    def resultMktAccomInventory = statement.executeQuery("select count(*) from mySasLib.mkt_accom_inventory where mkt_seg_ID= 16 ")
    resultMktAccomInventory.next()
    assert resultMktAccomInventory.getInt(1).toInteger() > 0: "mkt_accom_inventory table is not populated with CDP"
    def resultAccomInventory = statement.executeQuery("select count(*) from mySasLib.mkt_accom_inventory where accom_type_id= 7")
    resultAccomInventory.next()
    assert resultAccomInventory.getInt(1).toInteger() > 0: "mkt_accom_inventory table is not populated with CDP"

    // asserting on actual value to validate CDP data is inserted in Non-pace table
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def assertCount = 0;
    connection.eachRow("Select Mkt_Seg_ID from Mkt_Seg where Mkt_Seg_Code = 'HU'")
            {
                mktSegmentID = "$it.Mkt_Seg_ID"
            }

    connection.eachRow("select Accom_Type_ID from Accom_Type where Accom_Type_Code='QR'")
            {
                roomTypeID = "$it.Accom_Type_ID"
            }
    def thatsql = "SELECT * FROM mySasLib.mkt_accom_inventory where  occupancy_dt=19642 and mkt_seg_id=" + mktSegmentID + " and accom_type_id=" + roomTypeID + "  "
    println "Executing this against sas jdbc ${thatsql}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql}")
    result = statement.executeQuery(thatsql);

    while (result.next()) {
        assertEquals("Arrivals", "3.0", result.getDouble(5).toString())
        assertEquals("Departures", "2.0", result.getDouble(6).toString())
        assertEquals("Room Revenue", "0.12953", result.getDouble(9).toString())
        assertEquals("Room Sold", "4.0", result.getDouble(10).toString())
        assertEquals("Total Revenue", "0.01852", result.getDouble(11).toString())
        assertEquals("Food Revenue", "0.02953", result.getDouble(12).toString())

        assertCount = assertCount + 5;
    }

    assertTrue("assertion Count Should not be zero", assertCount == 5)
    statement.close();
    sasDatasetConnection.close();
    connection.close()
}


Then(~/Main SAS BDE activity tables are populated and Pace Got Updated Successfully in First BDE$/) { ->
    def statement;
    def sasDatasetConnection = DbConnection.getSasDatasetConnection(propertyId)
    statement = sasDatasetConnection.createStatement();

    def sasDatasetPartitionConnection = DbConnection.getSasDatasetPartitionConnection(propertyId)
    def statement2 = sasDatasetPartitionConnection.createStatement();

    def resultBDEAccom = statement.executeQuery("select rooms_sold from mySasLib.bde_accom_inventory where accom_type_id= 4 and occupancy_dt=19642 ")
    resultBDEAccom.next()
    assert resultBDEAccom.getInt(1).toInteger() == 27: "bde_accom_inventory table is not populated correctly in first BDE"

    def resultBDEMktAccom = statement.executeQuery("select rooms_sold from mySasLib.bde_mkt_accom_inventory where accom_type_id= 4 and occupancy_dt=19642 and mkt_seg_id=7")
    resultBDEMktAccom.next()
    assert resultBDEMktAccom.getInt(1).toInteger() == 5: "bde_mkt_accom_inventory table is not populated correctly in first BDE"

//    def thatsql2 = "SELECT rooms_sold FROM parLib.ma_7 where accom_type_id= 4 and occupancy_dt=19642 and capture_dttm=1694346960"
//    def resultPaceMa = statement2.executeQuery(thatsql2);
//    resultPaceMa.next()
//    assert resultPaceMa.getInt(1).toInteger() == 5: "ma_7 table is not populated correctly in first BDE"

    statement.close();
    sasDatasetConnection.close();
    statement2.close();
    sasDatasetConnection.close();
    sasDatasetPartitionConnection.close();
}


Then(~/Main SAS BDE activity tables are populated and Pace Got Updated Successfully in second BDE$/) { ->
    def statement;
    def sasDatasetConnection = DbConnection.getSasDatasetConnection(propertyId)
    statement = sasDatasetConnection.createStatement();

    def sasDatasetPartitionConnection = DbConnection.getSasDatasetPartitionConnection(propertyId)
    def statement2 = sasDatasetPartitionConnection.createStatement();

    def resultBDEAccom_1 = statement.executeQuery("select rooms_sold from mySasLib.bde_accom_inventory where accom_type_id= 4 and occupancy_dt=19642 ")
    resultBDEAccom_1.next()
    assert resultBDEAccom_1.getInt(1).toInteger() == 31: "bde_accom_inventory table is not populated correctly in first BDE"

    def resultBDEAccom_2 = statement.executeQuery("select rooms_sold from mySasLib.bde_accom_inventory where accom_type_id= 4 and occupancy_dt=19643 ")
    resultBDEAccom_2.next()
    assert resultBDEAccom_2.getInt(1).toInteger() == 32: "bde_accom_inventory table is not populated correctly in first BDE"

    def resultBDEMktAccom_1 = statement.executeQuery("select rooms_sold from mySasLib.bde_mkt_accom_inventory where accom_type_id= 4 and occupancy_dt=19642 and mkt_seg_id=7")
    resultBDEMktAccom_1.next()
    assert resultBDEMktAccom_1.getInt(1).toInteger() == 9: "bde_mkt_accom_inventory table is not populated correctly in first BDE"

    def resultBDEMktAccom_2 = statement.executeQuery("select rooms_sold from mySasLib.bde_mkt_accom_inventory where accom_type_id= 4 and occupancy_dt=19643 and mkt_seg_id=7")
    resultBDEMktAccom_2.next()
    assert resultBDEMktAccom_2.getInt(1).toInteger() == 10: "bde_mkt_accom_inventory table is not populated correctly in first BDE"

//    def thatsql2 = "SELECT rooms_sold FROM parLib.ma_7 where accom_type_id= 4 and occupancy_dt=19642 and capture_dttm=1694351760"
//    def resultPaceMa = statement2.executeQuery(thatsql2);
//    resultPaceMa.next()
//    assert resultPaceMa.getInt(1).toInteger() == 9: "ma_7 table is not populated correctly in first BDE"
//
//    def thatsql3 = "SELECT rooms_sold FROM parLib.ma_7 where accom_type_id= 4 and occupancy_dt=19643 and capture_dttm=1694351760"
//    def resultPaceMa_1 = statement2.executeQuery(thatsql3);
//    resultPaceMa_1.next()
//    assert resultPaceMa_1.getInt(1).toInteger() == 10: "ma_7 table is not populated correctly in first BDE"

    statement.close();
    sasDatasetConnection.close();
    statement2.close();
    sasDatasetConnection.close();
    sasDatasetPartitionConnection.close();
}

Then(~/System should be in Two Way due to resolving of Booked Data Usage Started Alert$/) { ->
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def stage = PropertiesTestHelper.getPropertyStage(client, propertyId)
    println "Stage= ${stage}"
    JemsTest.logm("Stage= ${stage}")
    assertTrue(stage == "TwoWay")
}

Then(~/System should be in One Way due to resolving of Sufficient Booked Data Alert$/) { ->
    checkSystemStageIsOneWay(propertyId)
}

Then(~/Check That (\d+) Decision Delivery Jobs are started$/) { int arg1 ->
    def decisionDeliveryJobInstancesCount = DbConnection.getJobDbConnection().firstRow("select COUNT(*) as cnt from JOB_INSTANCE as JI join JOB_INSTANCE_WORK_CONTEXT as JIWC on JI.JOB_INSTANCE_ID=JIWC.JOB_INSTANCE_ID " +
            " where JOB_NAME in ('RDSDecisionDeliveryJob','OperaDecisionDeliveryJob') and JIWC.PROPERTY_ID=11022")
    assertEquals(arg1, decisionDeliveryJobInstancesCount.cnt)
}

Then(~/Main SAS BDE activity tables are not populated and Pace is not Updated in CDP$/) { ->
    def statement;
    def sasDatasetConnection = DbConnection.getSasDatasetConnection(propertyId)
    statement = sasDatasetConnection.createStatement();

    def sasDatasetPartitionConnection = DbConnection.getSasDatasetPartitionConnection(propertyId)
    def statement2 = sasDatasetPartitionConnection.createStatement();

    def resultBDEAccom_1 = statement.executeQuery("select rooms_sold from mySasLib.bde_accom_inventory where accom_type_id= 4 and occupancy_dt=19642 ")
    resultBDEAccom_1.next()
    assert resultBDEAccom_1.getInt(1).toInteger() == 27: "bde_accom_inventory table is not populated correctly in first BDE"

    def resultBDEMktAccom_1 = statement.executeQuery("select rooms_sold from mySasLib.bde_mkt_accom_inventory where accom_type_id= 4 and occupancy_dt=19642 and mkt_seg_id=7")
    resultBDEMktAccom_1.next()
    assert resultBDEMktAccom_1.getInt(1).toInteger() == 5: "bde_mkt_accom_inventory table is not populated correctly in first BDE"

//    def thatsql2 = "SELECT rooms_sold FROM parLib.ma_7 where accom_type_id= 4 and occupancy_dt=19642 and capture_dttm=1694346960"
//    def resultPaceMa = statement2.executeQuery(thatsql2);
//    resultPaceMa.next()
//    assert resultPaceMa.getInt(1).toInteger() == 5: "ma_7 table is not populated correctly in first BDE"

    statement.close();
    sasDatasetConnection.close();
    statement2.close();
    sasDatasetConnection.close();
    sasDatasetPartitionConnection.close();
}

Then(~/Pace SAS activity tables are not populated$/) { ->

    def assertCount = 0;
    int rows;
    def result;
    def statement;
    def sasDatasetConnection = DbConnection.getSasDatasetConnection(propertyId)
    statement = sasDatasetConnection.createStatement();

    def thatsql = "select max(capture_dttm) format=best12. from mySasLib.accom_inventory_pace "
    println "Executing this against sas jdbc ${thatsql}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql}")
    result = statement.executeQuery(thatsql);
    while (result.next()) {
        rows = result.getDouble(1);
    }
    assertTrue(rows == 1694346960)
    assertCount = assertCount + 1
    statement.close();
    sasDatasetConnection.close();
    assertTrue("assertion Count Should not be zero", assertCount > 0)
    println("Total Number Of Assertions : " + assertCount)

}

Then(~/Forecasting is skipped$/) { ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(*) as cnt from Wash_Ind_Group_Fcst OccFcst\n" +
            "inner join Decision D on (OccFcst.Decision_ID = D.Decision_ID)\n" +
            "where D.Decision_Type_ID = 2");
     assert (result.cnt == 0): "Forecasting is not skipped in CDP"
    connection.close();
}

Then(~/Forecasting is not skipped$/) { ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(*) as cnt from Wash_Ind_Group_Fcst OccFcst\n" +
            "inner join Decision D on (OccFcst.Decision_ID = D.Decision_ID)\n" +
            "where D.Decision_Type_ID = 2");
    assert (result.cnt > 0): "Forecasting is not skipped in CDP for dirty system"
    connection.close();
}

Then(~/BAR Decisions Are Generated for "([^"]*)" window$/) { String decisionWindow ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(distinct(arrival_dt)) as numberOfRows   from Decision_Bar_Output");
    def resultPace = connection.firstRow("select COUNT(distinct(arrival_dt)) as numberOfRows   from Pace_Bar_Output");
    println("Decision_Bar_Output Row Count :" + result.numberOfRows);
    if (decisionWindow.equalsIgnoreCase("CDP")) {
        assert (45 == result.numberOfRows): "Decisions are not generated for $decisionWindow window. Actual count of decisions is " + result.numberOfRows
        assert (45 == resultPace.numberOfRows): "Pace Decisions are not generated for $decisionWindow window. Actual count of decisions is " + resultPace.numberOfRows
    } else

        connection.close();
}

Then(~/Daily Bar Decisions Are Generated for "([^"]*)" window$/) { String decisionWindow ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(distinct(Occupancy_Date)) as numberOfRows from Decision_Dailybar_Output");
    def resultPace = connection.firstRow("select COUNT(distinct(Occupancy_Date)) as numberOfRows from Pace_Dailybar_Output");
    println("Daily Bar Decision Row Count :" + result.numberOfRows);
    validateDecisionGenerationWindow(decisionWindow, result, resultPace)
    connection.close();
}

private void validateDecisionGenerationWindow(String decisionWindow, result, resultPace) {
    if (decisionWindow.equalsIgnoreCase("CDP")) {
        assert (45 == result.numberOfRows): "Decisions are not generated for $decisionWindow window. Actual count of decisions is " + result.numberOfRows
        assert (45 == resultPace.numberOfRows): "Pace Decisions are not generated for $decisionWindow window. Actual count of decisions is " + resultPace.numberOfRows
    } else {
        assert (365 == result.numberOfRows): "Decisions are not generated for $decisionWindow window. Actual count of decisions is " + result.numberOfRows
        assert (365 == resultPace.numberOfRows): "Pace Decisions are not generated for $decisionWindow window. Actual count of decisions is " + resultPace.numberOfRows
    }

}

Then(~/Last Room Value By Room Class Decisions Are Generated for "([^"]*)" window$/) { String decisionWindow ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(distinct(Occupancy_DT)) as numberOfRows from Decision_LRV");
    def resultPace = connection.firstRow("select COUNT(distinct(Occupancy_DT)) as numberOfRows from Pace_LRV");
    println("Last Room Value By Room Class Decisions Row Count :" + result.numberOfRows);
    validateDecisionGenerationWindow(decisionWindow, result, resultPace)
    connection.close();
}

Then(~/Hotel Overbooking Decisions Are Generated for "([^"]*)" window$/) { String decisionWindow ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(distinct(Occupancy_DT)) as numberOfRows from Decision_Ovrbk_Property");
    def resultPace = connection.firstRow("select COUNT(distinct(Occupancy_DT)) as numberOfRows from Pace_Ovrbk_Property");
    println("Hotel Overbooking Decisions Row Count :" + result.numberOfRows);
    validateDecisionGenerationWindow(decisionWindow, result, resultPace)
    connection.close();
}

Then(~/Room Type Overbooking Decisions Are Generated for "([^"]*)" window$/) { String decisionWindow ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(distinct(Occupancy_DT)) as numberOfRows from Decision_Ovrbk_Accom");
    def resultPace = connection.firstRow("select COUNT(distinct(Occupancy_DT)) as numberOfRows from Pace_Ovrbk_Accom");
    println("Room Type Overbooking Decisions Row Count :" + result.numberOfRows);
    validateDecisionGenerationWindow(decisionWindow, result, resultPace)
    connection.close();
}

Then(~/FPLOS Decisions Are Generated for "([^"]*)" window$/) { String decisionWindow ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(distinct(Arrival_DT)) as numberOfRows from Decision_Qualified_FPLOS");
    def resultPace = connection.firstRow("select COUNT(distinct(Arrival_DT)) as numberOfRows from Pace_Qualified_FPLOS");
    println("FPLOS Decisions Row Count :" + result.numberOfRows);
    validateDecisionGenerationWindow(decisionWindow, result, resultPace)
    connection.close();
}

Then(~/MINLOS Decisions Are Generated for "([^"]*)" window$/) { String decisionWindow ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def result = connection.firstRow("select COUNT(distinct(Arrival_DT)) as numberOfRows from Decision_MINLOS");
    def resultPace = connection.firstRow("select COUNT(distinct(Arrival_DT)) as numberOfRows from Pace_MINLOS");
    println("MINLOS Decisions Row Count :" + result.numberOfRows);
    validateDecisionGenerationWindow(decisionWindow, result, resultPace)
    connection.close();
}

Given(~/System is dirty$/) { ->
//    def params = [username: "<EMAIL>", password: "password"]
//    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
//    def context = 'pacman.SandBox.OPER2';
//    PropertyRolloutRESTUtil.registerSyncEvent(client, 'ACCOMMODATION_CONFIG_CHANGED', context)
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update Sync_Flags set Value = 1 where Name = 'ACCOMMODATION_CONFIGURATION_CHANGED'")
    connection.close()
}

Given(~/Special Event sync Flag is OFF$/) { ->
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def context = 'pacman.SandBox.OPER2';
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.core.property.sync.syncRequiredSpecialEventsChanged', context, 'false');
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.executeUpdate("update Sync_Flags set Value = 0 where name = 'SPECIAL_EVENTS_CHANGED'")
    connection.close()
}

void clearMainAndPaceTablesSAS() {
    def props = new java.util.Properties()
    props.load(StreamUtil.class.getResourceAsStream("/env.properties"))
    def String sasDirPath = props.getProperty("sasDir")
    def String sasPropertyPath = sasDirPath + "\\properties\\" + propertyId + "\\"
    List list = ['bde_mkt_accom_los_inventory.sas7bndx', 'bde_mkt_accom_los_inventory.sas7bdat', 'bde_accom_inventory.sas7bdat', 'bde_accom_inventory.sas7bndx', 'bde_mkt_accom_inventory.sas7bdat', 'bde_mkt_accom_inventory.sas7bndx']
//    'accom_inventory_pace.sas7bdat']
    for (int i = 0; i < 7; i++) {
        File filename = new File(sasPropertyPath + list[i])
        if (filename.exists()) {
            filename.delete();
        }
    }
    def String sasPropertyPartitiionPath = sasDirPath + "\\properties\\" + propertyId + "\\partitions\\"
    List Partitionlist = ['ma_16.sas7bdat', 'ma_los_16.sas7bdat']
    for (int i = 0; i < 6; i++) {
        File filename = new File(sasPropertyPartitiionPath + Partitionlist[i])
        if (filename.exists()) {
            filename.delete();
        }
    }

}


void setSynXisDecisionParameters(String value, String propertyId) {
    def params = [username: "<EMAIL>", password: "password"]
    tetrisRESTClient = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params);
    tetrisRESTClient.headers.propertyId = propertyId.toInteger();
    def context = 'SynXis.SandBox.OPER2';

    //SynXis
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.LRVatRoomType.uploadtype', context, value);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.LRVatRoomClass.uploadtype', context, value);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.HotelOverbooking.uploadtype', context, value);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.RoomTypeOverbooking.uploadtype', context, value);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.Fplos.uploadtype', context, value);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.BarByLOS.uploadtype', context, value);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.BarFplosByRoomType.uploadtype', context, value);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.DailyBAR.uploadtype', context, value);
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.integration.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', context, value);
}

Given(~/New Market segment got introduced$/) { ->
    introduceNewMarketSegment(DatabaseName)
}

Given(~/The one group Definite out of the Cancelled groups$/) { ->
    makeOneGroupDefinite(DatabaseName)
}

Given(~/The one group Cancelled out of the Definite groups$/) { ->
    makeOneGroupCancelled(DatabaseName)
}

Given(~/Occupancy Day demand override are disabled$/) { ->
    deleteOccupancyDayOverride(DatabaseName)
}
Given(~/Occupancy Day demand overrides are reenabled$/) { ->
    reActivateOccupancyDayOverride(DatabaseName)
}

Given(~/update Data During Second BDE$/) { ->
    updateDataDuringSecondBDE(DatabaseName)
}

Given(~/All Existing Jobs are clear$/) { ->
    cleanupJobs()
}

And(~/Clearing Existing failed jobs$/) { ->
    cleanupJobs()
}

void cleanupJobs() {
    def globalDbConnection = DbConnection.getGlobalConnection()
    PropertiesTestHelper.clearRegulator(globalDbConnection, "OPER2")
    globalDbConnection.close()
    cleanDecisionDeliveryJobs()
}

Then(~/SAS partition folder contained placeholder tables for new market segment$/) { ->

    def count = 0
    def p = ~/.*sas7bdat/
    def props = new java.util.Properties()
    props.load(StreamUtil.class.getResourceAsStream("/env.properties"))
    String sasDirPath = props.getProperty("sasDir")
    new File( sasDirPath + JemsTest.resolvePath("\\Properties\\" + propertyId + "\\partitions\\")).eachFileMatch(p) {
        file ->
            if (file.getName().contains("fin").equals(false) && file.getName().contains("template").equals(false)) {
                println file.getName().split("\\.")[0]
                JemsTest.logm(file.getName().split("\\.")[0])
                count++
            }
    }
    print "$count"
    assertTrue("Table count in partition folder", count == 200)
}

And(~/Data is blank in new tables and other tables are intact$/) { ->
    def assertCount = 0;
    def result;
    def statement;

    def sasDatasetPartitionConnection = DbConnection.getSasDatasetPartitionConnection(propertyId)
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    statement = sasDatasetPartitionConnection.createStatement();

    def mktSegmentIdRecord = connection.rows("select Mkt_Seg_id from Mkt_Seg where Mkt_Seg_Code='CDP'")
    def mkt_seg_ID = mktSegmentIdRecord.Mkt_Seg_id.get(0)

    def thatsql2 = "SELECT count(*) FROM parLib.ma_" + mkt_seg_ID + " "
    println "Executing this against sas jdbc ${thatsql2}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql2}")
    result = statement.executeQuery(thatsql2);
    while (result.next()) {
        def rows = result.getDouble(1);
        assertTrue(rows == 0.0)
        assertCount = assertCount + 1
        println "rows after CDP in new  ma_ tables : " + rows
        JemsTest.logm("rows after CDP in new  ma_ tables : " + rows)
    }

    def thatsql3 = "SELECT count(*) FROM parLib.ma_los_" + mkt_seg_ID + " "
    println "Executing this against sas jdbc ${thatsql3}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql3}")
    result = statement.executeQuery(thatsql3);
    while (result.next()) {
        def rows = result.getDouble(1);
        assertTrue(rows == 0.0)
        assertCount = assertCount + 1
        println "rows after CDP in new  ma_los tables : " + rows
        JemsTest.logm("rows after CDP in new  ma_los tables : " + rows)
    }

    def thatsq4 = "SELECT count(*) FROM parLib.ma_org_" + mkt_seg_ID + " "
    println "Executing this against sas jdbc ${thatsq4}"
    JemsTest.logm("Executing this against sas jdbc ${thatsq4}")
    result = statement.executeQuery(thatsq4);
    while (result.next()) {
        def rows = result.getDouble(1);
        assertTrue(rows == 0.0)
        assertCount = assertCount + 1
        println "rows after CDP in new  ma_org tables : " + rows
        JemsTest.logm("rows after CDP in new  ma_org tables : " + rows)
    }

    def thatsql5 = "SELECT count(*) FROM parLib.ma_los_org_" + mkt_seg_ID + " "
    println "Executing this against sas jdbc ${thatsql5}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql5}")
    result = statement.executeQuery(thatsql5);
    while (result.next()) {
        def rows = result.getDouble(1);
        assertTrue(rows == 0.0)
        assertCount = assertCount + 1
        println "rows after CDP in new  ma_los_org tables : " + rows
        JemsTest.logm("rows after CDP in new  ma_los_org tables : " + rows)
    }

    def mktSegmentIdRecord2 = connection.rows("select Mkt_Seg_id from Mkt_Seg where Mkt_Seg_Code='FI_TUQSBY'")
    def mkt_seg_ID2 = mktSegmentIdRecord2.Mkt_Seg_id.get(0)

    def thatsql6 = "SELECT count(*) FROM parLib.ma_" + mkt_seg_ID2 + " "
    println "Executing this against sas jdbc ${thatsql6}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql6}")
    result = statement.executeQuery(thatsql6);
    while (result.next()) {
        def rows = result.getDouble(1);
        assertTrue(rows == 17148.0)
        assertCount = assertCount + 1
        println "rows after CDP in existing ma_ tables : " + rows
        JemsTest.logm("rows after CDP in existing ma_ tables : " + rows)
    }

    def thatsql7 = "SELECT count(*) FROM parLib.ma_los_" + mkt_seg_ID2 + " "
    println "Executing this against sas jdbc ${thatsql7}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql7}")
    result = statement.executeQuery(thatsql7);
    while (result.next()) {
        def rows = result.getDouble(1);
        //assertTrue(rows == 10608.0)
        assertTrue(rows == 11396.0)
        assertCount = assertCount + 1
        println "rows after CDP in existing ma_los tables : " + rows
        JemsTest.logm("rows after CDP in existing ma_los tables : " + rows)
    }

    def thatsql8 = "SELECT count(*) FROM parLib.ma_org_" + mkt_seg_ID2 + " "
    println "Executing this against sas jdbc ${thatsql8}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql8}")
    result = statement.executeQuery(thatsql8);
    while (result.next()) {
        def rows = result.getDouble(1);
        assertTrue(rows == 9864.0)
        assertCount = assertCount + 1
        println "rows after CDP in existing ma_org tables : " + rows
        JemsTest.logm("rows after CDP in existing ma_org tables : " + rows)
    }

    def thatsql9 = "SELECT count(*) FROM parLib.ma_los_org_" + mkt_seg_ID2 + " "
    println "Executing this against sas jdbc ${thatsql9}"
    JemsTest.logm("Executing this against sas jdbc ${thatsql9}")
    result = statement.executeQuery(thatsql9);
    while (result.next()) {
        def rows = result.getDouble(1);
        assertTrue(rows == 5676.0)
        assertCount = assertCount + 1
        println "rows after CDP in existing ma_los_org tables : " + rows
        JemsTest.logm("rows after CDP in existing ma_los_org tables : " + rows)
    }


    def mktSegmentIdRecord3 = connection.rows("select Mkt_Seg_id from Mkt_Seg where Mkt_Seg_Code='HU'")
    def mkt_seg_ID3 = mktSegmentIdRecord3.Mkt_Seg_id.get(0)

    def RoomTypeIdRecord3 = connection.rows("select Accom_Type_ID from Accom_Type where Accom_Type_Code='QR'")
    def room_type_ID3 = RoomTypeIdRecord3.Accom_Type_ID.get(0)


    def thatsq20 = "SELECT * FROM parLib.ma_" + mkt_seg_ID3 + " where occupancy_dt=19642 and capture_dttm=1694000160 and accom_type_id=" + room_type_ID3 + " "
    println "Executing this against sas jdbc ${thatsq20}"
    JemsTest.logm("Executing this against sas jdbc ${thatsq20}")
    result = statement.executeQuery(thatsq20);
    while (result.next()) {
        assertEquals("Arrivals", "0.0", result.getDouble(4).toString())
        assertEquals("Departures", "0.0", result.getDouble(5).toString())
        assertEquals("Room Sold", "2.0", result.getDouble(9).toString())
        assertEquals("Room Revenue", "0.01852", result.getDouble(8).toString())
        assertEquals("Total Revenue", "0.01852", result.getDouble(10).toString())
        assertEquals("Food Revenue", "0.0", result.getDouble(11).toString())
        assertCount = assertCount + 5
    }
    statement.close();
    sasDatasetPartitionConnection.close();
    connection.close();
    assertTrue("assertion Count Should not be zero", assertCount == 13)
    println("Total Number Of Assertions : " + assertCount)

}


void cleanDecisionDeliveryJobs() {
    def jobDbConnection = DbConnection.getJobDbConnection()
    Set<Long> decisionDeliveryJobInstances = new HashSet<Long>()
    def selectJobId = "select JI.JOB_INSTANCE_ID from JOB_INSTANCE as JI join JOB_INSTANCE_WORK_CONTEXT as JIWC on JI.JOB_INSTANCE_ID=JIWC.JOB_INSTANCE_ID " +
            " where JOB_NAME in ('RDSDecisionDeliveryJob','OperaDecisionDeliveryJob') and JIWC.PROPERTY_ID=11022"
    jobDbConnection.eachRow(selectJobId) { row ->
        decisionDeliveryJobInstances.add((Long) row.JOB_INSTANCE_ID)
    }
    JemsTest jemsTest = new JemsTest() {
        @Override
        protected String getPropertyId() {
            return null
        }

        @Override
        protected String getPropertyCode() {
            return null
        }
    }
    jobDbConnection.executeUpdate("delete from PROBLEM_NOTE ")
    jemsTest.setJobDbConnection(jobDbConnection)
    jemsTest.deleteJobInstances(decisionDeliveryJobInstances)
}

Given(~/numberbookeddays greater than (\d+)$/) { int arg1 ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def FileMetaData = connection.rows("select top 1 SnapShot_DT from File_Metadata where Record_Type_ID =3 order by 1 desc")
    def snapshotDt = FileMetaData.SnapShot_DT.get(0)
    def newDate = snapshotDt - 366
    def BKHistoryStDate = newDate.format("ddMMMyyyy")
    connection.executeUpdate("update IP_Cfg_Property_Attribute set value = '" + BKHistoryStDate + "'where Attribute_Name = 'PROP_BK_HISTORY_START_DT'")
    connection.close()
}

Given(~/sasfeaturecount Equal to (\d+)$/) { int arg1 ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    // connection.executeUpdate("update IP_Cfg_Property_Attribute set value = 0 where Attribute_Name = 'Use_BOOK_DATA'")
    //connection.executeUpdate("update IP_Cfg_Property_Attribute set value = 1 where Attribute_Name = 'GRP_BLK_BKCRV'")
    connection.executeUpdate("update ovrd_property_attribute set value=0 where IP_Cfg_Property_Attribute_ID =(select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'use_book_data')")
    connection.executeUpdate("update ovrd_property_attribute set value=1 where IP_Cfg_Property_Attribute_ID = (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'GRP_BLK_BKCRV')")
    connection.close()
}

Given(~/ExtendedLOSDataEnabled is "([^"]*)"$/) { String arg1 ->
    // Write code here that turns the phrase above into concrete actions
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def context = 'pacman.SandBox.OPER2';
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.PreProduction.ExtendedLOSDataEnabled', context, arg1);
}

Then(~/UseBookDataToggle is enabled$/) { ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def ipCfgPropAtr = connection.rows("select value from IP_Cfg_Property_Attribute where Attribute_Name ='Use_BOOK_DATA'")
    def BkDataValue = ipCfgPropAtr.value.get(0)
    def ipCfgPropAtr2 = connection.rows("select value from IP_Cfg_Property_Attribute where Attribute_Name ='GRP_BLK_BKCRV'")
    def grpBlkBkCrv = ipCfgPropAtr2.value.get(0)
    def ovrdPropAtr = connection.rows("select value from ovrd_property_attribute where IP_Cfg_Property_Attribute_ID = (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'use_book_data')")
    def BkDataOvrdValue = ovrdPropAtr.value.get(0)
    def ovrdPropAtr2 = connection.rows("select value from ovrd_property_attribute where IP_Cfg_Property_Attribute_ID = (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'GRP_BLK_BKCRV')")
    def grpBlkBkCrvOvrd = ovrdPropAtr2.value.get(0)
    connection.close()
    assertTrue(Integer.parseInt(BkDataValue) == 0)
    assertTrue(Integer.parseInt(grpBlkBkCrv) == 1)
    assertTrue(Integer.parseInt(BkDataOvrdValue) == 1)
    assertTrue(Integer.parseInt(grpBlkBkCrvOvrd) == 1)
    System.out.println("Success")
}

Then(~/UseBookDataToggle is not enabled$/) { ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def ipCfgPropAtr = connection.rows("select value from IP_Cfg_Property_Attribute where Attribute_Name ='Use_BOOK_DATA'")
    def BkDataValue = ipCfgPropAtr.value.get(0)
    def ipCfgPropAtr2 = connection.rows("select value from IP_Cfg_Property_Attribute where Attribute_Name ='GRP_BLK_BKCRV'")
    def grpBlkBkCrv = ipCfgPropAtr2.value.get(0)
    def ovrdPropAtr = connection.rows("select value from ovrd_property_attribute where IP_Cfg_Property_Attribute_ID = (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'use_book_data')")
    def BkDataOvrdValue = ovrdPropAtr.value.get(0)
    def ovrdPropAtr2 = connection.rows("select value from ovrd_property_attribute where IP_Cfg_Property_Attribute_ID = (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'GRP_BLK_BKCRV')")
    def grpBlkBkCrvOvrd = ovrdPropAtr2.value.get(0)
    connection.close()
    assertTrue(Integer.parseInt(BkDataValue) == 0)
    assertTrue(Integer.parseInt(grpBlkBkCrv) == 1)
    assertTrue(BkDataOvrdValue == "0")
    assertTrue(grpBlkBkCrvOvrd == "1")
    System.out.println("Success")
}

And(~/Sync Flag isRoomClassDirty is set$/) { ->
    Long jobInstanceID = DbConnection.getJobDbConnection().rows(" select max(job_instance_id) jobInstanceID from job.dbo.Job_Instance where Job_Name='OperaDataLoad'")[0].jobInstanceID
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def context = 'pacman.SandBox.OPER2'
    String value = PropertyRolloutRESTUtil.isSyncFlagDirty(client, 'ACCOMMODATION_CONFIGURATION_CHANGED', context)
//    assertTrue(value=="true")
}


Given(~/numberbookeddays less than (\d+)$/) { int arg1 ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def FileMetaData = connection.rows("select top 1 SnapShot_DT from File_Metadata where Record_Type_ID =3 order by 1 desc")
    def snapshotDt = FileMetaData.SnapShot_DT.get(0)
    def newDate = snapshotDt - 360
    def BKHistoryStDate = newDate.format("ddMMMyyyy")
    connection.executeUpdate("update IP_Cfg_Property_Attribute set value = '" + BKHistoryStDate + "' where Attribute_Name = 'PROP_BK_HISTORY_START_DT'")
    connection.close()
}
Given(~/I clear OpHistory SAS Dataset$/) { ->
    // Write code here that turns the phrase above into concrete actions
    def statement;
    def sasDatasetConnection = com.ideas.tetris.util.DbConnection.getSasDatasetConnection("11022")
    statement = sasDatasetConnection.createStatement();
    def result = statement.executeUpdate("delete from mySasLib.ophistory")

}
Then(~/it is Pass$/) { ->
    // Write code here that turns the phrase above into concrete actions
    def i = 20
}
And(~/Calibration is Completed$/) { ->
    // Write code here that turns the phrase above into concrete actions
    def statement;
    def sasDatasetConnection = DbConnection.getSasDatasetConnection("11022")
    statement = sasDatasetConnection.createStatement();
    def result = statement.executeQuery("select * from mySasLib.ophistory where opName='calibration Succedded'")
    //result.getFetchSize()
    int i = 0
    if (null != result) {
        while (result.next()) {
            i++
        }
    }
    assertTrue(i > 0)
}

And(~/Calibration is not completed$/) { ->
    // Write code here that turns the phrase above into concrete actions
    def statement;
    def sasDatasetConnection = DbConnection.getSasDatasetConnection("11022")
    statement = sasDatasetConnection.createStatement();
    def result = statement.executeQuery("select * from mySasLib.ophistory where opName='calibration Succedded'")
    //result.getFetchSize()
    int i = 0
    if (null != result) {
        while (result.next()) {
            i++
        }
    }
    assertTrue(i == 0)
}
Given(~/Use_Book_Data disable in propAttr but enabled in OvrdAttr Table$/) { ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.executeUpdate("update IP_Cfg_Property_Attribute set value = 0 where Attribute_Name = 'Use_BOOK_DATA'")
    connection.executeUpdate("delete from ovrd_property_attribute where IP_Cfg_Property_Attribute_ID in (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name in ('use_book_data','GRP_BLK_BKCRV'))")
    connection.executeUpdate("insert into ovrd_property_attribute (IP_Cfg_Property_Attribute_ID ,value,Status_ID , Created_By_User_ID , Created_DTTM , Last_Updated_By_User_ID , Last_Updated_DTTM ) (select IP_Cfg_Property_Attribute_ID,1,1,11403,'2018-07-17 12:50:15.210',11403,'2018-07-17 12:50:15.210' from IP_Cfg_Property_Attribute where Attribute_Name in ('use_book_data','GRP_BLK_BKCRV') )")
    connection.close()
}

Then(~/UseBookDataToggle is not enabled in Main Table$/) { ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def ipCfgPropAtr = connection.rows("select value from IP_Cfg_Property_Attribute where Attribute_Name ='Use_BOOK_DATA'")
    def BkDataValue = ipCfgPropAtr.value.get(0)
    def ipCfgPropAtr2 = connection.rows("select value from IP_Cfg_Property_Attribute where Attribute_Name ='GRP_BLK_BKCRV'")
    def grpBlkBkCrv = ipCfgPropAtr2.value.get(0)
    def ovrdPropAtr = connection.rows("select value from ovrd_property_attribute where IP_Cfg_Property_Attribute_ID = (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'use_book_data')")
    def BkDataOvrdValue = ovrdPropAtr.value.get(0)
    def ovrdPropAtr2 = connection.rows("select value from ovrd_property_attribute where IP_Cfg_Property_Attribute_ID = (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'GRP_BLK_BKCRV')")
    def grpBlkBkCrvOvrd = ovrdPropAtr2.value.get(0)
    connection.close()
    assertTrue(Integer.parseInt(BkDataValue) == 0)
    assertTrue(Integer.parseInt(grpBlkBkCrv) == 1)
    assertTrue(BkDataOvrdValue == "1")
    assertTrue(grpBlkBkCrvOvrd == "1")
    System.out.println("Success")
}
Given(~/numberbookeddays equal to (\d+)$/) { int arg1 ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def FileMetaData = connection.rows("select top 1 SnapShot_DT from File_Metadata where Record_Type_ID =3 order by 1 desc")
    def snapshotDt = FileMetaData.SnapShot_DT.get(0)
    def newDate = snapshotDt - 365
    def BKHistoryStDate = newDate.format("ddMMMyyyy")
    connection.executeUpdate("update IP_Cfg_Property_Attribute set value = '" + BKHistoryStDate + "' where Attribute_Name = 'PROP_BK_HISTORY_START_DT'")
    connection.close()
}

Given(~/sasfeaturecount Equal to zero$/) { ->
    // Write code here that turns the phrase above into concrete actions
    connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    // connection.executeUpdate("update IP_Cfg_Property_Attribute set value = 0 where Attribute_Name = 'Use_BOOK_DATA'")
    //connection.executeUpdate("update IP_Cfg_Property_Attribute set value = 1 where Attribute_Name = 'GRP_BLK_BKCRV'")
    connection.executeUpdate("update ovrd_property_attribute set value=0 where IP_Cfg_Property_Attribute_ID =(select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'use_book_data')")
    connection.executeUpdate("update ovrd_property_attribute set value=0 where IP_Cfg_Property_Attribute_ID = (select IP_Cfg_Property_Attribute_ID from IP_Cfg_Property_Attribute where Attribute_Name = 'GRP_BLK_BKCRV')")
    connection.close()
}

Given(~/AutoDetectSufficientBookedRTPace is "([^"]*)"$/) { String arg1 ->
    // Write code here that turns the phrase above into concrete actions
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def context = 'pacman.SandBox.OPER2';
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.ams.autoDetectSufficientBookedRTPace', context, arg1);
}