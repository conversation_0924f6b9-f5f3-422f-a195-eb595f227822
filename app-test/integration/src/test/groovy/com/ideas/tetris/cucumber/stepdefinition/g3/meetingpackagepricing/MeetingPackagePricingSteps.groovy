package com.ideas.tetris.cucumber.stepdefinition.g3.meetingpackagepricing

import com.google.gson.Gson
import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.PropertiesTestHelper
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import io.cucumber.java.After
import io.cucumber.java.Before
import io.cucumber.java.en.And
import io.cucumber.java.en.Given
import io.cucumber.java.en.Then
import io.cucumber.java.en.When
import io.restassured.RestAssured
import io.restassured.builder.RequestSpecBuilder
import io.restassured.path.json.JsonPath
import io.restassured.response.Response
import io.restassured.specification.RequestSpecification
import org.testng.Assert

import java.nio.file.Files
import java.nio.file.Paths

import static io.restassured.RestAssured.given

class MeetingPackagePricingSteps {

    def tenantDbConnection
    TetrisRESTClient tetrisRestClient
    public static final String LOGIN_USERNAME = "<EMAIL>"
    public static final String LOGIN_PASSWORD = "password"
    public static final String REST_BASE_URI = RestServiceUtil.getPacmanRestURL()
    public static final String DBName_0026 = "991698"
    public static final int PROPERTY_ID_0026 = 991698
    public static final String CONTEXT_VALUE_0026 = "pacman.SandBox.0026"
    public static final String PROPERTY_EXTERNAL_SYSTEM = "pacman.core.property.externalSystem"
    public static final String PROPERTY_YIELD_CURRENCY_CODE = "pacman.core.property.YieldCurrencyCode"
    public static final String MEETING_PACKAGE_PRICING_UPLOAD_TYPE = "pacman.integration.pcrs.meetingPackagePricing.uploadtype"
    public static final String DECISION_UPLOAD_WINDOW = 'pacman.upload.DecisionUploadWindowBDEDays'
    public static final String OPTIMIZATION_WINDOW = 'pacman.optimization.optimizationWindowBDE'
    public static final String OPTIMIZATION_WINDOW_CDP = 'pacman.optimization.optimizationWindowCDP'
    public static final String APPLY_VARIABLE_DECISION_WINDOW = "pacman.integration.applyVariableDecisionWindow"
    public static final String USE_MEETING_PACKAGE_ID = "pacman.feature.useMeetingPackageId"
    public static final String VARIABLE_DECISION_WINDOW_DAYS = "pacman.integration.variableDecisionWindowDays"
    public static final String OVERRIDE_VARIABLE_WINDOW_DECISON = "pacman.integration.overrideVariableDecisionWindow"
    public static final String COMPLETE_DECISION_DAY_OF_WEEK = "pacman.integration.completeDecisionUploadDaysOfWeek"
    public static final String RESULT_DIR_PATH = System.getProperty("user.dir") + "/src/test/resources/MeetingPackageJSON/"
    public static final String FIRST_TIME_UPLOAD_MPP = "firstTimeFullUploadMPP.json"
    public static final String DIFFERENTIAL_CDP_UPLOAD_MPP = "differentialCDPUploadMPP.json"
    public static final String DIFFERENTIAL_BDE_UPLOAD_MPP = "differentialBDEUploadMPP.json";
    public static final String FETCH_DECISION_API_PATH = "/hilstarDecisions/meeting-package-pricing/v1"
    public static final String DIFFERENTIAL_PACE_DECISION_POPULATION_API_PATH = "/meeting-package/decision/populatePaceDifferentialDecisions/v1"
    public static final String PROPERTY_ID = "propertyId"
    public static final String EXTERNAL_SYSTEM = "externalSystem"
    public static final String OPERATION_TYPE = "operationType"
    public static final String PCRS = "pcrs"
    public static final String BDE = "BDE"
    public static final String CDP = "CDP"
    Gson gson = new Gson()

    @Before(value = "@MeetingPackagePricingDecisionUploadTest")
    void setup() {
        tenantDbConnection = DbConnection.getTenantDatabaseConnection(DBName_0026)
        tetrisRestClient = PropertiesTestHelper.login(REST_BASE_URI, LOGIN_USERNAME, LOGIN_PASSWORD)
        tetrisRestClient.headers.propertyId = PROPERTY_ID_0026
        RestAssured.baseURI = REST_BASE_URI
        clearAllMPPricingDecisions()
        setUpMPProducts()
    }

    private setUpMPProducts() {
        tenantDbConnection.execute("INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('FullDay', 1, '08:00:00.0000000', '20:00:00.0000000', 1, getdate(), 1, getdate())")
        tenantDbConnection.execute("INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('AM', 1, '08:00:00.0000000', '12:00:00.0000000', 1, getdate(), 1, getdate())")
        tenantDbConnection.execute("INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('PM', 1, '16:00:00.0000000', '20:00:00.0000000', 1, getdate(), 1, getdate())")
        tenantDbConnection.execute("Insert into MP_Product(Name, Description, System_Default, Code, Type, Dependent_Product_ID, Is_DOW_Offset, Is_Upload, Status_ID, Is_Default_Inactive, Invalid_Reason_ID, Is_Optimized, Product_Floor_Rate, Price_Rounding_Rule, Minimum_Price_Change, Offset_Method, Display_Order, Centrally_Managed, Is_Overridable, Floor_Type, Floor_Percentage, MP_Cfg_Day_Part_ID, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM, Package_ID)\n" +
                "Values\n" +
                "('All Day Meeting Package','AM Meeting Package',1,'Meeting Product','DAILY',NULL,0,1,1,0,NULL,0,NULL,1,NULL,NULL,1,0,1,2,NULL,(Select MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'FullDay'),11403,11403,11403,11403,'ADMP'),\n" +
                "('AM Meeting Package','AM Meeting Package',1,'Meeting Product','DAILY',NULL,0,1,1,0,NULL,0,NULL,1,NULL,NULL,1,0,1,2,NULL,(Select MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'AM'),11403,11403,11403,11403,'AMMP'),\n" +
                "('PM Meeting Package','PM Meeting Package',1,'Meeting Product','DAILY',NULL,0,1,1,0,NULL,0,NULL,1,NULL,NULL,1,0,1,2,NULL,(Select MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'PM'),11403,11403,11403,11403, 'PMMP')")
    }

    static RequestSpecification requestSpecification() {
        return new RequestSpecBuilder().setBaseUri(REST_BASE_URI)
                .addHeader("Authorization", "Basic ********************************")
                .addQueryParam(PROPERTY_ID, PROPERTY_ID_0026)
                .build();
    }

    @And("property external system is set to {string}")
    void setPropertyExternalSystem(String propertyExternalSystem) {
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, PROPERTY_EXTERNAL_SYSTEM, CONTEXT_VALUE_0026, propertyExternalSystem);
    }

    @And("property yieldCurrencyCode is set to {string}")
    void setPropertyYieldCurrencyCode(String yieldCurrencyCode) {
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, PROPERTY_YIELD_CURRENCY_CODE, CONTEXT_VALUE_0026, yieldCurrencyCode);
    }

    @And("property upload type is set to {string}")
    void setPropertyUploadType(String uploadType) {
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, MEETING_PACKAGE_PRICING_UPLOAD_TYPE, CONTEXT_VALUE_0026, uploadType);
    }

    @And("Property Optimization Window is set to {string} days")
    void setBDEOptimizationWindow(String optimizationWindow) {
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, OPTIMIZATION_WINDOW, CONTEXT_VALUE_0026, optimizationWindow);
    }

    @And("Property Upload Window is set to {string} days")
    void setBDEUploadWindow(String uploadWindow) {
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, DECISION_UPLOAD_WINDOW, CONTEXT_VALUE_0026, uploadWindow);
    }

    @And("Property CDP Optimization Window is {string} days")
    void setCDPOptimizationWindow(String uploadWindow) {
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, OPTIMIZATION_WINDOW_CDP, CONTEXT_VALUE_0026, uploadWindow);
    }

    @And("Variable Decision Toggle is set to {string}")
    void setVariableDecisionWindowToFalse(String variableDecisionEnabled) {
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, APPLY_VARIABLE_DECISION_WINDOW, CONTEXT_VALUE_0026, variableDecisionEnabled);
    }

    @And("Use Meeting Package Id toggle is set to {string}")
    void setUseMeetingPackageIdToggle(String useMeetingPackageId) {
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, USE_MEETING_PACKAGE_ID, CONTEXT_VALUE_0026, useMeetingPackageId);
    }

    @And("Variable Decision Window is set to {string} days")
    void setVariableDecisionWindowDays(String variableDecisionWindow){
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, VARIABLE_DECISION_WINDOW_DAYS, CONTEXT_VALUE_0026, variableDecisionWindow);
    }

    @And("Apply Variable Decision Window Toggle is Enabled")
    void enableVariableDecisionWindow(){
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, APPLY_VARIABLE_DECISION_WINDOW, CONTEXT_VALUE_0026, 'true');
    }

    @And("Override Variable Decision Window Toggle is Disabled")
    void disableVariableDecisionWindow(){
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, OVERRIDE_VARIABLE_WINDOW_DECISON, CONTEXT_VALUE_0026, 'false');
    }

    @And("Complete Decision Upload Day Of Week is {string}")
    void completeDecisionDayOfWeek(String weekday){
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, COMPLETE_DECISION_DAY_OF_WEEK, CONTEXT_VALUE_0026, weekday);
    }

    @And("The pace differential Meeting Package decisions are populated")
    static void populatePaceDifferentialMeetingPackageDecisions() {
        given().spec(requestSpecification())
                .queryParam(OPERATION_TYPE, BDE)
                .when().post(DIFFERENTIAL_PACE_DECISION_POPULATION_API_PATH)
                .then().assertThat().statusCode(200)
                .extract().response();

    }

    @And("The pace differential Meeting Package decisions are populated for CDP")
    static void populatePaceDifferentialMeetingPackageDecisionsCDP() {
        given().spec(requestSpecification())
                .queryParam(OPERATION_TYPE, CDP)
                .when().post(DIFFERENTIAL_PACE_DECISION_POPULATION_API_PATH)
                .then().assertThat().statusCode(200)
                .extract().response();

    }

    @And("Clear Dirty Sync flags")
    void clearDirtySyncFlags() {
        tenantDbConnection.execute("UPDATE Sync_Flags SET Value = 0 WHERE Value = 1")
    }

    @When("The Meeting Package decisions are generated")
    void populateMeetingPackagePricingDecisions() {
        tenantDbConnection.execute("Insert into MP_Decision_Bar(Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, Created_DTTM)\n" +
                "Select Decision_ID, (Select MP_Product_ID FROM MP_Product WHERE Name = 'All Day Meeting Package'), Decision_Reason_Type_ID,10,Arrival_dt, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, CreateDate from CP_Decision_Bar_Output where Product_Id=1 and Accom_Type_ID=13\n" +
                "\n" +
                "Insert into MP_Decision_Bar(Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, Created_DTTM)\n" +
                "Select Decision_ID, (Select MP_Product_ID FROM MP_Product WHERE Name = 'AM Meeting Package'), Decision_Reason_Type_ID,10,Arrival_dt, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, CreateDate from CP_Decision_Bar_Output where Product_Id=1 and Accom_Type_ID=12\n" +
                "\n" +
                "Insert into MP_Decision_Bar(Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, Created_DTTM)\n" +
                "Select Decision_ID, (Select MP_Product_ID FROM MP_Product WHERE Name = 'PM Meeting Package'), Decision_Reason_Type_ID,10,Arrival_dt, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, CreateDate from CP_Decision_Bar_Output where Product_Id=1 and Accom_Type_ID=6\n" +
                "\n" +
                "Insert into MP_Decision_Bar(Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, Created_DTTM)\n" +
                "Select Decision_ID, (Select MP_Product_ID FROM MP_Product WHERE Name = 'All Day Meeting Package'), Decision_Reason_Type_ID,11,Arrival_dt, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, CreateDate from CP_Decision_Bar_Output where Product_Id=1 and Accom_Type_ID=7\n" +
                "\n" +
                "Insert into MP_Decision_Bar(Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, Created_DTTM)\n" +
                "Select Decision_ID, (Select MP_Product_ID FROM MP_Product WHERE Name = 'AM Meeting Package'), Decision_Reason_Type_ID,11,Arrival_dt, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, CreateDate from CP_Decision_Bar_Output where Product_Id=1 and Accom_Type_ID=11\n" +
                "\n" +
                "Insert into MP_Decision_Bar(Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, Created_DTTM)\n" +
                "Select Decision_ID, (Select MP_Product_ID FROM MP_Product WHERE Name = 'PM Meeting Package'), Decision_Reason_Type_ID,11,Arrival_dt, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, CreateDate from CP_Decision_Bar_Output where Product_Id=1 and Accom_Type_ID=8")
        tenantDbConnection.execute("")
    }

    @And("The Meeting Package Pricing decisions are generated in the subsequent BDE optimization")
    void populateSubsequentBDEMeetingPackagePricingDecisions() {
        tenantDbConnection.execute("insert into decision values(991698,'2022-09-06','2022-09-06','2022-09-06','2022-09-06',1,GETDATE(),GETDATE(),13,GETDATE())")
        String latestDecisionIdSql = 'SELECT TOP 1(Decision_ID) FROM Decision WHERE Decision_Type_ID = 1 ORDER BY Decision_ID DESC'
        tenantDbConnection.execute("UPDATE MP_Decision_Bar SET Decision_ID = (" + latestDecisionIdSql + "), Final_BAR = Final_BAR + 10 \n" +
                " WHERE Occupancy_DT >= '2022-09-06' AND Occupancy_DT <= '2023-09-05' AND FS_Cfg_Func_Room_ID = 10 ")
    }

    @And("The Meeting Package Pricing decisions are generated in the subsequent CDP optimization")
    void populateSubsequentCDPMeetingPackagePricingDecisions() {
        tenantDbConnection.execute("insert into decision values(991698,'2022-09-06','2022-09-06','2022-09-06','2022-09-06',2,GETDATE(),GETDATE(),13,GETDATE())")
        String latestDecisionIdSql = 'SELECT TOP 1(Decision_ID) FROM Decision WHERE Decision_Type_ID = 2 ORDER BY Decision_ID DESC'
        tenantDbConnection.execute("UPDATE MP_Decision_Bar SET Decision_ID = (" + latestDecisionIdSql + "), Final_BAR = Final_BAR + 10 \n" +
                " WHERE Occupancy_DT >= '2022-09-06' AND Occupancy_DT <= '2022-10-20' AND FS_Cfg_Func_Room_ID IN(11) ")
    }

    @Then("Full decision for Meeting Package Pricing would be sent from caught up date till the upload window")
    void assertAllOccupancyDatesForMPPArePresentInTheResultFromCaughtUpDateTillUploadWindowEndDate() {
        Response actualMPPDecision = given().spec(requestSpecification())
                .queryParam(EXTERNAL_SYSTEM, PCRS)
                .queryParam(OPERATION_TYPE, BDE)
                .when().get(FETCH_DECISION_API_PATH)
                .then().assertThat().statusCode(200)
                .extract().response();

        JsonPath actualDecisionsJson = new JsonPath(actualMPPDecision.asString());
        String actualDecisions = gson.toJson(actualDecisionsJson.get());
        JsonPath expectedDecisionsJson = new JsonPath(new String(Files.readAllBytes(Paths.get(RESULT_DIR_PATH + FIRST_TIME_UPLOAD_MPP))));
        String expectedDecisions = gson.toJson(expectedDecisionsJson.get());
        Assert.assertTrue(expectedDecisions == actualDecisions, "Expected and Actual Meeting Package Decisions for first time full upload are same")
    }

    @Then("Full decision would be sent from caught up date till the variable upload window")
    void assertAllOccupancyDatesForMPPArePresentInTheResultFromCaughtUpDateTillVariableUploadWindowEndDate() {
        Response actualMPPDecision = given().spec(requestSpecification())
                .queryParam(EXTERNAL_SYSTEM, PCRS)
                .queryParam(OPERATION_TYPE, BDE)
                .when().get(FETCH_DECISION_API_PATH)
                .then().assertThat().statusCode(200)
                .extract().response();

        JsonPath actualDecisionsJson = new JsonPath(actualMPPDecision.asString());
        String actualDecisions = gson.toJson(actualDecisionsJson.get());
        JsonPath expectedDecisionsJson = new JsonPath(new String(Files.readAllBytes(Paths.get(RESULT_DIR_PATH + FIRST_TIME_UPLOAD_MPP))));
        String expectedDecisions = gson.toJson(expectedDecisionsJson.get());
        Assert.assertTrue(expectedDecisions == actualDecisions, "Expected and Actual Meeting Package Decisions for first time full upload are same")
    }

    @Then("Differential decision for Meeting Package Pricing would be sent from caught up date till the CDP window")
    void assertAllOccupancyDatesForMPPArePresentInTheResultFromCaughtUpDateTillCDPWindowEndDate() {
        Response actualMPPDecision = given().spec(requestSpecification())
                .queryParam(EXTERNAL_SYSTEM, PCRS)
                .queryParam(OPERATION_TYPE, CDP)
                .when().get(FETCH_DECISION_API_PATH)
                .then().assertThat().statusCode(200)
                .extract().response();

        JsonPath actualDecisionsJson = new JsonPath(actualMPPDecision.asString());
        String actualDecisions = gson.toJson(actualDecisionsJson.get());
        JsonPath expectedDecisionsJson = new JsonPath(new String(Files.readAllBytes(Paths.get(RESULT_DIR_PATH + DIFFERENTIAL_CDP_UPLOAD_MPP))));
        String expectedDecisions = gson.toJson(expectedDecisionsJson.get());
        Assert.assertTrue(expectedDecisions == actualDecisions, "Expected and Actual Meeting Package Decisions for differential CDP upload are same")
    }

    @Then("Differential decision for Meeting Package Pricing would be sent from caught up date till the BDE window")
    void assertAllOccupancyDatesForMPPArePresentInTheResultFromCaughtUpDateTillBDEWindowEndDate() {
        Response actualMPPDecision = given().spec(requestSpecification())
                .queryParam(EXTERNAL_SYSTEM, PCRS)
                .queryParam(OPERATION_TYPE, BDE)
                .when().get(FETCH_DECISION_API_PATH)
                .then().assertThat().statusCode(200)
                .extract().response();

        JsonPath actualDecisionsJson = new JsonPath(actualMPPDecision.asString());
        String actualDecisions = gson.toJson(actualDecisionsJson.get());
        JsonPath expectedDecisionsJson = new JsonPath(new String(Files.readAllBytes(Paths.get(RESULT_DIR_PATH + DIFFERENTIAL_BDE_UPLOAD_MPP))));
        String expectedDecisions = gson.toJson(expectedDecisionsJson.get());
        Assert.assertTrue(expectedDecisions == actualDecisions, "Expected and Actual Meeting Package Decisions for differential CDP upload are same")
    }

    @And("Meeting Package Pricing decision is uploaded for the first time")
    void deleteLastUploadDateForMeetingPackagePricing() {
        tenantDbConnection.execute("Delete from Decision_Upload_Date_To_External_System where Decision_Name='MeetingPackagePricing'")
    }

    @And("Meeting Package Pricing decisions has already been uploaded before")
    void insertFirstTimeUploadDateRecordForMeetingPackagePricing() {
        String lastUploadDecisionIDSql = "Select TOP 1(Decision_ID) from Decision_Upload_Date_To_External_System \n" +
                "where  Decision_Name='hoteloverbooking' AND Status = 'SUC'\n" +
                "ORDER BY Decision_ID DESC"
        String latestDecisionIDSql = "Select TOP 1(Decision_ID) from Decision WHERE  Decision_Type_Id = 5 ORDER BY Decision_ID DESC"
        tenantDbConnection.execute("Delete from Decision_Upload_Date_To_External_System where Decision_Name='MeetingPackagePricing'")
        println("Insert Last Upload Date Record for Meeting Package Pricing in Decision_Upload_Date_To_External_System for differential Upload")
        String decisionUploadToExternalSystemInsertSql = "Insert into Decision_Upload_Date_To_External_System (Decision_ID,Decision_Name, Created_DTTM,Modify_DTTM,Last_Upload_DTTM,Status,Upload_Type,External_System_Name) \n" +
                "Select (" + latestDecisionIDSql + "),'MeetingPackagePricing' as Decision_Name,Created_DTTM,Modify_DTTM,Last_Upload_DTTM,Status,Upload_Type,'pcrs' from Decision_Upload_Date_To_External_System where Decision_ID= (" + lastUploadDecisionIDSql + ") \n" +
                "AND Decision_Name='hoteloverbooking' AND Status = 'SUC'"
        tenantDbConnection.execute(decisionUploadToExternalSystemInsertSql)
        println("Decision_Upload_Date_To_External_System")
    }

    @Given("BDE processing for Meeting Package Pricing is completed")
    void updateLastRunProcessingToBDE() {

    }

    @Given("CDP processing for Meeting Package Pricing is completed")
    void updateLastRunProcessingToCDP() {

    }

    @After(value = "@MeetingPackagePricingDecisionUploadTest")
    void tearDown() {
        clearAllMPPricingDecisions()
        tenantDbConnection = null;
    }


    private clearAllMPPricingDecisions() {
        tenantDbConnection.execute("Truncate table MP_Decision_Bar")
        tenantDbConnection.execute("Truncate table MP_Pace_Decision_Bar_Differential")
        tenantDbConnection.execute("Delete from MP_Product")
        tenantDbConnection.execute("Delete from MP_Cfg_Day_Part")
        tenantDbConnection.execute("DELETE FROM Decision_Upload_Date_To_External_System WHERE Decision_Name='MeetingPackagePricing'")
    }
}
