package com.ideas.tetris.cucumber.stepdefinition.g3.componentrooms

import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.PropertiesTestHelper
import com.ideas.tetris.util.opera.OperaGlobalFunctions
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import com.ideas.tetris.util.propertyrollout.PropertyRolloutTestHelper
import common.AMSInsertQuery
import common.InsertDemandOverrideQuery
import common.JemsTest
import common.OperaUtil
import cucumber.api.groovy.EN
import cucumber.api.groovy.Hooks
import groovy.sql.GroovyRowResult
import groovy.sql.Sql
import groovy.transform.Field
import io.cucumber.datatable.DataTable

import java.time.LocalDate

import static org.junit.Assert.assertEquals
import static org.junit.Assert.assertTrue

this.metaClass.mixin(Hooks)
this.metaClass.mixin(EN)

final String HOSPITALITY_ROOMS = "common/seedData/HospitalityRooms.sql"

@Field String SANDBOX_DB_TERRANEA = "011007"
@Field String DatabaseName = "011007"
@Field String propertyId = "11007"
@Field TetrisRESTClient tetrisRESTClient
JemsTest jemstest

Before('@CompRoomWithNewSASBDEFlow') {
    jemstest = new JemsTest() {
        @Override
        protected String getPropertyId() {
            return null
        }

        @Override
        protected String getPropertyCode() {
            return null
        }
    }
    setGlobalParameters(propertyId);
    jemstest.executeSqlFile(HOSPITALITY_ROOMS, SANDBOX_DB_TERRANEA)
    jemstest.updateOptOutputVersionToZero(SANDBOX_DB_TERRANEA)
}

void setGlobalParameters(String propertyId) {
    def params = [username: "<EMAIL>", password: "password"]
    def client = new TetrisRESTClient(RestServiceUtil.getPacmanRestURL(), params)
    def context = 'pacman.SandBox.TERRANEA';
    def roomTypsToInclude = 'L0RK,L0RD,L0VK,L0VD,L0SK,L0SD,L0FK,L0FD,L1VKS,L1SKS,L1FKS,L1GKS,L1PKS,B0VK,B0VD,B1VKS,ROO,ROR,C0RK,C0RD,C0VK,C0VD,C0SK,C0SD,C1RKS,C1VKS,C1SKS,V2RKG,V2VKU,V2VTU,V3RDG,V3VDG,V3VK2,V4VK2,B2VKK,B2VKD,C2RKD,C2VKD,C2SKD,C3RKM,C3VKM,C3SKM';
    client.headers.propertyId = propertyId
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.core.property.externalSystem', context, 'OPERA');
    PropertiesTestHelper.setPropertyStage(client, propertyId, PropertyRolloutTestHelper.STAGE_ONE_WAY)
    PropertyRolloutRESTUtil.registerSyncEvent(client, "ACCOMMODATION_CONFIG_CHANGED", context)
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.optimization.optimizationWindowBDE', context, '365');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.bar.EnableSingleBarDecision', context, 'false');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.bar.barDecision', context, 'BARByLOS');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.isAdvancedPriceRankingEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.ComponentRoomsEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.ComponentRoomsConfigurationCompleted', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.AnalyticalMarketSegmentEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.population.analyticalMarketSegmentMappingComplete', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.GroupPricingEnabled', context, 'false');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.isContinuousPricingEnabled', context, 'false');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.opera.roomTypesToInclude', context, roomTypsToInclude);
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.RoomTypeRecodingEnabled', context, 'false');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.RoomTypeRecodingUIEnabled', context, 'false');
    //PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.ProfitOptimizationEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.profitPopulationEnabled', context, 'true');
    //PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.ChannelOptimizationEnabled', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.integration.SendRoomTypesAsRoomClassLRV', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.preProduction.propertyPostProcessingJobEnable', context, 'true');
    PropertyRolloutRESTUtil.setConfigParamValue(client, 'pacman.feature.LongTermBDEProcessingEnabled', context, 'true');
}
void insertAMS(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute(AMSInsertQuery.QUERY)
    connection.close();
}

void insertDemandOverride(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute(InsertDemandOverrideQuery.QUERY1)
    connection.execute(InsertDemandOverrideQuery.QUERY2)
    connection.close();
}

void updateCorrelationIDAndBusinessTime(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    String correlationID = OperaGlobalFunctions.getCorrelationID()
    connection.execute("update opera.History_Incoming_Metadata set Business_Time=(select convert(varchar(8),(DATEADD(MINUTE, 20, (SELECT convert(varchar(8), (select Business_Time from opera.History_Incoming_Metadata where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)), 108)))),108)    ) where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)")
    connection.execute("update opera.History_Incoming_Metadata set Prepared_Time=(select convert(varchar(8),(DATEADD(MINUTE, 20, (SELECT convert(varchar(8), (select Business_Time from opera.History_Incoming_Metadata where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)), 108)))),108)    ) where Business_DT in ( select MAX(Business_DT) from opera.History_Incoming_Metadata)")
    connection.execute("update opera.Data_Load_Metadata   set Correlation_ID = '" + correlationID + "' where Correlation_ID = (select correlation_ID from opera.Data_Load_Metadata where \n" +
            "Data_Load_Metadata_ID = (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata))")
    connection.close();
}

Given(~/update Extended_Evaluation_Schedule set Day_Of_month$/) { ->
    updateDayOfMonthForLTBDE();
    updateEnableLTBDEForPricingINCP_Cfg(DatabaseName);
}

private void updateDayOfMonthForLTBDE() {
    int dayOfMonth = getSnapshotDate().getDayOfMonth();
    def globalDbConnection = DbConnection.getGlobalConnection()
    globalDbConnection.execute("INSERT INTO Extended_Evaluation_Schedule VALUES (6, 11007, " + dayOfMonth-1 + ", NULL, NULL, 1);")
    globalDbConnection.close()
}
private void updateEnableLTBDEForPricingINCP_Cfg(String DatabaseName) {
    def tenantDbConnection = DbConnection.getTenantDatabaseConnection(DatabaseName);
    tenantDbConnection.execute("UPDATE CP_Cfg SET Enable_LTBDE_For_Pricing = 1");
    tenantDbConnection.close();
}

private LocalDate getSnapshotDate() {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    def FileMetaData = connection.rows("select top 1 SnapShot_DT from File_Metadata where Record_Type_ID =3 order by 1 desc")
    connection.close()
    return FileMetaData.SnapShot_DT.get(0).toLocalDate();
}

Then(~/LTBDE job is completed$/) { ->
    Long jobInstanceID = DbConnection.getJobDbConnection().rows(" select max(job_instance_id) jobInstanceID from Job_Instance where Job_Name='LongTermBDEJob'")[0].jobInstanceID

    assert jobInstanceID == null : "Long Term BDE job is skipped"
    assert JemsTest.JOB_STATUS_COMPLETED.equals(JemsTest.getJobInstanceExitStatus(jobInstanceID))
}

Then(~/Updated Last Successful Extended Evaluation date$/) { ->
    def extendedEvaluation = DbConnection.getGlobalConnection().rows("select Last_Successful_Extended_Evaluation from Extended_Evaluation_Schedule where Property_ID =" + propertyId)
    assert extendedEvaluation.get(0).get("Last_Successful_Extended_Evaluation") != null : "Last_Successful_Extended_Evaluation is not updated "
}


Given(~/I execute operaDataLoad job for CR$/) { ->
    OperaUtil.executeOperaDataLoadJob(DatabaseName, propertyId, Boolean.TRUE)

}
Given(~/I execute operaDataLoad job CDP for CR$/) { ->
    OperaUtil.executeOperaDataLoadJob(DatabaseName, propertyId, Boolean.FALSE)
}

Given(~/Clean CR data and Build new data$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("delete from PACE_CR_Accom_Activity; delete from CR_Accom_Activity; delete from CR_Mkt_Accom_Activity ; delete from CR_Total_Activity; ")
    connection.close();
}
Then(~/ComponentRoomCalculationStep runs and CR tables get populated$/) { DataTable dataCount ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    dataCount.asMaps().get(0).each { tableName, rowCount ->
            def result = connection.firstRow("select  COUNT(*) as 'noOfRows' from " + tableName)
        assertEquals("Row count mismatch for table:" + tableName, Integer.valueOf(rowCount), result.noOfRows)
    }
    connection.close();
}

And(~/Total Activity should get populated with Room Sold according to physical Inventory Only$/) { DataTable dataCount ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    for (map in dataCount.asMaps()) {
        String tableName = map.get("tableName")
        def query = "select  sum(Rooms_Sold) as Rooms_Sold from " +tableName + " where occupancy_dt = '"+ map.get("Occupancy_dt") +"' "
        query += map.get("File_Metadata_ID") ? " and File_Metadata_ID=(select max(File_Metadata_ID) from pace_total_activity)" : ""
        def result = connection.firstRow(query)
        assertEquals("room_sold mismatch for table:" + tableName, Double.parseDouble(map.get("Rooms_Sold")), result.getAt(0),0.0001)
    }
    connection.close();

}


Given(~/Data is modified For BDE$/) { ->
    updateCorrelationIDAndBusinessTime(DatabaseName)
}

Given(~/Data is modified For CDP$/) { ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    createDataForTotal(connection,1.34,2)
    createDataForCTAT(connection,1.34,2)
    createDataForCTATForC1VKSDuringCDP(connection,2.34,4)
    createDataForCSATForAMS(connection,1.34,2)
    createDataForCSATForNonAMS(connection,1.34,2)
    createDataForCSATForTransactionDuringCDPonly(connection,1.2);
    updateCorrelationIDAndBusinessTime(DatabaseName)
    connection.close();
}

And(~/CR data getting updated in sas during BDE$/) { DataTable dataCount ->
    def sasDatasetConnection = DbConnection.getSasDatasetConnection(propertyId)
    def statement = sasDatasetConnection.createStatement();
    def sasDatasetPartitionConnection = DbConnection.getSasDatasetPartitionConnection(propertyId)
    def statmentParalib = sasDatasetPartitionConnection.createStatement();
    for (map in dataCount.asMaps()) {
        executeSasQuery(statement, statmentParalib, true, map)
    }

}

And(~/Revenue Split Revenues updated in activity tables$/) { DataTable dataCount ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    for (map in dataCount.asMaps()) {
        String tableName = map.get("tableName")
        def query = "select  room_revenue, food_revenue, total_revenue from " +tableName + " where occupancy_dt = '"+ map.get("occupancy_dt") +"' "
        query += map.get("mkt_seg_id") ? " and mkt_seg_id=" + map.get("mkt_seg_id") : ""
        query += map.get("accom_type_id")? " and accom_type_id=" + map.get("accom_type_id") : ""
        query += map.get("Business_Day_End_DT") ? " and Business_Day_End_DT='" + map.get("Business_Day_End_DT")+"'" : ""
        def result = connection.firstRow(query)
//        System.out.println(" tableName : " + tableName + " revenue " + result.getAt(0)+ " " + result.getAt(1)+ " " + result.getAt(2))
        assertEquals("room_revenue mismatch for table:" + tableName, Double.parseDouble(map.get("room_revenue")), result.getAt(0),0.0001)
        assertEquals("room_revenue mismatch for table:" + tableName, Double.parseDouble(map.get("food_revenue")), result.getAt(1),0.0001)
        assertEquals("room_revenue mismatch for table:" + tableName, Double.parseDouble(map.get("total_revenue")), result.getAt(2),0.0001)
    }
    connection.close();

}
private void validateResults(List<GroovyRowResult> result, List<GroovyRowResult> resultTemp) {
    assertEquals("Occupancy_nbr mismatch for accomType Id: 22", result.get(0).getAt(1), resultTemp.get(0).getAt(1) + (resultTemp.get(3).getAt(1)) + (resultTemp.get(4).getAt(1)), 0.0002)
    assertEquals("Occupancy_nbr mismatch for accomType Id: 23", result.get(1).getAt(1), resultTemp.get(1).getAt(1) + (resultTemp.get(4).getAt(1)), 0.0002)
    assertEquals("Occupancy_nbr mismatch for accomType Id: 27", result.get(2).getAt(1), resultTemp.get(2).getAt(1) + (resultTemp.get(3).getAt(1)) + (resultTemp.get(4).getAt(1)), 0.0002)
    assertEquals("Occupancy_nbr mismatch for accomType Id: 30", result.get(3).getAt(1), resultTemp.get(3).getAt(1))
    assertEquals("Occupancy_nbr mismatch for accomType Id: 33", result.get(4).getAt(1), resultTemp.get(4).getAt(1))

    assertEquals("Revenue mismatch for accomType Id: 22", result.get(0).getAt(2), resultTemp.get(0).getAt(2) + (resultTemp.get(3).getAt(2) / 2) + (resultTemp.get(4).getAt(2) / 3), 0.0002)
    assertEquals("Revenue mismatch for accomType Id: 23", result.get(1).getAt(2), resultTemp.get(1).getAt(2) + (resultTemp.get(4).getAt(2) / 3), 0.0002)
    assertEquals("Revenue mismatch for accomType Id: 27", result.get(2).getAt(2), resultTemp.get(2).getAt(2) + (resultTemp.get(3).getAt(2) / 2) + (resultTemp.get(4).getAt(2) / 3), 0.0002)
    assertEquals("Revenue mismatch for accomType Id: 30", result.get(3).getAt(2), resultTemp.get(3).getAt(2))
    assertEquals("Revenue mismatch for accomType Id: 33", result.get(4).getAt(2), resultTemp.get(4).getAt(2))

//    assertEquals("Profit mismatch for accomType Id: 22", result.get(0).getAt(3), resultTemp.get(0).getAt(3) + (resultTemp.get(3).getAt(3) / 2) + (resultTemp.get(4).getAt(3) / 3), 0.0002)
//    assertEquals("Profit mismatch for accomType Id: 23", result.get(1).getAt(3), resultTemp.get(1).getAt(3) + (resultTemp.get(4).getAt(3) / 3), 0.0002)
//    assertEquals("Profit mismatch for accomType Id: 27", result.get(2).getAt(3), resultTemp.get(2).getAt(3) + (resultTemp.get(3).getAt(3) / 2) + (resultTemp.get(4).getAt(3) / 3), 0.0002)
//    assertEquals("Profit mismatch for accomType Id: 30", result.get(3).getAt(3), resultTemp.get(3).getAt(3))
//    assertEquals("Profit mismatch for accomType Id: 33", result.get(4).getAt(3), resultTemp.get(4).getAt(3))
}

private void validateResultsForBDE(List<GroovyRowResult> result, List<GroovyRowResult> resultTemp) {
    assertEquals("Occupancy_nbr mismatch for accomType Id: 22", result.get(0).getAt(1), resultTemp.get(0).getAt(1) + (resultTemp.get(3).getAt(1)) + (resultTemp.get(4).getAt(1)), 0.0002)
    assertEquals("Occupancy_nbr mismatch for accomType Id: 23", result.get(1).getAt(1), resultTemp.get(1).getAt(1) + (resultTemp.get(4).getAt(1)), 0.0002)
    assertEquals("Occupancy_nbr mismatch for accomType Id: 27", result.get(2).getAt(1), resultTemp.get(2).getAt(1) + (resultTemp.get(3).getAt(1)) + (resultTemp.get(4).getAt(1)), 0.0002)
    assertEquals("Occupancy_nbr mismatch for accomType Id: 30", result.get(3).getAt(1), resultTemp.get(3).getAt(1))
    assertEquals("Occupancy_nbr mismatch for accomType Id: 33", result.get(4).getAt(1), resultTemp.get(4).getAt(1))

    assertEquals("Revenue mismatch for accomType Id: 22", result.get(0).getAt(2), resultTemp.get(0).getAt(2) + (resultTemp.get(3).getAt(2) / 2) + (resultTemp.get(4).getAt(2) / 3), 0.0002)
    assertEquals("Revenue mismatch for accomType Id: 23", result.get(1).getAt(2), resultTemp.get(1).getAt(2) + (resultTemp.get(4).getAt(2) / 3), 0.0002)
    assertEquals("Revenue mismatch for accomType Id: 27", result.get(2).getAt(2), resultTemp.get(2).getAt(2) + (resultTemp.get(3).getAt(2) / 2) + (resultTemp.get(4).getAt(2) / 3), 0.0002)
    assertEquals("Revenue mismatch for accomType Id: 30", result.get(3).getAt(2), resultTemp.get(3).getAt(2))
    assertEquals("Revenue mismatch for accomType Id: 33", result.get(4).getAt(2), resultTemp.get(4).getAt(2))

//    assertEquals("Profit mismatch for accomType Id: 22", result.get(0).getAt(3), resultTemp.get(0).getAt(3) + (resultTemp.get(3).getAt(3) / 2) + (resultTemp.get(4).getAt(3) / 3), 0.0002)
//    assertEquals("Profit mismatch for accomType Id: 23", result.get(1).getAt(3), resultTemp.get(1).getAt(3) + (resultTemp.get(4).getAt(3) / 3), 0.0002)
//    assertEquals("Profit mismatch for accomType Id: 27", result.get(2).getAt(3), resultTemp.get(2).getAt(3) + (resultTemp.get(3).getAt(3) / 2) + (resultTemp.get(4).getAt(3) / 3), 0.0002)
//    assertEquals("Profit mismatch for accomType Id: 30", result.get(3).getAt(3), resultTemp.get(3).getAt(3))
//    assertEquals("Profit mismatch for accomType Id: 33", result.get(4).getAt(3), resultTemp.get(4).getAt(3))
}

And(~/Forecast Occupancy and Revenue data is split in tables for decision_Type_id (\d+) and mkt_seg_id (\d+) and occupancy_dt (\d+)-(\d+)-(\d+)$/) { int decision_Type_ID, int mktSegId, int arg3, int arg4, int arg5 ->

    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    if(decision_Type_ID==1)
    {
        decisionId=maxDecisionIDForBDE(connection)
    }
    else
    {
        decisionId=maxDecisionIDForCDP(connection)
    }
    def occupancyDt = arg3+"-"+arg4+"-"+arg5
    def query = "select accom_type_id,occupancy_nbr, revenue,Profit from Occupancy_FCST where occupancy_dt = '"+occupancyDt+"' "
    query += " and mkt_seg_id=" + mktSegId + " and decision_id = "+decisionId
    query += " and accom_type_id in (22,23,27,30,33) order by accom_type_id"
    def result = connection.rows(query)

    System.out.println("Query on Occupancy_FCST     "+query);
    System.out.println("Occupancy_FCST data     "+result);
    System.out.println("Occupancy_FCST array size     "+result.size());


    def queryTemp = "select accom_type_id,occupancy_nbr, revenue,Profit from Occ_FCST_Org where occupancy_dt = '"+occupancyDt+"' "
    queryTemp += " and mkt_seg_id=" + mktSegId + " and decision_id = "+decisionId
    queryTemp += " and accom_type_id in (22,23,27,30,33) order by accom_type_id"
    def resultTemp = connection.rows(queryTemp)

    System.out.println("Query on Occupancy_FCST_org     "+queryTemp);
    System.out.println("Occupancy_FCST_org data     "+resultTemp);
    System.out.println("Occupancy_FCST_org array size     "+resultTemp.size());

    validateResults(result, resultTemp)

    def queryToFechProfitFromPaceOccAccomFCSTNotification="select sum(Profit) from PACE_Accom_Occupancy_FCST_NOTIFICATION where Decision_id="+decisionId+" and Occupancy_DT='2017-03-20'"
    def queryToFechProfitFromPaceOccAccomFCST="select sum(Profit) from PACE_Accom_Occupancy_FCST where Decision_id="+decisionId+" and Occupancy_DT='2017-03-20'"
    def queryToFechProfitFromPaceOccMktFCSTNotification="select sum(Profit) from PACE_Mkt_Occupancy_FCST_NOTIFICATION where Decision_id="+decisionId+" and Occupancy_DT='2017-03-20'"
    def queryToFechProfitFromPaceOccMktFCST="select sum(Profit) from PACE_Mkt_Occupancy_FCST where Decision_id="+decisionId+" and Occupancy_DT='2017-03-20'"

    def resultProfitFromPaceOccAccomFCSTNotification = connection.rows(queryToFechProfitFromPaceOccAccomFCSTNotification)
    def resultProfitFromPaceOccAccomFCST = connection.rows(queryToFechProfitFromPaceOccAccomFCST)
    def resultProfitFromPaceOccMktFCSTNotification = connection.rows(queryToFechProfitFromPaceOccMktFCSTNotification)
    def resultProfitFromPaceOccMktFCST = connection.rows(queryToFechProfitFromPaceOccMktFCST)

    assertTrue(resultProfitFromPaceOccAccomFCSTNotification.get(0)==resultProfitFromPaceOccAccomFCST.get(0))
    assertTrue(resultProfitFromPaceOccMktFCSTNotification.get(0)==resultProfitFromPaceOccMktFCST.get(0))


    connection.close();
}

And(~/Pace Accom Forecast Occupancy and Revenue data is split in tables for decision_Type_id (\d+) and occupancy_dt (\d+)-(\d+)-(\d+) and isBDE "([^"]*)"$/) { int decision_Type_ID, int arg3, int arg4, int arg5, String isBDE ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    if(decision_Type_ID==1)
    {
        decisionId=maxDecisionIDForBDE(connection)
    }
    else
    {
        decisionId=maxDecisionIDForCDP(connection)
    }
    def occupancyDt = arg3+"-"+arg4+"-"+arg5
    def query = "select accom_type_id,occupancy_nbr, revenue,Profit from PACE_Accom_Occupancy_FCST where occupancy_dt = '"+occupancyDt+"' "
    query +=  "and decision_id = "+decisionId
    query += " and accom_type_id in (22,23,27,30,33) order by accom_type_id"
    def result = connection.rows(query)

    def queryTemp = "select accom_type_id, sum(occupancy_nbr) as occupancy_nbr, sum(revenue),sum(Profit) as revenue from Occ_FCST_Org where occupancy_dt = '"+occupancyDt+"' "
    queryTemp +=  "and decision_id = "+decisionId
    queryTemp += " and accom_type_id in (22,23,27,30,33) group by accom_type_id order by accom_type_id"
    def resultTemp = connection.rows(queryTemp)

    if (isBDE == "true") {
        validateResultsForBDE(result, resultTemp)
    } else {
        validateResults(result, resultTemp)
    }
    connection.close();

}

And(~/Pace mkt Forecast Occupancy and Revenue data is split in tables for decision_Type_id (\d+) and mkt_seg_id (\d+) and occupancy_dt (\d+)-(\d+)-(\d+)$/) { int decision_Type_ID, int mktSegId, int arg3, int arg4, int arg5 ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    if(decision_Type_ID==1)
    {
        decisionId=maxDecisionIDForBDE(connection)
    }
    else
    {
        decisionId=maxDecisionIDForCDP(connection)
    }
    def occupancyDt = arg3+"-"+arg4+"-"+arg5
    def query = "select MKT_SEG_ID,occupancy_nbr, revenue from PACE_mkt_Occupancy_FCST where occupancy_dt = '"+occupancyDt+"' "
    query += " and mkt_seg_id=" + mktSegId + "and decision_id = "+decisionId
    def result = connection.rows(query)

    def queryTemp = "select MKT_SEG_ID,sum(occupancy_nbr) as occupancy_nbr, sum(revenue) as revenue from Occupancy_FCST ocf inner join accom_type at on at.accom_type_id = ocf.accom_type_id where occupancy_dt = '"+occupancyDt+"' "
    queryTemp += " and mkt_seg_id=" + mktSegId + "and decision_id = "+decisionId +"and AT.isComponentRoom='N' group by MKT_SEG_ID"
    def resultTemp = connection.rows(queryTemp)

    assertEquals("Occupancy_nbr mismatch ", result.get(0).getAt(1), resultTemp.get(0).getAt(1))
    assertEquals("Revenue mismatch ", result.get(0).getAt(2), resultTemp.get(0).getAt(2))
    connection.close();

}

And(~/CR data getting updated in sas during CDP$/) { DataTable dataCount ->
    def sasDatasetConnection = DbConnection.getSasDatasetConnection(propertyId)
    def statement = sasDatasetConnection.createStatement();
    def sasDatasetPartitionConnection = DbConnection.getSasDatasetPartitionConnection(propertyId)
    def statmentParalib = sasDatasetPartitionConnection.createStatement();
    for (map in dataCount.asMaps()) {
        executeSasQuery(statement, statmentParalib, false, map)
    }
}

private void executeSasQuery(statement, statmentParalib, boolean isBde, Map<String, String> map) {
    def query = "select rooms_sold from " + map.get("sasTable") + " where accom_type_id=" + map.get("accom_type_id") + " and occupancy_dt=" + map.get("occupancy_dt")
    query += map.get("mkt_seg_id") ? " and mkt_seg_id=" + map.get("mkt_seg_id") : ""
    if (!isBde && (map.get("sasTable").toLowerCase().contains("accom_inventory_pace") || map.get("sasTable").toLowerCase().contains("ma_"))) {
        query += " and rooms_sold=" + map.get("rooms_sold")
        def queryMaxDtmm = query.replace("select rooms_sold", "select max(capture_dttm)")
        result = map.get("sasTable").contains("ma_") ? statmentParalib.executeQuery(queryMaxDtmm + " ") : statement.executeQuery(queryMaxDtmm + " ")
        result.next()
        assert result.getInt(1).toInteger() == Integer.parseInt(map.get("capture_dttm")): map.get("sasTable") + " Expected Value not returned"
    }
    query += map.get("capture_dttm") ? " and capture_dttm=" + map.get("capture_dttm") : ""
    result = map.get("sasTable").contains("ma_") ? statmentParalib.executeQuery(query + " ") : statement.executeQuery(query + " ")
    result.next()
    assert result.getInt(1).toInteger() == Integer.parseInt(map.get("rooms_sold")) : map.get("sasTable") + " Expected Value not returned"
}

private boolean createDataForTotal(Sql connection,double i,int roomSold) {
    connection.execute("update opera.History_Occupancy_Summary set " +
            "   Rooms_Sold=" + (1+roomSold) + ",Room_Revenue= '"+ (34567.873 + i) +"',Total_Revenue='"+(33840.204+i)+"',Food_Revenue='"+(23840.204+i)+"'   " +
            "   where Occupancy_DT='2017-10-12' " +
            " and Data_Load_Metadata_ID=" +
            " (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata where Incoming_File_Type_Code='CT') " +
            ";")
}

private boolean createDataForCTAT(Sql connection,double i,int roomSold) {
    connection.execute(" update opera.History_Occupancy_Summary set " +
            "   Rooms_Sold=" + (4+roomSold) + ",Room_Revenue='"+(24567.873+i)+"',Total_Revenue='"+(33840.204+i)+"',Food_Revenue='"+(23840.204+i)+"' " +
            "   where Occupancy_DT='2017-10-12' " +
            "   and Market_Code='' and Room_Type in ('C1VKS','C2VKD','C0VD')" +
            " and Data_Load_Metadata_ID=" +
            " (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata where Incoming_File_Type_Code='CTAT')  " +
            " ;")
}

private boolean createDataForCTATForC1VKSDuringCDP(Sql connection,double i,int roomSold) {
    connection.execute(" update opera.History_Occupancy_Summary set " +
            "   Rooms_Sold=" + (4+roomSold) + ",Room_Revenue='"+(24567.873+i)+"',Total_Revenue='"+(33840.204+i)+"',Food_Revenue='"+(23840.204+i)+"' " +
            "   where Occupancy_DT='2017-10-12' " +
            "   and Market_Code='' and Room_Type in ('C1VKS')" +
            " and Data_Load_Metadata_ID=" +
            " (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata where Incoming_File_Type_Code='CTAT')  " +
            " ;")
}


private boolean createDataForCSATForAMS(Sql connection,double i,int roomSold) {
    connection.execute(" update opera.History_Occupancy_Summary set " +
            "   Rooms_Sold=" + (3+roomSold) + ",Room_Revenue='"+(15567.873+i)+"',Total_Revenue='"+(23840.204+i)+"',Food_Revenue='"+(23840.204+i)+"'" +
            "  where Occupancy_DT='2017-10-12' " +
            "   and Market_Code='F' and Room_Type ='L0VK'" +
            " and Data_Load_Metadata_ID=" +
            " (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata where Incoming_File_Type_Code='CSAT')  " +
            " ;")
}

private Integer maxDecisionIDForBDE(Sql connection){
    def decision_ID = connection.firstRow("select MAX(Decision_ID) Decision_ID_BDE from decision where Decision_Type_ID=1");
    return  decision_ID.Decision_ID_BDE;
}

private Integer maxDecisionIDForCDP(Sql connection){
    def decision_ID = connection.firstRow("select MAX(Decision_ID) Decision_ID_CDP from decision where Decision_Type_ID=2");
    return  decision_ID.Decision_ID_CDP;
}



private boolean createDataForCSATForNonAMS(Sql connection,double i,int roomSold) {
    connection.execute(" update opera.History_Occupancy_Summary set " +
            "   Rooms_Sold=" + (3+roomSold) + ",Room_Revenue='"+(8567.873+i)+"',Total_Revenue='"+(10840.204+i)+"'," +
            "  Room_Arrivals=5,Room_Departures=4,Food_Revenue='"+(9840.0+i)+"',Room_Type='C3RKM' " +
            "  where Occupancy_DT='2017-10-12' " +
            "   and Market_Code='I' and Room_Type='L0RD'   " +
            " and Data_Load_Metadata_ID=" +
            " (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata where Incoming_File_Type_Code='CSAT')  " +
            " ;")
}

private boolean createDataForCSATForTransactionDuringCDPonly(Sql connection,double i) {
    connection.execute("update opera.History_Transaction set " +
            " Room_Revenue='"+(48967.873+i)+"',Total_Revenue='"+(48967.873+i)+"',Food_Beverage_Revenue='"+(38967.873+i)+"',Room_Type='C2VKD'" +
            " where Arrival_DT <='2017-10-12 00:00:00.0' and Departure_DT > '2017-10-12 00:00:00.0'" +
            "   and Market_Code='F' and Room_Type ='L0VK'" +
            "   and Transaction_DT='2017-10-12 00:00:00.0' " +
            " and Data_Load_Metadata_ID=" +
            " (select MAX(Data_Load_Metadata_ID) from opera.Data_Load_Metadata where Incoming_File_Type_Code='CTRANS')  " +
            " ;")
}



Given(~/Demand Override is present$/) { ->
    insertDemandOverride(DatabaseName)
}
