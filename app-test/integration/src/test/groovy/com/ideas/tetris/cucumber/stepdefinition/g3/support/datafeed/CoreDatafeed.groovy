package com.ideas.tetris.cucumber.stepdefinition.g3.support.datafeed


import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import com.ideas.tetris.util.testsuite.RestCallUtil
import common.JemsTest
import io.cucumber.java.After
import io.cucumber.java.Before
import io.cucumber.java.en.And
import io.cucumber.java.en.Given
import io.cucumber.java.en.Then
import io.restassured.RestAssured
import net.sf.json.JSONArray

class CoreDatafeed extends JemsTest {

    DatafeedBase datafeedBase = new DatafeedBase()
    def tenantDbConnection
    TetrisRESTClient tetrisRestClient
    String propertyCode

    @Before(value = "@CoreDatafeed")
    void setup() {
        datafeedBase.datafeedSetup()
        RestAssured.baseURI = datafeedBase.getREST_BASE_URI()
        setTetrisRestClient(datafeedBase.getTetrisRestClient())
    }

    @After(value = "@CoreDatafeed")
    void cleanup() {
        resetToggleValues()
        tenantDbConnection.close()
    }

    @After("@RoomClassConfigEnhanced")
    static void cleanUpRoomClassConfigs() {
        setIsDiscontinuedRoomClass("1")
    }

    @Given("I select property {string} with propertyId {string} for Core bucket apis")
    void setWorkContext(String propertyName, String propertyId) {
        datafeedBase.setWorkContext(propertyName, propertyId)
        setTetrisRestClient(datafeedBase.getTetrisRestClient())
        setTenantDbConnection(datafeedBase.getTenantDbConnection())
        propertyCode = propertyName
    }

    @And("Set Toggle {string} to {string} for property {string}")
    void setToggle(String toggleName, String toggleValue, String propertyName) {
        String context = 'pacman.SandBox.' + propertyName
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, toggleName, context, toggleValue)
    }

    @Then("Trigger {string} api with dto {string} and validate output with {string}")
    void triggerDatafeed(String datafeedName, String dto, String filename) {
        def datafeedUrl = "datafeed/v1/" + dto        
        JSONArray jsonResponse = RestCallUtil.triggerDatafeedGetApi(propertyCode, clientCode, datafeedUrl, datafeedName, true, false)
        String jsonFromFile = datafeedBase.readDatafeedJsonFile("src/test/resources/Datafeed/Core/" + filename)
        datafeedBase.assertDatafeedData(jsonFromFile, jsonResponse)
    }

    @And("Set isDiscontinued as {string} for accomType")
    static void setIsDiscontinuedRoomClass(String isDiscontinued) {
        Integer discontinuedVal = Integer.parseInt(isDiscontinued)
        DbConnection.databaseConnection_XNAES.execute("update Accom_Type set Display_Status_ID = " + discontinuedVal +
                " where Accom_Type_ID = 1")
    }

    void setTenantDbConnection(tenantDbConnection) {
        this.tenantDbConnection = tenantDbConnection
    }

    void setTetrisRestClient(TetrisRESTClient tetrisRestClient) {
        this.tetrisRestClient = tetrisRestClient
    }

    private void resetToggleValues() {
        setToggle("pacman.feature.RatePlanConfigurationEnabled", "false", "XNAES")
        setToggle("pacman.preProduction.isForecastAndDecisionWindowEnabledInInformationalDatafeed", "false", "XNAES")
    }

    @Override
    protected String getPropertyId() {
        return null
    }

    @Override
    protected String getPropertyCode() {
        return null
    }
}