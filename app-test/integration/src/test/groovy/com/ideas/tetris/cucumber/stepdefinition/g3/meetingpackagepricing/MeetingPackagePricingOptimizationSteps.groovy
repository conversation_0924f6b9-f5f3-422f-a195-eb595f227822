package com.ideas.tetris.cucumber.stepdefinition.g3.meetingpackagepricing

import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.PropertiesTestHelper
import io.cucumber.java.Before
import io.cucumber.java.en.And
import io.cucumber.java.en.Then
import io.cucumber.java.en.When
import io.restassured.RestAssured
import io.restassured.builder.RequestSpecBuilder
import io.restassured.response.Response
import io.restassured.specification.RequestSpecification

import static io.restassured.RestAssured.given;

class MeetingPackagePricingOptimizationSteps {

    def tenantDbConnection
    def decision1
    def decision2
    def decision3
    TetrisRESTClient tetrisRestClient
    public static final String LOGIN_USERNAME = "<EMAIL>"
    public static final String LOGIN_PASSWORD = "password"
    public static final String REST_BASE_URI = RestServiceUtil.getPacmanRestURL()
    public static final String DBName_0026 = "991698"
    public static final int PROPERTY_ID_0026 = 991698
    public static final String PROPERTY_ID = "propertyId"

    @Before("@MeetingPackagePricingOptimizationTest")
    void setup() {
        tenantDbConnection = DbConnection.getTenantDatabaseConnection(DBName_0026)
        tetrisRestClient = PropertiesTestHelper.login(REST_BASE_URI, LOGIN_USERNAME, LOGIN_PASSWORD)
        tetrisRestClient.headers.propertyId = PROPERTY_ID_0026
        RestAssured.baseURI = REST_BASE_URI
        populateDecisionTable()
        setUpMPProducts()
    }

    void populateDecisionTable() {
        decision1 = tenantDbConnection.firstRow("""
        INSERT INTO decision (
            Property_ID, Business_DT, Caught_up_DTTM, Rate_Unqualified_DTTM, WebRate_DTTM, 
            Decision_Type_ID, Start_DTTM, End_DTTM, Process_Status_ID, CreateDate_DTTM
        ) VALUES (
            991698, '2013-01-01', '2013-01-02', '2013-01-01', '2013-02-01',
            1, '2013-01-01', '2013-01-01', 13, '2013-01-01'
        );
        SELECT SCOPE_IDENTITY() AS Decision_ID;
    """).Decision_ID

        decision2 = tenantDbConnection.firstRow("""
        INSERT INTO decision (
            Property_ID, Business_DT, Caught_up_DTTM, Rate_Unqualified_DTTM, WebRate_DTTM, 
            Decision_Type_ID, Start_DTTM, End_DTTM, Process_Status_ID, CreateDate_DTTM
        ) VALUES (
            991698, '2013-02-01', '2013-02-02', '2013-02-01', '2013-03-01',
            1, '2013-02-01', '2013-02-01', 13, '2013-02-01'
        );
        SELECT SCOPE_IDENTITY() AS Decision_ID;
    """).Decision_ID

//        decision3 = tenantDbConnection.firstRow("""
//        INSERT INTO decision (
//            Property_ID, Start_DT, End_DT, Create_DT, Optimize_End_DT,
//            Optimization_Window_Type_ID, Optimize_Start_DT, Caught_Up_DTTM,
//            Decision_Window_Type_ID, Last_Updated_DTTM
//        ) VALUES (
//            991698, '2013-03-01', '2013-03-02', '2013-03-01', '2013-04-01',
//            18, '2013-03-01', '2013-03-01', 13, '2013-03-01'
//        );
//        SELECT SCOPE_IDENTITY() AS Decision_ID;
//    """).Decision_ID
    }

    private setUpMPProducts() {
        tenantDbConnection.execute("INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('FullDay', 1, '08:00:00.0000000', '20:00:00.0000000', 1, getdate(), 1, getdate())")
        tenantDbConnection.execute("INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('AM', 1, '08:00:00.0000000', '12:00:00.0000000', 1, getdate(), 1, getdate())")
        tenantDbConnection.execute("INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('PM', 1, '16:00:00.0000000', '20:00:00.0000000', 1, getdate(), 1, getdate())")
        tenantDbConnection.execute("Insert into MP_Product(Name, Description, System_Default, Code, Type, Dependent_Product_ID, Is_DOW_Offset, Is_Upload, Status_ID, Is_Default_Inactive, Invalid_Reason_ID, Is_Optimized, Product_Floor_Rate, Price_Rounding_Rule, Minimum_Price_Change, Offset_Method, Display_Order, Centrally_Managed, Is_Overridable, Floor_Type, Floor_Percentage, MP_Cfg_Day_Part_ID, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "Values\n" +
                "('All Day Meeting Package','AM Meeting Package',1,'Meeting Product','DAILY',NULL,0,1,1,0,NULL,0,NULL,1,NULL,NULL,1,0,1,2,NULL,(Select MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'FullDay'),11403,11403,11403,11403),\n" +
                "('AM Meeting Package','AM Meeting Package',1,'Meeting Product','DAILY',NULL,0,1,1,0,NULL,0,NULL,1,NULL,NULL,1,0,1,2,NULL,(Select MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'AM'),11403,11403,11403,11403),\n" +
                "('PM Meeting Package','PM Meeting Package',1,'Meeting Product','DAILY',NULL,0,1,1,0,NULL,0,NULL,1,NULL,NULL,1,0,1,2,NULL,(Select MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'PM'),11403,11403,11403,11403)")
    }

    @When("Meeting Package Pricing decisions are populated")
    void populateMeetingPackagePricingDecisions() {
        tenantDbConnection.execute("""
        INSERT INTO MP_Decision_Bar (
            Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID,
            Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate,
            User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type,
            Adjustment_Value, Created_DTTM
        )
        SELECT ${decision1}, 
               (SELECT MP_Product_ID FROM MP_Product WHERE Name = 'All Day Meeting Package'),
               Decision_Reason_Type_ID, 10, Arrival_dt, Optimal_BAR, Pretty_BAR, Override,
               Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR,
               Optimal_BAR_Type, Adjustment_Value, CreateDate
        FROM CP_Decision_Bar_Output
        WHERE Product_Id = 1 AND Accom_Type_ID = 13;
        
        INSERT INTO MP_Decision_Bar (
            Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID,
            Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate,
            User_Specified_Rate, Final_BAR, Previous_BAR, Optimal_BAR_Type,
            Adjustment_Value, Created_DTTM
        )
        SELECT ${decision2}, 
               (SELECT MP_Product_ID FROM MP_Product WHERE Name = 'All Day Meeting Package'),
               Decision_Reason_Type_ID, 10, Arrival_dt, Optimal_BAR, Pretty_BAR, Override,
               Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Previous_BAR,
               Optimal_BAR_Type, Adjustment_Value, CreateDate
        FROM CP_Decision_Bar_Output
        WHERE Product_Id = 1 AND Accom_Type_ID = 13;""");

        tenantDbConnection.execute("")
    }

    @And("Optimal Price to BAR Step is run")
    void runOptimalPriceToBARStep() {
        Response response = given().spec(requestSpecification())
                .queryParam("startDate", )
    }

    static RequestSpecification requestSpecification() {
        return new RequestSpecBuilder().setBaseUri(REST_BASE_URI)
                .addHeader("Authorization", "Basic ********************************")
                .addQueryParam(PROPERTY_ID, PROPERTY_ID_0026)
                .build();
    }

    @Then("The decisions should be optimized")
    void assertDecisionOptimization() {
        def updatedRows = tenantDbConnection.rows("""
        SELECT * FROM MP_Decision_Bar
        WHERE Decision_ID = ${decision2}
    """)

        assert updatedRows.size() > 0 : "No records found with Decision_ID = ${decision2}"
        assert updatedRows.every { row ->
            def value = row.Final_Bar as BigDecimal
            def onesPlace = value.intValue() % 10
            def firstDecimal = (value * 10).intValue() % 10
            def secondDecimal = (value * 100).intValue() % 10

            (onesPlace in [5, 9]) && (firstDecimal == 0) && (secondDecimal == 0)
        }
    }
}
