package common

import com.aventstack.extentreports.Status
import com.aventstack.extentreports.markuputils.ExtentColor
import com.aventstack.extentreports.markuputils.MarkupHelper
import com.google.common.io.Files
import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.ClientLocator
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.PropertiesTestHelper
import com.mongodb.MongoClient
import com.mongodb.MongoClientURI
import groovy.sql.Sql
import groovy.transform.Synchronized
import groovyx.net.http.ContentType
import groovyx.net.http.HttpResponseException
import net.sf.json.JSONObject
import org.apache.commons.lang.StringUtils
import org.junit.*
import org.junit.internal.AssumptionViolatedException
import org.junit.rules.TestName
import org.junit.rules.TestRule
import org.junit.rules.TestWatcher
import org.junit.runner.Description
import java.nio.file.Path
import java.nio.file.Paths
import java.sql.Timestamp
import java.text.MessageFormat
import java.text.SimpleDateFormat

abstract class JemsTest {

    // Common/misc
    public static final long JOB_RUN_TIMEOUT = 3600000 // 1 hr
    public static final String CLIENT_CODE_SANDBOX = "SandBox"
    public static final String CLIENT_ID_SANDBOX = "6"
    public static final String CLIENT_CODE_HILTON = "Hilton"
    public static final String CLIENT_CODE_BLACKSTONE = "BSTN"
    public static final String CLIENT_CODE_LEARNING = "G3LMS"
    private final MongoClientURI connectionString = new MongoClientURI("mongodb://localhost:27017");
    // Application Login information
    protected static final String USER_NAME_SSO = "<EMAIL>"
    protected static final String USER_PWD_SSO = "password"
    static final String USER_ID_SSO = "11403"
    protected static final String DEFAULT_CLIENT_ENVIRONMENT_NAME = "g3"

    // REST endpoints for Job Information
    private static final String URL_JOB_DETAIL_GET = "jobs/v1/{0,number,#}"
    private static final String URL_JOB_EXECUTION_GET = "jobs/execution/v1/{0,number,#}"
    private static final String URL_JOB_RESUME = "jobs/resume/v1/{0,number,#}"
    private static final String URL_JOB_RESUME_JOB_INSTANCE = "jobs/resumeInstance/v1/{0,number,#}"
    private static final String URL_JOB_ABANDON = "jobs/abandon/v1/{0,number,#}"
    private static final String URL_JOB_STOP = "jobs/stop/v1/{0,number,#}"
    private static final String URL_JOB_FORCE_STOP = "jobs/forceStop/v1/{0,number,#}"
    private static final String URL_JOB_CONTINUE = "jobs/continue/v1/{0,number,#}"

    // Some commonly used rest endpoints
    public static final String REST_URI_WORKCONTEXT_CLIENT = "workcontext/client/v1/"
    public static final String REST_URI_FIND_PROPERTY = "property/rollout/find/v1"
    public static final String REST_API_SYNC_FLAG_ENABLE = "sync/v1/{0}"
    public static final String REST_API_ENABLE_SYSTEM_PROPERTY = "systemproperty/v1/{0}"
    public static final String REST_API_SET_SYSTEM_PROPERTY = "systemproperty/v1/{0}/{1}"
    public static final String REST_API_FOR_CONFIG_PARAM_MIGRATION_STATS = "configParamDataMigrationStats/v1"

    public static final String JEMS_JOB_LAST_GOOD_DECISION_DELIVERY = "LastGoodDecisionsDeliveryJob"
    public static final String JEMS_JOB_NGI_TARS_DECISION_DELIVERY = "NGITARSDecisionDeliveryJob"

    // Rest Clients
    public static final String REST_BASE_PACMAN_SERVICES_URI = RestServiceUtil.getPacmanRestURL()
    public static final String REST_BASE_PLATFORM_SERVICES_URI = RestServiceUtil.getPlatformRestURL()
    public static final String REST_BASE_JEMS_URI = RestServiceUtil.getJEMSRestURL()
    public static TetrisRESTClient pacmanServicesRestClient
    public static TetrisRESTClient platformServicesRestClient
    public static TetrisRESTClient jobRestClient
    public static TetrisRESTClient ngiRestClient
    // public static String INTEGRATION_SETTINGS_BASE_PATH = System.getProperty("ngi.integration.settings.url","http://localhost:9090")
    public static String INTEGRATION_SETTINGS_BASE_PATH = System.getProperty("ngi.rest.url","http://localhost:9090")

    public static TetrisRESTClient INTEGRATION_SETTINGS_REST_CLIENT = new TetrisRESTClient(INTEGRATION_SETTINGS_BASE_PATH)

    // Job names - TODO: Get from JobName enum
    public static final String JEMS_JOB_SYNC_CALIBRATION_FORCE = "ForceSyncCalibration"
    public static final String JEMS_JOB_FG_CREATE = "CreateForecastGroups"
    public static final String JEMS_JOB_FG_COMMIT = "CommitForecastGroups"
    public static final String JEMS_JOB_TROUBLEMAKER = "Troublemaker"
    public static final String JEMS_JOB_TROUBLEMAKER_ASYNC = "AsyncTroublemaker"
    public static final String JEMS_JOB_TROUBLEMAKER_LOOP = "TroublemakerWithLoopStep"
    public static final String JEMS_JOB_TROUBLEMAKER_LOOP_ASYNC = "AsyncTroublemakerWithLoop"
    public static final String JEMS_JOB_REFRESH_PROPERTY = "RefreshLearningDatabaseJob"
    public static final String JEMS_JOB_BUILD_LMS_PROPERTIES = "BuildLearningDatabasesJob"
    public static final String JEMS_JOB_CRS_CATCHUP = "CrsCatchup"
    public static final String JEMS_JOB_WEBRATE_CATCHUP = "WebRateCatchup"
    public static final String JEMS_JOB_PURGE_JEMS = "JemsDbPurge"
    // TODO: remove JEMS_JOB_CRS_PROCESS_BDE, JEMS_JOB_CRS_PROCESS_CDP, JEMS_JOB_EXTRACT_AND_ANALYZE and CrsCatchup usage
    public static final String JEMS_JOB_CRS_PROCESS_BDE = "ProcessCRSFileBDE"
    public static final String JEMS_JOB_CRS_PROCESS_CDP = "ProcessCRSFileCDP"
    public static final String JEMS_JOB_EXTRACT_AND_ANALYZE = "ExtractAndAnalyzeJob"
    public static final String JEMS_JOB_NGI_DEFERRED_DELIVERY_JOB = "NGIDeferredDeliveryJob"
    public static final String JEMS_JOB_RUN_TASK = "RunTaskJob"
    public static final String JEMS_JOB_LDBBudget_PROCESS = "LDBBudgetDataProcessingJob"
    public static final String JEMS_JOB_LDB = "LimitedDataBuildJob"
    public static final String JEMS_JOB_BENEFITMEASUREMENT = "BenefitMeasurementJob"
    public static final String JEMS_JOB_MIGRATE_PARAMETERS_TO_NEW_STYLE = "ConfigParamMigrateToNewStyleJob"
    public static final String JEMS_JOB_CP_MIGRATION = "ContinuousPricingMigrationJob"
    public static final String JEMS_JOB_HILTON_IPP_MIGRATION = "HiltonIppMigrationJob"
    public static final String JEMS_JOB_HILTON_CP_MIGRATION_STARTER_JOB = "HiltonCPMigrationStarterJob"
    public static final String JEMS_JOB_NGI_HILTON_DECISION_DELIVERY = "NGIHiltonDecisionDeliveryJob"
    public static final String JEMS_JOB_PROFIT_POPULATION = "ProfitPopulationJob"

    // Job parameters - these should be pushed to common location
    public static final String PARAM_PROP_ID = "propertyId"
    public static final String PARAM_DATE = "date"
    public static final String PARAM_TASK_ID = "taskId"
    public static final String PARAM_USER_ID = "userId"
    public static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    // Job statuses
    public static final String JOB_STATUS_STOPPED = "STOPPED"
    public static final String JOB_STATUS_STOPPING = "STOPPING"
    public static final String JOB_STATUS_COMPLETED = "COMPLETED"
    public static final String JOB_STATUS_ABANDONED = "ABANDONED"
    public static final String JOB_STATUS_FAILED = "FAILED"
    public static final String JOB_STATUS_UNKNOWN = "UNKNOWN"
    public static final String JOB_STATUS_STARTED = "STARTED"

    // Exit codes
    public static final String JOB_STEP_EXIT_CODE_NOOP = "NOOP"
    public static final String JOB_EXIT_CODE_NOOP_LOOP = "NOOP_LOOP"
    public static final String JOB_EXIT_CODE_NOOP_LOOP_COMPLETE = "NOOP_LOOP_COMPLETE"

    // Sync flags?
    public static final String SYNC_EVENT_SPECIAL_EVENTS_CHANGED = "SPECIAL_EVENTS_CHANGED"
    public static final String SYNC_EVENT_ACCOMMODATION_CONFIG_CHANGED = "ACCOMMODATION_CONFIG_CHANGED"
    public static final String SYNC_EVENT_OVERBOOKING_CONFIG_CHANGED = "OVERBOOKING_CONFIG_CHANGED"
    public static final String SYNC_EVENT_COST_OF_WALK_CONFIG_CHANGED = "COST_OF_WALK_CONFIG_CHANGED"
    public static final String SYNC_EVENT_PRICING_STRATEGY_CONFIG_CHANGED = "PRICING_STRATEGY_CONFIG_CHANGED"
    public static final String SYNC_EVENT_INVENTORY_SHARING_CONFIG_CHANGED = "INVENTORY_SHARING_CONFIG_CHANGED"
    public static final String SYNC_EVENT_WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED = "WEBRATE_CONFIG_CHANGED_NO_SYNC_REQUIRED"
    public static final String SYNC_EVENT_FORECAST_GROUPS_CONFIG_CHANGED = "FORECAST_GROUPS_CONFIG_CHANGED"

    public static final String SYSTEM_COMPONENT_ACCOMMODATION_CONFIGURATION = "ACCOMMODATION_CONFIGURATION"
    public static final String SYSTEM_COMPONENT_SPECIAL_EVENTS = "SPECIAL_EVENTS"
    public static final String SYSTEM_COMPONENT_COST_OF_WALK = "COST_OF_WALK"
    public static final String SYSTEM_COMPONENT_INVENTORY_SHARING = "INVENTORY_SHARING"
    public static final String SYSTEM_COMPONENT_PRICING_STRATEGY = "PRICING_STRATEGY"
    public static final String SYSTEM_COMPONENT_OVERBOOKING = "OVERBOOKING"
    public static final String SYSTEM_COMPONENT_WEBRATE_NO_SYNC_REQUIRED = "WEBRATE_NO_SYNC_REQUIRED"
    public static final String UPDATE_OPT_VERSION = "update IP_Cfg_Property_Attribute set Value=0 where IP_Cfg_Property_Attribute_ID=307"
    private static  connection
    // Regulators Ride
    public static final String REGULATOR_CONSTRAINT = "Pacman::{0}::{1}"

    // List of all Job Instances that were launched by test
    public ThreadLocal<Set<Long>> JOB_INSTANCES_LAUNCHED = new ThreadLocal<HashSet<Long>>() {

        @Override
        protected HashSet<Long> initialValue() {
            return new HashSet();
        }
    };

    // Database
    protected Sql tenantDbConnection
    public static def jobDbConnection
    public static def globalDbConnection

    // File System
    public static Properties envProps
    public static def tetrisDataDir
    public static def appDataDir
    public static def soapUIDir
    public static def tetrisDeployDir

    // Get reference to when test started
    public static Timestamp timeTestStarted
    public static long HALF_MINUTE = 30000

    public TestName method;

    //enableAutomaticDefectReportingInRally Toggle is only for QA Automation Environments. DO NOT TURN ON on local
    public static boolean enableAutomaticDefectReporting = Boolean.parseBoolean(System.getProperty("enable.Automation.Defect.Reporting", "false").trim());
    public static boolean isReleaseCut = Boolean.parseBoolean(System.getProperty("is.Release.Cut", "false").trim());
    public static boolean enableExtentReporting = Boolean.parseBoolean(System.getProperty("extent.test.report.enabled", "false").trim());
    public static String JIRA_KEY = System.getProperty("defect.jiraApiKey", "").trim();
    public static String JIRA_ID = System.getProperty("defect.jiraId", "").trim();

    //set to True for enabling klov reporting
    public static String API_KEY = System.getProperty("defect.apiKey", "").trim();
    private static final String KLOV_URL = System.getProperty("extent.klov.reporting.url", "http://localhost:8081/").trim();
    private static final String MONGODB_HOST = System.getProperty("mongodb.host", "localhost").trim();
    private static final int MONGODB_PORT = Integer.parseInt(System.getProperty("mongodb.port", "27017").trim());
    public static final String INTEGRATION_ENVIRONMENT = System.getProperty("envHost", "localhost").trim();
    public static final String INTEGRATION_ENVIRONMENT_DOMAIN = System.getProperty("envDomain", "").trim();
    public static final String FINAL_BUILD_DIR = "/target/test-classes/ExtentReports";
    public static final String BUILD_DIR = System.getProperty("buildDirectory", FINAL_BUILD_DIR);
    public static final String APP_VERSION = System.getProperty("appVersion", "").trim();

    //NGI BASE URL
    public static String NGI_BASE_URL

    public static ThreadLocal<Boolean> classToggle = new ThreadLocal<Boolean>();

    @Synchronized
    @BeforeClass
    static void beforeClass() {

        NGI_BASE_URL = "http://" + INTEGRATION_ENVIRONMENT + ":9090/".trim()


        if (!INTEGRATION_ENVIRONMENT_DOMAIN.isEmpty()) {
            NGI_BASE_URL = "http://" + INTEGRATION_ENVIRONMENT +"."+ INTEGRATION_ENVIRONMENT_DOMAIN + ":9090/".trim()
        }

        if (enableExtentReporting) {
            ExtentTestManager.createInstance(BUILD_DIR, MONGODB_HOST, MONGODB_PORT, INTEGRATION_ENVIRONMENT, APP_VERSION, KLOV_URL);
        }
        classToggle.set(true);

        // Suspect local & CI are different and ocasionally get compilation errors in CI that are not flagged locally
        println "!!! Before class : Groovy version: " + GroovySystem.getVersion()
        // Get Database Connections
        jobDbConnection = DbConnection.getJobDbConnection()
        println "jobDbConnection attached"
        globalDbConnection = DbConnection.getGlobalConnection()
        println "globalDbConnection attached"

        // Load Env Properties
        envProps = PropertiesTestHelper.loadEnvProps()
        tetrisDataDir = envProps.getProperty("tetrisDataDir")
        tetrisDeployDir = envProps.getProperty("tetrisDeployDir")
        appDataDir = envProps.getProperty("appDataDir")
        soapUIDir = envProps.getProperty("soapUIDir")
        println "All properties set"

        if(Boolean.parseBoolean(envProps.getProperty("enableRelativePath"))){
            setRelativePathAtGlobalLevel()
        }
    }

    private static void setRelativePathAtGlobalLevel() {
        TetrisRESTClient restClient = new TetrisRESTClient(REST_BASE_PACMAN_SERVICES_URI,
                [username: USER_NAME_SSO, password: USER_PWD_SSO])
    }


    @Rule
    //Executes for each Test - Test Listener
    public synchronized TestRule watcher = new TestWatcher() {

        @Override
        protected void starting(Description description) {

            if (enableExtentReporting) {
                if (classToggle.get()) {

                    ExtentTestManager.startTest(description.getClassName(), APP_VERSION);
                    classToggle.set(false);
                }

                ExtentTestManager.startChildTest(description.getMethodName(), description.getClassName());
                logm("Starting test: " + description.getMethodName());
            }

        }

        @Override
        protected void succeeded(Description description) {
            if (enableExtentReporting) {
                ExtentTestManager.getTest().log(Status.PASS, MarkupHelper.createLabel("Test PASSED: " + description.getMethodName(), ExtentColor.GREEN));
            }
        }

        @Override
        protected void failed(Throwable e, Description description) {
            if (enableExtentReporting) {
                ExtentTestManager.getTest().log(Status.FAIL, MarkupHelper.createLabel("Test FAILED: " + description.getMethodName(), ExtentColor.RED));
                ExtentTestManager.getTest().log(Status.FAIL, e.message + "\n" + e.getStackTrace() as String);
            }

            if (enableAutomaticDefectReporting) {
                String DefectID
                String error = e.message + "\n" + e.getStackTrace() as String
//                Rally Defect logging code
//                DefectID = RallyRestAPIUtil.createDefect(description.getClassName().substring(description.getClassName().lastIndexOf(".")+1), description.getMethodName(), error, isReleaseCut)
//                if (enableExtentReporting) {
//                    logm(DefectID);
//                }
//                print(DefectID);
                DefectID = JiraApiUtils.createDefect(description.getClassName().substring(description.getClassName().lastIndexOf(".") + 1), description.getMethodName(), error, isReleaseCut);
                if (enableExtentReporting) {
                    logm(DefectID);
                }
                print(DefectID);
            }
        }

        @Override
        protected void skipped(AssumptionViolatedException e, Description description) {
            if (enableExtentReporting) {
                ExtentTestManager.getTest().log(Status.SKIP, MarkupHelper.createLabel("Test SKIPPED: " + description.getMethodName(), ExtentColor.YELLOW));
                ExtentTestManager.getTest().log(Status.SKIP, "SKIPPED: " + e.message + "\n" + e.getStackTrace());
            }
        }
    }

    @Synchronized
    @Before
    void jemsTestBefore() {

        def maxProblemNoteId
        def maxSupportBulletinId
        // Database
        maxProblemNoteId = (Long) jobDbConnection.firstRow("select max(PROBLEM_NOTE_ID) from PROBLEM_NOTE").getAt(0)
        maxProblemNoteId = (maxProblemNoteId == null ? new Long(0) : maxProblemNoteId)
        maxSupportBulletinId = (Long) jobDbConnection.firstRow("select max(SUPPORT_BULLETIN_ID) from SUPPORT_BULLETIN").getAt(0)
        maxSupportBulletinId = (maxSupportBulletinId == null ? new Long(0) : maxSupportBulletinId)

        // Create/login to platform REST Client
        platformServicesRestClient = new TetrisRESTClient(REST_BASE_PLATFORM_SERVICES_URI)
        if (null != getPropertyId()) {
            platformServicesRestClient.headers.propertyId = getPropertyId()
        }
        // Create/login to Services REST Client
        String clientId = getClientId()
        String propertyId = getPropertyId()
        println "Before login property id: ${getPropertyId()}"
        logm("Before login property id: ${getPropertyId()}")
        pacmanServicesRestClient = login()
        println "After login property id: ${getPropertyId()}"
        logm("After login property id: ${getPropertyId()}")
        pacmanServicesRestClient.setHeaders([ Authorization: "Basic ${"<EMAIL>:password".bytes.encodeBase64()}"])
        if (clientId) {
            pacmanServicesRestClient.headers.clientId = clientId
        }
        if (propertyId) {
            // Set the PropertyID on the header
            pacmanServicesRestClient.headers.propertyId = getPropertyId()
            println "Before tenant db  property id: ${getPropertyId()}"
            logm("Before tenant db  property id: ${getPropertyId()}")
            // Create a connection to the Tenant DB
            tenantDbConnection = DbConnection.getTenantDatabaseConnection(StringUtils.leftPad(propertyId, 6, "0"))
            tenantDbConnection.executeUpdate("update [dbo].[Property] set [Manages_Index_Rebuild] =1 where Property_Code != '-1'")
            println "After tenant db  property id: ${getPropertyId()}"
            logm("After tenant db  property id: ${getPropertyId()}")
        }

        // Create a reference to the Job's Rest Client
        jobRestClient = new TetrisRESTClient(REST_BASE_JEMS_URI)
        println "Rest Client inited ${REST_BASE_JEMS_URI}"
        logm("Rest Client inited ${REST_BASE_JEMS_URI}")
        clearRegulator()

        // Keep track of the time the test started - can be useful when querying for data created by the Job
        timeTestStarted = new Timestamp((new Date().getTime()) - HALF_MINUTE)
    }

    // Default to SandBox - override - if you dare
    protected String getClientCode() {
        return CLIENT_CODE_SANDBOX
    }

    protected String getClientId() {
        return CLIENT_CODE_SANDBOX.equals(getClientCode()) ? CLIENT_ID_SANDBOX : ClientLocator.getClientIDByClientCode(getClientCode())
    }

    protected String getHiltonClientCode() {
        return CLIENT_CODE_HILTON
    }

    protected abstract String getPropertyId()

    protected abstract String getPropertyCode()


    protected String getConfigParamNode() {
        return "pacman." + getClientCode() + "." + getPropertyCode()
    }

    @Synchronized
    @After
    void after() {
        // Delete any Job records created by the test
        if (JOB_INSTANCES_LAUNCHED.get().size() > 0) { // JOB_INSTANCES_LAUNCHED.isEmpty
            println "Job Instances Launched: " + JOB_INSTANCES_LAUNCHED.get().size()
            logm("Job Instances Launched: " + JOB_INSTANCES_LAUNCHED.get().size())
            waitForRunningJobs()
        }
        if (tenantDbConnection != null) {
            tenantDbConnection.close()
        }

    }

    @Synchronized
    @AfterClass
    static void afterJemsTestClass() {
        if (enableExtentReporting) {
            ExtentTestManager.getInstance().flush();
        }


    }


    @Synchronized
    static void logm(def message) {
        println message
        if (enableExtentReporting) {
            ExtentTestManager.getTest().log(Status.INFO, message.toString());
        }
    }

    @Synchronized
    void waitForRunningJobs() {
        println "WAITING FOR JOB INSTANCES:- " + JOB_INSTANCES_LAUNCHED.get()
        logm("WAITING FOR JOB INSTANCES:- " + JOB_INSTANCES_LAUNCHED.get())
        waitForRunningJobs(JOB_RUN_TIMEOUT)
    }

    void waitForRunningJobs(long timeout) {
        long startTime = new Date().getTime()
        ThreadLocal<Boolean> anyJobsRunning = new ThreadLocal<Boolean>()
        anyJobsRunning.set(true);
        while (anyJobsRunning.get() && (timeout > (new Date().getTime() - startTime))) {
            anyJobsRunning.set(areAnyJobsRunning())
            logm("Any jobs still running: " + anyJobsRunning.get())
            logm("Sleeping for 10 Seconds...")
            Thread.sleep(10000)
        }

        println "\nDone waiting..."
        logm("\nDone waiting...")
        assert !anyJobsRunning.get(): "No jobs should be running"
    }

    @Synchronized
    void waitForNewJobInstancesWithName(String jobName) {
        waitForNewJobInstanceWithName(null, jobName)
    }

    @Synchronized
    void waitForNewJobInstanceWithName(String propertyId, String jobName) {
        addJobInstancesAfterMaxJobInstanceIdAndWithJobName(jobName, propertyId ? Integer.valueOf(propertyId) : null)
        waitForRunningJobs()
    }

    @Synchronized
    Long getLatestJobInstanceIdByJobName(String propertyId, String jobName){
        def connection = DbConnection.getJobDbConnection()
        Long jobInstanceId = connection.rows(" select max(job_instance_id) jobInstanceId from JOB_VIEW where Job_Name='" + jobName + "' and property_id=" + propertyId)[0].jobInstanceId
        connection.close()
        return jobInstanceId
    }

    @Synchronized
    void waitForNewJobInstanceWithNameAndTimeout(String propertyId, String jobName, long timeout) {
        addJobInstancesAfterMaxJobInstanceIdAndWithJobName(jobName, propertyId ? Integer.valueOf(propertyId) : null)
        waitForRunningJobs(timeout)
    }

    @Synchronized
    protected void addJobInstancesAfterMaxJobInstanceIdAndWithJobName(String jobName, Integer propertyId) {
        def sql = "select "

        if (JOB_INSTANCES_LAUNCHED.get().size() > 0) {
            List<Long> listOfJobInstances = new ArrayList<Long>(JOB_INSTANCES_LAUNCHED.get())
            Collections.sort(listOfJobInstances)
            sql += "ji.job_instance_id from Job_Instance ji "
            if (propertyId != null) {
                sql += "inner join Job_Instance_Work_Context jiwk on ji.job_instance_id = jiwk.job_instance_id "
            }
            sql += "where ji.job_name = '" + jobName + "'"
            if (propertyId != null) {
                sql += " and jiwk.property_id = " + propertyId
            }
            sql += " and ji.job_instance_id > " + listOfJobInstances.get(listOfJobInstances.size() - 1)

            jobDbConnection.eachRow(sql, { row -> JOB_INSTANCES_LAUNCHED.get().add((Long) row[0]) })
        } else {
            // i think we only want the last job when first job launched
            sql += "max(ji.job_instance_id) from Job_Instance ji "
            if (propertyId != null) {
                sql += "inner join Job_Instance_Work_Context jiwk on ji.job_instance_id = jiwk.job_instance_id "
            }
            sql += "where ji.job_name = '" + jobName + "' "
            if (propertyId != null) {
                sql += "and jiwk.property_id = " + propertyId
            }

            jobDbConnection.eachRow(sql, { row -> JOB_INSTANCES_LAUNCHED.get().add((Long) row[0]) })
        }
    }

    public static void deleteJobInstances(Set<Long> jobInstances) {

        def maxProblemNoteId
        def maxSupportBulletinId

        if (jobInstances == null || jobInstances.size() == 0) {
            // nothing to delete
            return
        }

        maxProblemNoteId = (Long) jobDbConnection.firstRow("select max(PROBLEM_NOTE_ID) from PROBLEM_NOTE").getAt(0)
        maxProblemNoteId = (maxProblemNoteId == null ? new Long(0) : maxProblemNoteId)

        maxSupportBulletinId = (Long) jobDbConnection.firstRow("select max(SUPPORT_BULLETIN_ID) from SUPPORT_BULLETIN").getAt(0)
        maxSupportBulletinId = (maxSupportBulletinId == null ? new Long(0) : maxSupportBulletinId)

        String jobInstanceIds = StringUtils.join(jobInstances, ",")
        String jobInstanceClause = MessageFormat.format(" where JOB_INSTANCE_ID in ({0})", jobInstanceIds)
        String jobExecutionClause = MessageFormat.format(" where JOB_EXECUTION_ID in (select JOB_EXECUTION_ID from \
				JOB_EXECUTION where JOB_INSTANCE_ID in ({0}))", jobInstanceIds)
        String stepExecutionClause = MessageFormat.format(" where STEP_EXECUTION_ID in (select STEP_EXECUTION_ID from \
				STEP_EXECUTION where JOB_EXECUTION_ID in (select JOB_EXECUTION_ID from JOB_EXECUTION where \
				JOB_INSTANCE_ID in ({0})))", jobInstanceIds)

        // Problems, Notes, Solutions, Bulletins
        jobDbConnection.executeUpdate(MessageFormat.format("delete from PROBLEM_NOTE where PROBLEM_NOTE_ID > {0}", maxProblemNoteId))
        jobDbConnection.executeUpdate("delete from PROBLEM" + stepExecutionClause)
        //jobDbConnection.executeUpdate(MessageFormat.format("delete from PROPOSED_SOLUTION where PROPOSED_SOLUTION_ID > {0}", maxProposedSolutionId))
        jobDbConnection.executeUpdate(MessageFormat.format("delete from SUPPORT_BULLETIN where SUPPORT_BULLETIN_ID > {0}", maxSupportBulletinId))

        // Jobs, steps
        jobDbConnection.executeUpdate("delete from JOB_STATE" + stepExecutionClause)
        jobDbConnection.executeUpdate("delete from STEP_EXECUTION_CONTEXT" + stepExecutionClause)
        jobDbConnection.executeUpdate("delete from STEP_EXECUTION" + jobExecutionClause)
        jobDbConnection.executeUpdate("delete from JOB_EXECUTION_PARAMS" + jobExecutionClause)
        jobDbConnection.executeUpdate("delete from JOB_EXECUTION_CONTEXT" + jobExecutionClause)
        jobDbConnection.executeUpdate("delete from JOB_EXECUTION" + jobInstanceClause)
        jobDbConnection.executeUpdate("delete from JOB_INSTANCE_WORK_CONTEXT" + jobInstanceClause)
        jobDbConnection.executeUpdate("delete from JOB_INSTANCE" + jobInstanceClause)
    }

    protected boolean areAnyJobsRunning() {
        boolean anyRunning = false
        def status
        for (jobInstanceId in JOB_INSTANCES_LAUNCHED.get()) {
            status = getJobInstanceExitStatus(jobInstanceId)
            logm("Job running?: " + jobInstanceId + " : " + status)
            // Exit Status
            // http://static.springsource.org/spring-batch/apidocs/org/springframework/batch/core/ExitStatus.html
            // COMPLETED, EXECUTING, FAILED, NOOP, STOPPED, UNKNOWN

            // Batch Status
            // http://static.springsource.org/spring-batch/apidocs/org/springframework/batch/core/BatchStatus.html
            // ABANDONED, COMPLETED, FAILED, STARTED, STARTING, STOPPED, STOPPING, UNKNOWN
            if (status.equals("EXECUTING") || status.equals("UNKNOWN") || status.equals("RUNNING_ASYNC")) {
                logm("Job Instance " + jobInstanceId + " is still running")
                anyRunning = true
                break
            }
        }
        return anyRunning
    }

    public static Object getJobInstanceDetails(Long jobInstanceId) {
//        sleep(50) // Have seen some exceptions on querying rest service - throwing a dart
        def httpResponse
        try {
            httpResponse = pacmanServicesRestClient.get(path: MessageFormat.format(URL_JOB_DETAIL_GET, jobInstanceId))
        } catch (HttpResponseException ex) {
            println "Failed to get job execution details: " + ex.response.data
            logm("Failed to get job execution details: " + ex.response.data)
            assert !ex.response: "Failed to get job execution details"
        }

        return httpResponse.data
    }

    public static Object getJobExecutionDetails(Long jobExecutionId) {
//        sleep(50) // Have seen some exceptions on querying rest service - throwing a dart
        def httpResponse
        try {
            httpResponse = pacmanServicesRestClient.get(path: MessageFormat.format(URL_JOB_EXECUTION_GET, jobExecutionId))
        } catch (HttpResponseException ex) {
            println "Failed to invoke job status: " + ex.response.data
            logm("Failed to invoke job status: " + ex.response.data)
            assert !ex.response: "Failed to get job execution details"
        }

        return httpResponse.data
    }

    public static String getJobInstanceExitStatus(Long jobInstanceId) {
        def sql = """select exit_code from Job_Execution where job_execution_id in 
				(select max(job_execution_id) from Job_Execution where Job_Instance_Id = ${jobInstanceId})"""
        return (String) jobDbConnection.firstRow(sql).getAt(0)
    }

    public static String getJobInstanceStatus(Long jobInstanceId) {
        def sql = """select status from Job_Execution where job_execution_id in 
            (select max(job_execution_id) from Job_Execution where Job_Instance_Id =  ${jobInstanceId})"""
        return (String) jobDbConnection.firstRow(sql).getAt(0)
    }

    public static void isJobStuckInRegulator(Long jobInstanceId, Long timeout) {
        long startTime = new Date().getTime()
        int count = 1
        def sql = """select count(*) as cnt from Job_Execution where job_instance_id = ${jobInstanceId}
                               and status = 'STOPPED' and exit_code = 'RUNNING_ASYNC' and (select count(*) as cnt from 
                               Job_Execution where job_instance_id = ${jobInstanceId}) = 1 ;"""

        count = jobDbConnection.firstRow(sql).cnt

        while(count == 1 && (timeout > (new Date().getTime() - startTime))) {
            count =  jobDbConnection.firstRow(sql).cnt
            logm("Waiting for Job to move out of regulator - Job InstanceId: " + jobInstanceId)
            logm("Sleeping for 10 seconds...")
            Thread.sleep(10000)
        }

        assert count == 0 : "The job has been stuck in regulator for more than 5 minutes, hence not waiting for the complete timeout to fail the test"
    }

    public static Long getJobInstanceIdForJobExecutionId(Long jobExecutionId) {
        def sql = "select job_instance_id from Job_Execution where job_execution_id = ${jobExecutionId}"
        return (Long) jobDbConnection.firstRow(sql).getAt(0)
    }

    public static String getJobStepName(Long jobInstanceId) {
        def sql = "select STEP_NAME from JOB_STATE where Job_Instance_Id = ${jobInstanceId}"
        return (String) jobDbConnection.firstRow(sql).getAt(0)
    }

    public static String getJobExecutionExitStatus(Long jobExecutionId) {
        def jobExecution = getJobExecutionDetails(jobExecutionId)
        def status = jobExecution.exitCode
        println "Job Execution " + jobExecutionId + " exit status: " + status
        logm("Job Execution " + jobExecutionId + " exit status: " + status)
        return status
    }

    void assertAllJobInstancesCompletedSuccessfully() {
        // Build a Map of non-completed job instances
        Map<Long, String> nonCompletedJobInstances = new TreeMap<Long, String>()
        for (Long jobInstanceId : JOB_INSTANCES_LAUNCHED.get()) {
            String status = getJobInstanceStatus(jobInstanceId)
            if (!"COMPLETED".equals(getJobInstanceStatus(jobInstanceId))) {
                nonCompletedJobInstances.put(jobInstanceId, status)
            }
        }

        // Force an assert failed to indicate that jobs did not complete as expected
        assert nonCompletedJobInstances.isEmpty(): "Job Instances did not complete: " + nonCompletedJobInstances.keySet()

        // If we have non-completed job instances, let's get the problem details and write out what failed (purposely not filtering on a specific client/property)
        if (!nonCompletedJobInstances.isEmpty()) {
            // Print out a list of all non-completed job instances with their current status
            println "================================================================================================================================================="
            logm("=================================================================================================================================================")
            println "NON-COMPLETED JOB INSTANCES LAUNCHED DURING TEST"
            logm("NON-COMPLETED JOB INSTANCES LAUNCHED DURING TEST")
            println "================================================================================================================================================="
            logm("=================================================================================================================================================")
            println "ID      	       CURRENT STATUS"
            logm("ID      	       CURRENT STATUS")
            println "--------------------------------"
            logm("--------------------------------")
            for (Long jobInstanceId : nonCompletedJobInstances.keySet()) {
                println StringUtils.rightPad(String.valueOf(jobInstanceId), 18) + " " + nonCompletedJobInstances.get(jobInstanceId)
                logm(StringUtils.rightPad(String.valueOf(jobInstanceId), 18) + " " + nonCompletedJobInstances.get(jobInstanceId))
            }
            println "--------------------------------"
            logm("--------------------------------")

        }
    }

    @Synchronized
    protected TetrisRESTClient login() {
        TetrisRESTClient restClient = new TetrisRESTClient(REST_BASE_PACMAN_SERVICES_URI,
                [username: USER_NAME_SSO, password: USER_PWD_SSO])
        if (null != getPropertyId()) {
            restClient.headers.propertyId = getPropertyId()
        }
        return restClient
    }

    @Synchronized
    protected Long invokeJobViaRestService(String jobName, Map<String, String> params) {
//        sleep(50) // Have seen some exceptions on querying rest service - throwing a dart
        Long jobExecutionId = JemsUtil.invokeJob(jobRestClient, jobName, params)

        // Add the JobInstanceId to the list of launched jobs
        JOB_INSTANCES_LAUNCHED.get().add(JemsUtil.getJobInstanceIdForJobExecutionId(jobExecutionId))

        sleep(300) // let job get past "UNKNOWN" state
        return jobExecutionId
    }

    // Helper method for common execution context
    protected Long invokeJobWithPropIdAndDate(String jobName) {
        Map<String, String> parameters = new HashMap<String, String>()
        parameters.put(PARAM_PROP_ID, String.valueOf(getPropertyId()))
        parameters.put(PARAM_DATE, DATE_FORMAT.format(new Date()))
        return invokeJobViaRestService(jobName, parameters)
    }

    // Helper method for common execution context
    protected Long invokeJobWithTaskIdAndDate(String jobName, Long taskId) {
        Map<String, String> parameters = new HashMap<String, String>()
        parameters.put(PARAM_TASK_ID, String.valueOf(taskId))
        parameters.put(PARAM_DATE, DATE_FORMAT.format(new Date()))
        return invokeJobViaRestService(jobName, parameters)
    }

    // Takes a JOB INSTANCE ID
    protected void stopJobViaRestService(Long jobInstanceId) {
        invokeJobActionService(jobInstanceId, URL_JOB_STOP)
    }

    // Takes a JOB INSTANCE ID
    protected void forceStopJobViaRestService(Long jobInstanceId) {
        invokeJobActionService(jobInstanceId, URL_JOB_FORCE_STOP)
    }

    // Takes a JOB INSTANCE ID
    protected void abandonJobViaRestService(Long jobInstanceId) {
        invokeJobActionService(jobInstanceId, URL_JOB_ABANDON)
    }

    // Takes a JOB EXECUTION ID
    protected Long continueJobViaRestService(Long jobExecutionId) {
        def httpResponse = invokeJobActionService(jobExecutionId, URL_JOB_CONTINUE)
        // Get the new Job Execution ID
        return null != httpResponse ? Long.valueOf(httpResponse.data.text) : null
    }

    // Takes a JOB EXECUTION ID - RESUME is a GET for some reason
    protected Long resumeJobViaRestService(Long jobExecutionId, boolean shouldFailOnHttpError) {
//        sleep(200) // Have seen some exceptions on querying rest service - throwing a dart
        def path = MessageFormat.format(URL_JOB_RESUME, jobExecutionId)
        def httpResponse
        try {
            httpResponse = pacmanServicesRestClient.get(path: path)
            sleep(200) // Let rest service execute
        } catch (HttpResponseException ex) {
            println "Failed to execute job service " + path + " for job " + jobExecutionId + ": " + ex.response.data
            logm("Failed to execute job service " + path + " for job " + jobExecutionId + ": " + ex.response.data)
            if (shouldFailOnHttpError) {
                assert !ex.response: ("Failed to execute job service " + path + " for job " + jobExecutionId)
            }
        }
        // Get the new Job Execution ID
        return Long.valueOf(httpResponse.data.text)
    }

    protected Long resumeJobInstanceViaRestService(Long jobInstanceId, boolean shouldFailOnHttpError) {
//        sleep(200) // Have seen some exceptions on querying rest service - throwing a dart
        def path = MessageFormat.format(URL_JOB_RESUME_JOB_INSTANCE, jobInstanceId)
        def httpResponse
        try {
            httpResponse = pacmanServicesRestClient.get(path: path)
            sleep(200) // Let rest service execute
        } catch (HttpResponseException ex) {
            println "Failed to execute job service " + path + " for job instance " + jobInstanceId + ": " + ex.response.data
            logm("Failed to execute job service " + path + " for job instance " + jobInstanceId + ": " + ex.response.data)
            if (shouldFailOnHttpError) {
                assert !ex.response: ("Failed to execute job service instance " + path + " for job " + jobInstanceId)
            }
        }
        // Get the new Job Execution ID
        return Long.valueOf(httpResponse.data.text)
    }

    private def invokeJobActionService(Long jobInstanceOrExecutionId, String url) {
//        sleep(200) // Have seen some exceptions on querying rest service - throwing a dart
        def path = MessageFormat.format(url, jobInstanceOrExecutionId)
        def httpResponse
        try {
            httpResponse = pacmanServicesRestClient.post(path: path)
            sleep(200) // Let rest service execute
        } catch (HttpResponseException ex) {
            println "Failed to execute job service " + path + " for job " + jobInstanceOrExecutionId + ": " + ex.response.data
            logm("Failed to execute job service " + path + " for job " + jobInstanceOrExecutionId + ": " + ex.response.data)
            assert !ex.response: ("Failed to execute job service " + path + " for job " + jobInstanceOrExecutionId)
        }
        return httpResponse
    }

    // TODO: Move to PropertiesTestHelper.clearRegulator
    protected void clearRegulator() {
        def constraint = MessageFormat.format(REGULATOR_CONSTRAINT, getClientCode(), getPropertyCode())
        globalDbConnection.execute("update Regulator_Request set Status_ID = 4 where Request_Constraint = ${constraint}")
    }

    @Synchronized
    public static void enableSyncronizationFlag(String event) {
        def header = [ Authorization: "Basic ${"<EMAIL>:password".bytes.encodeBase64()}"]
        def param = [ propertyId: 5]
        pacmanServicesRestClient.setHeaders(header)
        pacmanServicesRestClient.post(path: MessageFormat.format(REST_API_SYNC_FLAG_ENABLE, event) , query : param)
    }

    @Synchronized
    public static void enableSyncronizationFlagForForceSync(String event) {
        def header = [ Authorization: "Basic ${"<EMAIL>:password".bytes.encodeBase64()}"]
        def param = [ propertyId: 10069]
        pacmanServicesRestClient.setHeaders(header)
        pacmanServicesRestClient.post(path: MessageFormat.format(REST_API_SYNC_FLAG_ENABLE, event) , query : param)
    }

    @Synchronized
    public static void enableSyncronizationFlagForForceSync(String event, int propertyId) {
        def header = [ Authorization: "Basic ${"<EMAIL>:password".bytes.encodeBase64()}"]
        def param = [ propertyId: propertyId]
        pacmanServicesRestClient.setHeaders(header)
        pacmanServicesRestClient.post(path: MessageFormat.format(REST_API_SYNC_FLAG_ENABLE, event) , query : param)
    }

    @Synchronized
    public static void setSystemProperty(String property, String value) {
        pacmanServicesRestClient.post(path: MessageFormat.format(REST_API_SET_SYSTEM_PROPERTY, property, value))
    }

    @Synchronized
    public static void setWildflySystemProperties(String value) {
        pacmanServicesRestClient.post(path: MessageFormat.format(REST_API_ENABLE_SYSTEM_PROPERTY, value))
    }

    @Synchronized
    public static Object getWildflySystemProperties(String value) {
        pacmanServicesRestClient.get(path: MessageFormat.format(REST_API_ENABLE_SYSTEM_PROPERTY, value))
    }

    @Synchronized
    public static void removeWildflySystemProperty(String key) {
        pacmanServicesRestClient.delete(path: MessageFormat.format(REST_API_ENABLE_SYSTEM_PROPERTY, key))
    }

    static int getCountForTheQuery(Sql connection, String sqlQuery) {
        // avoids that loop paradigm when seem to be enamored with:
        // connection.eachRow(sqlQuery) { row -> count = (int) row[0] }
        return (int) connection.firstRow(sqlQuery).getAt(0)
    }

    static def getGlobalDbConnection() {
        return globalDbConnection
    }

    @Synchronized
    void executeSqlFile(String filePath) {
        InputStream inputFile = getClass().classLoader.getResourceAsStream(filePath)
        String[] lines = inputFile.text.split('\n')
        for (String line : lines) {
            line = line.replaceAll(":propertyId", getPropertyId())
            tenantDbConnection.execute(line)
        }
    }

    static void checkJobStatus(String jobName, String propertyID) {
        def sql = "select max(a.JOB_INSTANCE_ID) as jobInstanceID from JOB_INSTANCE a inner join JOB_INSTANCE_WORK_CONTEXT  b on a.JOB_INSTANCE_ID=b.JOB_INSTANCE_ID where  a.JOB_NAME = '${jobName}' and  b.PROPERTY_ID='${propertyID}'"
        Long jobInstanceID = (Long) DbConnection.getJobDbConnection().firstRow(sql)[0]
        JemsTest jemsTest = new JemsTest() {
            @Override
            protected String getPropertyId() {
                return null
            }

            @Override
            protected String getPropertyCode() {
                return null
            }
        }

        println("Job Instance ID: ${jobInstanceID}")
        jemsTest.setJobDbConnection(DbConnection.getJobDbConnection())
        jemsTest.JOB_INSTANCES_LAUNCHED.get().add(jobInstanceID)
        jemsTest.waitForRunningJobs()
        //jemsTest.assertAllJobInstancesCompletedSuccessfully()
    }

    static boolean checkIfJobTriggerred(String jobName, String propertyID) {
        def sql = "select max(a.JOB_INSTANCE_ID) as jobInstanceID from JOB_INSTANCE a inner join JOB_INSTANCE_WORK_CONTEXT  b on a.JOB_INSTANCE_ID=b.JOB_INSTANCE_ID where  a.JOB_NAME = '${jobName}' and  b.PROPERTY_ID='${propertyID}'"
        Long jobInstanceID = (Long) DbConnection.getJobDbConnection().firstRow(sql)[0];
        return jobInstanceID !=null && getJobInstanceStatus(jobInstanceID)!=null;
    }

    static void cleanJobDb(String jobNames, String propertyID) {
        Set<Long> lastGoodKnownJobInstances = new HashSet<Long>()
        def selectJobId = "select JI.JOB_INSTANCE_ID from JOB_INSTANCE as JI join JOB_INSTANCE_WORK_CONTEXT as JIWC on JI.JOB_INSTANCE_ID=JIWC.JOB_INSTANCE_ID " +
                " where JOB_NAME in (" + jobNames + ") and JIWC.PROPERTY_ID=" + propertyID
        jobDbConnection.eachRow(selectJobId) { row ->
            lastGoodKnownJobInstances.add((Long) row.JOB_INSTANCE_ID)
        }
        deleteJobInstances(lastGoodKnownJobInstances)
    }

    public static void copyFile(String absolutePathFrom, String absolutePathTo) throws IOException {
        Path src = Paths.get(absolutePathFrom.replaceAll("//", "/"));
        Path dest = Paths.get(absolutePathTo.replaceAll("//", "/"));
        Files.copy(src.toFile(), dest.toFile());
        logm("Copying files: From Path-> " + absolutePathFrom + " To Path-> " + absolutePathTo)
    }

    public static void setJobDbConnection(def jobDBConnection) {
        jobDbConnection = jobDBConnection;

    }

    public void executeSqlFile(String filePath, String DatabaseName) {
        def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
        InputStream inputFile = getClass().classLoader.getResourceAsStream(filePath)
        String[] lines = inputFile.text.split('\n')
        for (String line : lines) {
            println(line)
            connection.execute(line);
        }
        connection.close()
    }


    public void executeRatchetFile(String filePath) {
        def connection = DbConnection.getRatchetConnection()
        InputStream inputFile = getClass().classLoader.getResourceAsStream(filePath)
        String[] lines = inputFile.text.split('\n')
        for (String line : lines) {
            println(line)
            connection.execute(line);
        }
        connection.close()
    }

    public void executeGlobalSqlFile(String filePath) {
        def connection = DbConnection.getGlobalConnection()
        InputStream inputFile = getClass().classLoader.getResourceAsStream(filePath)
        String[] lines = inputFile.text.split('\n')
        for (String line : lines) {
            println(line)
            try {
                connection.execute(line);
            } catch (Exception e) {
                println("SQL QUERY CAN'T BE EXECUTED:" + line)
            }

        }
        connection.close()
    }

    public void executeSqlFile(String filePath, String DatabaseName, String propertyIdInScript, String newPropertyId) {
        def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
        InputStream inputFile = getClass().classLoader.getResourceAsStream(filePath)
        String[] lines = inputFile.text.split('\n')
        for (String line : lines) {
            line = line.replace(propertyIdInScript, newPropertyId)
            println(line)
            connection.execute(line);
        }
        connection.close()
    }

    public def getMongoDataBase(){
        MongoClient mongoClient = new MongoClient(connectionString);
        return mongoClient.getDatabase("ngi");
    }

    public static String resolvePath(String input){
        return input.replace("\\",File.separator);
    }

    static void addIntegrationSettings(Map hotelProperties, String vednorId, String integrationType, String clientCode, String propertyCode) {
        def vendorConfig = ensureConfiguration(vednorId, integrationType, clientCode, propertyCode,
                false, true, false)
        vendorConfig.putAll(hotelProperties)
        updateIntegrationConfig(vendorConfig)
    }

    protected static ensureConfiguration(String vendorId, String integrationType, String clientCode, String propertyCode,
                                         boolean oxiRoomStayReservationByDay, boolean useLegacyRoomStayHandling,
                                         boolean installMode) {

        // Client
        def intergationClientConfig = getClientConfiguration(vendorId, clientCode)
        if (intergationClientConfig == null) {
            intergationClientConfig = new JSONObject([
                    "integrationType": integrationType
            ])
        }
        intergationClientConfig.put("vendorId", vendorId)
        intergationClientConfig.put("clientCode", clientCode)
        intergationClientConfig.put("clientEnvironmentName", DEFAULT_CLIENT_ENVIRONMENT_NAME)
        intergationClientConfig.put("baseCurrencyCode", "USD")
        intergationClientConfig.put("tokenUrl", "http://localhost:9090/mock/hilton/token")
        intergationClientConfig.put("baseUrl", "http://localhost:9090/mock/hilton")
        intergationClientConfig.put("outgoingUrl", "http://localhost:9090/ngipublic/rest/mockserver/oxi/requestroomtype")
        intergationClientConfig.put("oxiRoomStayReservationByDay", oxiRoomStayReservationByDay)
        intergationClientConfig.put("useLegacyRoomStayHandling", useLegacyRoomStayHandling)

        updateIntegrationConfig(intergationClientConfig)

        // Property
        def intergationPropertyConfig = getPropertyConfiguration(vendorId, clientCode, propertyCode)
        if (intergationPropertyConfig == null) {
            intergationPropertyConfig = new JSONObject([
                    "integrationType": integrationType
            ])
        }
        intergationPropertyConfig.put("vendorId", vendorId)
        intergationPropertyConfig.put("clientCode", clientCode)
        intergationPropertyConfig.put("propertyCode", propertyCode)
        intergationPropertyConfig.put("clientEnvironmentName", DEFAULT_CLIENT_ENVIRONMENT_NAME)
        intergationPropertyConfig.put("baseCurrencyCode", "USD")
        intergationPropertyConfig.put("taxAdjustmentValue", "10")
        intergationPropertyConfig.put("outgoingUrl", "http://localhost:9090/ngipublic/rest/mockserver/oxi/requestroomtype")
        intergationPropertyConfig.put("oxiInterfaceName", "OXI_PMS")
        intergationPropertyConfig.put("oxiRoomStayReservationByDay", oxiRoomStayReservationByDay)
        intergationPropertyConfig.put("useLegacyRoomStayHandling", useLegacyRoomStayHandling)
        intergationPropertyConfig.put("installMode", installMode)

        updateIntegrationConfig(intergationPropertyConfig)

        return intergationPropertyConfig
    }


    protected static void updateIntegrationConfig(integrationSettingsConfig) {
        if (integrationSettingsConfig.containsKey("id")) {
            integrationSettingsConfig.remove("integrationConfigType")
            integrationSettingsConfig.remove("createDate")
            integrationSettingsConfig.remove("lastModifiedDate")
            integrationSettingsConfig.remove("version")
            integrationSettingsConfig.remove("extendedAttributes")
            def id = integrationSettingsConfig.get("id")
            INTEGRATION_SETTINGS_REST_CLIENT.put(
                    path: "/integration/settings/$id",
                    body: integrationSettingsConfig,
                    requestContentType: ContentType.JSON
            )
        } else {
            INTEGRATION_SETTINGS_REST_CLIENT.post(
                    path: "/integration/settings",
                    body: integrationSettingsConfig,
                    requestContentType: ContentType.JSON
            )
        }
    }

    protected static Map getClientConfiguration(String vendorId, String clientCode) {
        try {
            def response = INTEGRATION_SETTINGS_REST_CLIENT.get(
                    path: "/integration/settings/clients",
                    query: [vendorId: vendorId]
            ).responseData as Map
            return response.content == null ? null : response.content.find { it.clientCode == clientCode } as Map
        } catch (HttpResponseException e) {
            if (e.getStatusCode() != 404) {
                throw e
            }
        }

        return null
    }

    protected static Map getPropertyConfiguration(String vendorId, String clientCode, String propertyCode) {
        try {
            def response = INTEGRATION_SETTINGS_REST_CLIENT.get(
                    path: "integration/settings/properties",
                    query: [vendorId: vendorId, clientCode: clientCode]
            ).responseData as Map
            return response.content == null ? null : response.content.find { it.propertyCode == propertyCode } as Map
        } catch (HttpResponseException e) {
            if (e.getStatusCode() != 404) {
                throw e
            }
        }

        return null
    }

    static void deleteIntegrationSetting(String vendorId, String clientCode) {
        try {
            INTEGRATION_SETTINGS_REST_CLIENT.delete(
                    path: "integration/settings/properties",
                    query: [vendorId: vendorId, clientCode: clientCode]
            )
            println("DELETED Integration Settings for Vendor ID: " + vendorId)
        } catch (HttpResponseException e) {
            if (e.getStatusCode() != 404) {
                throw e
            }
        }
    }

    public static void updateOptOutputVersionToZero(String propertyID){
        connection = DbConnection.getTenantDatabaseConnection(propertyID);
        connection.execute(UPDATE_OPT_VERSION);
        connection.close();
    }
}


