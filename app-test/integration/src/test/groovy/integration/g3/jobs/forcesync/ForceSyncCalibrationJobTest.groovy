package integration.g3.jobs.forcesync

import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import common.forcesync.AbstractForceSyncCalibrationJobTest
import org.junit.Before
import org.junit.Test
import static org.junit.Assert.assertEquals
import com.ideas.tetris.util.PropertiesTestHelper
import com.ideas.tetris.util.propertyrollout.PropertyRolloutTestHelper


class ForceSyncCalibrationJobTest extends AbstractForceSyncCalibrationJobTest {

	/*RC/RT end to end test.
	 1) Move RT (SDQL) from master Class [Standard] to [Suite]
	 2) Set the flag for Room Class Configuration [staleness/dirty]
	 3) Verify the flag against DB for SPE
	 4) Run the SyncAll
	 5) Verify the flag against DB for SPE */
	 
	 /*Special Event end to end test.
	 1) Define or Edit special event
	 2) Set the flag for Special Event [staleness/dirty]
	 3) Verify the flag against DB for SPE
	 4) Run the SyncAll
	 5) Verify the flag against DB for SPE */

	@Before
	void before() {
		//super.before();

		PropertiesTestHelper.setPropertyStage(pacmanServicesRestClient, getPropertyId(), PropertyRolloutTestHelper.STAGE_ONE_WAY)
		PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, "pacman.PreProduction.ReferDifferentialTableForPaceOvrbkAccom", "10016", "True")
	}
	@Test
	void invokeForceSyncCalibrationJob() {
		// Move RT
		moveRTtoDifferentRC()
		
		// Change a special event to impact forecasting
		modifySpecialEventToImpactForecasting()
		
		// Assert that the SystemComponents are set
		assertSystemComponentFlag(SYSTEM_COMPONENT_ACCOMMODATION_CONFIGURATION, true)
		assertSystemComponentFlag(SYSTEM_COMPONENT_SPECIAL_EVENTS, true)

		// Start the job
		invokeJob()
		
		// Wait for any running jobs to complete
		waitForRunningJobs()
        //assertAllJobInstancesCompletedSuccessfully()

		// Assert Calibration ran successfully
		assertCalibration()

		// Assert Forecasting and Optimization ran successfully
		assertForecastingAndOptimization()

		// Assert that the SystemComponents are cleared of sync flags
		assertSystemComponentFlag(SYSTEM_COMPONENT_ACCOMMODATION_CONFIGURATION, false)
		assertSystemComponentFlag(SYSTEM_COMPONENT_SPECIAL_EVENTS, false)
		assertJobStepCompleted("populateDifferentialPaceForOverbookingAccomDecisionsStep", "ForceSyncCalibration", 10016)
	}

    void moveRTtoDifferentRC() {
		// Move the RT
		tenantDbConnection.execute("update Accom_Type set Accom_Class_ID=72 where Accom_Type_ID=46 and Property_ID=10016")
		
		// Enable the sync flag so that calibration will run
		enableSyncronizationFlagForForceSync(SYNC_EVENT_ACCOMMODATION_CONFIG_CHANGED, 10016)
	}
	
	void modifySpecialEventToImpactForecasting() {
		tenantDbConnection.execute("update Property_Special_Event set Impact_On_Forecast=1 where \
				Property_ID=10016 and Property_Special_Event_ID=494")
		tenantDbConnection.execute("update Property_Special_Event_Instance set Enable_Forecast=1 where \
				Property_Special_Event_ID=494")
		
		// Enable the sync flag so that calibration will run
		enableSyncronizationFlagForForceSync(SYNC_EVENT_SPECIAL_EVENTS_CHANGED, 10016)
	}

    void executeSqlFile(String filePath) {
        InputStream inputFile = getClass().classLoader.getResourceAsStream(filePath)
        String[] lines = inputFile.text.split('\n')
        for (String line: lines) {
            line = line.replaceAll(":propertyId", getPropertyId())
            tenantDbConnection.execute(line)
        }
    }

	void assertJobStepCompleted(String stepName, String jobName, long propertyId) {
		def connection = DbConnection.getJobDbConnection()
		def row = connection.firstRow("""
        SELECT se.status AS stepExecutionStatus
        FROM step_execution se
        JOIN job_view jv ON se.job_execution_id = jv.job_execution_id
        WHERE jv.job_name = ? 
          AND jv.property_id = ?
          AND se.step_name = ?
        ORDER BY jv.job_execution_id DESC
        FETCH FIRST 1 ROWS ONLY
    """, [jobName, propertyId as Long, stepName])

		String stepExecutionStatus = row.stepExecutionStatus

		assertEquals("Step $stepName for job $jobName (property $propertyId) did not complete",
				"COMPLETED", stepExecutionStatus)
	}
}
