package integration.g3.jobs.createcommit

import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import common.ForecastGroupUtil
import common.JemsTest
import org.junit.After
import org.junit.Before
import org.junit.Test

import java.text.MessageFormat

/*
 * You've been warned. I believe this MUST run after ExtractFile BUT not before the create/commit forecast groups
 */

class PostExtractForecastGroupCreateCommitNewMarketSegmentTest extends JemsTest {
    static final String REST_URI_CREATE_FORECAST_GROUP = "createforecastgroups/create/v1"
    static final String REST_URI_COMMIT_FORECAST_GROUP = "proposedforecastgroups/commit/v1"
    static final String PROPERTY_ID = "10019"
    static final String PROPERTY_CODE = "DALMC"
    static final String PROPERTY_CRS_TIMEZONE = "America/Phoenix"

    static def connection
    static TetrisRESTClient restClient

    @Override
    protected String getPropertyId() {
        return PROPERTY_ID
    }

    @Override
    protected String getPropertyCode() {
        return PROPERTY_CODE
    }

    @Before
    void before() {
        //super.before()
        connection = DbConnection.getDatabaseConnection_DALMC()
        connection.executeUpdate(" " +
                " update File_Metadata set file_name =  'HILTON_DALMC_20120615_0148_TRANS.psv',  SnapShot_DT='2012-06-15' where  File_Name = 'HILTON_DALMC_20110615_0148_TRANS.psv' and  SnapShot_DT = '2011-06-15' " +
                " update File_Metadata set file_name =  'HILTON_DALMC_20120615_0148_rates.xml',  SnapShot_DT='2012-06-15' where  File_Name = 'HILTON_DALMC_20110615_0148_rates.xml' and  SnapShot_DT = '2011-06-15' " +
                " update File_Metadata set file_name =  'HILTON_DALMC_20120615_0148_T2SNAP.psv', SnapShot_DT='2012-06-15' where  File_Name = 'HILTON_DALMC_20110615_0148_T2SNAP.psv' and SnapShot_DT = '2011-06-15' ")
        connection.close();
        restClient = ForecastGroupUtil.login(PROPERTY_ID)
    }

    @After
    void after() {
        connection = DbConnection.getDatabaseConnection_DALMC()
        connection.executeUpdate(" " +
                " update File_Metadata set file_name =  'HILTON_DALMC_20110615_0148_TRANS.psv',  SnapShot_DT='2011-06-15' where  File_Name = 'HILTON_DALMC_20120615_0148_TRANS.psv'  and SnapShot_DT = '2012-06-15' " +
                " update File_Metadata set file_name =  'HILTON_DALMC_20110615_0148_rates.xml',  SnapShot_DT='2011-06-15' where  File_Name = 'HILTON_DALMC_20120615_0148_rates.xml'  and SnapShot_DT = '2012-06-15' " +
                " update File_Metadata set file_name =  'HILTON_DALMC_20110615_0148_T2SNAP.psv', SnapShot_DT='2011-06-15' where  File_Name = 'HILTON_DALMC_20120615_0148_T2SNAP.psv' and SnapShot_DT = '2012-06-15' ")
        connection.close();
        //super.after()
    }


    public void stageMarketSegmentsProposed() {
        final def SQL_MARKET_SEGMENT_PROPOSED_DELETE = "delete from Mkt_Seg_Details_Proposed"
        connection.executeUpdate(SQL_MARKET_SEGMENT_PROPOSED_DELETE.toString())

        def marketSegmentId
        // CREATED by extract file test ???
        final String[] SEGMENT_CODES = ["%CNR%", "%DISC%", "%IT%", "%LNR%", "%MKT%"]
        final int[] BUSINESS_TYPE_IDS = [2, 2, 1, 2, 2]
        final int[] YIELD_TYPE_IDS = [2, 3, 1, 3, 1]
        final int[] FORECAST_ACTIVITY_TYPE_IDS = [2, 3, 1, 1, 1]
        final int[] QUALIFIED = [1, 1, 1, 1, 0]
        final int[] BOOKING_BLOCK = [31, 0, 100, 0, 0]
//		final def SQL_MARKET_SEGMENT_PROPOSED_GET = "select count(
// *) as count from Mkt_Seg_Details_Proposed \
//				where Mkt_Seg_ID = {0} and Business_Type_ID = {1} and Yield_Type_ID = {2} and \
//				Forecast_Activity_Type_ID = {3} and Qualified = {4} and Booking_Block_Pc = {5}"
        final def SQL_MARKET_SEQMENT_PROPOSED_INSERT =
                "insert into Mkt_Seg_Details_Proposed \
                (Mkt_Seg_ID,Business_Type_ID,Yield_Type_ID,Forecast_Activity_Type_ID,Qualified, \
                Booking_Block_Pc,Fenced,Package,Link,Template_ID,Template_Default, \
                Process_Status_ID,Offset_Type_ID,Offset_Value,Status_ID,Priced_By_BAR) values \
                ({0},{1},{2},{3},{4},{5},0,0,0,0,0,10,null,null,1,1)"
//		final def SQL_MARKET_SEGMENT_PROPOSED_UPDATE = "update Mkt_Seg_Details_Proposed \
//				set Process_Status_ID = 10 \
//				where Mkt_Seg_ID = {0} and Business_Type_ID = {1} and Yield_Type_ID = {2} and \
//				Forecast_Activity_Type_ID = {3} and Qualified = {4} and Booking_Block_Pc = {5}"

        for (int i = 0; i < SEGMENT_CODES.length; i++) {
            marketSegmentId = getMarketSegmentByCode(SEGMENT_CODES[i])
//			if (0 == getTotalRowsForQuery(MessageFormat.format(SQL_MARKET_SEGMENT_PROPOSED_GET,
//					 marketSegmentId, BUSINESS_TYPE_IDS[i], YIELD_TYPE_IDS[i],
//					 FORECAST_ACTIVITY_TYPE_IDS[i], QUALIFIED[i], BOOKING_BLOCK[i]))) {
            println "Missing Mkt_Seg_Details_Proposed. Inserting."
            logm("Missing Mkt_Seg_Details_Proposed. Inserting.")
            println MessageFormat.format(SQL_MARKET_SEQMENT_PROPOSED_INSERT, marketSegmentId, BUSINESS_TYPE_IDS[i], YIELD_TYPE_IDS[i], FORECAST_ACTIVITY_TYPE_IDS[i], QUALIFIED[i], BOOKING_BLOCK[i])
            logm(MessageFormat.format(SQL_MARKET_SEQMENT_PROPOSED_INSERT, marketSegmentId, BUSINESS_TYPE_IDS[i], YIELD_TYPE_IDS[i], FORECAST_ACTIVITY_TYPE_IDS[i], QUALIFIED[i], BOOKING_BLOCK[i]))
            connection.executeUpdate(MessageFormat.format(SQL_MARKET_SEQMENT_PROPOSED_INSERT,
                    marketSegmentId, BUSINESS_TYPE_IDS[i], YIELD_TYPE_IDS[i],
                    FORECAST_ACTIVITY_TYPE_IDS[i], QUALIFIED[i], BOOKING_BLOCK[i]))
//			} else {
            println "Stale Mkt_Seg_Details_Proposed. Updating."
            logm("Stale Mkt_Seg_Details_Proposed. Updating.")
//				connection.executeUpdate(MessageFormat.format(SQL_MARKET_SEGMENT_PROPOSED_UPDATE,
//						getMktSegByCode, BUSINESS_TYPE_IDS[i], YIELD_TYPE_IDS[i],
//						FORECAST_ACTIVITY_TYPE_IDS[i], QUALIFIED[i], BOOKING_BLOCK[i]))
//			}
        }
    }

    int getMarketSegmentByCode(String marketSegmentCode) {
        final def SQL_MARKET_SEGMENT_ID = "select Mkt_Seg_ID from Mkt_Seg \
				where Mkt_Seg_Code like '${marketSegmentCode}'"
        return (int) connection.firstRow(SQL_MARKET_SEGMENT_ID.toString()).getAt(0)
    }

    @Test
    void createCommitForecastGroupsWithNewMarketSegments() {
        connection = DbConnection.getDatabaseConnection_DALMC();
        ForecastGroupUtil.setupEnvironment(restClient, PROPERTY_CODE, PROPERTY_CRS_TIMEZONE, PROPERTY_ID)
        ForecastGroupUtil.configurePropertyForForecastGroupies(connection, PROPERTY_CODE, PROPERTY_ID)

        println "!!! New market segments create/commit forecast groups"
        logm("!!! New market segments create/commit forecast groups")
        stageMarketSegmentsProposed()

        // Invoke same rest endpoint but flip the config parameter
        ForecastGroupUtil.checkAndUpdateForecastGroup(connection)
        ForecastGroupUtil.triggerJob(restClient, REST_URI_CREATE_FORECAST_GROUP)
        waitForNewJobInstancesWithName(JEMS_JOB_FG_CREATE)

        // Check CreateForecastGroups data conditions
        ForecastGroupUtil.assertCreateForecastGroupsData(connection)

        // Execute CommitForecastGroups
        ForecastGroupUtil.checkAndUpdateForecastGroup(connection)
        ForecastGroupUtil.triggerJob(restClient, REST_URI_COMMIT_FORECAST_GROUP)
        waitForNewJobInstancesWithName(JEMS_JOB_FG_COMMIT)

        // Verify that all the job instances completed successfully
        //assertAllJobInstancesCompletedSuccessfully()

        // Check CommitForecastGroups data conditions
        ForecastGroupUtil.assertCommitForecastGroupsData(connection, PROPERTY_CODE, PROPERTY_ID, timeTestStarted)
        ForecastGroupUtil.assertPropertyStage(pacmanServicesRestClient, PROPERTY_ID)
        connection.close();
    }
}
