package integration.g3.configure.decisions.decisionconfiguration

import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.PropertiesTestHelper
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import com.ideas.tetris.util.propertyrollout.PropertyRolloutTestHelper
import common.JemsTest
import groovy.json.JsonSlurper
import groovy.sql.Sql
import groovyx.net.http.ContentType
import org.joda.time.LocalDateTime
import org.testng.annotations.AfterTest
import org.testng.annotations.BeforeClass
import org.testng.annotations.BeforeTest
import org.testng.annotations.Test

import java.text.SimpleDateFormat

class ForcefulFullDecisionUploadTest extends JemsTest {

    private static final int CPGP02_PROPERTY_ID = 11033
    private static final String CPGP02_PROPERTY_CODE = "CPGP02"
    private static final int CPGP01_PROPERTY_ID = 11032
    private static final String CPGP01_PROPERTY_CODE = "CPGP01"
    public static final String BDE_IN_PROGRESS = "BDE_IN_PROGRESS"
    public static final String CDP_IN_PROGRESS = "CDP_IN_PROGRESS"
    public static final String IN_PROGRESS = "IN_PROGRESS"
    public static final String OPERA = 'opera'
    public static final String OPTIMA_CONTROLS = 'optimacontrols'
    String DB_CPGP02 = "011033"
    String DB_CPGP01 = "011032"
    String CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM = "pacman.core.property.externalSystem"
    String PROPERTY_EXTERNAL_SYSTEM = "NGI"
    String CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM_SUBSYSTEM = "pacman.core.property.externalSystem.subSystem"
    String PROPERTY_EXTERNAL_SYSTEM_SUBSYSTEM = "OXI"
    String CONFIG_PARAM_DECISION_HOTEL_OVERBOOKING_UPLOAD_TYPE = "pacman.integration.opera.HotelOverbooking.uploadtype"
    String CONFIG_PARAM_HTNG_HOTEL_OVERBOOKING_UPLOAD_TYPE = "pacman.integration.HotelOverbooking.uploadtype"
    String CONFIG_PARAM_HTNG_DAILY_BAR_UPLOAD_TYPE = "pacman.integration.DailyBAR.uploadtype"
    String CONFIG_PARAM_HTNG_ROOM_TYPE_OVERBOOKING_UPLOAD_TYPE = "pacman.integration.RoomTypeOverbooking.uploadtype"
    String CONFIG_PARAM_DECISION_ROOM_TYPE_OVERBOOKING_UPLOAD_TYPE = "pacman.integration.opera.RoomTypeOverbooking.uploadtype"
    String CONFIG_PARAM_DECISION_DAILY_BAR_UPLOAD_TYPE = "pacman.integration.opera.DailyBAR.uploadtype"
    String CONFIG_PARAM_DECISION_DAILY_BAR_RATE_CODE = "pacman.integration.DailyBAR.dailybarRateCode"
    String DAILY_BAR_RATE_CODE = "DBAR"
    String CONFIG_PARAM_DECISION_CONFIGURATION_ENABLED = "pacman.feature.DecisionConfigurationEnabled"
    String CONFIG_PARAM_PROPERTY_TIME_ZONE = "pacman.core.propertyTimeZone"
    String CPGP02_PROPERTY_CONTEXT = "pacman.SandBox.CPGP02"
    String CPGP01_PROPERTY_CONTEXT = "pacman.SandBox.CPGP01"
    String CPGP02_OPTIMA_CONTROLS_PROPERTY_CONTEXT = "optimacontrols.SandBox.CPGP02"
    String CPGP01_OPTIMA_CONTROLS_PROPERTY_CONTEXT = "optimacontrols.SandBox.CPGP01"
    String CONFIG_PARAM_FORCEFUL_UPLOAD = "pacman.feature.enableForceFullDecisions"
    String CONFIG_PARAM_FORCEFUL_UPLOAD_OUTBOUND = "pacman.feature.ForceFullDecisionsOutbounds"
    String DIFFERENTIAL_UPLOAD = "differential"
    //String PROPERTY_TIME_ZONE = "America/New_York"
    String PROPERTY_TIME_ZONE = "Asia/Kolkata"
    String SEED_DAILY_PROCESSING_JOB = "SeedDailyProcessingJob"
    HashMap<String, String> queryParam = new HashMap<String, String>()
    HashMap<String, String> htngQueryParam = new HashMap<String, String>()
    Map<String, String> PCRSQueryParam = new HashMap<>(Map.of(
            "operationType", "BDE",
            "externalSystem", "PCRS",
            "correlationId", "1234",
            "propertyId", "011033"
    ));
    Integer IS_BDE_IN_FILE_METADATA = 1
    Integer IS_CDP_IN_FILE_METADATA = 0
    String TRUE = "true"
    static Sql connection
    static Sql globalconnection
    String processingDate;

    @Override
    protected String getPropertyId() {
        return CPGP02_PROPERTY_ID
    }

    @Override
    protected String getPropertyCode() {
        return CPGP02_PROPERTY_CODE
    }

    @BeforeClass
    void setupBeforeClass(){
        setupMPPData()
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
    }

    @BeforeTest
    void setUp() {
        queryParam.put("externalSystem", "OPERA")
        htngQueryParam.put("partner", "OptimaControls")
        pacmanServicesRestClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), JemsTest.USER_NAME_SSO, JemsTest.USER_PWD_SSO)
        pacmanServicesRestClient.headers.clientCode = JemsTest.CLIENT_CODE_SANDBOX
        pacmanServicesRestClient.headers.clientId = getClientId()
        jobRestClient = new TetrisRESTClient(RestServiceUtil.getJEMSRestURL())
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM, CPGP02_PROPERTY_CONTEXT, PROPERTY_EXTERNAL_SYSTEM)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM_SUBSYSTEM, CPGP02_PROPERTY_CONTEXT, PROPERTY_EXTERNAL_SYSTEM_SUBSYSTEM)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM, CPGP01_PROPERTY_CONTEXT, PROPERTY_EXTERNAL_SYSTEM)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM_SUBSYSTEM, CPGP01_PROPERTY_CONTEXT, PROPERTY_EXTERNAL_SYSTEM_SUBSYSTEM)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_DECISION_HOTEL_OVERBOOKING_UPLOAD_TYPE, CPGP02_PROPERTY_CONTEXT, DIFFERENTIAL_UPLOAD)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_DECISION_ROOM_TYPE_OVERBOOKING_UPLOAD_TYPE, CPGP02_PROPERTY_CONTEXT, DIFFERENTIAL_UPLOAD)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_DECISION_ROOM_TYPE_OVERBOOKING_UPLOAD_TYPE, CPGP02_PROPERTY_CONTEXT, DIFFERENTIAL_UPLOAD)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_DECISION_DAILY_BAR_UPLOAD_TYPE, CPGP01_PROPERTY_CONTEXT, DIFFERENTIAL_UPLOAD)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_DECISION_CONFIGURATION_ENABLED, CPGP01_PROPERTY_CONTEXT, TRUE)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_DECISION_CONFIGURATION_ENABLED, CPGP02_PROPERTY_CONTEXT, TRUE)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_FORCEFUL_UPLOAD, CPGP01_PROPERTY_CONTEXT, TRUE)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_FORCEFUL_UPLOAD, CPGP02_PROPERTY_CONTEXT, TRUE)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_FORCEFUL_UPLOAD_OUTBOUND, CPGP01_PROPERTY_CONTEXT, TRUE)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_FORCEFUL_UPLOAD_OUTBOUND, CPGP02_PROPERTY_CONTEXT, TRUE)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_HTNG_HOTEL_OVERBOOKING_UPLOAD_TYPE, CPGP02_OPTIMA_CONTROLS_PROPERTY_CONTEXT, DIFFERENTIAL_UPLOAD)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_HTNG_ROOM_TYPE_OVERBOOKING_UPLOAD_TYPE, CPGP02_OPTIMA_CONTROLS_PROPERTY_CONTEXT, DIFFERENTIAL_UPLOAD)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_HTNG_DAILY_BAR_UPLOAD_TYPE, CPGP02_OPTIMA_CONTROLS_PROPERTY_CONTEXT, DIFFERENTIAL_UPLOAD)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_HTNG_DAILY_BAR_UPLOAD_TYPE, CPGP01_OPTIMA_CONTROLS_PROPERTY_CONTEXT, DIFFERENTIAL_UPLOAD)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_DECISION_DAILY_BAR_RATE_CODE, CPGP01_OPTIMA_CONTROLS_PROPERTY_CONTEXT, DAILY_BAR_RATE_CODE)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_TIME_ZONE, CPGP02_PROPERTY_CONTEXT, PROPERTY_TIME_ZONE)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_TIME_ZONE, CPGP01_PROPERTY_CONTEXT, PROPERTY_TIME_ZONE)
        globalconnection = DbConnection.getGlobalConnection()
        PropertiesTestHelper.setPropertyStage(pacmanServicesRestClient, CPGP02_PROPERTY_ID.toString(), PropertyRolloutTestHelper.STAGE_TWO_WAY)
        def seedParams = ['date': LocalDateTime.now()]
        def seedDailyProcessingJobId = invokeJobViaRestService(SEED_DAILY_PROCESSING_JOB, seedParams)
        logm("Seed Daily Processing Job with Id" + seedDailyProcessingJobId + " trigerred")
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "property/getPropertyZoneDate/v1", query: queryParam, requestContentType: ContentType.JSON)
        def jsonSlurper = new JsonSlurper()
        def object = jsonSlurper.parseText(response.properties.data.getAt("str").toString())
        def processingDateStr = object.getAt("propertyZoneDate").toString()
        def inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH)
        Date parsedDate = inputFormat.parse(processingDateStr)
        def outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        processingDate = outputFormat.format(parsedDate)
    }

    void setPropertyIdInRestClient(Integer propertyId) {
        pacmanServicesRestClient.headers.propertyId = propertyId
        queryParam.put("propertyId", propertyId)
        htngQueryParam.put("propertyId", propertyId)
    }

    void setDbConnection(String dbName) {
        connection = DbConnection.getTenantDatabaseConnection(dbName)
    }

    @Test
    void shouldFetchFullHotelOverBookingDecisionWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/HotelOverbookingByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def hotelOverbooking = response.properties.data
        assert hotelOverbooking.collect().size() == 365
    }

    @Test
    void shouldFetchFullHotelOverBookingDecisionWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/propertyOverbookingDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        def hotelOverbooking = response.properties.data
        assert hotelOverbooking.collect().size() == 365
    }

    @Test
    void shouldFetchDifferentialHotelOverBookingDecisionWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsCDP() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_CDP_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, CDP_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/HotelOverbookingByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def hotelOverbooking = response.properties.data
        assert hotelOverbooking.collect().size() == 219
    }

    @Test
    void shouldFetchDifferentialHotelOverBookingDecisionWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsCDP() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_CDP_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, CDP_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/propertyOverbookingDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        def hotelOverbooking = response.properties.data
        assert hotelOverbooking.collect().size() == 219
    }

    @Test
    void shouldFetchDifferentialHotelOverBookingDecisionForHTNGAndFullForOXIWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/HotelOverbookingByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def hotelOverbooking = response.properties.data
        assert hotelOverbooking.collect().size() == 365
        response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/propertyOverbookingDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        hotelOverbooking = response.properties.data
        assert hotelOverbooking.collect().size() == 219
    }

    @Test
    void shouldFetchDifferentialHotelOverBookingDecisionForOXIAndFullForHTNGWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/HotelOverbookingByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def hotelOverbooking = response.properties.data
        assert hotelOverbooking.collect().size() == 219
        response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/propertyOverbookingDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        hotelOverbooking = response.properties.data
        assert hotelOverbooking.collect().size() == 365
    }

    @Test
    void shouldFetchFullRoomOverBookingDecisionWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/RoomTypeOverbookingByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def roomTypeOverbooking = response.properties.data
        assert roomTypeOverbooking.collect().size() == 3285
    }

    @Test
    void shouldFetchFullRoomOverBookingDecisionWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomTypeOverbookingDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        def roomTypeOverbooking = response.properties.data
        assert roomTypeOverbooking.collect().size() == 3285
    }

    @Test
    void shouldFetchDifferentialRoomTypeOverBookingDecisionWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsCDP() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_CDP_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, CDP_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/RoomTypeOverbookingByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def roomTypeOverbooking = response.properties.data
        assert roomTypeOverbooking.collect().size() == 876
    }

    @Test
    void shouldFetchDifferentialRoomTypeOverBookingDecisionWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsCDP() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_CDP_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, CDP_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomTypeOverbookingDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        def roomTypeOverbooking = response.properties.data
        assert roomTypeOverbooking.collect().size() == 876
    }

    @Test
    void shouldFetchDifferentialRoomTypeOverBookingDecisionForHTNGAndFullForOXIWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/RoomTypeOverbookingByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def roomTypeOverbooking = response.properties.data
        assert roomTypeOverbooking.collect().size() == 3285
        response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomTypeOverbookingDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        roomTypeOverbooking = response.properties.data
        assert roomTypeOverbooking.collect().size() == 876
    }

    @Test
    void shouldFetchDifferentialRoomTypeOverBookingDecisionForOXIAndFullForHTNGWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP02, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP02_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/RoomTypeOverbookingByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def roomTypeOverbooking = response.properties.data
        assert roomTypeOverbooking.collect().size() == 876
        response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/roomTypeOverbookingDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        roomTypeOverbooking = response.properties.data
        assert roomTypeOverbooking.collect().size() == 3285
    }

    @Test
    void shouldFetchFullDailyBarDecisionWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP01_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP01, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP01_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP01_PROPERTY_CODE, CPGP01_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/DailyBARByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def dailyBarDecision = response.properties.data
        assert dailyBarDecision.collect().size() == 4380
    }

    @Test
    void shouldFetchFullDailyBarDecisionWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP01_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP01, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP01_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP01_PROPERTY_CODE, CPGP01_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/dailyBarDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        def dailyBarDecision = response.properties.data
        assert dailyBarDecision.getAt("DBAR").collect().size() == 4380
    }

    @Test
    void shouldFetchDifferentialDailyBarDecisionForHTNGAndFullForOXIWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP01_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP01, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP01_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP01_PROPERTY_CODE, CPGP01_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/DailyBARByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def dailyBarDecision = response.properties.data
        assert dailyBarDecision.collect().size() == 4380
        response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/dailyBarDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        dailyBarDecision = response.properties.data
        assert dailyBarDecision.getAt("DBAR").collect().size() == 3705
    }

    @Test
    void shouldFetchDifferentialDailyBarDecisionForOXIAndFullForHTNGWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsBDE() {
        setPropertyIdInRestClient(CPGP01_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP01, IS_BDE_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP01_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP01_PROPERTY_CODE, CPGP01_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/DailyBARByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def dailyBarDecision = response.properties.data
        assert dailyBarDecision.collect().size() == 3705
        response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/dailyBarDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        dailyBarDecision = response.properties.data
        assert dailyBarDecision.getAt("DBAR").collect().size() == 4380
    }

    @Test
    void shouldFetchDifferentialDailyBarDecisionWhenFullUploadConfiguredForOXIAndPropertyCurrentProcessingIsCDP() {
        setPropertyIdInRestClient(CPGP01_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP01, IS_CDP_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPERA, CPGP01_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP01_PROPERTY_CODE, CPGP01_PROPERTY_ID, CDP_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "agentDecision/DailyBARByExternalSystem/v1", query: queryParam, requestContentType: ContentType.JSON)
        def dailyBarDecision = response.properties.data
        assert dailyBarDecision.collect().size() == 3705
    }

    @Test
    void shouldFetchDifferentialDailyBarDecisionWhenFullUploadConfiguredForHTNGAndPropertyCurrentProcessingIsCDP() {
        setPropertyIdInRestClient(CPGP01_PROPERTY_ID)
        insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(DB_CPGP01, IS_CDP_IN_FILE_METADATA)
        setForcefulFullDecisionUploadForExternalOutBound(OPTIMA_CONTROLS, CPGP01_PROPERTY_ID)
        updateProcessingDateInPDP(CPGP01_PROPERTY_CODE, CPGP01_PROPERTY_ID, CDP_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "htng/dailyBarDecisionByExternalSystem/v1", query: htngQueryParam, requestContentType: ContentType.JSON)
        def dailyBarDecision = response.properties.data
        assert dailyBarDecision.getAt("DBAR").collect().size() == 3705
    }

    @Test
    void shouldFetchFullMPPDecisionWhenFullUploadConfigured() {
        setForceFullFlag(1)
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        PropertiesTestHelper.setPropertyStage(pacmanServicesRestClient, "11033", PropertyRolloutTestHelper.STAGE_TWO_WAY)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM, CPGP02_PROPERTY_CONTEXT, "PCRS")
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "hilstarDecisions/meeting-package-pricing/v1", query: PCRSQueryParam, requestContentType: ContentType.JSON)
        def MPPDecision = response.properties.data
        assert MPPDecision.getAt("Sandbox").collect().size() == 2190
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM, CPGP02_PROPERTY_CONTEXT, PROPERTY_EXTERNAL_SYSTEM)
    }

    @Test
    void shouldFetchDifferentialMPPDecisionWhenFullUploadNotConfigured() {
        setForceFullFlag(0)
        setPropertyIdInRestClient(CPGP02_PROPERTY_ID)
        PropertiesTestHelper.setPropertyStage(pacmanServicesRestClient, "11033", PropertyRolloutTestHelper.STAGE_TWO_WAY)
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM, CPGP02_PROPERTY_CONTEXT, "PCRS")
        updateProcessingDateInPDP(CPGP02_PROPERTY_CODE, CPGP02_PROPERTY_ID, BDE_IN_PROGRESS, IN_PROGRESS)
        def response = pacmanServicesRestClient.get(path: RestServiceUtil.getPacmanRestURL() + "hilstarDecisions/meeting-package-pricing/v1", query: PCRSQueryParam, requestContentType: ContentType.JSON)
        def MPPDecision = response.properties.data
        assert MPPDecision.getAt("Sandbox").collect().size() == 1898
        PropertyRolloutRESTUtil.setConfigParamValue(pacmanServicesRestClient, CONFIG_PARAM_PROPERTY_EXTERNAL_SYSTEM, CPGP02_PROPERTY_CONTEXT, PROPERTY_EXTERNAL_SYSTEM)
    }

    void updateProcessingDateInPDP(String propertyCode, Integer propertyId, String pdpStatus, String inputProcessingStatus) {
        globalconnection.execute("Update Property_Daily_Processing Set Status='" + pdpStatus + "' where Processing_Date=" + "Cast(GETDATE() as date)" + " and Property_Code=" + "'" + propertyCode + "'")
        globalconnection.execute("Update IP Set IP.Status='" + inputProcessingStatus + "'\n" + "from \n" + "Property_Daily_Processing PDP\n" + "inner join Input_Processing IP\n" + "on PDP.Property_Daily_Processing_ID=IP.Property_Daily_Processing_ID\n" + "where PDP.Processing_Date='" + processingDate + "' and PDP.Property_Code=" + "'" + propertyCode + "'")
    }

    void insertDecisionsInLastUploadTableAndCreateDecisionUploadEnteries(String dbName, int isBDE) {
        setDbConnection(dbName)
        globalconnection.execute("Truncate table Force_Full_Decisions")
        connection.execute("Truncate table Decision_Upload_Date_To_External_System")
        connection.execute("Truncate table PACE_Ovrbk_Property_Upload")
        connection.execute("Truncate table PACE_Ovrbk_Accom_Upload")
        connection.execute("Truncate table Decision_Upload_Date_To_External_System")
        connection.execute("Insert into PACE_Ovrbk_Property_Upload Select Decision_ID, Property_ID, Occupancy_DT, Overbooking_Decision, Expected_Walks, CreateDate_DTTM , Max_Solds from PACE_Ovrbk_Property")
        connection.execute("Insert into PACE_Ovrbk_Accom_Upload Select Decision_ID, Property_ID, Occupancy_DT, Accom_Type_ID, Overbooking_Decision, CreateDate_DTTM from PACE_Ovrbk_Accom")
        connection.execute("Insert into Decision_Upload_Date_To_External_System (Decision_ID, Decision_Name, Created_DTTM, Modify_DTTM, Last_Upload_DTTM, Status, Upload_Type,External_System_Name)\n" + "values(94,'HotelOverbooking','2017-11-20 14:16:33.323','2020-02-05 08:56:54.560','2020-02-05 08:56:54.560','SUC','full','opera')\n")
        connection.execute("Insert into Decision_Upload_Date_To_External_System (Decision_ID, Decision_Name, Created_DTTM, Modify_DTTM, Last_Upload_DTTM, Status, Upload_Type,External_System_Name)\n" + "values(94,'HotelOverbooking','2017-11-20 14:16:33.323','2020-02-05 08:56:54.560','2020-02-05 08:56:54.560','SUC','full','OptimaControls')\n")
        connection.execute("Insert into Decision_Upload_Date_To_External_System (Decision_ID, Decision_Name, Created_DTTM, Modify_DTTM, Last_Upload_DTTM, Status, Upload_Type,External_System_Name)\n" + "values(94,'RoomTypeOverbooking','2017-11-20 14:16:33.323','2020-02-05 08:56:54.560','2020-02-05 08:56:54.560','SUC','full','opera')")
        connection.execute("Insert into Decision_Upload_Date_To_External_System (Decision_ID, Decision_Name, Created_DTTM, Modify_DTTM, Last_Upload_DTTM, Status, Upload_Type,External_System_Name)\n" + "values(94,'RoomTypeOverbooking','2017-11-20 14:16:33.323','2020-02-05 08:56:54.560','2020-02-05 08:56:54.560','SUC','full','OptimaControls')")
        connection.execute("Insert into Decision_Upload_Date_To_External_System (Decision_ID, Decision_Name, Created_DTTM, Modify_DTTM, Last_Upload_DTTM, Status, Upload_Type,External_System_Name)\n" + "values(12,'DailyBAR','2017-11-17 21:32:09.773','2017-11-17 21:32:09.773','2017-11-17 21:32:09.773','SUC','full','opera')")
        connection.execute("Insert into Decision_Upload_Date_To_External_System (Decision_ID, Decision_Name, Created_DTTM, Modify_DTTM, Last_Upload_DTTM, Status, Upload_Type,External_System_Name)\n" + "values(12,'MeetingPackagePricing','2017-11-17 21:32:09.773','2017-11-17 21:32:09.773','2017-11-24 15:39:42.740','SUC','full','PCRS')")
        connection.execute("Insert into Decision_Upload_Date_To_External_System (Decision_ID, Decision_Name, Created_DTTM, Modify_DTTM, Last_Upload_DTTM, Status, Upload_Type,External_System_Name)\n" + "values(12,'DailyBAR','2017-11-17 21:32:09.773','2017-11-17 21:32:09.773','2017-11-17 21:32:09.773','SUC','full','OptimaControls')")
        connection.execute("Update File_Metadata Set IsBDE=" + isBDE + "  where File_Metadata_ID = (Select top 1 File_Metadata_ID from File_Metadata where Record_Type_ID=3 order by 1 desc )")
    }

    void setForcefulFullDecisionUploadForExternalOutBound(String externalSystem, Integer propertyId) {
        globalconnection.execute("Insert into Force_Full_Decisions\n" + "(Client_ID, Property_ID,\tOutbound_Name,\tIsForceFullDecision, Created_by_User_ID,Created_DTTM, Last_Updated_by_User_ID, Last_Updated_DTTM)\n" + "values (6, " + propertyId + ", '" + externalSystem + "', 1, 11403, GetDate(), 11403, GetDate())\n")
    }

    void setForceFullFlag(int flagValue) {
        globalconnection.executeUpdate("""
        UPDATE Property SET Force_Full_Decisions = ${flagValue};
    """)
    }

    void setupMPPData(){
        setDbConnection(DB_CPGP02)
        connection.execute("INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('FullDay', 1, '08:00:00.0000000', '20:00:00.0000000', 1, getdate(), 1, getdate())\n" +
                "\n" +
                "INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('AM', 1, '08:00:00.0000000', '12:00:00.0000000', 1, getdate(), 1, getdate())\n" +
                "\n" +
                "INSERT INTO MP_Cfg_Day_Part (Name, Status_ID, Start_Time, End_Time, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "VALUES ('PM', 1, '16:00:00.0000000', '20:00:00.0000000', 1, getdate(), 1, getdate())\n" +
                "\n" +
                "Insert into MP_Product(\tName, Description, System_Default, Code, Type, Dependent_Product_ID, Is_DOW_Offset, Is_Upload, Status_ID, Is_Default_Inactive, Invalid_Reason_ID, Is_Optimized, Product_Floor_Rate, Price_Rounding_Rule, Minimum_Price_Change, Offset_Method, Display_Order, Centrally_Managed, Is_Overridable, Floor_Type, Floor_Percentage, MP_Cfg_Day_Part_ID, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM)\n" +
                "Values\n" +
                "('All Day Meeting Package','All Day Meeting Package',1,'MP_AGILE','INDEPENDENTLY',NULL,0,1,1,0,NULL,1,NULL,1,5,2,1,0,1,2,NULL,(SELECT MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'FullDay'),11403,11403,11403,11403),\n" +
                "('AM Meeting Package','AM Meeting Package',1,'MP_AGILE','INDEPENDENTLY',NULL,0,1,1,0,NULL,1,NULL,1,5,2,1,0,1,2,NULL,(SELECT MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'AM'),11403,11403,11403,11403),\n" +
                "('PM Meeting Package','PM Meeting Package',1,'MP_AGILE','INDEPENDENTLY',NULL,0,1,1,0,NULL,1,NULL,1,5,2,1,0,1,2,NULL,(SELECT MP_Cfg_Day_Part_ID FROM MP_Cfg_Day_Part WHERE Name = 'PM'),11403,11403,11403,11403)\n")
        connection.execute("WITH mapping AS (\n" +
                "    SELECT 'All Day Meeting Package' AS ProductName, 1  AS FS_Cfg_Func_Room_ID, 2 AS Accom_Type_ID UNION ALL\n" +
                "    SELECT 'All Day Meeting Package', 2, 2 UNION ALL\n" +
                "\n" +
                "    SELECT 'AM Meeting Package', 1, 5 UNION ALL\n" +
                "    SELECT 'AM Meeting Package', 2, 5 UNION ALL\n" +
                "\n" +
                "    SELECT 'PM Meeting Package', 1, 8 UNION ALL\n" +
                "    SELECT 'PM Meeting Package', 2, 8 \n" +
                ")\n" +
                "INSERT INTO MP_Decision_Bar\n" +
                "(Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT,\n" +
                " Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate,\n" +
                " Final_BAR, Previous_BAR, Optimal_BAR_Type, Adjustment_Value, Created_DTTM)\n" +
                "SELECT \n" +
                "    c.Decision_ID,\n" +
                "    mp.MP_Product_ID,\n" +
                "    c.Decision_Reason_Type_ID,\n" +
                "    m.FS_Cfg_Func_Room_ID,\n" +
                "    c.Arrival_dt,\n" +
                "    c.Optimal_BAR,\n" +
                "    c.Pretty_BAR,\n" +
                "    c.Override,\n" +
                "    c.Floor_Rate,\n" +
                "    c.Ceil_Rate,\n" +
                "    c.User_Specified_Rate,\n" +
                "    c.Final_BAR,\n" +
                "    c.Previous_BAR,\n" +
                "    c.Optimal_BAR_Type,\n" +
                "    c.Adjustment_Value,\n" +
                "    c.CreateDate\n" +
                "FROM CP_Decision_Bar_Output c\n" +
                "JOIN mapping m \n" +
                "    ON c.Accom_Type_ID = m.Accom_Type_ID\n" +
                "JOIN MP_Product mp\n" +
                "    ON mp.Name = m.ProductName\n" +
                "WHERE c.Product_Id = 1;")
        connection.execute("INSERT INTO MP_Pace_Decision_Bar_Differential (Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Optimal_BAR_Type, Adjustment_Value, Business_DT, Created_DTTM) \n" +
                "SELECT c.Decision_ID, (SELECT MP_Product_ID FROM MP_Product WHERE Name = 'All Day Meeting Package'), c.Decision_Reason_Type_ID, 1, c.arrival_dt, c.optimal_bar, c.Pretty_BAR, c.Override, c.Floor_Rate, c.Ceil_Rate, c.User_Specified_Rate, c.Final_BAR, c.Optimal_bar_type, c.adjustment_value, d.Business_DT, c.CreateDate FROM CP_Pace_Decision_Bar_Output c JOIN Decision d ON c.Decision_ID = d.Decision_ID WHERE c.Accom_Type_ID = 2 AND c.Product_id = 1;\n" +
                "\n" +
                "INSERT INTO MP_Pace_Decision_Bar_Differential (Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Optimal_BAR_Type, Adjustment_Value, Business_DT, Created_DTTM) \n" +
                "SELECT c.Decision_ID, (SELECT MP_Product_ID FROM MP_Product WHERE Name = 'All Day Meeting Package'), c.Decision_Reason_Type_ID, 2, c.arrival_dt, c.optimal_bar, c.Pretty_BAR, c.Override, c.Floor_Rate, c.Ceil_Rate, c.User_Specified_Rate, c.Final_BAR, c.Optimal_bar_type, c.adjustment_value, d.Business_DT, c.CreateDate FROM CP_Pace_Decision_Bar_Output c JOIN Decision d ON c.Decision_ID = d.Decision_ID WHERE c.Accom_Type_ID = 2 AND c.Product_id = 1;\n" +
                "\n" +
                "INSERT INTO MP_Pace_Decision_Bar_Differential (Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Optimal_BAR_Type, Adjustment_Value, Business_DT, Created_DTTM) \n" +
                "SELECT c.Decision_ID, (SELECT MP_Product_ID FROM MP_Product WHERE Name = 'AM Meeting Package'), c.Decision_Reason_Type_ID, 1, c.arrival_dt, c.optimal_bar, c.Pretty_BAR, c.Override, c.Floor_Rate, c.Ceil_Rate, c.User_Specified_Rate, c.Final_BAR, c.Optimal_bar_type, c.adjustment_value, d.Business_DT, ISNULL(c.CreateDate, GETDATE()) FROM CP_Pace_Decision_Bar_Output c JOIN Decision d ON c.Decision_ID = d.Decision_ID WHERE c.Accom_Type_ID = 5 AND c.Product_id = 1;\n" +
                "\n" +
                "INSERT INTO MP_Pace_Decision_Bar_Differential (Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Optimal_BAR_Type, Adjustment_Value, Business_DT, Created_DTTM) \n" +
                "SELECT c.Decision_ID, (SELECT MP_Product_ID FROM MP_Product WHERE Name = 'AM Meeting Package'), c.Decision_Reason_Type_ID, 2, c.arrival_dt, c.optimal_bar, c.Pretty_BAR, c.Override, c.Floor_Rate, c.Ceil_Rate, c.User_Specified_Rate, c.Final_BAR, c.Optimal_bar_type, c.adjustment_value, d.Business_DT, ISNULL(c.CreateDate, GETDATE()) FROM CP_Pace_Decision_Bar_Output c JOIN Decision d ON c.Decision_ID = d.Decision_ID WHERE c.Accom_Type_ID = 5 AND c.Product_id = 1;\n" +
                "\n" +
                "INSERT INTO MP_Pace_Decision_Bar_Differential (Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Optimal_BAR_Type, Adjustment_Value, Business_DT, Created_DTTM) \n" +
                "SELECT c.Decision_ID, (SELECT MP_Product_ID FROM MP_Product WHERE Name = 'PM Meeting Package'), c.Decision_Reason_Type_ID, 1, c.arrival_dt, c.optimal_bar, c.Pretty_BAR, c.Override, c.Floor_Rate, c.Ceil_Rate, c.User_Specified_Rate, c.Final_BAR, c.Optimal_bar_type, c.adjustment_value, d.Business_DT, c.CreateDate FROM CP_Pace_Decision_Bar_Output c JOIN Decision d ON c.Decision_ID = d.Decision_ID WHERE c.Accom_Type_ID = 8 AND c.Product_id = 1;\n" +
                "\n" +
                "INSERT INTO MP_Pace_Decision_Bar_Differential (Decision_ID, MP_Product_ID, Decision_Reason_Type_ID, FS_Cfg_Func_Room_ID, Occupancy_DT, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate, User_Specified_Rate, Final_BAR, Optimal_BAR_Type, Adjustment_Value, Business_DT, Created_DTTM) \n" +
                "SELECT c.Decision_ID, (SELECT MP_Product_ID FROM MP_Product WHERE Name = 'PM Meeting Package'), c.Decision_Reason_Type_ID, 2, c.arrival_dt, c.optimal_bar, c.Pretty_BAR, c.Override, c.Floor_Rate, c.Ceil_Rate, c.User_Specified_Rate, c.Final_BAR, c.Optimal_bar_type, c.adjustment_value, d.Business_DT, c.CreateDate FROM CP_Pace_Decision_Bar_Output c JOIN Decision d ON c.Decision_ID = d.Decision_ID WHERE c.Accom_Type_ID = 8 AND c.Product_id = 1;")
    }

    @AfterTest
    void tearDown() {
        globalconnection.execute("Truncate table Force_Full_Decisions")
        connection.execute("Update File_Metadata Set IsBDE=1 where File_Metadata_ID = (Select top 1 File_Metadata_ID from File_Metadata where Record_Type_ID=3 order by 1 desc )")
    }
}