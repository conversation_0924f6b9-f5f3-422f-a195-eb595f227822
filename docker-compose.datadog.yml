name: g3
services:
  app:
    build: docker/app
    container_name: g3_app
    env_file:
      - ./.devcontainer/scripts/.env
    environment:
      - DEPLOY_EXPLODED_LOCAL=true
      - TZ=America/Chicago
      - JAVA_OPTS=${JAVA_OPTS:--Xmx4g -Xms4g -Djava.net.preferIPv4Stack=true -agentpath:/libjrebel64.so -Drebel.remoting_plugin=true -Drebel.log.stdout=false -Dspring.profiles.active=LocalDevelopment -XX:-OmitStackTraceInFastThrow -javaagent:/opt/datadog/dd-java-agent.jar}
      - ENV=LocalDevelopment
      - DB_HOST=localhost
      - SAS_HOST=localhost
      - DB_USER=sa
      - DB_PASSWORD=IDeaS123
      - DD_SERVICE=g3_app
      - DD_DBM_PROPAGATION_MODE=${DD_DBM_PROPAGATION_MODE:-full}
      - DD_INTEGRATION_JDBC_DATASOURCE_ENABLED=${DD_INTEGRATION_JDBC_DATASOURCE_ENABLED:-false}
      - DD_DBM_TRACE_PREPARED_STATEMENTS=${DD_DBM_TRACE_PREPARED_STATEMENTS:-false}
      - DD_LOGS_INJECTION=${DD_LOGS_INJECTION:-true}
      - DD_TRACE_DEBUG=${DD_TRACE_DEBUG:-false}
      - DD_TRACE_METHODS=${DD_TRACE_METHODS:-com.ideas.tetris.jems.core.health.G3ApplicationHealthIndicator[*];com.ideas.tetris.jems.core.health.SqsQueueHealthIndicator[*];com.ideas.tetris.jems.core.health.SasHealthIndicator[*];com.ideas.tetris.jems.core.health.CacheHealthIndicator[*];com.ideas.tetris.jems.core.health.DatabaseHealthIndicator[*];com.ideas.tetris.jems.core.health.FileSystemHealthIndicator[*];com.ideas.tetris.jems.core.health.JmsHealthIndicator[*];com.ideas.tetris.jems.core.health.RedisCacheChannelHealthIndicator[*];com.ideas.tetris.jems.core.health.SqsQueueHealthIndicator[*];com.ideas.tetris.ui.modules.businessanalysis.BusinessAnalysisView[*];com.ideas.tetris.ui.modules.businessanalysis.BusinessAnalysisPresenter[*];com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService[*]}
      - DD_PROFILING_ENABLED=${DD_PROFILING_ENABLEDL:-true}
      - DD_RUM_ENABLE=${DD_RUM_ENABLE:-true}
      - DD_DYNAMIC_INSTRUMENTATION_ENABLED=${DD_DYNAMIC_INSTRUMENTATION_ENABLED:-true}
      - DD_CODE_ORIGIN_FOR_SPANS_ENABLED=${DD_CODE_ORIGIN_FOR_SPANS_ENABLED:-true}
      - AWS_REGION=us-east-2
      - AWS_S3_ENDPOINT="http://localhost:4566
    volumes:
      - ${HOST_PROJECT_PATH:-.}/ear/platform-ear/target/platform-ear-4.1.0-SNAPSHOT/lib:/opt/wildfly/standalone/deployments/platform-ear.ear/lib
      - ${HOST_PROJECT_PATH:-.}/ear/platform-ear/target/platform-ear-4.1.0-SNAPSHOT/META-INF:/opt/wildfly/standalone/deployments/platform-ear.ear/META-INF
      - ${HOST_PROJECT_PATH:-.}/source/services/global-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/global-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/tenant-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/tenant-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/idg-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/idg-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/ratchet-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/ratchet-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/job-services/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/job-services.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/ejb-htng-integration/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/ejb-htng-integration.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/ejb-pacman/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/ejb-pacman.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/reports-g3/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/reports-g3.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/war-g3/target/pacman-platformsecurity:/opt/wildfly/standalone/deployments/platform-ear.ear/pacman-platformsecurity.war
      - ${HOST_PROJECT_PATH:-.}/source/ui/vaadin/vaadin-web/target/vaadin-web:/opt/wildfly/standalone/deployments/platform-ear.ear/vaadin-web.war
      - ${HOST_PROJECT_PATH:-.}/source/ui/angular/target/war-angular:/opt/wildfly/standalone/deployments/platform-ear.ear/war-angular.war
      - ${HOST_PROJECT_PATH:-.}/source/db/tenant-runnable/target/classes:/opt/wildfly/standalone/deployments/platform-ear.ear/tenant-runnable.jar
      - ${HOST_PROJECT_PATH:-.}/source/services/war-job/job-web/target/job-web:/opt/wildfly/standalone/deployments/job-web.war
      - ${HOST_PROJECT_PATH:-.}/source/services/war-api/target/api:/opt/wildfly/standalone/deployments/api.war
      - ~/.jrebel:/.jrebel
      - /opt/jrebel/jrebel.jar:/jrebel.jar
      - /opt/jrebel/lib/libjrebel64.so:/libjrebel64.so
      - ${HOST_PROJECT_PATH:-.}/.data/G3:/opt/G3
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Logs:/opt/SAS/Logs
      - ${HOST_PROJECT_PATH:-.}/.data/wildfly/logs:/opt/wildfly/standalone/log
    entrypoint:
      [
        "/bin/bash",
        "-c",
        "/entrypoint.sh && /opt/wildfly/bin/standalone.sh -b=0.0.0.0 -bmanagement=0.0.0.0 --debug *:8787",
      ]
    depends_on:
      db:
        condition: service_healthy
      apache:
        condition: service_healthy
      sas:
        condition: service_healthy
      localstack:
        condition: service_healthy
      dd-agent:
        condition: service_healthy
    labels:
      com.datadoghq.ad.init_configs: |
        [
            {
                "is_jmx": "true",
                "collect_default_metrics": "true",
                "new_gc_metrics": "true",
                "custom_jar_paths":[
                    "/usr/local/datadog-agent/lib/jboss-cli-client.jar"
                ]
            }
        ]
      com.datadoghq.ad.instances: |
        [
            {
                "jmx_mx": "service:jmx:remote+http://localhost:9990",
                "user": "idread",
                "password": "IDeaS123",
                "name": "g3_app",
                "max_returned_metrics": 800,
                "tags": [
                    "env:local",
                    "role:app"
                ]
            }
        ]
      com.datadoghq.ad.logs: >-
        [
            {
                "type": "file",
                "path": "/opt/wildfly/log/server.json",
                "source": "jboss_wildfly",
                "service": "g3_app",
                "tags": [
                    "application:g3"
                ]
            }
        ]
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "10"
    network_mode: host

  apache:
    build:
      context: docker/apache
      args:
        DATADOG_VERSION: ${DATADOG_VERSION:-v1.0.3}
        INSTALL_DATADOG: ${INSTALL_DATADOG:-false}
    container_name: g3_apache
    environment:
      - APP_HOST=localhost
      - TZ=America/Chicago
      - DD_SERVICE=g3_apache
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/apache/logs:/usr/local/apache2/logs
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "10"
    labels:
      com.datadoghq.ad.checks: |
        {
            "apache": {
                "apache_status_url": "http://%%host%%/server-status?auto",
                "host": "${DD_HOSTNAME:-$(hostname)}",
                "reported_hostname": "${DD_HOSTNAME:-$(hostname)}",
                "tags": [
                    "env:local",
                    "role:web"
                ]
            }
        }
      com.datadoghq.ad.logs: >-
        [
            {"type": "file",
            "path": "/opt/Apache/Logs/access*.log*",
            "source": "apache",
            "service": "g3_apache",
            "tags": [
                "application:apache"
            ]},
            {"type": "file",
                "path": "/opt/Apache/Logs/error*.log*",
                "source": "apache",
                "service": "g3_apache",
                "tags": [
                    "application:apache"
                ]}
        ]
    network_mode: host

  db:
    build: docker/db
    environment:
      - TZ=America/Chicago
      - MSSQL_AGENT_ENABLED=False
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/mssql:/var/opt/mssql
      - ${HOST_PROJECT_PATH:-.}/.data/G3/Temp/ftp:/opt/G3/Temp/ftp
    labels:
      com.datadoghq.ad.checks: |
        {
            "sqlserver": {
                "instances": [
                {
                    "dbm": "true",
                    "host": "localhost",
                    "port": 1433,
                    "reported_hostname": "${DD_HOSTNAME:-$(hostname)}",
                    "connector": "odbc",
                    "driver": "{ODBC Driver 18 for SQL Server}",
                    "connection_string": "TrustServerCertificate=yes;",
                    "username": ${DD_SQL_USER:-"sa"},
                    "password": ${DD_SQL_USER_PAS:-"IDeaS123"},
                    "include_ao_metrics": "true",
                    "database_autodiscovery": "true",
                    "schemas_collection": {
                        "enabled": "false",
                        "collection_interval": 600,
                        "max_schemas": 1000
                    },
                    "database_metrics": {
                        "index_usage_metrics": {
                            "enabled": true
                        }
                    },
                    "include_system_database": "false",
                    "include_index_usage_metrics": "false",
                    "collect_settings": {
                        "enabled": "true",
                        "collection_interval": 600
                    },
                    "agent_jobs": {
                        "enabled": true,
                        "collection_interval": 15,
                        "history_row_limit": 10000
                    },
                    "deadlocks_collection": {
                        "enabled": true,
                        "collection_interval": 600,
                        "max_deadlocks": 100
                    },
                    "custom_queries": [
                    {
                        "query": "SELECT ISNULL((SELECT TOP 1 DateDiff(Second, dtat.transaction_begin_time, GETDATE()) AS indoubt_transaction_run_time FROM sys.dm_tran_locks dtl INNER JOIN sys.dm_tran_active_transactions dtat ON dtl.request_owner_guid = dtat.transaction_uow INNER JOIN sys.databases d ON dtl.resource_database_id = d.database_id WHERE dtl.request_session_id = -2 ORDER BY indoubt_transaction_run_time DESC), 0) AS indoubt_transaction_run_time",
                        "columns": [
                            {
                                "name": "indoubt_transaction_run_time",
                                "type": "gauge"
                            }
                        ],
                        "tags": [
                            "ideas:indoubt_transaction_run_time"
                        ]
                    }],
                    "tags": [
                        "env:local",
                        "role:dbs"
                    ]
                }
                ]
            }
        }
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "10"
    network_mode: host

  sas:
    image: ideasinc/g3_sas:main
    environment:
      TZ: America/Chicago
      AWS_REGION: us-east-2
      SPRING_APPLICATION_JSON: '{
        "spring.profiles.active": "local",
        "aws.s3.endpoint": "http://localhost:4566"
        }'
    working_dir: /opt/SAS
    entrypoint: /entrypoint.sh encode
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Data/Properties:/opt/SAS/Data/Properties
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Data/Ratchet/Properties:/opt/SAS/Data/Ratchet/Properties
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Logs:/opt/SAS/Logs
      - ${HOST_PROJECT_PATH:-.}/.data/SAS/Temp:/opt/SAS/Temp
      - ${HOST_PROJECT_PATH:-.}/.data/G3/Data:/opt/G3/Data
      - ${HOST_PROJECT_PATH:-.}/.data/G3/Temp:/opt/G3/Temp
    labels:
      com.datadoghq.ad.logs: >-
        [
           {
                "source": "tomcat",
                "service": "g3_sas",
                "tags": [
                    "application:sas"
                ],
                "log_processing_rules": [{
                    "type": "multi_line",
                    "name": "log_start_with_date",
                    "pattern": "\\d{4}-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])"
                }]
            }
        ]
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "10"
    network_mode: host

  localstack:
    image: localstack/localstack:latest
    environment:
      - AWS_REGION=us-east-2
      - DEBUG=1
      - USE_SSL=0
      - SERVICES=s3,sqs
      - AWS_DEFAULT_REGION=us-east-2
      - PERSISTENCE=1
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
      - ${HOST_PROJECT_PATH:-.}/.data/localstack:/var/lib/localstack
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "10"
    network_mode: host

  dd-agent:
    image: gcr.io/datadoghq/agent:latest-jmx
    container_name: dd-agent
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc/:/host/proc/:ro
      - /sys/fs/cgroup/:/host/sys/fs/cgroup:ro
      - /var/run/datadog/:/var/run/s6
      - ${HOST_PROJECT_PATH:-.}/.data/apache/logs:/opt/Apache/Logs:ro
      - ${HOST_PROJECT_PATH:-.}/.data/wildfly/logs:/opt/wildfly/log/:ro
      - ${HOST_PROJECT_PATH:-.}/.data/datadog/logs:/var/log/datadog
    env_file:
      - ./.devcontainer/scripts/.env
    environment:
      - TZ=America/Chicago
      - DD_PROCESS_AGENT_ENABLED=true
      - DD_LOGS_ENABLED=true
      - DD_APM_ENABLED=true
      - DD_APM_ENV=local
      - DD_JMX_COLLECTION_TIMEOUT=180
      - DOCKER_DD_AGENT=true
      - DD_LOG_LEVEL=INFO
      - DD_JMX_RECONNECTION_TIMEOUT=180
      - DD_TAGS="env:${DD_TAG_ENV:-local}"
      - DD_APM_IGNORE_RESOURCES="select\s*\?", "use\s+\[\s*\?\s*\]", "set\s+context_info\s+\?", ".*xp_sqljdbc_xa_.*", "PING"
      - DD_PROCESS_CONFIG_PROCESS_COLLECTION_ENABLED=true
      - DD_PROCESS_CONFIG_CONTAINER_COLLECTION_ENABLED=true
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "10"
    network_mode: host

  ui_test:
    build: app-test/ui/playwright
    container_name: g3_app_ui_test
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/G3/test-results:/playwright/test-results
    command: ["npx", "playwright", "test"]
    network_mode: host

  echo-server:
    image: ealen/echo-server:latest
    container_name: g3_echo_server
    environment:
      - PORT=8080
      - LOGS__IGNORE__PING=true
    ports:
      - "8080:8080"
    labels:
      com.datadoghq.ad.logs: >-
        [
            {
                "source": "echo-server",
                "service": "g3_echo_server",
                "tags": [
                    "application:echo-server"
                ]
            }
        ]
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "10"
    network_mode: host

  ui_mvn_test:
    build:
      context: docker/test
      args:
        NO_CACHE: ${NO_CACHE:-false}
      pull: true
    container_name: g3_app_ui_mvn_test
    volumes:
      - ${HOST_PROJECT_PATH:-.}/.data/G3/test-results:/root/test-results
      - ${HOST_HOME_PATH:-~}/.m2/repository:/root/.m2/repository
      - ${HOST_PROJECT_PATH:-.}/.:/root/repo/
      - ${HOST_PROJECT_PATH:-.}/docker/build/settings.xml:/usr/share/maven/conf/settings.xml
    tty: true
    stdin_open: true
    #command:
    #  [
    #  "mvn", "-B", "-s", "/usr/share/maven/conf/settings.xml", "integration-test",
    #  "-f", "source/ui/vaadin/pom.xml",
    #  "-DenvHost=localhost",
    #  "-DdbHost=localhost",
    #  "-Ptestbench",
    #  "-DfailIfNoTests=false,-includeJunitSurefirePlugin=false",
    #  "-Dtest=com.ideas.tetris.ui.modules.functional.g3.shell.ShellSmokeUiTest",
    #  "-Dtestbench.domain=http://localhost/"
    #  ]
    network_mode: host
