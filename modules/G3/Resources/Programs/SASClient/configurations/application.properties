server.host=${sasClientHost}
server.port=${sasClientPort}

server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=${sasClientHome}/logs
server.tomcat.accesslog.rename-on-rotate=true
server.tomcat.accesslog.request-attributes-enabled=true

macro.timeout.ms=6900000

sas.path=${sasClientHomePath}/SASFoundation/9.4/sas
sas.config.file=${sasClientHome}/config/sasv9_standalone.cfg
sas.sp.file=${sasClientHome}/config/ideasStandalone.sas
sas.options=-NOTERMINAL,-NOSPLASH,-NOSTATUSWIN,-NOICON
sas.log.directory=${sasLogDir}
sas.temp.directory=${sasTempDir}
sas.data.directory=C:/SAS/Data/Properties
sas.ddl.directory=C:/SAS/Deploy/DDL/Analytics/DDL_Scripts
sas.autoexec.path=${sasClientHome}/config/autoexec_std.sas 
sas.baseseed.path=C:/SAS/Deploy/DDL/Analytics/run_base_tetris_seed.sas
sas.ratchet.data.directory=C:/SAS/Data/Ratchet/Properties
sas.ratchet.ddl.directory=C:/SAS/Deploy/DDL/DataIntg/Ratchet/DDL_Scripts
sas.ratchet.baseseed.path=C:/SAS/Deploy/DDL/DataIntg/Ratchet/run_base_ratchet_seed.sas

tk.path=${sasClientHomePath}/SASFoundation/9.4/core/sasext
tkbridge.path=${sasClientHomePath}/SASFoundation/9.4/tkbridge
tk.function.package=tktetrisbridge
tk.function.to.execute=tetrisExecute
# management api properties
management.server.port=7072
management.address=0.0.0.0
management.security.enabled=false

management.endpoints.web.exposure.include=*
management.endpoint.shutdown.enabled=false
management.endpoint.env.post.enabled=true
management.endpoint.configprops.keys-to-sanitize=password,secret
management.endpoint.health.show-details=always
management.endpoints.web.base-path=
# sas file path properties
sas.base.drive.path=C:\\
linux.app.base.drive.path=/opt
sas.include.path.conversion.params=dataset_path,rmsFilePath,sasXleMapFile,sasDataSetPath,sascatalogsbasepath,validationlogpath,etlFileName,logFilePath,output_lib,ldbInputFolder,outputPath,etlFileName,scriptPath,location_path

# Logback properties
logback.logfile.path=../logs/sasClient.json

# JMX
endpoints.jmx.enabled=true

spring.profiles.active=${sasActiveProfile}
aws.s3.region=${sasAwsRegion}
aws.s3.endpoint=https://s3.us-east-2.amazonaws.com
aws.s3.access-key=${iamAccessKey}
aws.s3.secret-key=${iamSecretKey}
aws.s3.bucket-name=${sasAwsS3BucketName}
aws.s3.base-path-prefix=${sasAwsS3BucketPathPrefix}
aws.s3.async-throughput=20
dataset.encode.proc.migrate.path=C:/SAS/Deploy/Scripts/encoding/proc_migrate_sas_encoding.sas
dataset.encode.thread.corePoolSize=5
dataset.encode.thread.maxPoolSize=7
dataset.encode.thread.queueCapacity=300
use.proc.migrate=true
dataset.encode.output.path=C:/SAS/Data/Properties
dataset.encode.source.path=C:/SAS/Data/Properties/SourceLinuxProperties
